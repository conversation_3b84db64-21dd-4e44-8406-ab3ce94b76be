# Production-specific Docker Compose overrides
# Use with: docker-compose -f docker-compose.yml -f docker-compose.prod.yml --profile production up -d

services:
  # Production frontend (built and served with nginx)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: xd-frontend-prod
    restart: unless-stopped
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    # Remove development-specific volumes and healthcheck
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  backend:
    environment:
      - DEBUG=False
      - DJANGO_SETTINGS_MODULE=config.settings.production
    command: python /app/scripts/run_with_reload.py
    # Remove development-specific volumes and healthcheck
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/').read()"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  celery:
    environment:
      - DEBUG=False
      - DJANGO_SETTINGS_MODULE=config.settings.production
    command: sh -c "celery -A config worker -l info --concurrency=4"
