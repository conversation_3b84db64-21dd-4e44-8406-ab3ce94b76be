"""
Local development settings for XD Incentives project.
This file contains settings specific to local development.
"""

from .development import *

# Local-specific logging (completely different from development)
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "{levelname} {asctime} {module} {process:d} {thread:d} {message}",
            "style": "{",
        },
        "simple": {
            "format": "{levelname} {message}",
            "style": "{",
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "simple",
        },
        "file": {
            "level": "DEBUG",
            "class": "logging.FileHandler",
            "filename": BASE_DIR.parent / "logs" / "django-local.log",
            "formatter": "verbose",
        },
    },
    "root": {
        "handlers": ["console", "file"],
        "level": "DEBUG",
    },
    "loggers": {
        "django": {
            "handlers": ["console", "file"],
            "level": "INFO",
            "propagate": False,
        },
    },
}

# Local-specific CORS settings (additional to development)
CORS_ALLOW_CREDENTIALS = True

# Local-specific static files (not defined in development)
STATICFILES_DIRS = [
    BASE_DIR.parent / "static",
]

# Local-specific media files (different path from development)
MEDIA_ROOT = BASE_DIR.parent / "media"

# Local-specific cache (memory cache vs default)
CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
        "LOCATION": "unique-snowflake",
    }
}

# Local-specific session settings (not defined in development)
SESSION_ENGINE = "django.contrib.sessions.backends.db"
SESSION_COOKIE_AGE = 1209600  # 2 weeks

# Local-specific debug toolbar (if you want to install it)
# INSTALLED_APPS += ['debug_toolbar']
# MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware']
# INTERNAL_IPS = ['127.0.0.1', '::1']

# Log which settings are being used
import logging

logger = logging.getLogger(__name__)
logger.info("🔧 Using LOCAL settings")
