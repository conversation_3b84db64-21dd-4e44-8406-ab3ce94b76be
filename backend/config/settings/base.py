# Standard library imports
import os
from datetime import <PERSON><PERSON>ta
from pathlib import Path

# Third-party imports
from decouple import Csv, config

# Build paths inside the project
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config("SECRET_KEY", default="your-secret-key-here-change-in-production")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config("DEBUG", default=False, cast=bool)

ALLOWED_HOSTS = config("ALLOWED_HOSTS", default="localhost,127.0.0.1", cast=Csv())

# Application definition
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # Phone number handling
    "phonenumber_field",  # Phone number support
    # Django admin support
    "django.contrib.sites",
    # API Structure
    "rest_framework",
    "rest_framework_simplejwt",
    # React Assistance
    "corsheaders",
    # Async Options
    "channels",
    # HTML Builder & Styling
    "crispy_forms",
    "crispy_tailwind",
    "tailwind",
    "apps.theme",
    "ckeditor",
    # Core Configuration (must be before custom apps for signal registration)
    "config.apps.ConfigConfig",
    # Custom Apps
    "apps.testapp",
    "apps.member",
    "apps.customer",
    # API Endpoints
    "apps.api",
]

SITE_ID = 1

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",  # Must be first
    "config.middleware.CorrelationIdMiddleware",  # Add correlation IDs
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "config.middleware.RequestLoggingMiddleware",  # Request/response logging
    "config.middleware.SecurityLoggingMiddleware",  # Security event logging
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "config.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "config.wsgi.application"

# Database
DATABASES = {
    "default": {
        "ENGINE": config("DB_ENGINE", default="django.db.backends.mysql"),
        "NAME": config("DB_NAME", default="xd_incentives"),
        "USER": config("DB_USER", default="root"),
        "PASSWORD": config("DB_PASSWORD", default=""),
        "HOST": config("DB_HOST", default="localhost"),
        "PORT": config("DB_PORT", default="3306"),
    }
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

AUTHENTICATION_BACKENDS = ("django.contrib.auth.backends.ModelBackend",)

PASSWORD_HASHERS = [
    "django.contrib.auth.hashers.Argon2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2PasswordHasher",  # Fallbacks
    "django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher",
    "django.contrib.auth.hashers.BCryptSHA256PasswordHasher",
]

# Internationalization
LANGUAGE_CODE = config("LANGUAGE_CODE", default="en-us")
TIME_ZONE = config("TIME_ZONE", default="UTC")
USE_I18N = config("USE_I18N", default=True, cast=bool)
USE_TZ = config("USE_TZ", default=True, cast=bool)

# Static files (CSS, JavaScript, Images)
STATIC_URL = config("STATIC_URL", default="/static/")
STATIC_ROOT = config("STATIC_ROOT", default=BASE_DIR.parent / "staticfiles")
STATICFILES_DIRS = [
    BASE_DIR.parent / "static",
]

# Media files
MEDIA_URL = config("MEDIA_URL", default="/media/")
MEDIA_ROOT = config("MEDIA_ROOT", default=BASE_DIR.parent / "media")

# Default primary key field type
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Custom user model
AUTH_USER_MODEL = "member.Member"

# REST Framework settings
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": 20,
    "DEFAULT_RENDERER_CLASSES": ("rest_framework.renderers.JSONRenderer",),
    # API Versioning
    "DEFAULT_VERSIONING_CLASS": "rest_framework.versioning.URLPathVersioning",
    "DEFAULT_VERSION": "v1",
    "ALLOWED_VERSIONS": ["v1"],
    "VERSION_PARAM": "version",
}

# JWT Settings
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(
        minutes=config("JWT_ACCESS_TOKEN_LIFETIME", default=60, cast=int)
    ),
    "REFRESH_TOKEN_LIFETIME": timedelta(
        days=config("JWT_REFRESH_TOKEN_LIFETIME", default=1, cast=int)
    ),
    "ROTATE_REFRESH_TOKENS": config(
        "JWT_ROTATE_REFRESH_TOKENS", default=False, cast=bool
    ),
}

# Email configuration
EMAIL_BACKEND = config(
    "EMAIL_BACKEND", default="django.core.mail.backends.console.EmailBackend"
)
EMAIL_HOST = config("EMAIL_HOST", default="localhost")
EMAIL_PORT = config("EMAIL_PORT", default=587, cast=int)
EMAIL_USE_TLS = config("EMAIL_USE_TLS", default=True, cast=bool)
EMAIL_HOST_USER = config("EMAIL_HOST_USER", default="")
EMAIL_HOST_PASSWORD = config("EMAIL_HOST_PASSWORD", default="")
DEFAULT_FROM_EMAIL = config("DEFAULT_FROM_EMAIL", default="<EMAIL>")

# Logging configuration
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "{levelname} {asctime} {module} {process:d} {thread:d} {message}",
            "style": "{",
        },
    },
    "handlers": {
        "file": {
            "level": config("LOG_LEVEL", default="INFO"),
            "class": "logging.FileHandler",
            "filename": config(
                "LOG_FILE", default=BASE_DIR.parent / "logs" / "django.log"
            ),
            "formatter": "verbose",
        },
    },
    "root": {
        "handlers": ["file"],
        "level": config("LOG_LEVEL", default="INFO"),
    },
}

# Redis + Channels Options
ASGI_APPLICATION = "config.asgi.application"

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [
                ("redis", 6379)
            ],  # 'redis' matches your docker-compose service name
        },
    },
}

# Redis + Celery Options
CELERY_BROKER_URL = "redis://redis:6379/0"
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_BACKEND = "redis://redis:6379/0"

# Crispy Forms Configuration
CRISPY_ALLOWED_TEMPLATE_PACKS = "tailwind"
CRISPY_TEMPLATE_PACK = "tailwind"

# Tailwind Configuration
TAILWIND_APP_NAME = "theme"

# CKEditor Configuration
CKEDITOR_CONFIGS = {
    "default": {
        "toolbar": "Custom",
        "toolbar_Custom": [
            ["Bold", "Italic", "Underline", "Strike"],
            ["NumberedList", "BulletedList", "-", "Outdent", "Indent"],
            ["JustifyLeft", "JustifyCenter", "JustifyRight", "JustifyBlock"],
            ["Link", "Unlink"],
            ["RemoveFormat", "Source"],
            ["Format", "Font", "FontSize"],
            ["TextColor", "BGColor"],
            ["Table", "HorizontalRule", "SpecialChar"],
            ["Image", "Flash"],
            ["Maximize"],
        ],
        "height": 300,
        "width": "100%",
        "removePlugins": "stylesheetparser",
        "allowedContent": True,
    },
    "simple": {
        "toolbar": "Basic",
        "toolbar_Basic": [
            ["Bold", "Italic", "Underline"],
            ["NumberedList", "BulletedList"],
            ["Link", "Unlink"],
            ["RemoveFormat"],
        ],
        "height": 200,
        "width": "100%",
    },
    "full": {
        "toolbar": "Full",
        "toolbar_Full": [
            [
                "Styles",
                "Format",
                "Bold",
                "Italic",
                "Underline",
                "Strike",
                "SpellChecker",
                "Undo",
                "Redo",
            ],
            ["Link", "Unlink", "Anchor"],
            ["Image", "Flash", "Table", "HorizontalRule"],
            ["TextColor", "BGColor"],
            ["Smiley", "SpecialChar"],
            ["Source"],
            ["JustifyLeft", "JustifyCenter", "JustifyRight", "JustifyBlock"],
            ["NumberedList", "BulletedList"],
            ["Indent", "Outdent"],
            ["Maximize"],
        ],
        "height": 400,
        "width": "100%",
    },
}

CKEDITOR_UPLOAD_PATH = "uploads/"
CKEDITOR_IMAGE_BACKEND = "pillow"

# Django Admin Customization
SITE_HEADER = "XD Incentives Admin"
SITE_TITLE = "XD Incentives Admin"
INDEX_TITLE = "Welcome to XD Incentives Administration"

# Authentication Settings
LOGIN_URL = "auth:login"
LOGIN_REDIRECT_URL = "/"

PHONENUMBER_DEFAULT_REGION = "US"
PHONENUMBER_DB_FORMAT = "E164"

# Google Address Validation API Settings
GOOGLE_ADDRESS_VALIDATION_API_KEY = config(
    "GOOGLE_ADDRESS_VALIDATION_API_KEY", default=""
)
GOOGLE_ADDRESS_VALIDATION_ENABLED = config(
    "GOOGLE_ADDRESS_VALIDATION_ENABLED", default=True, cast=bool
)
GOOGLE_ADDRESS_VALIDATION_URL = (
    "https://addressvalidation.googleapis.com/v1:validateAddress"
)
GOOGLE_ADDRESS_VALIDATION_TIMEOUT = config(
    "GOOGLE_ADDRESS_VALIDATION_TIMEOUT", default=10, cast=int
)

# OpenAI API Settings
OPENAI_API_KEY = config("OPENAI_API_KEY", default="")

# Theme configuration
THEME_COLORS = {
    "primary": "#1A73E8",
    "secondary": "#34A853",
    "accent": "#FBBC05",
    "background": "#FFFFFF",
    "text": "#202124",
}

THEME_MODES = {
    "light": {
        "background": "#FFFFFF",
        "text": "#000000",
    },
    "dark": {
        "background": "#202124",
        "text": "#E8EAED",
    },
}

THEME_FONTS = {
    "primary": "Inter, sans-serif",
    "secondary": "Georgia, serif",
}

LOGOS = {
    "default": "/static/images/logo.svg",
    "small": "/static/images/logo-sm.svg",
    "dark_mode": "/static/images/logo-dark.svg",
    "favicon": "/static/images/favicon.ico",
}

# CORS Settings
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = config(
    "CORS_ALLOWED_ORIGINS",
    default="http://localhost:3000,http://localhost:8000",
    cast=Csv(),
)
CORS_ALLOW_ALL_ORIGINS = config("CORS_ALLOW_ALL_ORIGINS", default=False, cast=bool)

# API Configuration
EXTERNAL_API_TIMEOUT = config("EXTERNAL_API_TIMEOUT", default=30, cast=int)
