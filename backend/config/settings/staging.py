"""
Staging settings for XD Incentives project.
This file contains settings specific to staging environment.
"""

from .production import *

# Staging-specific overrides
DEBUG = True  # Keep debug on for staging to help with troubleshooting

# Staging-specific allowed hosts
ALLOWED_HOSTS = [
    "staging.yourdomain.com",
    "staging-xd-incentives.com",
    "localhost",
    "127.0.0.1",
]

# Staging-specific email backend (use console for staging)
EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

# Staging-specific logging (more verbose than production)
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "{levelname} {asctime} {module} {process:d} {thread:d} {message}",
            "style": "{",
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "verbose",
        },
        "file": {
            "level": "DEBUG",
            "class": "logging.FileHandler",
            "filename": BASE_DIR.parent / "logs" / "django-staging.log",
            "formatter": "verbose",
        },
    },
    "root": {
        "handlers": ["console", "file"],
        "level": "DEBUG",
    },
}

# Staging-specific CORS settings (more permissive than production)
CORS_ALLOWED_ORIGINS = [
    "http://staging.yourdomain.com",
    "https://staging.yourdomain.com",
    "http://localhost:3000",
    "http://localhost:8000",
]

# Staging-specific security settings (less strict than production)
SECURE_SSL_REDIRECT = False  # Allow HTTP for staging
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# Staging-specific static files
STATIC_ROOT = BASE_DIR.parent / "staticfiles-staging"

# Staging-specific media files
MEDIA_ROOT = BASE_DIR.parent / "media-staging"

# Log which settings are being used
import logging

logger = logging.getLogger(__name__)
logger.info("🚀 Using STAGING settings")
