"""
Custom logging formatters for XD Incentives
Provides JSON and structured text formatting with correlation IDs
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Any, Dict


class CorrelationIdFilter(logging.Filter):
    """
    Add correlation ID to log records for tracing requests across components
    """

    def filter(self, record):
        # Get correlation ID from thread local storage or create new one
        correlation_id = getattr(record, "correlation_id", None)
        if not correlation_id:
            record.correlation_id = str(uuid.uuid4())[:8]
        return True


class JSONFormatter(logging.Formatter):
    """
    JSON log formatter for structured logging with correlation IDs
    """

    def format(self, record):
        log_data = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "correlation_id": getattr(record, "correlation_id", ""),
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # Add exception information if present
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)

        # Add extra fields if present
        if hasattr(record, "extra_data"):
            log_data["extra"] = record.extra_data

        # Add request information if available
        if hasattr(record, "request"):
            request = record.request
            log_data["request"] = {
                "method": getattr(request, "method", ""),
                "path": getattr(request, "path", ""),
                "user": getattr(
                    getattr(request, "user", None), "username", "anonymous"
                ),
                "ip": self._get_client_ip(request),
                "user_agent": (
                    request.META.get("HTTP_USER_AGENT", "")
                    if hasattr(request, "META")
                    else ""
                ),
            }

        # Add response information if available
        if hasattr(record, "response"):
            response = record.response
            log_data["response"] = {
                "status_code": getattr(response, "status_code", ""),
                "content_type": (
                    response.get("Content-Type", "") if hasattr(response, "get") else ""
                ),
            }

        # Add performance metrics if available
        if hasattr(record, "performance"):
            log_data["performance"] = record.performance

        # Add business context if available
        if hasattr(record, "business_context"):
            log_data["business_context"] = record.business_context

        return json.dumps(log_data, default=str)

    def _get_client_ip(self, request):
        """Extract client IP from request"""
        if not hasattr(request, "META"):
            return ""

        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip or ""


class StructuredTextFormatter(logging.Formatter):
    """
    Human-readable structured text formatter for development
    """

    def format(self, record):
        correlation_id = getattr(record, "correlation_id", "N/A")

        # Base log message
        message = f"[{record.levelname}] {record.name} | {correlation_id} | {record.getMessage()}"

        # Add location information
        message += f" | {record.module}:{record.funcName}:{record.lineno}"

        # Add request context if available
        if hasattr(record, "request"):
            request = record.request
            user = getattr(getattr(request, "user", None), "username", "anonymous")
            method = getattr(request, "method", "")
            path = getattr(request, "path", "")
            message += f" | {method} {path} | User: {user}"

        # Add performance metrics if available
        if hasattr(record, "performance"):
            perf = record.performance
            if "duration_ms" in perf:
                message += f" | Duration: {perf['duration_ms']}ms"

        # Add business context if available
        if hasattr(record, "business_context"):
            context = record.business_context
            if "action" in context:
                message += f" | Action: {context['action']}"

        # Add exception information
        if record.exc_info:
            message += f"\n{self.formatException(record.exc_info)}"

        return message


class SecurityFormatter(logging.Formatter):
    """
    Security-focused formatter that emphasizes security events
    """

    def format(self, record):
        log_data = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "security_event": True,
            "level": record.levelname,
            "correlation_id": getattr(record, "correlation_id", ""),
            "message": record.getMessage(),
            "source": f"{record.module}:{record.funcName}",
        }

        # Add security context
        if hasattr(record, "security_context"):
            log_data["security"] = record.security_context

        # Add user information if available
        if hasattr(record, "request") and hasattr(record.request, "user"):
            user = record.request.user
            log_data["user"] = {
                "username": getattr(user, "username", "anonymous"),
                "id": getattr(user, "id", None),
                "is_authenticated": getattr(user, "is_authenticated", False),
                "is_staff": getattr(user, "is_staff", False),
                "is_superuser": getattr(user, "is_superuser", False),
            }

        # Add request details for security analysis
        if hasattr(record, "request"):
            request = record.request
            log_data["request_details"] = {
                "method": getattr(request, "method", ""),
                "path": getattr(request, "path", ""),
                "ip": self._get_client_ip(request),
                "user_agent": (
                    request.META.get("HTTP_USER_AGENT", "")
                    if hasattr(request, "META")
                    else ""
                ),
                "referer": (
                    request.META.get("HTTP_REFERER", "")
                    if hasattr(request, "META")
                    else ""
                ),
            }

        return json.dumps(log_data, default=str)

    def _get_client_ip(self, request):
        """Extract client IP from request"""
        if not hasattr(request, "META"):
            return ""

        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip or ""
