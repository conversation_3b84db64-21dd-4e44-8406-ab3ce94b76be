"""
Custom logging handlers for XD Incentives
Provides specialized handlers for different types of log events
"""

import logging
import logging.handlers
import os
from typing import Any, Dict


class BusinessLogicHandler(logging.FileHandler):
    """
    Handler specifically for business logic events
    Filters and routes business-related log messages
    """

    def __init__(self, filename, mode="a", encoding="utf-8", delay=False):
        super().__init__(filename, mode, encoding, delay)

    def filter(self, record):
        # Only handle records with business context
        return hasattr(record, "business_context")


class PerformanceHandler(logging.FileHandler):
    """
    Handler for performance-related logging
    Captures slow queries, API response times, etc.
    """

    def __init__(self, filename, mode="a", encoding="utf-8", delay=False):
        super().__init__(filename, mode, encoding, delay)

    def filter(self, record):
        # Handle records with performance metrics or slow operation indicators
        return (
            hasattr(record, "performance")
            or "slow" in record.getMessage().lower()
            or "performance" in record.getMessage().lower()
            or "timeout" in record.getMessage().lower()
        )


class SecurityHandler(logging.FileHandler):
    """
    Handler for security-related events
    Captures authentication failures, permission denials, etc.
    """

    def __init__(self, filename, mode="a", encoding="utf-8", delay=False):
        super().__init__(filename, mode, encoding, delay)

    def filter(self, record):
        # Handle security-related records
        security_keywords = [
            "authentication",
            "authorization",
            "permission",
            "login",
            "logout",
            "failed",
            "denied",
            "unauthorized",
            "forbidden",
            "suspicious",
            "security",
            "attack",
            "breach",
            "violation",
        ]

        message = record.getMessage().lower()
        return (
            hasattr(record, "security_context")
            or any(keyword in message for keyword in security_keywords)
            or record.levelno >= logging.WARNING
        )


class ErrorHandler(logging.FileHandler):
    """
    Handler for error and exception logging
    Captures all errors with full stack traces
    """

    def __init__(self, filename, mode="a", encoding="utf-8", delay=False):
        super().__init__(filename, mode, encoding, delay)

    def filter(self, record):
        # Handle ERROR and CRITICAL level messages
        return record.levelno >= logging.ERROR


class TimedRotatingFileHandlerWithCompression(
    logging.handlers.TimedRotatingFileHandler
):
    """
    Enhanced TimedRotatingFileHandler with compression
    Automatically compresses old log files to save space
    """

    def __init__(
        self, filename, when="midnight", interval=1, backupCount=30, encoding="utf-8"
    ):
        super().__init__(filename, when, interval, backupCount, encoding=encoding)
        self.suffix = "%Y-%m-%d"
        self.extMatch = None

    def doRollover(self):
        """
        Override to add compression of old log files
        """
        super().doRollover()

        # Compress the rolled over file
        import glob
        import gzip

        # Find the most recent backup file
        backup_files = glob.glob(f"{self.baseFilename}.*")
        if backup_files:
            # Sort to get the most recent
            backup_files.sort()
            latest_backup = backup_files[-1]

            # Don't compress if already compressed
            if not latest_backup.endswith(".gz"):
                compressed_name = latest_backup + ".gz"
                try:
                    with open(latest_backup, "rb") as f_in:
                        with gzip.open(compressed_name, "wb") as f_out:
                            f_out.writelines(f_in)
                    os.remove(latest_backup)
                except Exception as e:
                    # Log compression failure but don't break logging
                    # Use stderr to avoid circular logging issues
                    import sys

                    sys.stderr.write(
                        f"Failed to compress log file {latest_backup}: {e}\n"
                    )


class DatabaseQueryHandler(logging.StreamHandler):
    """
    Handler for database query logging
    Only logs slow queries and query errors
    """

    def __init__(self, stream=None):
        super().__init__(stream)

    def filter(self, record):
        # Only log slow queries (> 100ms) or query errors
        if not hasattr(record, "duration"):
            return False

        # Log if query took more than 100ms or if there's an error
        return record.duration > 0.1 or record.levelno >= logging.WARNING

    def format(self, record):
        if hasattr(record, "duration") and hasattr(record, "sql"):
            duration_ms = record.duration * 1000
            return f"SLOW QUERY ({duration_ms:.2f}ms): {record.sql}"
        return super().format(record)


class CustomSMTPHandler(logging.handlers.SMTPHandler):
    """
    Enhanced SMTP handler for critical error notifications
    Only sends emails for CRITICAL level messages to avoid spam
    """

    def __init__(
        self, mailhost, fromaddr, toaddrs, subject, credentials=None, secure=None
    ):
        super().__init__(mailhost, fromaddr, toaddrs, subject, credentials, secure)

    def filter(self, record):
        # Only send emails for CRITICAL level messages
        return record.levelno >= logging.CRITICAL

    def getSubject(self, record):
        """
        Customize email subject with environment and error details
        """
        env = os.environ.get("DJANGO_ENV", "development")
        return f"[{env.upper()}] CRITICAL ERROR: {record.getMessage()[:50]}..."
