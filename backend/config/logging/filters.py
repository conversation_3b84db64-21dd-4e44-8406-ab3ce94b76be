"""
Custom logging filters for XD Incentives
Provides filtering capabilities for different types of log messages
"""

import logging
import re
from typing import List, Pattern


class LevelRangeFilter(logging.Filter):
    """
    Filter that only allows log records within a specified level range
    """

    def __init__(self, min_level=logging.DEBUG, max_level=logging.CRITICAL):
        super().__init__()
        self.min_level = min_level
        self.max_level = max_level

    def filter(self, record):
        return self.min_level <= record.levelno <= self.max_level


class ExcludeFilter(logging.Filter):
    """
    Filter that excludes log records containing specified patterns
    """

    def __init__(self, exclude_patterns: List[str]):
        super().__init__()
        self.exclude_patterns = [re.compile(pattern) for pattern in exclude_patterns]

    def filter(self, record):
        message = record.getMessage()
        return not any(pattern.search(message) for pattern in self.exclude_patterns)


class IncludeFilter(logging.Filter):
    """
    Filter that only includes log records containing specified patterns
    """

    def __init__(self, include_patterns: List[str]):
        super().__init__()
        self.include_patterns = [re.compile(pattern) for pattern in include_patterns]

    def filter(self, record):
        if not self.include_patterns:
            return True

        message = record.getMessage()
        return any(pattern.search(message) for pattern in self.include_patterns)


class BusinessContextFilter(logging.Filter):
    """
    Filter for business logic related log messages
    """

    def filter(self, record):
        # Include records with business context or business-related keywords
        if hasattr(record, "business_context"):
            return True

        business_keywords = [
            "member",
            "team",
            "hierarchy",
            "communication",
            "terms",
            "privacy",
            "enrollment",
            "approval",
            "region",
            "organization",
            "workflow",
        ]

        message = record.getMessage().lower()
        return any(keyword in message for keyword in business_keywords)


class SecurityContextFilter(logging.Filter):
    """
    Filter for security-related log messages
    """

    def filter(self, record):
        # Include records with security context or security-related events
        if hasattr(record, "security_context"):
            return True

        security_keywords = [
            "auth",
            "login",
            "logout",
            "permission",
            "access",
            "denied",
            "unauthorized",
            "forbidden",
            "failed",
            "suspicious",
            "attack",
            "breach",
            "violation",
            "token",
            "session",
            "csrf",
            "xss",
            "sql",
        ]

        message = record.getMessage().lower()
        logger_name = record.name.lower()

        return (
            any(keyword in message for keyword in security_keywords)
            or any(keyword in logger_name for keyword in security_keywords)
            or record.levelno >= logging.WARNING
        )


class PerformanceFilter(logging.Filter):
    """
    Filter for performance-related log messages
    """

    def filter(self, record):
        # Include records with performance metrics or performance-related keywords
        if hasattr(record, "performance"):
            return True

        performance_keywords = [
            "slow",
            "timeout",
            "performance",
            "latency",
            "duration",
            "query",
            "cache",
            "memory",
            "cpu",
            "optimization",
            "bottleneck",
        ]

        message = record.getMessage().lower()
        return any(keyword in message for keyword in performance_keywords)


class APIContextFilter(logging.Filter):
    """
    Filter for API-related log messages
    """

    def filter(self, record):
        # Include API-related records
        if hasattr(record, "request") and hasattr(record.request, "path"):
            path = record.request.path
            return path.startswith("/api/")

        api_keywords = ["api", "endpoint", "request", "response", "json", "rest"]
        message = record.getMessage().lower()
        logger_name = record.name.lower()

        return any(keyword in message for keyword in api_keywords) or any(
            keyword in logger_name for keyword in api_keywords
        )


class DatabaseFilter(logging.Filter):
    """
    Filter for database-related log messages
    """

    def filter(self, record):
        # Include database-related records
        db_keywords = [
            "database",
            "db",
            "sql",
            "query",
            "mysql",
            "migration",
            "connection",
            "transaction",
            "cursor",
            "orm",
        ]

        message = record.getMessage().lower()
        logger_name = record.name.lower()

        return (
            any(keyword in message for keyword in db_keywords)
            or any(keyword in logger_name for keyword in db_keywords)
            or logger_name.startswith("django.db")
        )


class DevelopmentFilter(logging.Filter):
    """
    Filter for development environment - excludes noisy debug messages
    """

    def filter(self, record):
        # Exclude common noisy debug messages in development
        exclude_patterns = [
            r"GET /static/",
            r"GET /media/",
            r"\"GET /favicon.ico",
            r"System check identified no issues",
            r"Watching for file changes",
        ]

        message = record.getMessage()
        return not any(re.search(pattern, message) for pattern in exclude_patterns)


class ProductionFilter(logging.Filter):
    """
    Filter for production environment - only important messages
    """

    def filter(self, record):
        # In production, only log INFO and above, except for specific loggers
        if record.levelno >= logging.INFO:
            return True

        # Allow debug for specific important loggers
        important_loggers = [
            "apps.member",
            "apps.api",
            "security",
            "business",
            "performance",
        ]

        return any(record.name.startswith(logger) for logger in important_loggers)


class CeleryFilter(logging.Filter):
    """
    Filter for Celery task-related log messages
    """

    def filter(self, record):
        celery_keywords = ["celery", "task", "worker", "job", "queue", "broker"]
        message = record.getMessage().lower()
        logger_name = record.name.lower()

        return (
            any(keyword in message for keyword in celery_keywords)
            or any(keyword in logger_name for keyword in celery_keywords)
            or logger_name.startswith("celery")
        )
