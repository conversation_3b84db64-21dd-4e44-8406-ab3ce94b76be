"""
Clerk Configuration for Django
"""

import os

from django.conf import settings

# Clerk Configuration
CLERK_PUBLISHABLE_KEY = os.environ.get("NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY", "")
CLERK_SECRET_KEY = os.environ.get("CLERK_SECRET_KEY", "")

# Clerk API Configuration
CLERK_API_URL = os.environ.get("CLERK_API_URL", "https://api.clerk.com/v1")

# Clerk Frontend Configuration
CLERK_FRONTEND_API = os.environ.get(
    "CLERK_FRONTEND_API", "https://clerk.your-domain.com"
)

# Clerk JWT Configuration
CLERK_JWT_ALGORITHM = "RS256"
CLERK_JWT_ISSUER = os.environ.get("CLERK_JWT_ISSUER", "")

# Clerk Webhook Configuration
CLERK_WEBHOOK_SECRET = os.environ.get("CLERK_WEBHOOK_SECRET", "")

# Clerk User Sync Configuration
CLERK_SYNC_USERS = os.environ.get("CLERK_SYNC_USERS", "True").lower() == "true"

# Clerk Session Configuration
CLERK_SESSION_DURATION = int(
    os.environ.get("CLERK_SESSION_DURATION", 86400)
)  # 24 hours in seconds

# Clerk Multi-factor Authentication
CLERK_MFA_ENABLED = os.environ.get("CLERK_MFA_ENABLED", "True").lower() == "true"

# Clerk Organization Features
CLERK_ORGANIZATIONS_ENABLED = (
    os.environ.get("CLERK_ORGANIZATIONS_ENABLED", "True").lower() == "true"
)

# Clerk Development Settings
CLERK_DEBUG = os.environ.get("CLERK_DEBUG", "False").lower() == "true"

# Clerk CORS Settings
CLERK_ALLOWED_ORIGINS = (
    os.environ.get("CLERK_ALLOWED_ORIGINS", "").split(",")
    if os.environ.get("CLERK_ALLOWED_ORIGINS")
    else []
)

# Clerk User Model Mapping
CLERK_USER_MODEL_MAPPING = {
    "id": "clerk_id",
    "email": "email",
    "first_name": "first_name",
    "last_name": "last_name",
    "username": "username",
    "phone_number": "phone_cell",
    "image_url": "profile_image",
    "created_at": "date_joined",
    "updated_at": "modified",
}

# Clerk Organization Model Mapping (if using organizations)
CLERK_ORGANIZATION_MODEL_MAPPING = {
    "id": "clerk_org_id",
    "name": "name",
    "slug": "slug",
    "created_at": "created",
    "updated_at": "modified",
}
