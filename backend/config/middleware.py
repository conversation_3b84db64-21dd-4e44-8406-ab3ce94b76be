"""
Custom middleware for XD Incentives
Provides request/response logging with correlation IDs and performance tracking
"""

import logging
import threading
import time
import uuid

from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin

# Thread-local storage for correlation IDs
_thread_local = threading.local()

logger = logging.getLogger("apps.api")
performance_logger = logging.getLogger("performance.api")
security_logger = logging.getLogger("security.auth")


class CorrelationIdMiddleware(MiddlewareMixin):
    """
    Middleware to add correlation IDs to all requests for tracing
    """

    def process_request(self, request):
        # Get or create correlation ID
        correlation_id = request.META.get("HTTP_X_CORRELATION_ID")
        if not correlation_id:
            correlation_id = str(uuid.uuid4())[:8]

        # Store in thread-local storage
        _thread_local.correlation_id = correlation_id
        request.correlation_id = correlation_id

        # Add to response headers
        return None

    def process_response(self, request, response):
        correlation_id = getattr(request, "correlation_id", "")
        if correlation_id:
            response["X-Correlation-ID"] = correlation_id
        return response


class RequestLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log all HTTP requests and responses with correlation IDs
    """

    def process_request(self, request):
        # Start timing the request
        request._start_time = time.time()

        # Log the incoming request
        correlation_id = getattr(request, "correlation_id", "")

        # Get client IP
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            client_ip = x_forwarded_for.split(",")[0]
        else:
            client_ip = request.META.get("REMOTE_ADDR")

        # Log request details
        extra_data = {
            "request_method": request.method,
            "request_path": request.path,
            "request_query": request.META.get("QUERY_STRING", ""),
            "client_ip": client_ip,
            "user_agent": request.META.get("HTTP_USER_AGENT", ""),
            "referer": request.META.get("HTTP_REFERER", ""),
            "content_type": request.META.get("CONTENT_TYPE", ""),
            "content_length": request.META.get("CONTENT_LENGTH", 0),
        }

        # Add user information if available
        if hasattr(request, "user") and request.user.is_authenticated:
            extra_data["user_id"] = request.user.id
            extra_data["username"] = request.user.username
            extra_data["is_staff"] = request.user.is_staff
            extra_data["is_superuser"] = request.user.is_superuser

        logger.info(
            f"Request started: {request.method} {request.path}",
            extra={
                "correlation_id": correlation_id,
                "request": request,
                "extra_data": extra_data,
            },
        )

    def process_response(self, request, response):
        # Calculate request duration
        start_time = getattr(request, "_start_time", time.time())
        duration = time.time() - start_time
        duration_ms = round(duration * 1000, 2)

        correlation_id = getattr(request, "correlation_id", "")

        # Determine log level based on status code
        if response.status_code >= 500:
            log_level = logging.ERROR
        elif response.status_code >= 400:
            log_level = logging.WARNING
        else:
            log_level = logging.INFO

        # Log response details
        extra_data = {
            "request_method": request.method,
            "request_path": request.path,
            "response_status": response.status_code,
            "response_size": (
                len(response.content) if hasattr(response, "content") else 0
            ),
            "duration_ms": duration_ms,
        }

        # Add user information if available
        if hasattr(request, "user") and request.user.is_authenticated:
            extra_data["user_id"] = request.user.id
            extra_data["username"] = request.user.username

        # Log performance metrics for slow requests
        if duration_ms > 1000:  # Log slow requests (>1 second)
            performance_logger.warning(
                f"Slow request: {request.method} {request.path} took {duration_ms}ms",
                extra={
                    "correlation_id": correlation_id,
                    "request": request,
                    "response": response,
                    "performance": {"duration_ms": duration_ms, "status": "slow"},
                    "extra_data": extra_data,
                },
            )

        # Log the response
        logger.log(
            log_level,
            f"Request completed: {request.method} {request.path} - {response.status_code} ({duration_ms}ms)",
            extra={
                "correlation_id": correlation_id,
                "request": request,
                "response": response,
                "performance": {"duration_ms": duration_ms},
                "extra_data": extra_data,
            },
        )

        return response

    def process_exception(self, request, exception):
        # Log exceptions with full context
        start_time = getattr(request, "_start_time", time.time())
        duration = time.time() - start_time
        duration_ms = round(duration * 1000, 2)

        correlation_id = getattr(request, "correlation_id", "")

        extra_data = {
            "request_method": request.method,
            "request_path": request.path,
            "exception_type": type(exception).__name__,
            "exception_message": str(exception),
            "duration_ms": duration_ms,
        }

        # Add user information if available
        if hasattr(request, "user") and request.user.is_authenticated:
            extra_data["user_id"] = request.user.id
            extra_data["username"] = request.user.username

        logger.error(
            f"Request failed: {request.method} {request.path} - {type(exception).__name__}: {exception}",
            extra={
                "correlation_id": correlation_id,
                "request": request,
                "extra_data": extra_data,
            },
            exc_info=True,
        )


class SecurityLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log security-related events
    """

    def process_request(self, request):
        correlation_id = getattr(request, "correlation_id", "")

        # Log suspicious patterns
        suspicious_patterns = [
            "admin",
            "wp-admin",
            "phpmyadmin",
            ".env",
            "config.php",
            "passwd",
            "shadow",
            "../",
            "..\\",
            "etc/passwd",
            "<script",
            "javascript:",
            "vbscript:",
            "onload=",
            "onerror=",
            "union select",
            "drop table",
            "delete from",
            "insert into",
            "exec(",
            "eval(",
            "system(",
            "shell_exec(",
        ]

        path_lower = request.path.lower()
        query_lower = request.META.get("QUERY_STRING", "").lower()
        user_agent = request.META.get("HTTP_USER_AGENT", "").lower()

        # Check for suspicious patterns
        for pattern in suspicious_patterns:
            if pattern in path_lower or pattern in query_lower or pattern in user_agent:
                security_logger.warning(
                    f"Suspicious request detected: pattern '{pattern}' found",
                    extra={
                        "correlation_id": correlation_id,
                        "request": request,
                        "security_context": {
                            "event_type": "suspicious_request",
                            "pattern_matched": pattern,
                            "severity": "medium",
                            "action": "log_and_monitor",
                        },
                    },
                )
                break

        # Log requests to admin areas
        if request.path.startswith("/admin/"):
            security_logger.info(
                f"Admin area access: {request.method} {request.path}",
                extra={
                    "correlation_id": correlation_id,
                    "request": request,
                    "security_context": {
                        "event_type": "admin_access",
                        "severity": "low",
                        "action": "monitor",
                    },
                },
            )

    def process_response(self, request, response):
        correlation_id = getattr(request, "correlation_id", "")

        # Log authentication failures
        if response.status_code == 401:
            security_logger.warning(
                f"Authentication failed: {request.method} {request.path}",
                extra={
                    "correlation_id": correlation_id,
                    "request": request,
                    "response": response,
                    "security_context": {
                        "event_type": "authentication_failure",
                        "severity": "medium",
                        "action": "monitor_and_alert",
                    },
                },
            )

        # Log authorization failures
        elif response.status_code == 403:
            security_logger.warning(
                f"Authorization failed: {request.method} {request.path}",
                extra={
                    "correlation_id": correlation_id,
                    "request": request,
                    "response": response,
                    "security_context": {
                        "event_type": "authorization_failure",
                        "severity": "medium",
                        "action": "monitor_and_alert",
                    },
                },
            )

        return response


def get_correlation_id():
    """
    Get the current correlation ID from thread-local storage
    """
    return getattr(_thread_local, "correlation_id", "")
