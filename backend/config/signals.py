"""
Django signals for automatic logging of model changes and business events
"""

import logging

from django.contrib.auth import get_user_model
from django.contrib.auth.signals import (
    user_logged_in,
    user_logged_out,
    user_login_failed,
)
from django.db.models.signals import post_delete, post_save, pre_save
from django.dispatch import receiver

from config.logging_utils import (
    log_authentication_event,
    log_business_event,
    log_hierarchy_change,
    log_member_activity,
    log_team_activity,
)

User = get_user_model()


# Authentication signals
@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """Log successful user login"""
    log_authentication_event(
        event_type="login_success",
        username=user.username,
        success=True,
        details={
            "user_id": user.id,
            "is_staff": user.is_staff,
            "is_superuser": user.is_superuser,
        },
        request=request,
    )


@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """Log user logout"""
    log_authentication_event(
        event_type="logout",
        username=user.username if user else "unknown",
        success=True,
        details={
            "user_id": user.id if user else None,
        },
        request=request,
    )


@receiver(user_login_failed)
def log_user_login_failed(sender, credentials, request, **kwargs):
    """Log failed login attempts"""
    username = credentials.get("username", "unknown")
    log_authentication_event(
        event_type="login_failed",
        username=username,
        success=False,
        details={
            "attempt_username": username,
            "failure_reason": "invalid_credentials",
        },
        request=request,
    )


# Member model signals
@receiver(post_save, sender=User)
def log_member_changes(sender, instance, created, **kwargs):
    """Log member creation and updates"""
    if created:
        log_member_activity(
            member=instance,
            action="created",
            details={
                "email": instance.email,
                "first_name": instance.first_name,
                "last_name": instance.last_name,
                "is_staff": instance.is_staff,
                "is_active": instance.is_active,
            },
        )
    else:
        # For updates, we could track specific field changes
        # This would require storing previous values
        log_member_activity(
            member=instance,
            action="updated",
            details={
                "email": instance.email,
                "is_active": instance.is_active,
                "is_staff": instance.is_staff,
            },
        )


@receiver(post_delete, sender=User)
def log_member_deletion(sender, instance, **kwargs):
    """Log member deletion"""
    log_member_activity(
        member=instance,
        action="deleted",
        details={
            "username": instance.username,
            "email": instance.email,
        },
    )


# Team-related signals (if team models exist)
def setup_team_signals():
    """Setup signals for team models when they're available"""
    try:
        from apps.member.models import MemberTeam, Team

        @receiver(post_save, sender=MemberTeam)
        def log_team_membership_changes(sender, instance, created, **kwargs):
            """Log team membership changes"""
            if created:
                log_team_activity(
                    team_id=instance.team.id,
                    action="member_added",
                    details={
                        "member_id": instance.member.id,
                        "member_username": instance.member.username,
                        "role": getattr(instance, "role", "member"),
                    },
                    user=instance.member,
                )
            else:
                log_team_activity(
                    team_id=instance.team.id,
                    action="member_updated",
                    details={
                        "member_id": instance.member.id,
                        "member_username": instance.member.username,
                        "role": getattr(instance, "role", "member"),
                    },
                    user=instance.member,
                )

        @receiver(post_delete, sender=MemberTeam)
        def log_team_membership_removal(sender, instance, **kwargs):
            """Log team membership removal"""
            log_team_activity(
                team_id=instance.team.id,
                action="member_removed",
                details={
                    "member_id": instance.member.id,
                    "member_username": instance.member.username,
                },
                user=instance.member,
            )

        @receiver(post_save, sender=Team)
        def log_team_changes(sender, instance, created, **kwargs):
            """Log team creation and updates"""
            if created:
                log_team_activity(
                    team_id=instance.id,
                    action="created",
                    details={
                        "team_name": instance.name,
                        "description": getattr(instance, "description", ""),
                    },
                )
            else:
                log_team_activity(
                    team_id=instance.id,
                    action="updated",
                    details={
                        "team_name": instance.name,
                        "description": getattr(instance, "description", ""),
                    },
                )

    except ImportError:
        # Team models don't exist yet
        pass


# Hierarchy-related signals
def setup_hierarchy_signals():
    """Setup signals for hierarchy models when they're available"""
    try:
        from apps.member.models import MemberHierarchy

        @receiver(post_save, sender=MemberHierarchy)
        def log_hierarchy_changes(sender, instance, created, **kwargs):
            """Log hierarchy relationship changes"""
            if created:
                log_hierarchy_change(
                    parent_id=instance.parent.id,
                    child_id=instance.child.id,
                    action="relationship_created",
                )
            else:
                log_hierarchy_change(
                    parent_id=instance.parent.id,
                    child_id=instance.child.id,
                    action="relationship_updated",
                )

        @receiver(post_delete, sender=MemberHierarchy)
        def log_hierarchy_removal(sender, instance, **kwargs):
            """Log hierarchy relationship removal"""
            log_hierarchy_change(
                parent_id=instance.parent.id,
                child_id=instance.child.id,
                action="relationship_removed",
            )

    except ImportError:
        # Hierarchy models don't exist yet
        pass


# Terms and Privacy signals
def setup_terms_privacy_signals():
    """Setup signals for terms and privacy models when they're available"""
    try:
        from apps.member.models import MemberPrivacyLog, MemberTermsLog

        @receiver(post_save, sender=MemberTermsLog)
        def log_terms_acceptance(sender, instance, created, **kwargs):
            """Log terms acceptance"""
            if created:
                log_business_event(
                    action="terms.accepted",
                    details={
                        "member_id": instance.member.id,
                        "member_username": instance.member.username,
                        "terms_version": getattr(instance, "version", "unknown"),
                        "acceptance_method": getattr(
                            instance, "acceptance_method", "web"
                        ),
                    },
                    user=instance.member,
                )

        @receiver(post_save, sender=MemberPrivacyLog)
        def log_privacy_acceptance(sender, instance, created, **kwargs):
            """Log privacy policy acceptance"""
            if created:
                log_business_event(
                    action="privacy.accepted",
                    details={
                        "member_id": instance.member.id,
                        "member_username": instance.member.username,
                        "privacy_version": getattr(instance, "version", "unknown"),
                        "acceptance_method": getattr(
                            instance, "acceptance_method", "web"
                        ),
                    },
                    user=instance.member,
                )

    except ImportError:
        # Terms/Privacy models don't exist yet
        pass


# Initialize all signals
def setup_all_signals():
    """Setup all available signals"""
    setup_team_signals()
    setup_hierarchy_signals()
    setup_terms_privacy_signals()
