"""
Django logging configuration for XD Incentives
Comprehensive logging setup with structured JSON format, correlation IDs, and multiple output channels
"""

import os
from pathlib import Path

# Build paths
BASE_DIR = Path(__file__).resolve().parent.parent
LOG_DIR = BASE_DIR / "logs"

# Ensure log directories exist
LOG_DIR.mkdir(exist_ok=True)
(LOG_DIR / "django").mkdir(exist_ok=True)
(LOG_DIR / "celery").mkdir(exist_ok=True)

# Get environment setting
DEBUG = os.environ.get("DEBUG", "False").lower() == "true"
ENVIRONMENT = os.environ.get("DJANGO_ENV", "development")

# Logging configuration
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    # Formatters
    "formatters": {
        "json": {
            "()": "config.logging.formatters.JSONFormatter",
        },
        "structured_text": {
            "()": "config.logging.formatters.StructuredTextFormatter",
        },
        "security": {
            "()": "config.logging.formatters.SecurityFormatter",
        },
        "simple": {
            "format": "[{asctime}] {levelname} {name}: {message}",
            "style": "{",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
        "verbose": {
            "format": "[{asctime}] {levelname} {name} {process:d} {thread:d}: {message}",
            "style": "{",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
        "django.server": {
            "()": "django.utils.log.ServerFormatter",
            "format": "[{server_time}] {message}",
            "style": "{",
        },
    },
    # Filters
    "filters": {
        "correlation_id": {
            "()": "config.logging.formatters.CorrelationIdFilter",
        },
        "require_debug_false": {
            "()": "django.utils.log.RequireDebugFalse",
        },
        "require_debug_true": {
            "()": "django.utils.log.RequireDebugTrue",
        },
        "business_context": {
            "()": "config.logging.filters.BusinessContextFilter",
        },
        "security_context": {
            "()": "config.logging.filters.SecurityContextFilter",
        },
        "performance": {
            "()": "config.logging.filters.PerformanceFilter",
        },
        "api_context": {
            "()": "config.logging.filters.APIContextFilter",
        },
        "database": {
            "()": "config.logging.filters.DatabaseFilter",
        },
        "development": {
            "()": "config.logging.filters.DevelopmentFilter",
        },
        "production": {
            "()": "config.logging.filters.ProductionFilter",
        },
        "celery": {
            "()": "config.logging.filters.CeleryFilter",
        },
    },
    # Handlers
    "handlers": {
        # Console handlers
        "console": {
            "level": "DEBUG" if DEBUG else "INFO",
            "class": "logging.StreamHandler",
            "formatter": "structured_text",
            "filters": ["correlation_id", "development" if DEBUG else "production"],
        },
        "console_json": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "json",
            "filters": ["correlation_id"],
        },
        # File handlers - Application logs
        "file_application": {
            "level": "INFO",
            "class": "config.logging.handlers.TimedRotatingFileHandlerWithCompression",
            "filename": str(LOG_DIR / "django" / "application.log"),
            "when": "midnight",
            "interval": 1,
            "backupCount": 30,
            "formatter": "json",
            "filters": ["correlation_id"],
        },
        "file_debug": {
            "level": "DEBUG",
            "class": "config.logging.handlers.TimedRotatingFileHandlerWithCompression",
            "filename": str(LOG_DIR / "django" / "debug.log"),
            "when": "midnight",
            "interval": 1,
            "backupCount": 7,
            "formatter": "json",
            "filters": ["correlation_id", "require_debug_true"],
        },
        # Specialized handlers
        "file_security": {
            "level": "WARNING",
            "class": "config.logging.handlers.SecurityHandler",
            "filename": str(LOG_DIR / "django" / "security.log"),
            "formatter": "security",
            "filters": ["correlation_id", "security_context"],
        },
        "file_business": {
            "level": "INFO",
            "class": "config.logging.handlers.BusinessLogicHandler",
            "filename": str(LOG_DIR / "django" / "business.log"),
            "formatter": "json",
            "filters": ["correlation_id", "business_context"],
        },
        "file_performance": {
            "level": "INFO",
            "class": "config.logging.handlers.PerformanceHandler",
            "filename": str(LOG_DIR / "django" / "performance.log"),
            "formatter": "json",
            "filters": ["correlation_id", "performance"],
        },
        "file_api": {
            "level": "INFO",
            "class": "config.logging.handlers.TimedRotatingFileHandlerWithCompression",
            "filename": str(LOG_DIR / "django" / "api.log"),
            "when": "midnight",
            "interval": 1,
            "backupCount": 30,
            "formatter": "json",
            "filters": ["correlation_id", "api_context"],
        },
        "file_database": {
            "level": "WARNING",  # Only log slow queries and errors
            "class": "config.logging.handlers.TimedRotatingFileHandlerWithCompression",
            "filename": str(LOG_DIR / "django" / "database.log"),
            "when": "midnight",
            "interval": 1,
            "backupCount": 7,
            "formatter": "json",
            "filters": ["correlation_id", "database"],
        },
        "file_errors": {
            "level": "ERROR",
            "class": "config.logging.handlers.ErrorHandler",
            "filename": str(LOG_DIR / "django" / "errors.log"),
            "formatter": "json",
            "filters": ["correlation_id"],
        },
        # Celery handlers
        "file_celery": {
            "level": "INFO",
            "class": "config.logging.handlers.TimedRotatingFileHandlerWithCompression",
            "filename": str(LOG_DIR / "celery" / "celery.log"),
            "when": "midnight",
            "interval": 1,
            "backupCount": 30,
            "formatter": "json",
            "filters": ["correlation_id", "celery"],
        },
        # Email handler for critical errors (production only)
        "mail_admins": {
            "level": "CRITICAL",
            "class": "config.logging.handlers.CustomSMTPHandler",
            "filters": ["require_debug_false"],
            "formatter": "verbose",
            # Configure these in production environment
            "mailhost": os.environ.get("SMTP_HOST", "localhost"),
            "fromaddr": os.environ.get("FROM_EMAIL", "<EMAIL>"),
            "toaddrs": [os.environ.get("ADMIN_EMAIL", "<EMAIL>")],
            "subject": "XD Incentives Critical Error",
        },
        # Null handler for disabling certain loggers
        "null": {
            "class": "logging.NullHandler",
        },
    },
    # Root logger
    "root": {
        "level": "INFO",
        "handlers": ["console", "file_application"],
    },
    # Loggers
    "loggers": {
        # Django framework loggers
        "django": {
            "handlers": ["console", "file_application"],
            "level": "INFO",
            "propagate": False,
        },
        "django.db.backends": {
            "handlers": ["file_database"],
            "level": "WARNING",  # Only log slow queries and errors
            "propagate": False,
        },
        "django.request": {
            "handlers": ["console", "file_application", "file_security"],
            "level": "WARNING",  # Log 4xx and 5xx responses
            "propagate": False,
        },
        "django.security": {
            "handlers": ["file_security", "console"],
            "level": "WARNING",
            "propagate": False,
        },
        "django.server": {
            "handlers": ["console"],
            "level": "INFO",
            "formatter": "django.server",
            "propagate": False,
        },
        # Application loggers
        "apps.member": {
            "handlers": [
                "console",
                "file_application",
                "file_business",
                "file_security",
            ],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
        "apps.api": {
            "handlers": ["console", "file_application", "file_api"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
        "apps.testapp": {
            "handlers": ["console", "file_application"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
        # Business logic loggers
        "business": {
            "handlers": ["file_business", "console"],
            "level": "INFO",
            "propagate": False,
        },
        "business.member": {
            "handlers": ["file_business", "console"],
            "level": "INFO",
            "propagate": False,
        },
        "business.team": {
            "handlers": ["file_business", "console"],
            "level": "INFO",
            "propagate": False,
        },
        "business.hierarchy": {
            "handlers": ["file_business", "console"],
            "level": "INFO",
            "propagate": False,
        },
        "business.communication": {
            "handlers": ["file_business", "console"],
            "level": "INFO",
            "propagate": False,
        },
        # Security loggers
        "security": {
            "handlers": ["file_security", "console", "mail_admins"],
            "level": "WARNING",
            "propagate": False,
        },
        "security.auth": {
            "handlers": ["file_security", "console"],
            "level": "WARNING",
            "propagate": False,
        },
        "security.permission": {
            "handlers": ["file_security", "console"],
            "level": "WARNING",
            "propagate": False,
        },
        # Performance loggers
        "performance": {
            "handlers": ["file_performance", "console"],
            "level": "INFO",
            "propagate": False,
        },
        "performance.database": {
            "handlers": ["file_performance", "file_database"],
            "level": "INFO",
            "propagate": False,
        },
        "performance.api": {
            "handlers": ["file_performance", "file_api"],
            "level": "INFO",
            "propagate": False,
        },
        # Celery loggers
        "celery": {
            "handlers": ["file_celery", "console"],
            "level": "INFO",
            "propagate": False,
        },
        "celery.task": {
            "handlers": ["file_celery", "console"],
            "level": "INFO",
            "propagate": False,
        },
        "celery.worker": {
            "handlers": ["file_celery", "console"],
            "level": "INFO",
            "propagate": False,
        },
        # Third-party loggers
        "rest_framework": {
            "handlers": ["file_api", "console"],
            "level": "INFO",
            "propagate": False,
        },
        "channels": {
            "handlers": ["console", "file_application"],
            "level": "INFO",
            "propagate": False,
        },
        "channels_redis": {
            "handlers": ["console", "file_application"],
            "level": "WARNING",
            "propagate": False,
        },
        # Suppress noisy third-party loggers
        "urllib3": {
            "handlers": ["null"],
            "level": "CRITICAL",
            "propagate": False,
        },
        "requests": {
            "handlers": ["null"],
            "level": "CRITICAL",
            "propagate": False,
        },
    },
}
