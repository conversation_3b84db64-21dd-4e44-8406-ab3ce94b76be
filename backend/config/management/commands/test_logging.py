"""
Django management command to test the logging system
Usage: docker exec xd-web python manage.py test_logging
"""

import logging
import time

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand

from config.logging_utils import (
    PerformanceTimer,
    log_authentication_event,
    log_business_event,
    log_member_activity,
    log_permission_event,
    log_security_event,
    performance_monitor,
)

User = get_user_model()


class Command(BaseCommand):
    help = "Test the XD Incentives logging system with sample events"

    def add_arguments(self, parser):
        parser.add_argument(
            "--level",
            type=str,
            default="all",
            choices=["all", "business", "security", "performance", "auth"],
            help="Type of logging to test",
        )
        parser.add_argument(
            "--count", type=int, default=5, help="Number of test events to generate"
        )

    def handle(self, *args, **options):
        level = options["level"]
        count = options["count"]

        self.stdout.write(
            self.style.SUCCESS(
                f"Testing XD Incentives logging system - Level: {level}, Count: {count}"
            )
        )

        # Get or create a test user
        test_user, created = User.objects.get_or_create(
            username="test_logging_user",
            defaults={
                "email": "<EMAIL>",
                "first_name": "Test",
                "last_name": "User",
                "is_active": True,
            },
        )

        if created:
            self.stdout.write(f"Created test user: {test_user.username}")

        # Test different types of logging
        if level in ["all", "business"]:
            self.test_business_logging(test_user, count)

        if level in ["all", "security"]:
            self.test_security_logging(test_user, count)

        if level in ["all", "performance"]:
            self.test_performance_logging(count)

        if level in ["all", "auth"]:
            self.test_authentication_logging(test_user, count)

        self.stdout.write(
            self.style.SUCCESS(
                f"Logging test completed! Check logs in ./logs/ directory"
            )
        )

        # Display log file locations
        self.stdout.write("\nLog files to check:")
        self.stdout.write("- ./logs/django/application.log (main application logs)")
        self.stdout.write("- ./logs/django/business.log (business events)")
        self.stdout.write("- ./logs/django/security.log (security events)")
        self.stdout.write("- ./logs/django/performance.log (performance metrics)")
        self.stdout.write("- ./logs/django/errors.log (error logs)")

    def test_business_logging(self, user, count):
        """Test business logic logging"""
        self.stdout.write("Testing business logic logging...")

        for i in range(count):
            # Member activity
            log_member_activity(
                member=user,
                action="profile_updated",
                details={
                    "test_iteration": i + 1,
                    "fields_updated": ["email", "phone"],
                    "source": "management_command",
                },
            )

            # Team activity (simulated)
            log_business_event(
                action="team.member_added",
                details={
                    "test_iteration": i + 1,
                    "team_id": 100 + i,
                    "team_name": f"Test Team {i + 1}",
                    "member_id": user.id,
                    "role": "member",
                    "source": "management_command",
                },
                user=user,
            )

            # Communication event
            log_business_event(
                action="communication.email.sent",
                details={
                    "test_iteration": i + 1,
                    "recipient_id": user.id,
                    "communication_type": "welcome_email",
                    "status": "delivered",
                    "source": "management_command",
                },
                user=user,
            )

        self.stdout.write(f"✓ Generated {count * 3} business log events")

    def test_security_logging(self, user, count):
        """Test security event logging"""
        self.stdout.write("Testing security event logging...")

        for i in range(count):
            # Authentication events
            log_authentication_event(
                event_type="login_success",
                username=user.username,
                success=True,
                details={
                    "test_iteration": i + 1,
                    "user_id": user.id,
                    "login_method": "password",
                    "source": "management_command",
                },
            )

            # Permission events
            log_permission_event(
                event_type="resource_access",
                resource=f"/api/test-resource-{i + 1}",
                permission="read",
                user=user,
                granted=True,
            )

            # Security warnings
            log_security_event(
                event_type="suspicious_activity",
                severity="medium",
                details={
                    "test_iteration": i + 1,
                    "activity_type": "multiple_login_attempts",
                    "source_ip": f"192.168.1.{i + 1}",
                    "source": "management_command",
                },
                user=user,
            )

        self.stdout.write(f"✓ Generated {count * 3} security log events")

    def test_performance_logging(self, count):
        """Test performance monitoring"""
        self.stdout.write("Testing performance logging...")

        # Test performance timer
        for i in range(count):
            with PerformanceTimer(f"test_operation_{i + 1}", threshold_ms=50) as timer:
                # Simulate varying operation times
                sleep_time = 0.02 + (i * 0.03)  # 20ms to 140ms
                time.sleep(sleep_time)

        # Test performance decorator
        @performance_monitor("test_function")
        def slow_test_function(iteration):
            """Test function that sometimes runs slow"""
            # Make some iterations slow to trigger logging
            if iteration % 3 == 0:
                time.sleep(0.15)  # 150ms - should trigger slow log
            else:
                time.sleep(0.05)  # 50ms - normal
            return f"Result {iteration}"

        for i in range(count):
            result = slow_test_function(i + 1)

        self.stdout.write(f"✓ Generated {count * 2} performance log events")

    def test_authentication_logging(self, user, count):
        """Test authentication logging"""
        self.stdout.write("Testing authentication logging...")

        for i in range(count):
            # Successful login
            log_authentication_event(
                event_type="login_success",
                username=user.username,
                success=True,
                details={
                    "test_iteration": i + 1,
                    "user_id": user.id,
                    "is_staff": user.is_staff,
                    "login_method": "password",
                    "source": "management_command",
                },
            )

            # Failed login attempt
            log_authentication_event(
                event_type="login_failed",
                username=f"invalid_user_{i + 1}",
                success=False,
                details={
                    "test_iteration": i + 1,
                    "failure_reason": "invalid_credentials",
                    "attempted_username": f"invalid_user_{i + 1}",
                    "source": "management_command",
                },
            )

            # Logout
            log_authentication_event(
                event_type="logout",
                username=user.username,
                success=True,
                details={
                    "test_iteration": i + 1,
                    "user_id": user.id,
                    "session_duration_minutes": 30 + i,
                    "source": "management_command",
                },
            )

        self.stdout.write(f"✓ Generated {count * 3} authentication log events")
