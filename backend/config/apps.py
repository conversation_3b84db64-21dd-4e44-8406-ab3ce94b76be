"""
Django app configuration for the config package
"""

from django.apps import AppConfig


class ConfigConfig(AppConfig):
    """
    Configuration for the config app
    Handles signal registration and logging setup
    """

    default_auto_field = "django.db.models.BigAutoField"
    name = "config"
    verbose_name = "XD Incentives Configuration"

    def ready(self):
        """
        Called when Django starts up
        Register signals and setup logging components
        """
        # Import and setup signals
        from config.signals import setup_all_signals

        setup_all_signals()

        # Initialize logging components
        import logging

        logger = logging.getLogger("apps.config")
        logger.info("XD Incentives logging system initialized")
