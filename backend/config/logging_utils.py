"""
Logging utilities for XD Incentives
Helper functions for business logic logging, performance tracking, and security events
"""

import functools
import logging
import time
from typing import Any, Dict, Optional

from django.contrib.auth import get_user_model
from django.http import HttpRequest

from config.middleware import get_correlation_id

User = get_user_model()

# Logger instances
business_logger = logging.getLogger("business")
performance_logger = logging.getLogger("performance")
security_logger = logging.getLogger("security")


def log_business_event(
    action: str,
    details: Dict[str, Any],
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
    level: int = logging.INFO,
):
    """
    Log business logic events with structured data

    Args:
        action: The business action being performed
        details: Dictionary of relevant details
        user: User performing the action
        request: HTTP request object if available
        level: Logging level (default: INFO)
    """
    correlation_id = get_correlation_id()

    business_context = {
        "action": action,
        "details": details,
        "timestamp": time.time(),
    }

    if user:
        business_context["user"] = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "is_staff": user.is_staff,
        }

    extra = {
        "correlation_id": correlation_id,
        "business_context": business_context,
    }

    if request:
        extra["request"] = request

    business_logger.log(level, f"Business event: {action}", extra=extra)


def log_member_activity(
    member: User,
    action: str,
    details: Dict[str, Any],
    request: Optional[HttpRequest] = None,
):
    """
    Log member-specific activities
    """
    log_business_event(
        action=f"member.{action}",
        details={"member_id": member.id, "member_username": member.username, **details},
        user=member,
        request=request,
    )


def log_team_activity(
    team_id: int,
    action: str,
    details: Dict[str, Any],
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
):
    """
    Log team-related activities
    """
    log_business_event(
        action=f"team.{action}",
        details={"team_id": team_id, **details},
        user=user,
        request=request,
    )


def log_hierarchy_change(
    parent_id: int,
    child_id: int,
    action: str,
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
):
    """
    Log hierarchy relationship changes
    """
    log_business_event(
        action=f"hierarchy.{action}",
        details={
            "parent_member_id": parent_id,
            "child_member_id": child_id,
        },
        user=user,
        request=request,
    )


def log_communication_event(
    recipient_id: int,
    communication_type: str,
    status: str,
    details: Dict[str, Any],
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
):
    """
    Log communication delivery events
    """
    log_business_event(
        action=f"communication.{communication_type}.{status}",
        details={
            "recipient_id": recipient_id,
            "communication_type": communication_type,
            "status": status,
            **details,
        },
        user=user,
        request=request,
    )


def log_security_event(
    event_type: str,
    severity: str,
    details: Dict[str, Any],
    user: Optional[User] = None,
    request: Optional[HttpRequest] = None,
):
    """
    Log security-related events

    Args:
        event_type: Type of security event (login_failure, permission_denied, etc.)
        severity: Severity level (low, medium, high, critical)
        details: Event-specific details
        user: User involved in the event
        request: HTTP request if available
    """
    correlation_id = get_correlation_id()

    security_context = {
        "event_type": event_type,
        "severity": severity,
        "details": details,
        "timestamp": time.time(),
    }

    if user:
        security_context["user"] = {
            "id": user.id,
            "username": user.username,
            "is_authenticated": user.is_authenticated,
            "is_staff": user.is_staff,
            "is_superuser": user.is_superuser,
        }

    # Determine log level based on severity
    level_map = {
        "low": logging.INFO,
        "medium": logging.WARNING,
        "high": logging.ERROR,
        "critical": logging.CRITICAL,
    }
    level = level_map.get(severity, logging.WARNING)

    extra = {
        "correlation_id": correlation_id,
        "security_context": security_context,
    }

    if request:
        extra["request"] = request

    security_logger.log(level, f"Security event: {event_type}", extra=extra)


def log_authentication_event(
    event_type: str,
    username: str,
    success: bool,
    details: Dict[str, Any] = None,
    request: Optional[HttpRequest] = None,
):
    """
    Log authentication events (login, logout, 2FA, etc.)
    """
    severity = "low" if success else "medium"
    log_security_event(
        event_type=f"authentication.{event_type}",
        severity=severity,
        details={"username": username, "success": success, **(details or {})},
        request=request,
    )


def log_permission_event(
    event_type: str,
    resource: str,
    permission: str,
    user: Optional[User] = None,
    granted: bool = False,
    request: Optional[HttpRequest] = None,
):
    """
    Log permission-related events
    """
    severity = "low" if granted else "medium"
    log_security_event(
        event_type=f"permission.{event_type}",
        severity=severity,
        details={
            "resource": resource,
            "permission": permission,
            "granted": granted,
        },
        user=user,
        request=request,
    )


def performance_monitor(func_name: str = None):
    """
    Decorator to monitor function performance

    Usage:
        @performance_monitor()
        def slow_function():
            pass

        @performance_monitor("custom_name")
        def another_function():
            pass
    """

    def decorator(func):
        name = func_name or f"{func.__module__}.{func.__name__}"

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            correlation_id = get_correlation_id()

            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                duration_ms = round(duration * 1000, 2)

                # Log if function is slow (>100ms)
                if duration_ms > 100:
                    performance_logger.warning(
                        f"Slow function execution: {name} took {duration_ms}ms",
                        extra={
                            "correlation_id": correlation_id,
                            "performance": {
                                "function": name,
                                "duration_ms": duration_ms,
                                "status": "slow",
                                "threshold_ms": 100,
                            },
                        },
                    )
                else:
                    performance_logger.debug(
                        f"Function execution: {name} took {duration_ms}ms",
                        extra={
                            "correlation_id": correlation_id,
                            "performance": {
                                "function": name,
                                "duration_ms": duration_ms,
                                "status": "normal",
                            },
                        },
                    )

                return result

            except Exception as e:
                duration = time.time() - start_time
                duration_ms = round(duration * 1000, 2)

                performance_logger.error(
                    f"Function execution failed: {name} failed after {duration_ms}ms - {type(e).__name__}: {e}",
                    extra={
                        "correlation_id": correlation_id,
                        "performance": {
                            "function": name,
                            "duration_ms": duration_ms,
                            "status": "error",
                            "error_type": type(e).__name__,
                            "error_message": str(e),
                        },
                    },
                    exc_info=True,
                )
                raise

        return wrapper

    return decorator


def log_database_query(query: str, duration: float, params: Optional[list] = None):
    """
    Log slow database queries
    """
    duration_ms = round(duration * 1000, 2)

    if duration_ms > 100:  # Log queries slower than 100ms
        correlation_id = get_correlation_id()

        performance_logger.warning(
            f"Slow database query: {duration_ms}ms",
            extra={
                "correlation_id": correlation_id,
                "performance": {
                    "query_duration_ms": duration_ms,
                    "query_sql": query[:500],  # Truncate long queries
                    "query_params": str(params)[:200] if params else None,
                    "status": "slow",
                    "threshold_ms": 100,
                },
            },
        )


class PerformanceTimer:
    """
    Context manager for timing operations

    Usage:
        with PerformanceTimer("database_operation") as timer:
            # Do slow operation
            pass
        # Automatically logs if operation is slow
    """

    def __init__(self, operation_name: str, threshold_ms: float = 100):
        self.operation_name = operation_name
        self.threshold_ms = threshold_ms
        self.start_time = None
        self.correlation_id = None

    def __enter__(self):
        self.start_time = time.time()
        self.correlation_id = get_correlation_id()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time is None:
            return

        duration = time.time() - self.start_time
        duration_ms = round(duration * 1000, 2)

        if exc_type is not None:
            # Operation failed
            performance_logger.error(
                f"Operation failed: {self.operation_name} failed after {duration_ms}ms - {exc_type.__name__}: {exc_val}",
                extra={
                    "correlation_id": self.correlation_id,
                    "performance": {
                        "operation": self.operation_name,
                        "duration_ms": duration_ms,
                        "status": "error",
                        "error_type": exc_type.__name__,
                        "error_message": str(exc_val),
                    },
                },
            )
        elif duration_ms > self.threshold_ms:
            # Operation was slow
            performance_logger.warning(
                f"Slow operation: {self.operation_name} took {duration_ms}ms",
                extra={
                    "correlation_id": self.correlation_id,
                    "performance": {
                        "operation": self.operation_name,
                        "duration_ms": duration_ms,
                        "status": "slow",
                        "threshold_ms": self.threshold_ms,
                    },
                },
            )
        else:
            # Operation was normal
            performance_logger.debug(
                f"Operation completed: {self.operation_name} took {duration_ms}ms",
                extra={
                    "correlation_id": self.correlation_id,
                    "performance": {
                        "operation": self.operation_name,
                        "duration_ms": duration_ms,
                        "status": "normal",
                    },
                },
            )
