[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "xd-incentives"
version = "1.0.0"
description = "XD Incentives Django Member Management System"
requires-python = ">=3.12"

# =============================================================================
# Black Configuration (Code Formatting)
# =============================================================================
[tool.black]
line-length = 88
target-version = ["py312"]
include = '\.pyi?$'
extend-exclude = '''
/(
    # directories
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
  | node_modules
  | static
)/
'''

# =============================================================================
# isort Configuration (Import Sorting)
# =============================================================================
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
known_django = "django"
known_first_party = ["apps", "config"]
sections = ["FUTURE", "STDLIB", "DJANGO", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
skip_glob = ["**/migrations/*.py", "**/node_modules/**", "**/static/**", "frontend/**"]

# =============================================================================
# MyPy Configuration (Type Checking)
# =============================================================================
[tool.mypy]
python_version = "3.12"
check_untyped_defs = true
ignore_missing_imports = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_unused_configs = true
disallow_untyped_calls = false
disallow_untyped_defs = false
disallow_incomplete_defs = false
show_error_codes = true
show_column_numbers = true
pretty = true

# Django-specific settings - disabled for pre-commit compatibility
# plugins = ["mypy_django_plugin.main"]

[[tool.mypy.overrides]]
module = "*.migrations.*"
ignore_errors = true

[[tool.mypy.overrides]]
module = "manage"
ignore_errors = true

[tool.django-stubs]
django_settings_module = "config.settings"

# =============================================================================
# Pylint Configuration
# =============================================================================
[tool.pylint.main]
# Django plugins disabled for pre-commit compatibility
# load-plugins = ["pylint_django"]
# django-settings-module = "config.settings"

[tool.pylint.format]
max-line-length = 88

[tool.pylint.messages_control]
disable = [
    "missing-docstring",
    "too-few-public-methods",
    "too-many-ancestors",
    "import-outside-toplevel",
    "duplicate-code",
    "unused-argument",
    "no-member",  # Django model fields
    "too-many-arguments",
    "too-many-locals",
    "too-many-branches",
    "too-many-statements",
    "fixme",
]

[tool.pylint.design]
max-attributes = 12
max-args = 8

# =============================================================================
# Bandit Configuration (Security)
# =============================================================================
[tool.bandit]
exclude_dirs = ["tests", "migrations", "node_modules", "static", "*/management/commands/create_sample*", "frontend"]
skips = ["B101", "B601", "B106", "B311", "B104", "B404", "B603", "B607"]  # Skip assert_used, shell=True, hardcoded passwords, weak random, bind interfaces, subprocess imports

# =============================================================================
# Coverage Configuration
# =============================================================================
[tool.coverage.run]
source = ["apps", "config"]
omit = [
    "*/migrations/*",
    "*/tests/*",
    "*/venv/*",
    "*/env/*",
    "manage.py",
    "*/settings/*",
    "*/node_modules/*",
    "*/static/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
]
show_missing = true
precision = 2

# =============================================================================
# Django Upgrade Configuration
# =============================================================================
[tool.django-upgrade]
target-version = "5.2"
