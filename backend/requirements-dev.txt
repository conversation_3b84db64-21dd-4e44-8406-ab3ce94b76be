# Development & Code Quality Tools
# Install with: pip install -r requirements.txt -r requirements-dev.txt

# Code Formatting & Style
black==24.10.0
isort==5.13.2
mypy==1.13.0
bandit==1.8.0
flake8==7.0.0
flake8-bugbear==23.12.2
flake8-comprehensions==3.15.0

# Django-Specific Linting
flake8-docstrings==1.7.0
flake8-import-order==0.18.2
flake8-quotes==3.4.0

# Static Analysis
mypy==1.13.0
django-stubs==5.1.0
types-requests==2.32.0.20241016
pylint==2.17.7
pylint-django==2.5.5

# Security Scanning
bandit==1.8.0
safety==3.2.9

# Django Tools
django-upgrade==1.22.0
django-extensions==3.2.3

# Testing & Coverage
pytest==8.3.3
pytest-django==4.9.0
pytest-cov==6.0.0
coverage==7.6.4

# Pre-commit Hooks
pre-commit==4.3.0
