/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    // Templates in theme app
    '../templates/**/*.html',
    // Templates in other django apps (e.g. base templates)
    '../../templates/**/*.html',
    // JavaScript files that might contain CSS classes
    '../../static_src/src/**/*.js',
    // Python files that might contain HTML strings
    '../**/*.py',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
}
