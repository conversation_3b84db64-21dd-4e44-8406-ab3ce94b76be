@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom form styles for Django Crispy Forms */
@layer components {
    .form-input {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm;
    }

    .form-textarea {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm;
    }

    .form-select {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm;
    }

    .form-checkbox {
        @apply h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded;
    }

    .btn-primary {
        @apply inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500;
    }

    .btn-secondary {
        @apply inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500;
    }

    .card {
        @apply bg-white overflow-hidden shadow rounded-lg;
    }

    .card-header {
        @apply px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200;
    }

    .card-body {
        @apply px-4 py-5 sm:p-6;
    }
}
