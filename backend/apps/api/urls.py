# api/urls.py
from django.urls import include, path

from .versioning import APIChangelogView, APIDocsView, APIVersionView

urlpatterns = [
    # API Version Information
    path("", APIVersionView.as_view(), name="api-version"),
    path("docs/", APIDocsView.as_view(), name="api-docs"),
    path("changelog/", APIChangelogView.as_view(), name="api-changelog"),
    # Versioned API endpoints
    path("v1/", include("apps.api.v1.urls", namespace="v1")),
]
