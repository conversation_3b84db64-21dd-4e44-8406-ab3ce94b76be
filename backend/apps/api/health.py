"""
Health check endpoints for monitoring service status.
"""

import logging

from django.core.cache import cache
from django.db import connection
from django.http import JsonResponse
from django.views.decorators.cache import never_cache
from django.views.decorators.http import require_http_methods

logger = logging.getLogger(__name__)


@never_cache
@require_http_methods(["GET"])
def health_check(request):
    """
    Basic health check endpoint.
    Returns 200 if the service is running.
    """
    return JsonResponse({"status": "healthy", "service": "xd-incentives-backend"})


@never_cache
@require_http_methods(["GET"])
def readiness_check(request):
    """
    Readiness check that verifies database and cache connectivity.
    Returns 200 if all dependencies are available.
    """
    checks = {"database": False, "cache": False, "overall": False}

    # Check database connection
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        checks["database"] = True
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        checks["database"] = False

    # Check Redis/cache connection
    try:
        cache.set("health_check", "ok", 10)
        cache.get("health_check")
        checks["cache"] = True
    except Exception as e:
        logger.error(f"Cache health check failed: {e}")
        checks["cache"] = False

    # Overall status
    checks["overall"] = all([checks["database"], checks["cache"]])

    status_code = 200 if checks["overall"] else 503

    return JsonResponse(checks, status=status_code)


@never_cache
@require_http_methods(["GET"])
def liveness_check(request):
    """
    Liveness check for Kubernetes/Docker.
    Returns 200 if the application is running (basic process check).
    """
    return JsonResponse({"status": "alive", "service": "xd-incentives-backend"})
