# api/versioning.py
from rest_framework import status
from rest_framework.response import Response
from rest_framework.versioning import URLPathVersioning
from rest_framework.views import APIView


class APIVersionView(APIView):
    """API version information endpoint"""

    permission_classes = []  # No authentication required

    def get(self, request, *args, **kwargs):
        """Return current API version information"""
        version = getattr(request, "version", "v1")

        return Response(
            {
                "version": version,
                "status": "active",
                "deprecated": False,
                "endpoints": {
                    "health": f"/api/{version}/health/",
                    "members": f"/api/{version}/members/",
                    "teams": f"/api/{version}/teams/",
                    "communications": f"/api/{version}/communications/",
                    "theme": f"/api/{version}/theme/config/",
                },
                "documentation": f"/api/{version}/docs/",
                "changelog": f"/api/{version}/changelog/",
            }
        )


class APIChangelogView(APIView):
    """API changelog endpoint"""

    permission_classes = []  # No authentication required

    def get(self, request, *args, **kwargs):
        """Return API changelog for the current version"""
        version = getattr(request, "version", "v1")

        changelog = {
            "v1": {
                "version": "1.0.0",
                "release_date": "2024-01-01",
                "status": "stable",
                "changes": [
                    "Initial API release",
                    "Member management endpoints",
                    "Team hierarchy endpoints",
                    "Communication system",
                    "JWT authentication",
                    "Theme configuration",
                ],
                "breaking_changes": [],
                "deprecations": [],
            }
        }

        return Response(
            changelog.get(
                version,
                {
                    "version": version,
                    "status": "unknown",
                    "message": "Version not found",
                },
            )
        )


class APIDocsView(APIView):
    """API documentation endpoint"""

    permission_classes = []  # No authentication required

    def get(self, request, *args, **kwargs):
        """Return API documentation links"""
        version = getattr(request, "version", "v1")

        return Response(
            {
                "version": version,
                "documentation": {
                    "swagger": f"/api/{version}/swagger/",
                    "redoc": f"/api/{version}/redoc/",
                    "postman_collection": f"/api/{version}/postman/",
                    "openapi_spec": f"/api/{version}/openapi.json",
                },
                "authentication": {
                    "type": "JWT",
                    "endpoint": "/api/token/",
                    "refresh_endpoint": "/api/token/refresh/",
                },
                "rate_limiting": {
                    "requests_per_minute": 100,
                    "burst_limit": 200,
                },
            }
        )


class CustomURLPathVersioning(URLPathVersioning):
    """Custom URL path versioning with additional features"""

    default_version = "v1"
    allowed_versions = ["v1"]
    version_param = "version"

    def determine_version(self, request, *args, **kwargs):
        """Determine the API version from the URL path"""
        version = super().determine_version(request, *args, **kwargs)

        # Validate version
        if version not in self.allowed_versions:
            return self.default_version

        return version
