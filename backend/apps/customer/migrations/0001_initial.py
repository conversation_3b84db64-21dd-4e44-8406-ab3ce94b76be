# Generated by Django 5.2.4 on 2025-07-29 19:01

import django.db.models.deletion
import phonenumber_field.modelfields
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(help_text='Customer company or individual name', max_length=100, verbose_name='Customer Name')),
                ('api_date_of_match', models.DateField(blank=True, editable=False, null=True, verbose_name='API Date of Match')),
                ('api_distributor_end_customer_id', models.CharField(blank=True, max_length=150, null=True, verbose_name='Customer Number')),
                ('api_distributor_end_customer_id_unique', models.Char<PERSON>ield(blank=True, max_length=150, null=True, verbose_name='Customer Number Unique')),
                ('address1', models.CharField(default='', max_length=150, verbose_name='Address 1')),
                ('address2', models.CharField(blank=True, default='', max_length=50, verbose_name='Address 2')),
                ('city', models.CharField(default='', max_length=50, verbose_name='City')),
                ('state', models.CharField(default='', max_length=2, verbose_name='State/Province')),
                ('postal', models.CharField(default='', max_length=10, verbose_name='Zip Code')),
                ('country', models.CharField(blank=True, default='usa', max_length=20, verbose_name='Country')),
                ('ship_to_number', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='Ship-To Number')),
                ('ship_to_name', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='Ship-To Name')),
                ('ship_to_address1', models.CharField(blank=True, default='', max_length=150, null=True, verbose_name='Ship-To Address 1')),
                ('ship_to_address2', models.CharField(blank=True, default='', max_length=150, null=True, verbose_name='Ship-To Address 2')),
                ('ship_to_city', models.CharField(blank=True, default='', max_length=150, null=True, verbose_name='Ship-To City')),
                ('ship_to_state', models.CharField(blank=True, default='', max_length=150, null=True, verbose_name='Ship-To State')),
                ('ship_to_zip', models.CharField(blank=True, default='', max_length=150, null=True, verbose_name='Ship-To Postal')),
                ('ship_to_country', models.CharField(blank=True, default='', max_length=150, null=True, verbose_name='Ship-To Country')),
                ('verified_address1', models.CharField(blank=True, default='', max_length=150, verbose_name='UPS Address 1')),
                ('verified_address2', models.CharField(blank=True, default='', max_length=150, verbose_name='UPS Address 2')),
                ('verified_city', models.CharField(blank=True, default='', max_length=150, verbose_name='UPS City')),
                ('verified_state', models.CharField(blank=True, default='', max_length=150, verbose_name='UPS State')),
                ('verified_postal', models.CharField(blank=True, default='', max_length=150, verbose_name='UPS Postal')),
                ('verified_country', models.CharField(blank=True, default='', max_length=150, verbose_name='UPS Country')),
                ('contact_fname', models.CharField(blank=True, default='', max_length=50, verbose_name='Contact First Name')),
                ('contact_lname', models.CharField(blank=True, default='', max_length=50, verbose_name='Contact Last Name')),
                ('contact_email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('contact_phone', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, region=None, verbose_name='Work Phone')),
                ('contact_cell', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, region=None, verbose_name='Cell Phone')),
                ('contact_fax', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, region=None, verbose_name='Fax')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('member', models.ForeignKey(blank=True, help_text='Member associated with this customer', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customers', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Customer',
                'verbose_name_plural': 'Customers',
                'db_table': 'customer',
                'ordering': ['title'],
                'permissions': (('customer.manager', 'Customer Manager'), ('customer.view_all', 'View All Customers'), ('customer.create', 'Create Customers'), ('customer.edit', 'Edit Customers')),
                'indexes': [models.Index(fields=['title'], name='customer_title_0d9a84_idx'), models.Index(fields=['member'], name='customer_member__2c4e7f_idx'), models.Index(fields=['contact_email'], name='customer_contact_e01ab9_idx'), models.Index(fields=['created'], name='customer_created_0f1c65_idx')],
            },
        ),
    ]
