from django.conf import settings
from django.db import models
from django.utils import timezone

from phonenumber_field.modelfields import PhoneNumberField


class Customer(models.Model):
    """Customer model for managing customer information and relationships"""

    # Relationship to Member
    member = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name="customers",
        blank=True,
        null=True,
        on_delete=models.CASCADE,
        help_text="Member associated with this customer",
    )

    # Basic Customer Information
    title = models.CharField(
        "Customer Name", max_length=100, help_text="Customer company or individual name"
    )
    customer_number = models.Char<PERSON>ield(
        "Customer Number", max_length=150, null=True, blank=True
    )

    # Primary Address
    address1 = models.Char<PERSON>ield("Address 1", max_length=150, default="")
    address2 = models.CharField("Address 2", max_length=50, default="", blank=True)
    city = models.CharField("City", max_length=50, default="")
    state = models.CharField("State/Province", max_length=2, default="")
    postal = models.Char<PERSON><PERSON>("Zip Code", max_length=10, default="")
    country = models.CharField("Country", max_length=20, default="usa", blank=True)

    # Shipping Address
    ship_to_number = models.CharField(
        "Ship-To Number", max_length=100, null=True, default="", blank=True
    )
    ship_to_name = models.CharField(
        "Ship-To Name", max_length=100, null=True, default="", blank=True
    )
    ship_to_address1 = models.CharField(
        "Ship-To Address 1", max_length=150, null=True, default="", blank=True
    )
    ship_to_address2 = models.CharField(
        "Ship-To Address 2", max_length=150, null=True, default="", blank=True
    )
    ship_to_city = models.CharField(
        "Ship-To City", max_length=150, null=True, default="", blank=True
    )
    ship_to_state = models.CharField(
        "Ship-To State", max_length=150, null=True, default="", blank=True
    )
    ship_to_zip = models.CharField(
        "Ship-To Postal", max_length=150, null=True, default="", blank=True
    )
    ship_to_country = models.CharField(
        "Ship-To Country", max_length=150, null=True, default="", blank=True
    )

    # Verified Address (UPS)
    verified_address1 = models.CharField(
        "UPS Address 1", max_length=150, default="", blank=True
    )
    verified_address2 = models.CharField(
        "UPS Address 2", max_length=150, default="", blank=True
    )
    verified_city = models.CharField("UPS City", max_length=150, default="", blank=True)
    verified_state = models.CharField(
        "UPS State", max_length=150, default="", blank=True
    )
    verified_postal = models.CharField(
        "UPS Postal", max_length=150, default="", blank=True
    )
    verified_country = models.CharField(
        "UPS Country", max_length=150, default="", blank=True
    )

    # Contact Information
    contact_fname = models.CharField(
        "Contact First Name", max_length=50, default="", blank=True
    )
    contact_lname = models.CharField(
        "Contact Last Name", max_length=50, default="", blank=True
    )
    contact_email = models.EmailField("Email", blank=True)
    contact_phone = PhoneNumberField("Work Phone", blank=True)
    contact_cell = PhoneNumberField("Cell Phone", blank=True)
    contact_fax = PhoneNumberField("Fax", blank=True)

    # System Fields
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title or f"Customer {self.id}"

    def get_full_contact_name(self):
        """Get the full contact name"""
        if self.contact_fname and self.contact_lname:
            return f"{self.contact_fname} {self.contact_lname}"
        elif self.contact_fname:
            return self.contact_fname
        elif self.contact_lname:
            return self.contact_lname
        return "N/A"

    def get_primary_address(self):
        """Get the primary address as a formatted string"""
        address_parts = []
        if self.address1:
            address_parts.append(self.address1)
        if self.address2:
            address_parts.append(self.address2)
        if self.city:
            address_parts.append(self.city)
        if self.state:
            address_parts.append(self.state)
        if self.postal:
            address_parts.append(self.postal)
        if self.country and self.country != "usa":
            address_parts.append(self.country)
        return ", ".join(address_parts) if address_parts else "No address provided"

    def get_shipping_address(self):
        """Get the shipping address as a formatted string"""
        address_parts = []
        if self.ship_to_name:
            address_parts.append(self.ship_to_name)
        if self.ship_to_address1:
            address_parts.append(self.ship_to_address1)
        if self.ship_to_address2:
            address_parts.append(self.ship_to_address2)
        if self.ship_to_city:
            address_parts.append(self.ship_to_city)
        if self.ship_to_state:
            address_parts.append(self.ship_to_state)
        if self.ship_to_zip:
            address_parts.append(self.ship_to_zip)
        if self.ship_to_country:
            address_parts.append(self.ship_to_country)
        return ", ".join(address_parts) if address_parts else "No shipping address"

    def get_verified_address(self):
        """Get the verified address as a formatted string"""
        address_parts = []
        if self.verified_address1:
            address_parts.append(self.verified_address1)
        if self.verified_address2:
            address_parts.append(self.verified_address2)
        if self.verified_city:
            address_parts.append(self.verified_city)
        if self.verified_state:
            address_parts.append(self.verified_state)
        if self.verified_postal:
            address_parts.append(self.verified_postal)
        if self.verified_country:
            address_parts.append(self.verified_country)
        return ", ".join(address_parts) if address_parts else "No verified address"

    class Meta:
        db_table = "customer"
        verbose_name = "Customer"
        verbose_name_plural = "Customers"
        ordering = ["title"]
        permissions = (
            ("customer.manager", "Customer Manager"),
            ("customer.view_all", "View All Customers"),
            ("customer.create", "Create Customers"),
            ("customer.edit", "Edit Customers"),
        )
        indexes = [
            models.Index(fields=["title"]),
            models.Index(fields=["member"]),
            models.Index(fields=["contact_email"]),
            models.Index(fields=["created"]),
        ]

    FIELDSETS = (
        (
            "Basic Information",
            {
                "fields": ("member", "title", "customer_number"),
                "description": "Customer basic information and member association",
            },
        ),
        (
            "Primary Address",
            {
                "fields": (
                    "address1",
                    "address2",
                    "city",
                    "state",
                    "postal",
                    "country",
                ),
                "description": "Primary business address",
            },
        ),
        (
            "Shipping Address",
            {
                "fields": (
                    "ship_to_number",
                    "ship_to_name",
                    "ship_to_address1",
                    "ship_to_address2",
                    "ship_to_city",
                    "ship_to_state",
                    "ship_to_zip",
                    "ship_to_country",
                ),
                "description": "Shipping address information",
            },
        ),
        (
            "Verified Address (UPS)",
            {
                "fields": (
                    "verified_address1",
                    "verified_address2",
                    "verified_city",
                    "verified_state",
                    "verified_postal",
                    "verified_country",
                ),
                "description": "Address verified through UPS or other service",
            },
        ),
        (
            "Contact Information",
            {
                "fields": (
                    "contact_fname",
                    "contact_lname",
                    "contact_email",
                    "contact_phone",
                    "contact_cell",
                    "contact_fax",
                ),
                "description": "Primary contact person information",
            },
        ),
        (
            "System Information",
            {
                "fields": ("created", "modified"),
                "description": "System timestamps and metadata",
            },
        ),
    )

    LISTDISPLAY = (
        "title",
        "member",
        "get_full_contact_name",
        "contact_email",
        "contact_phone",
        "created",
    )
