import random

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.utils import timezone

from faker import Faker

from apps.customer.models import Customer

User = get_user_model()
fake = Faker()


class Command(BaseCommand):
    help = "Create sample customer data for testing and development"

    def add_arguments(self, parser):
        parser.add_argument(
            "--count",
            type=int,
            default=10,
            help="Number of customers to create (default: 10)",
        )
        parser.add_argument(
            "--member",
            type=str,
            help="Username of member to associate customers with (optional)",
        )
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear existing customers before creating new ones",
        )

    def handle(self, *args, **options):
        count = options["count"]
        member_username = options["member"]
        clear_existing = options["clear"]

        # Clear existing customers if requested
        if clear_existing:
            deleted_count = Customer.objects.count()
            Customer.objects.all().delete()
            self.stdout.write(
                self.style.SUCCESS(f"Deleted {deleted_count} existing customers")
            )

        # Get or create member
        member = None
        if member_username:
            try:
                member = User.objects.get(username=member_username)
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(
                        f'Member with username "{member_username}" not found. Creating customers without member association.'
                    )
                )
        else:
            # Use first available member or create one
            member = User.objects.first()
            if not member:
                member = User.objects.create_user(
                    username="sample_member",
                    email="<EMAIL>",
                    password="samplepass123",
                )
                self.stdout.write(
                    self.style.SUCCESS(f"Created sample member: {member.username}")
                )

        # Sample company names and types
        company_types = [
            "Inc.",
            "Corp.",
            "LLC",
            "Ltd.",
            "Company",
            "Enterprises",
            "Industries",
            "Manufacturing",
            "Solutions",
            "Technologies",
            "Services",
            "Group",
        ]

        industries = [
            "Technology",
            "Manufacturing",
            "Healthcare",
            "Finance",
            "Retail",
            "Construction",
            "Education",
            "Transportation",
            "Energy",
            "Food & Beverage",
        ]

        # Sample states
        states = [
            "AL",
            "AK",
            "AZ",
            "AR",
            "CA",
            "CO",
            "CT",
            "DE",
            "FL",
            "GA",
            "HI",
            "ID",
            "IL",
            "IN",
            "IA",
            "KS",
            "KY",
            "LA",
            "ME",
            "MD",
            "MA",
            "MI",
            "MN",
            "MS",
            "MO",
            "MT",
            "NE",
            "NV",
            "NH",
            "NJ",
            "NM",
            "NY",
            "NC",
            "ND",
            "OH",
            "OK",
            "OR",
            "PA",
            "RI",
            "SC",
            "SD",
            "TN",
            "TX",
            "UT",
            "VT",
            "VA",
            "WA",
            "WV",
            "WI",
            "WY",
        ]

        customers_created = 0

        for i in range(count):
            # Generate company name
            company_name = fake.company()
            if random.choice([True, False]):
                company_type = random.choice(company_types)
                company_name = f"{company_name} {company_type}"

            # Generate customer number
            customer_number = f"CUST{fake.unique.random_number(digits=6)}"

            # Generate addresses
            primary_address = fake.street_address()
            primary_city = fake.city()
            primary_state = random.choice(states)
            primary_zip = fake.postcode_in_state(primary_state)

            # Sometimes add shipping address
            has_shipping = random.choice([True, False, False])  # 33% chance
            if has_shipping:
                ship_to_name = f"{company_name} - Shipping"
                ship_to_address = fake.street_address()
                ship_to_city = fake.city()
                ship_to_state = random.choice(states)
                ship_to_zip = fake.postcode_in_state(ship_to_state)
            else:
                ship_to_name = ship_to_address = ship_to_city = ship_to_state = (
                    ship_to_zip
                ) = ""

            # Generate contact information
            contact_fname = fake.first_name()
            contact_lname = fake.last_name()
            contact_email = fake.email()
            contact_phone = fake.phone_number()
            contact_cell = fake.phone_number() if random.choice([True, False]) else ""
            contact_fax = (
                fake.phone_number() if random.choice([True, False, False]) else ""
            )

            # Sometimes add verified address (UPS)
            has_verified = random.choice([True, False, False, False])  # 25% chance
            if has_verified:
                verified_address = fake.street_address()
                verified_city = fake.city()
                verified_state = random.choice(states)
                verified_zip = fake.postcode_in_state(verified_state)
            else:
                verified_address = verified_city = verified_state = verified_zip = ""

            # Create customer
            customer = Customer.objects.create(
                member=member,
                title=company_name,
                customer_number=customer_number,
                address1=primary_address,
                address2=(
                    fake.secondary_address() if random.choice([True, False]) else ""
                ),
                city=primary_city,
                state=primary_state,
                postal=primary_zip,
                country="usa",
                ship_to_name=ship_to_name,
                ship_to_address1=ship_to_address,
                ship_to_city=ship_to_city,
                ship_to_state=ship_to_state,
                ship_to_zip=ship_to_zip,
                ship_to_country="usa" if ship_to_name else "",
                verified_address1=verified_address,
                verified_city=verified_city,
                verified_state=verified_state,
                verified_postal=verified_zip,
                verified_country="usa" if verified_address else "",
                contact_fname=contact_fname,
                contact_lname=contact_lname,
                contact_email=contact_email,
                contact_phone=contact_phone,
                contact_cell=contact_cell,
                contact_fax=contact_fax,
            )

            customers_created += 1

            # Progress indicator
            if customers_created % 5 == 0 or customers_created == count:
                self.stdout.write(f"Created {customers_created}/{count} customers...")

        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully created {customers_created} sample customers!"
            )
        )

        if member:
            self.stdout.write(
                f"All customers associated with member: {member.username}"
            )

        # Show some sample data
        self.stdout.write("\nSample customers created:")
        for customer in Customer.objects.order_by("-created")[:3]:
            self.stdout.write(
                f"  - {customer.title} ({customer.customer_number}) - {customer.get_full_contact_name()}"
            )
