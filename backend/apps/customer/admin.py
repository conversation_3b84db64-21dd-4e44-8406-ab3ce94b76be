from django.contrib import admin
from django.db import models
from django.utils.html import format_html

from .models import Customer


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    """Admin configuration for Customer model"""

    list_display = (
        "title",
        "member",
        "get_full_contact_name",
        "contact_email",
        "contact_phone",
        "get_primary_address_short",
        "created",
    )
    list_filter = ("member", "created", "country", "state")
    search_fields = (
        "title",
        "contact_fname",
        "contact_lname",
        "contact_email",
        "address1",
        "city",
        "customer_number",
    )
    readonly_fields = ("created", "modified")

    fieldsets = Customer.FIELDSETS

    @admin.display(description="Contact Name", ordering="contact_fname")
    def get_full_contact_name(self, obj):
        """Display full contact name in admin list"""
        return obj.get_full_contact_name()

    @admin.display(description="Location")
    def get_primary_address_short(self, obj):
        """Display shortened primary address in admin list"""
        address_parts = []
        if obj.city:
            address_parts.append(obj.city)
        if obj.state:
            address_parts.append(obj.state)
        return ", ".join(address_parts) if address_parts else "No address"

    def get_queryset(self, request):
        """Optimize queryset with select_related for member"""
        return super().get_queryset(request).select_related("member")

    def get_search_results(self, request, queryset, search_term):
        """Enhanced search functionality"""
        queryset, use_distinct = super().get_search_results(
            request, queryset, search_term
        )

        if search_term:
            # Add additional search fields
            additional_qs = queryset.filter(
                models.Q(contact_phone__icontains=search_term)
                | models.Q(contact_cell__icontains=search_term)
                | models.Q(contact_fax__icontains=search_term)
                | models.Q(address1__icontains=search_term)
                | models.Q(address2__icontains=search_term)
                | models.Q(ship_to_name__icontains=search_term)
                | models.Q(ship_to_address1__icontains=search_term)
                | models.Q(customer_number__icontains=search_term)
            )
            queryset = queryset | additional_qs

        return queryset, use_distinct

    def has_add_permission(self, request):
        """Check if user can add customers"""
        return request.user.has_perm("customer.create")

    def has_change_permission(self, request, obj=None):
        """Check if user can change customers"""
        return request.user.has_perm("customer.edit")

    def has_delete_permission(self, request, obj=None):
        """Check if user can delete customers"""
        return request.user.has_perm("customer.manager")

    def has_view_permission(self, request, obj=None):
        """Check if user can view customers"""
        return request.user.has_perm("customer.view_all")

    class Media:
        css = {"all": ("admin/css/customer_admin.css",)}
        js = ("admin/js/customer_admin.js",)
