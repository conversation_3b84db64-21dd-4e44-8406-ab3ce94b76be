from django.contrib.auth import get_user_model
from django.test import TestCase
from django.utils import timezone

from .models import Customer

User = get_user_model()


class CustomerModelTest(TestCase):
    """Test cases for the Customer model"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )

        self.customer = Customer.objects.create(
            title="Test Customer Inc.",
            member=self.user,
            address1="123 Main St",
            city="Test City",
            state="CA",
            postal="12345",
            contact_fname="<PERSON>",
            contact_lname="<PERSON><PERSON>",
            contact_email="<EMAIL>",
        )

    def test_customer_creation(self):
        """Test that a customer can be created"""
        self.assertEqual(self.customer.title, "Test Customer Inc.")
        self.assertEqual(self.customer.member, self.user)
        self.assertEqual(self.customer.contact_fname, "<PERSON>")
        self.assertEqual(self.customer.contact_lname, "Doe")

    def test_customer_string_representation(self):
        """Test the string representation of customer"""
        self.assertEqual(str(self.customer), "Test Customer Inc.")

        # Test with no title
        customer_no_title = Customer.objects.create(title="", member=self.user)
        self.assertEqual(str(customer_no_title), f"Customer {customer_no_title.id}")

    def test_get_full_contact_name(self):
        """Test the get_full_contact_name method"""
        self.assertEqual(self.customer.get_full_contact_name(), "John Doe")

        # Test with only first name
        customer_first_only = Customer.objects.create(
            title="First Only", contact_fname="Jane", member=self.user
        )
        self.assertEqual(customer_first_only.get_full_contact_name(), "Jane")

        # Test with only last name
        customer_last_only = Customer.objects.create(
            title="Last Only", contact_lname="Smith", member=self.user
        )
        self.assertEqual(customer_last_only.get_full_contact_name(), "Smith")

        # Test with no names
        customer_no_names = Customer.objects.create(title="No Names", member=self.user)
        self.assertEqual(customer_no_names.get_full_contact_name(), "N/A")

    def test_get_primary_address(self):
        """Test the get_primary_address method"""
        expected = "123 Main St, Test City, CA, 12345"
        self.assertEqual(self.customer.get_primary_address(), expected)

        # Test with no address
        customer_no_address = Customer.objects.create(
            title="No Address", member=self.user
        )
        self.assertEqual(
            customer_no_address.get_primary_address(), "No address provided"
        )

    def test_get_shipping_address(self):
        """Test the get_shipping_address method"""
        # Test with shipping address
        self.customer.ship_to_name = "Shipping Dept"
        self.customer.ship_to_address1 = "456 Ship St"
        self.customer.ship_to_city = "Ship City"
        self.customer.ship_to_state = "NY"
        self.customer.save()

        expected = "Shipping Dept, 456 Ship St, Ship City, NY"
        self.assertEqual(self.customer.get_shipping_address(), expected)

        # Test with no shipping address
        customer_no_shipping = Customer.objects.create(
            title="No Shipping", member=self.user
        )
        self.assertEqual(
            customer_no_shipping.get_shipping_address(), "No shipping address"
        )

    def test_get_verified_address(self):
        """Test the get_verified_address method"""
        # Test with verified address
        self.customer.verified_address1 = "789 Verified St"
        self.customer.verified_city = "Verified City"
        self.customer.verified_state = "TX"
        self.customer.save()

        expected = "789 Verified St, Verified City, TX"
        self.assertEqual(self.customer.get_verified_address(), expected)

        # Test with no verified address
        customer_no_verified = Customer.objects.create(
            title="No Verified", member=self.user
        )
        self.assertEqual(
            customer_no_verified.get_verified_address(), "No verified address"
        )

    def test_customer_ordering(self):
        """Test that customers are ordered by title"""
        customer_a = Customer.objects.create(title="A Customer", member=self.user)
        customer_c = Customer.objects.create(title="C Customer", member=self.user)
        customer_b = Customer.objects.create(title="B Customer", member=self.user)

        customers = Customer.objects.all()
        self.assertEqual(customers[0], customer_a)
        self.assertEqual(customers[1], customer_b)
        self.assertEqual(customers[2], customer_c)

    def test_customer_permissions(self):
        """Test that customer permissions are properly set"""
        from django.contrib.auth.models import Permission
        from django.contrib.contenttypes.models import ContentType

        content_type = ContentType.objects.get_for_model(Customer)
        permissions = Permission.objects.filter(content_type=content_type)

        permission_names = [perm.codename for perm in permissions]
        expected_permissions = [
            "add_customer",
            "change_customer",
            "delete_customer",
            "view_customer",
        ]

        for expected in expected_permissions:
            self.assertIn(expected, permission_names)
