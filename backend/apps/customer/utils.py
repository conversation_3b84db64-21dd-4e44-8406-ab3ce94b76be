import logging
from typing import Dict, Optional, Tuple

from django.conf import settings

import requests

logger = logging.getLogger(__name__)


class GoogleAddressValidator:
    """Utility class for validating addresses using Google Address Validation API"""

    def __init__(self):
        self.api_key = settings.GOOGLE_ADDRESS_VALIDATION_API_KEY
        self.enabled = settings.GOOGLE_ADDRESS_VALIDATION_ENABLED
        self.url = settings.GOOGLE_ADDRESS_VALIDATION_URL
        self.timeout = getattr(
            settings, "GOOGLE_ADDRESS_VALIDATION_TIMEOUT", settings.EXTERNAL_API_TIMEOUT
        )

    def validate_address(self, address_components: Dict) -> Optional[Dict]:
        """
        Validate an address using Google Address Validation API

        Args:
            address_components: Dict containing address fields
                {
                    'address1': str,
                    'address2': str,
                    'city': str,
                    'state': str,
                    'postal': str,
                    'country': str
                }

        Returns:
            Dict with validated address components or None if validation fails
        """
        if not self.enabled or not self.api_key:
            logger.warning(
                "Google Address Validation is disabled or API key not configured"
            )
            return None

        try:
            # Build the address string for Google API
            address_lines = []
            if address_components.get("address1"):
                address_lines.append(address_components["address1"])
            if address_components.get("address2"):
                address_lines.append(address_components["address2"])

            # Build the address object for Google API
            address_obj = {
                "addressLines": address_lines,
                "locality": address_components.get("city", ""),
                "administrativeArea": address_components.get("state", ""),
                "postalCode": address_components.get("postal", ""),
                "regionCode": address_components.get("country", "US"),
            }

            # Prepare the request payload
            payload = {
                "address": address_obj,
                "enableUspsCass": True,  # Enable USPS CASS validation
            }

            # Make the API request
            response = requests.post(
                f"{self.url}?key={self.api_key}", json=payload, timeout=self.timeout
            )

            if response.status_code == 200:
                result = response.json()
                return self._parse_validation_result(result, address_components)
            else:
                logger.error(
                    f"Google Address Validation API error: {response.status_code} - {response.text}"
                )
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"Error calling Google Address Validation API: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in address validation: {e}")
            return None

    def _parse_validation_result(
        self, api_result: Dict, original_address: Dict
    ) -> Dict:
        """
        Parse the Google API validation result and extract relevant address components

        Args:
            api_result: Raw API response from Google
            original_address: Original address components for fallback

        Returns:
            Dict with validated address components
        """
        try:
            # Extract the validated address from the API response
            validation_result = api_result.get("result", {})
            address = validation_result.get("address", {})
            postal_address = address.get("postalAddress", {})
            verdict = validation_result.get("verdict", {})

            # Extract address components from postalAddress
            address_lines = postal_address.get("addressLines", [])
            locality = postal_address.get("locality", "")
            administrative_area = postal_address.get("administrativeArea", "")
            postal_code = postal_address.get("postalCode", "")
            region_code = postal_address.get("regionCode", "US")

            # Build validated address components
            validated_address = {
                "verified_address1": address_lines[0] if len(address_lines) > 0 else "",
                "verified_address2": address_lines[1] if len(address_lines) > 1 else "",
                "verified_city": locality,
                "verified_state": administrative_area,
                "verified_postal": postal_code,
                "verified_country": region_code,
                "validation_confidence": verdict.get(
                    "validationGranularity", "UNKNOWN"
                ),
                "is_valid": verdict.get("validationGranularity")
                in [
                    "COMPONENT_LEVEL",
                    "SUB_PREMISE_LEVEL",
                    "PREMISE_LEVEL",
                    "PREMISE",
                    "SUB_PREMISE",
                ],
            }

            # Log validation results
            validation_granularity = verdict.get("validationGranularity", "UNKNOWN")
            if validated_address["is_valid"]:
                logger.info(
                    f"Address validated successfully: {validated_address['verified_address1']}, {validated_address['verified_city']} (confidence: {validation_granularity})"
                )
            else:
                logger.info(
                    f"Address validation completed with low confidence: {original_address.get('address1', '')} (confidence: {validation_granularity})"
                )

            return validated_address

        except Exception as e:
            logger.error(f"Error parsing Google Address Validation result: {e}")
            return None

    def validate_customer_address(self, customer_data: Dict) -> Optional[Dict]:
        """
        Validate a customer's primary address and return verified address components

        Args:
            customer_data: Customer form data containing address fields

        Returns:
            Dict with verified address components or None if validation fails
        """
        # Extract primary address components
        address_components = {
            "address1": customer_data.get("address1", ""),
            "address2": customer_data.get("address2", ""),
            "city": customer_data.get("city", ""),
            "state": customer_data.get("state", ""),
            "postal": customer_data.get("postal", ""),
            "country": customer_data.get("country", "US"),
        }

        # Convert country names to ISO codes for Google API
        country_mapping = {
            "USA": "US",
            "United States": "US",
            "United States of America": "US",
            "Canada": "CA",
            "Mexico": "MX",
            "UK": "GB",
            "United Kingdom": "GB",
            "Great Britain": "GB",
        }

        country = address_components["country"]
        if country in country_mapping:
            address_components["country"] = country_mapping[country]

        # Only validate if we have enough address information
        if (
            not address_components["address1"]
            or not address_components["city"]
            or not address_components["state"]
        ):
            logger.info("Insufficient address information for validation")
            return None

        return self.validate_address(address_components)


def validate_customer_address(customer_data: Dict) -> Optional[Dict]:
    """
    Convenience function to validate a customer's address

    Args:
        customer_data: Customer form data

    Returns:
        Dict with verified address components or None
    """
    validator = GoogleAddressValidator()
    return validator.validate_customer_address(customer_data)
