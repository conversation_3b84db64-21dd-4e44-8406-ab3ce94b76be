from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render

from .forms import CustomerSubmissionForm
from .models import Customer
from .utils import validate_customer_address


@login_required
def customer_list(request):
    """Display a list of customers"""
    customers = Customer.objects.all().order_by("title")

    # Search functionality
    search_query = request.GET.get("search", "")
    if search_query:
        customers = customers.filter(
            Q(title__icontains=search_query)
            | Q(contact_fname__icontains=search_query)
            | Q(contact_lname__icontains=search_query)
            | Q(contact_email__icontains=search_query)
            | Q(address1__icontains=search_query)
            | Q(city__icontains=search_query)
        )

    # Filter by member if specified
    member_filter = request.GET.get("member", "")
    if member_filter:
        customers = customers.filter(member_id=member_filter)

    # Pagination
    paginator = Paginator(customers, 25)
    page_number = request.GET.get("page")
    page_obj = paginator.get_page(page_number)

    context = {
        "customers": page_obj,
        "search_query": search_query,
        "member_filter": member_filter,
    }

    return render(request, "customer/customer_list.html", context)


@login_required
def customer_detail(request, customer_id):
    """Display customer details"""
    customer = get_object_or_404(Customer, id=customer_id)

    context = {
        "customer": customer,
    }

    return render(request, "customer/customer_detail.html", context)


@login_required
def customer_create(request):
    """Create a new customer"""
    if request.method == "POST":
        form = CustomerSubmissionForm(request.POST)
        if form.is_valid():
            customer = form.save(user=request.user)
            messages.success(
                request, f'Customer "{customer.title}" created successfully.'
            )
            return redirect("customer:customer_detail", customer_id=customer.id)
        else:
            messages.error(request, "Please correct the errors below.")
    else:
        form = CustomerSubmissionForm()

    context = {
        "form": form,
        "action": "create",
    }

    return render(request, "customer/customer_form.html", context)


@login_required
def customer_update(request, customer_id):
    """Update an existing customer"""
    customer = get_object_or_404(Customer, id=customer_id)

    if request.method == "POST":
        form = CustomerSubmissionForm(request.POST, instance=customer)
        if form.is_valid():
            customer = form.save()

            # Validate address using Google Address Validation API
            try:
                validated_address = validate_customer_address(request.POST)
                if validated_address and validated_address.get("is_valid"):
                    # Update customer with validated address
                    customer.verified_address1 = validated_address.get(
                        "verified_address1", ""
                    )
                    customer.verified_address2 = validated_address.get(
                        "verified_address2", ""
                    )
                    customer.verified_city = validated_address.get("verified_city", "")
                    customer.verified_state = validated_address.get(
                        "verified_state", ""
                    )
                    customer.verified_postal = validated_address.get(
                        "verified_postal", ""
                    )
                    customer.verified_country = validated_address.get(
                        "verified_country", ""
                    )
                    customer.save()

                    messages.success(
                        request,
                        f'Customer "{customer.title}" updated successfully! Address has been validated and verified.',
                    )
                else:
                    messages.success(
                        request,
                        f'Customer "{customer.title}" updated successfully! Address validation was not available or failed.',
                    )
            except Exception as e:
                # Log the error but don't fail the customer update
                import logging

                logger = logging.getLogger(__name__)
                logger.error(
                    f"Address validation error for customer {customer.id}: {e}"
                )
                messages.success(
                    request,
                    f'Customer "{customer.title}" updated successfully! Address validation encountered an error.',
                )

            return redirect("customer:customer_detail", customer_id=customer.id)
        else:
            messages.error(request, "Please correct the errors below.")
    else:
        form = CustomerSubmissionForm(instance=customer)

    context = {
        "form": form,
        "customer": customer,
        "action": "update",
    }

    return render(request, "customer/customer_form.html", context)


@login_required
def customer_delete(request, customer_id):
    """Delete a customer"""
    customer = get_object_or_404(Customer, id=customer_id)

    if request.method == "POST":
        customer.delete()
        messages.success(request, "Customer deleted successfully.")
        return redirect("customer:customer_list")

    context = {
        "customer": customer,
    }

    return render(request, "customer/customer_confirm_delete.html", context)


def customer_api_search(request):
    """API endpoint for customer search"""
    search_query = request.GET.get("q", "")
    limit = int(request.GET.get("limit", 10))

    if not search_query:
        return JsonResponse({"results": []})

    customers = Customer.objects.filter(
        Q(title__icontains=search_query)
        | Q(contact_fname__icontains=search_query)
        | Q(contact_lname__icontains=search_query)
        | Q(contact_email__icontains=search_query)
    )[:limit]

    results = []
    for customer in customers:
        results.append(
            {
                "id": customer.id,
                "title": customer.title,
                "contact_name": customer.get_full_contact_name(),
                "contact_email": customer.contact_email,
                "address": customer.get_primary_address(),
            }
        )

    return JsonResponse({"results": results})


@login_required
def customer_submission(request):
    """Customer submission page with comprehensive form"""
    if request.method == "POST":
        form = CustomerSubmissionForm(request.POST)
        if form.is_valid():
            customer = form.save(user=request.user)

            # Validate address using Google Address Validation API
            try:
                validated_address = validate_customer_address(request.POST)
                if validated_address and validated_address.get("is_valid"):
                    # Update customer with validated address
                    customer.verified_address1 = validated_address.get(
                        "verified_address1", ""
                    )
                    customer.verified_address2 = validated_address.get(
                        "verified_address2", ""
                    )
                    customer.verified_city = validated_address.get("verified_city", "")
                    customer.verified_state = validated_address.get(
                        "verified_state", ""
                    )
                    customer.verified_postal = validated_address.get(
                        "verified_postal", ""
                    )
                    customer.verified_country = validated_address.get(
                        "verified_country", ""
                    )
                    customer.save()

                    messages.success(
                        request,
                        f'Customer "{customer.title}" created successfully! Address has been validated and verified.',
                    )
                else:
                    messages.success(
                        request,
                        f'Customer "{customer.title}" created successfully! Address validation was not available or failed.',
                    )
            except Exception as e:
                # Log the error but don't fail the customer creation
                import logging

                logger = logging.getLogger(__name__)
                logger.error(
                    f"Address validation error for customer {customer.id}: {e}"
                )
                messages.success(
                    request,
                    f'Customer "{customer.title}" created successfully! Address validation encountered an error.',
                )

            return redirect("customer:customer_detail", customer_id=customer.id)
        else:
            messages.error(request, "Please correct the errors below.")
    else:
        form = CustomerSubmissionForm()

    context = {
        "form": form,
        "page_title": "Create New Customer",
        "submit_button_text": "Create Customer",
    }

    return render(request, "customer/customer_submission.html", context)
