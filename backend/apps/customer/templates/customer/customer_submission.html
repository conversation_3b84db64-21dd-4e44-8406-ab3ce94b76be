{% extends 'member/base.html' %}
{% load static %}

{% block title %}{{ page_title|default:"Create New Customer" }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ page_title|default:"Create New Customer" }}</h1>
                <p class="mt-2 text-gray-600">Enter customer information below</p>
            </div>
            <a href="{% url 'customer:customer_list' %}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Customers
            </a>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="mb-8">
        <div class="bg-gray-200 rounded-full h-2">
            <div id="formProgress" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
        </div>
        <p class="text-sm text-gray-600 mt-2">Form completion: <span id="progressText">0%</span></p>
    </div>

    <form method="post" id="customerSubmissionForm" class="space-y-8">
        {% csrf_token %}

        <!-- Basic Information Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-6">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                    <p class="text-sm text-gray-500">Customer name and identification</p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Customer Name <span class="text-red-500">*</span>
                    </label>
                    {{ form.title }}
                    {% if form.title.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.title.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.customer_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Customer Number
                    </label>
                    {{ form.customer_number }}
                    {% if form.customer_number.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.customer_number.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Primary Address Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-6">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">Primary Address</h3>
                    <p class="text-sm text-gray-500">Main business address</p>
                </div>
            </div>

            <div class="space-y-6">
                <div>
                    <label for="{{ form.address1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Street Address <span class="text-red-500">*</span>
                    </label>
                    {{ form.address1 }}
                    {% if form.address1.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.address1.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.address2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Address Line 2
                    </label>
                    {{ form.address2 }}
                    {% if form.address2.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.address2.errors.0 }}</p>
                    {% endif %}
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="{{ form.city.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            City <span class="text-red-500">*</span>
                        </label>
                        {{ form.city }}
                        {% if form.city.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.city.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.state.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            State <span class="text-red-500">*</span>
                        </label>
                        {{ form.state }}
                        {% if form.state.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.state.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.postal.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            ZIP Code <span class="text-red-500">*</span>
                        </label>
                        {{ form.postal }}
                        {% if form.postal.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.postal.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <div>
                    <label for="{{ form.country.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Country
                    </label>
                    {{ form.country }}
                    {% if form.country.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.country.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Shipping Address Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Shipping Address</h3>
                        <p class="text-sm text-gray-500">Different shipping address (optional)</p>
                    </div>
                </div>
                <div class="flex items-center">
                    {{ form.use_shipping_address }}
                    <label for="{{ form.use_shipping_address.id_for_label }}" class="ml-2 text-sm font-medium text-gray-700">
                        Use different shipping address
                    </label>
                </div>
            </div>

            <div id="shippingAddressFields" class="hidden space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.ship_to_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Ship-To Number
                        </label>
                        {{ form.ship_to_number }}
                        {% if form.ship_to_number.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.ship_to_number.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.ship_to_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Ship-To Name
                        </label>
                        {{ form.ship_to_name }}
                        {% if form.ship_to_name.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.ship_to_name.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <div>
                    <label for="{{ form.ship_to_address1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Ship-To Street Address
                    </label>
                    {{ form.ship_to_address1 }}
                    {% if form.ship_to_address1.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.ship_to_address1.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.ship_to_address2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Ship-To Address Line 2
                    </label>
                    {{ form.ship_to_address2 }}
                    {% if form.ship_to_address2.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.ship_to_address2.errors.0 }}</p>
                    {% endif %}
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="{{ form.ship_to_city.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Ship-To City
                        </label>
                        {{ form.ship_to_city }}
                        {% if form.ship_to_city.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.ship_to_city.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.ship_to_state.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Ship-To State
                        </label>
                        {{ form.ship_to_state }}
                        {% if form.ship_to_state.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.ship_to_state.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.ship_to_zip.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Ship-To ZIP Code
                        </label>
                        {{ form.ship_to_zip }}
                        {% if form.ship_to_zip.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.ship_to_zip.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <div>
                    <label for="{{ form.ship_to_country.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Ship-To Country
                    </label>
                    {{ form.ship_to_country }}
                    {% if form.ship_to_country.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.ship_to_country.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Verified Address Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Verified Address (UPS)</h3>
                        <p class="text-sm text-gray-500">Address verified through UPS (optional)</p>
                    </div>
                </div>
                <div class="flex items-center">
                    {{ form.use_verified_address }}
                    <label for="{{ form.use_verified_address.id_for_label }}" class="ml-2 text-sm font-medium text-gray-700">
                        Use verified address
                    </label>
                </div>
            </div>

            <div id="verifiedAddressFields" class="hidden space-y-6">
                <div>
                    <label for="{{ form.verified_address1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Verified Street Address
                    </label>
                    {{ form.verified_address1 }}
                    {% if form.verified_address1.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.verified_address1.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.verified_address2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Verified Address Line 2
                    </label>
                    {{ form.verified_address2 }}
                    {% if form.verified_address2.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.verified_address2.errors.0 }}</p>
                    {% endif %}
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="{{ form.verified_city.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Verified City
                        </label>
                        {{ form.verified_city }}
                        {% if form.verified_city.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.verified_city.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.verified_state.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Verified State
                        </label>
                        {{ form.verified_state }}
                        {% if form.verified_state.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.verified_state.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.verified_postal.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Verified ZIP Code
                        </label>
                        {{ form.verified_postal }}
                        {% if form.verified_postal.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.verified_postal.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <div>
                    <label for="{{ form.verified_country.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Verified Country
                    </label>
                    {{ form.verified_country }}
                    {% if form.verified_country.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.verified_country.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Contact Information Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-6">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">Contact Information</h3>
                    <p class="text-sm text-gray-500">Primary contact person details</p>
                </div>
            </div>

            <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.contact_fname.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Contact First Name
                        </label>
                        {{ form.contact_fname }}
                        {% if form.contact_fname.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.contact_fname.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.contact_lname.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Contact Last Name
                        </label>
                        {{ form.contact_lname }}
                        {% if form.contact_lname.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.contact_lname.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.contact_email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        {{ form.contact_email }}
                        {% if form.contact_email.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.contact_email.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.contact_phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Work Phone
                        </label>
                        {{ form.contact_phone }}
                        {% if form.contact_phone.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.contact_phone.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.contact_cell.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Cell Phone
                        </label>
                        {{ form.contact_cell }}
                        {% if form.contact_cell.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.contact_cell.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.contact_fax.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Fax Number
                        </label>
                        {{ form.contact_fax }}
                        {% if form.contact_fax.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.contact_fax.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex justify-between items-center">
                <a href="{% url 'customer:customer_list' %}" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    {{ submit_button_text|default:"Create Customer" }}
                </button>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle shipping address fields
    const shippingCheckbox = document.getElementById('{{ form.use_shipping_address.id_for_label }}');
    const shippingFields = document.getElementById('shippingAddressFields');

    if (shippingCheckbox) {
        shippingCheckbox.addEventListener('change', function() {
            if (this.checked) {
                shippingFields.classList.remove('hidden');
            } else {
                shippingFields.classList.add('hidden');
            }
            updateProgress();
        });
    }

    // Toggle verified address fields
    const verifiedCheckbox = document.getElementById('{{ form.use_verified_address.id_for_label }}');
    const verifiedFields = document.getElementById('verifiedAddressFields');

    if (verifiedCheckbox) {
        verifiedCheckbox.addEventListener('change', function() {
            if (this.checked) {
                verifiedFields.classList.remove('hidden');
            } else {
                verifiedFields.classList.add('hidden');
            }
            updateProgress();
        });
    }

    // Form progress tracking
    const form = document.getElementById('customerSubmissionForm');
    const progressBar = document.getElementById('formProgress');
    const progressText = document.getElementById('progressText');

    function updateProgress() {
        const requiredFields = form.querySelectorAll('label:has(span.text-red-500)');
        const filledRequiredFields = Array.from(requiredFields).filter(field => {
            const input = form.querySelector(`[name="${field.getAttribute('for').replace('id_', '')}"]`);
            return input && input.value.trim() !== '';
        });

        const progress = requiredFields.length > 0 ? (filledRequiredFields.length / requiredFields.length) * 100 : 0;
        progressBar.style.width = progress + '%';
        progressText.textContent = Math.round(progress) + '%';
    }

    // Update progress on input changes
    form.addEventListener('input', updateProgress);

    // Initial progress update
    updateProgress();

    // Form validation
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('label:has(span.text-red-500)');
        let isValid = true;

        requiredFields.forEach(field => {
            const input = form.querySelector(`[name="${field.getAttribute('for').replace('id_', '')}"]`);
            if (input && input.value.trim() === '') {
                isValid = false;
                input.classList.add('border-red-500');
                input.classList.remove('border-gray-300');
            } else if (input) {
                input.classList.remove('border-red-500');
                input.classList.add('border-gray-300');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
});
</script>
{% endblock %}
