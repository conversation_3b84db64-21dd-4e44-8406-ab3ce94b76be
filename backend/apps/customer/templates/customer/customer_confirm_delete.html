{% extends 'member/base.html' %}
{% load static %}

{% block title %}Delete {{ customer.title }}{% endblock %}

{% block extra_css %}
<style>
    .delete-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .delete-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 20px;
        border-radius: 8px 8px 0 0;
    }

    .delete-body {
        padding: 20px;
    }

    .warning-icon {
        font-size: 3rem;
        color: #dc3545;
        margin-bottom: 15px;
    }

    .customer-info {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 15px;
        margin: 20px 0;
    }

    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
        padding: 12px 24px;
        font-weight: 600;
        border-radius: 6px;
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        padding: 12px 24px;
        font-weight: 600;
        border-radius: 6px;
    }

    .btn-secondary:hover {
        background-color: #545b62;
        border-color: #545b62;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Delete Customer</h1>
                <a href="{% url 'customer:customer_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Customers
                </a>
            </div>

            <div class="delete-card">
                <!-- Delete Header -->
                <div class="delete-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-1">Delete Customer</h2>
                            <p class="mb-0">Are you sure you want to delete this customer?</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>

                <!-- Delete Body -->
                <div class="delete-body">
                    <div class="text-center">
                        <div class="warning-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h4 class="text-danger mb-3">Warning: This action cannot be undone!</h4>
                        <p class="text-muted mb-4">
                            Deleting this customer will permanently remove all associated data from the system.
                        </p>
                    </div>

                    <!-- Customer Information -->
                    <div class="customer-info">
                        <h5><i class="fas fa-info-circle"></i> Customer Information</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Customer Name:</strong> {{ customer.title }}</p>
                                {% if customer.customer_number %}
                                    <p><strong>Customer Number:</strong> {{ customer.customer_number }}</p>
                                {% endif %}
                                {% if customer.member %}
                                    <p><strong>Associated Member:</strong> {{ customer.member.get_full_name }}</p>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <p><strong>Created:</strong> {{ customer.created|date:"F d, Y" }}</p>
                                <p><strong>Last Modified:</strong> {{ customer.modified|date:"F d, Y" }}</p>
                                {% if customer.get_full_contact_name != "N/A" %}
                                    <p><strong>Contact:</strong> {{ customer.get_full_contact_name }}</p>
                                {% endif %}
                            </div>
                        </div>

                        {% if customer.contact_email or customer.contact_phone %}
                        <div class="mt-3">
                            <h6>Contact Information:</h6>
                            {% if customer.contact_email %}
                                <p><i class="fas fa-envelope"></i> {{ customer.contact_email }}</p>
                            {% endif %}
                            {% if customer.contact_phone %}
                                <p><i class="fas fa-phone"></i> {{ customer.contact_phone }}</p>
                            {% endif %}
                        </div>
                        {% endif %}

                        {% if customer.get_primary_address != "No address provided" %}
                        <div class="mt-3">
                            <h6>Primary Address:</h6>
                            <p>{{ customer.get_primary_address }}</p>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Confirmation Form -->
                    <form method="post">
                        {% csrf_token %}
                        <div class="text-center mt-4">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Please confirm:</strong> This action will permanently delete the customer "{{ customer.title }}" and all associated data.
                            </div>

                            <div class="d-flex justify-content-center gap-3">
                                <a href="{% url 'customer:customer_detail' customer.id %}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> Delete Customer
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
