{% extends "member/base.html" %}

{% block title %}Customers{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Customers</h1>
        <a href="{% url 'customer:customer_create' %}"
           class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Add Customer
        </a>
    </div>

    <!-- Search Form -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
        <form method="get" class="flex gap-4">
            <div class="flex-1">
                <input type="text"
                       name="search"
                       value="{{ search_query }}"
                       placeholder="Search customers..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <button type="submit"
                    class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Search
            </button>
            {% if search_query %}
                <a href="{% url 'customer:customer_list' %}"
                   class="bg-gray-400 hover:bg-gray-500 text-white font-bold py-2 px-4 rounded">
                    Clear
                </a>
            {% endif %}
        </form>
    </div>

    <!-- Customers Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        {% if customers %}
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Customer Name
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Contact
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Email
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Phone
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Location
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Member
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for customer in customers %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">
                                <a href="{% url 'customer:customer_detail' customer.id %}"
                                   class="text-blue-600 hover:text-blue-900">
                                    {{ customer.title }}
                                </a>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                {{ customer.get_full_contact_name }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                {% if customer.contact_email %}
                                    <a href="mailto:{{ customer.contact_email }}"
                                       class="text-blue-600 hover:text-blue-900">
                                        {{ customer.contact_email }}
                                    </a>
                                {% else %}
                                    <span class="text-gray-400">No email</span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                {% if customer.contact_phone %}
                                    <a href="tel:{{ customer.contact_phone }}"
                                       class="text-blue-600 hover:text-blue-900">
                                        {{ customer.contact_phone }}
                                    </a>
                                {% else %}
                                    <span class="text-gray-400">No phone</span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                {% if customer.city or customer.state %}
                                    {{ customer.city }}{% if customer.city and customer.state %}, {% endif %}{{ customer.state }}
                                {% else %}
                                    <span class="text-gray-400">No location</span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                {% if customer.member %}
                                    <a href="{% url 'member:member_detail' customer.member.id %}"
                                       class="text-blue-600 hover:text-blue-900">
                                        {{ customer.member.username }}
                                    </a>
                                {% else %}
                                    <span class="text-gray-400">No member</span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'customer:customer_update' customer.id %}"
                               class="text-indigo-600 hover:text-indigo-900 mr-3">
                                Edit
                            </a>
                            <a href="{% url 'customer:customer_delete' customer.id %}"
                               class="text-red-600 hover:text-red-900">
                                Delete
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="text-center py-12">
                <div class="text-gray-400 text-lg mb-4">
                    {% if search_query %}
                        No customers found matching "{{ search_query }}"
                    {% else %}
                        No customers found
                    {% endif %}
                </div>
                {% if not search_query %}
                    <a href="{% url 'customer:customer_create' %}"
                       class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Add Your First Customer
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if customers.has_other_pages %}
    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6">
        <div class="flex-1 flex justify-between sm:hidden">
            {% if customers.has_previous %}
                <a href="?page={{ customers.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                </a>
            {% endif %}
            {% if customers.has_next %}
                <a href="?page={{ customers.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                </a>
            {% endif %}
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    Showing <span class="font-medium">{{ customers.start_index }}</span> to
                    <span class="font-medium">{{ customers.end_index }}</span> of
                    <span class="font-medium">{{ customers.paginator.count }}</span> results
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    {% if customers.has_previous %}
                        <a href="?page={{ customers.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Previous
                        </a>
                    {% endif %}

                    {% for num in customers.paginator.page_range %}
                        {% if customers.number == num %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                {{ num }}
                            </span>
                        {% elif num > customers.number|add:'-3' and num < customers.number|add:'3' %}
                            <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}"
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}

                    {% if customers.has_next %}
                        <a href="?page={{ customers.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Next
                        </a>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
