{% extends 'member/base.html' %}
{% load static %}

{% block title %}{% if action == 'update' %}Edit {{ customer.title }}{% else %}Create New Customer{% endif %}{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid #e9ecef;
    }

    .form-section h4 {
        color: #495057;
        margin-bottom: 15px;
        border-bottom: 2px solid #007bff;
        padding-bottom: 8px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .form-control {
        border-radius: 6px;
        border: 1px solid #ced4da;
        padding: 10px 12px;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .form-check-input:checked {
        background-color: #007bff;
        border-color: #007bff;
    }

    .btn-primary {
        background-color: #007bff;
        border-color: #007bff;
        padding: 12px 24px;
        font-weight: 600;
        border-radius: 6px;
    }

    .btn-primary:hover {
        background-color: #0056b3;
        border-color: #0056b3;
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        padding: 12px 24px;
        font-weight: 600;
        border-radius: 6px;
    }

    .btn-secondary:hover {
        background-color: #545b62;
        border-color: #545b62;
    }

    .hidden-section {
        display: none;
    }

    .alert {
        border-radius: 6px;
        padding: 12px 16px;
        margin-bottom: 20px;
    }

    .alert-success {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }

    .alert-danger {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    {% if action == 'update' %}
                        Edit Customer: {{ customer.title }}
                    {% else %}
                        Create New Customer
                    {% endif %}
                </h1>
                <a href="{% url 'customer:customer_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Customers
                </a>
            </div>

            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <form method="post" id="customerForm">
                {% csrf_token %}

                <!-- Basic Information Section -->
                <div class="form-section">
                    <h4><i class="fas fa-info-circle"></i> Basic Information</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.title.id_for_label }}" class="form-label required-field">
                                    Customer Name
                                </label>
                                {{ form.title }}
                                {% if form.title.errors %}
                                    <div class="text-danger small">{{ form.title.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.customer_number.id_for_label }}" class="form-label">
                                    Customer Number
                                </label>
                                {{ form.customer_number }}
                                {% if form.customer_number.errors %}
                                    <div class="text-danger small">{{ form.customer_number.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Primary Address Section -->
                <div class="form-section">
                    <h4><i class="fas fa-map-marker-alt"></i> Primary Address</h4>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="{{ form.address1.id_for_label }}" class="form-label required-field">
                                    Street Address
                                </label>
                                {{ form.address1 }}
                                {% if form.address1.errors %}
                                    <div class="text-danger small">{{ form.address1.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.address2.id_for_label }}" class="form-label">
                                    Address Line 2
                                </label>
                                {{ form.address2 }}
                                {% if form.address2.errors %}
                                    <div class="text-danger small">{{ form.address2.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.city.id_for_label }}" class="form-label required-field">
                                    City
                                </label>
                                {{ form.city }}
                                {% if form.city.errors %}
                                    <div class="text-danger small">{{ form.city.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="{{ form.state.id_for_label }}" class="form-label required-field">
                                    State
                                </label>
                                {{ form.state }}
                                {% if form.state.errors %}
                                    <div class="text-danger small">{{ form.state.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="{{ form.postal.id_for_label }}" class="form-label required-field">
                                    ZIP Code
                                </label>
                                {{ form.postal }}
                                {% if form.postal.errors %}
                                    <div class="text-danger small">{{ form.postal.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="{{ form.country.id_for_label }}" class="form-label">
                                    Country
                                </label>
                                {{ form.country }}
                                {% if form.country.errors %}
                                    <div class="text-danger small">{{ form.country.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shipping Address Section -->
                <div class="form-section">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4><i class="fas fa-shipping-fast"></i> Shipping Address</h4>
                        <div class="form-check">
                            {{ form.use_shipping_address }}
                            <label class="form-check-label" for="{{ form.use_shipping_address.id_for_label }}">
                                Use different shipping address
                            </label>
                        </div>
                    </div>
                    <div id="shippingAddressFields" class="hidden-section">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.ship_to_number.id_for_label }}" class="form-label">
                                        Ship-To Number
                                    </label>
                                    {{ form.ship_to_number }}
                                    {% if form.ship_to_number.errors %}
                                        <div class="text-danger small">{{ form.ship_to_number.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.ship_to_name.id_for_label }}" class="form-label">
                                        Ship-To Name
                                    </label>
                                    {{ form.ship_to_name }}
                                    {% if form.ship_to_name.errors %}
                                        <div class="text-danger small">{{ form.ship_to_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="{{ form.ship_to_address1.id_for_label }}" class="form-label">
                                        Ship-To Street Address
                                    </label>
                                    {{ form.ship_to_address1 }}
                                    {% if form.ship_to_address1.errors %}
                                        <div class="text-danger small">{{ form.ship_to_address1.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.ship_to_address2.id_for_label }}" class="form-label">
                                        Ship-To Address Line 2
                                    </label>
                                    {{ form.ship_to_address2 }}
                                    {% if form.ship_to_address2.errors %}
                                        <div class="text-danger small">{{ form.ship_to_address2.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.ship_to_city.id_for_label }}" class="form-label">
                                        Ship-To City
                                    </label>
                                    {{ form.ship_to_city }}
                                    {% if form.ship_to_city.errors %}
                                        <div class="text-danger small">{{ form.ship_to_city.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.ship_to_state.id_for_label }}" class="form-label">
                                        Ship-To State
                                    </label>
                                    {{ form.ship_to_state }}
                                    {% if form.ship_to_state.errors %}
                                        <div class="text-danger small">{{ form.ship_to_state.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.ship_to_zip.id_for_label }}" class="form-label">
                                        Ship-To ZIP Code
                                    </label>
                                    {{ form.ship_to_zip }}
                                    {% if form.ship_to_zip.errors %}
                                        <div class="text-danger small">{{ form.ship_to_zip.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.ship_to_country.id_for_label }}" class="form-label">
                                        Ship-To Country
                                    </label>
                                    {{ form.ship_to_country }}
                                    {% if form.ship_to_country.errors %}
                                        <div class="text-danger small">{{ form.ship_to_country.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Verified Address Section -->
                <div class="form-section">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4><i class="fas fa-check-circle"></i> Verified Address (UPS)</h4>
                        <div class="form-check">
                            {{ form.use_verified_address }}
                            <label class="form-check-label" for="{{ form.use_verified_address.id_for_label }}">
                                Use verified address
                            </label>
                        </div>
                    </div>
                    <div id="verifiedAddressFields" class="hidden-section">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="{{ form.verified_address1.id_for_label }}" class="form-label">
                                        Verified Street Address
                                    </label>
                                    {{ form.verified_address1 }}
                                    {% if form.verified_address1.errors %}
                                        <div class="text-danger small">{{ form.verified_address1.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.verified_address2.id_for_label }}" class="form-label">
                                        Verified Address Line 2
                                    </label>
                                    {{ form.verified_address2 }}
                                    {% if form.verified_address2.errors %}
                                        <div class="text-danger small">{{ form.verified_address2.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.verified_city.id_for_label }}" class="form-label">
                                        Verified City
                                    </label>
                                    {{ form.verified_city }}
                                    {% if form.verified_city.errors %}
                                        <div class="text-danger small">{{ form.verified_city.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.verified_state.id_for_label }}" class="form-label">
                                        Verified State
                                    </label>
                                    {{ form.verified_state }}
                                    {% if form.verified_state.errors %}
                                        <div class="text-danger small">{{ form.verified_state.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.verified_postal.id_for_label }}" class="form-label">
                                        Verified ZIP Code
                                    </label>
                                    {{ form.verified_postal }}
                                    {% if form.verified_postal.errors %}
                                        <div class="text-danger small">{{ form.verified_postal.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.verified_country.id_for_label }}" class="form-label">
                                        Verified Country
                                    </label>
                                    {{ form.verified_country }}
                                    {% if form.verified_country.errors %}
                                        <div class="text-danger small">{{ form.verified_country.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information Section -->
                <div class="form-section">
                    <h4><i class="fas fa-user"></i> Contact Information</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.contact_fname.id_for_label }}" class="form-label">
                                    Contact First Name
                                </label>
                                {{ form.contact_fname }}
                                {% if form.contact_fname.errors %}
                                    <div class="text-danger small">{{ form.contact_fname.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.contact_lname.id_for_label }}" class="form-label">
                                    Contact Last Name
                                </label>
                                {{ form.contact_lname }}
                                {% if form.contact_lname.errors %}
                                    <div class="text-danger small">{{ form.contact_lname.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.contact_email.id_for_label }}" class="form-label">
                                    Email Address
                                </label>
                                {{ form.contact_email }}
                                {% if form.contact_email.errors %}
                                    <div class="text-danger small">{{ form.contact_email.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.contact_phone.id_for_label }}" class="form-label">
                                    Work Phone
                                </label>
                                {{ form.contact_phone }}
                                {% if form.contact_phone.errors %}
                                    <div class="text-danger small">{{ form.contact_phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.contact_cell.id_for_label }}" class="form-label">
                                    Cell Phone
                                </label>
                                {{ form.contact_cell }}
                                {% if form.contact_cell.errors %}
                                    <div class="text-danger small">{{ form.contact_cell.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.contact_fax.id_for_label }}" class="form-label">
                                    Fax Number
                                </label>
                                {{ form.contact_fax }}
                                {% if form.contact_fax.errors %}
                                    <div class="text-danger small">{{ form.contact_fax.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-section">
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'customer:customer_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if action == 'update' %}
                                Update Customer
                            {% else %}
                                Create Customer
                            {% endif %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle shipping address fields
    const shippingCheckbox = document.getElementById('{{ form.use_shipping_address.id_for_label }}');
    const shippingFields = document.getElementById('shippingAddressFields');

    if (shippingCheckbox) {
        shippingCheckbox.addEventListener('change', function() {
            if (this.checked) {
                shippingFields.classList.remove('hidden-section');
            } else {
                shippingFields.classList.add('hidden-section');
            }
        });
    }

    // Toggle verified address fields
    const verifiedCheckbox = document.getElementById('{{ form.use_verified_address.id_for_label }}');
    const verifiedFields = document.getElementById('verifiedAddressFields');

    if (verifiedCheckbox) {
        verifiedCheckbox.addEventListener('change', function() {
            if (this.checked) {
                verifiedFields.classList.remove('hidden-section');
            } else {
                verifiedFields.classList.add('hidden-section');
            }
        });
    }

    // Form validation
    const form = document.getElementById('customerForm');
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('.required-field');
        let isValid = true;

        requiredFields.forEach(field => {
            const input = form.querySelector(`[name="${field.textContent.split('*')[0].trim().toLowerCase().replace(/\s+/g, '_')}"]`);
            if (input && input.value.trim() === '') {
                isValid = false;
                input.classList.add('is-invalid');
            } else if (input) {
                input.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
});
</script>
{% endblock %}
