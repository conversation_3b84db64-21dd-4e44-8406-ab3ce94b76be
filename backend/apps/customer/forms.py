from django import forms
from django.contrib.auth import get_user_model

from .models import Customer

User = get_user_model()


class CustomerSubmissionForm(forms.ModelForm):
    """Form for customer submissions"""

    # Override some fields for better UX
    contact_phone = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": "(*************"}
        ),
    )
    contact_cell = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": "(*************"}
        ),
    )
    contact_fax = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": "(*************"}
        ),
    )

    # Add a checkbox for shipping address
    use_shipping_address = forms.BooleanField(
        required=False,
        initial=False,
        label="Use different shipping address",
        widget=forms.CheckboxInput(attrs={"class": "form-check-input"}),
    )

    # Add a checkbox for verified address
    use_verified_address = forms.BooleanField(
        required=False,
        initial=False,
        label="Use verified address (UPS)",
        widget=forms.CheckboxInput(attrs={"class": "form-check-input"}),
    )

    class Meta:
        model = Customer
        fields = [
            "title",
            "customer_number",
            "address1",
            "address2",
            "city",
            "state",
            "postal",
            "country",
            "ship_to_number",
            "ship_to_name",
            "ship_to_address1",
            "ship_to_address2",
            "ship_to_city",
            "ship_to_state",
            "ship_to_zip",
            "ship_to_country",
            "verified_address1",
            "verified_address2",
            "verified_city",
            "verified_state",
            "verified_postal",
            "verified_country",
            "contact_fname",
            "contact_lname",
            "contact_email",
            "contact_phone",
            "contact_cell",
            "contact_fax",
        ]
        widgets = {
            "title": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Company or Individual Name",
                }
            ),
            "customer_number": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Customer Number (optional)",
                }
            ),
            "address1": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Street Address"}
            ),
            "address2": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Suite, Apt, etc. (optional)",
                }
            ),
            "city": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "City"}
            ),
            "state": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "State (2 letter code)"}
            ),
            "postal": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "ZIP Code"}
            ),
            "country": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Country (default: USA)"}
            ),
            "ship_to_number": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Ship-To Number (optional)",
                }
            ),
            "ship_to_name": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Ship-To Name"}
            ),
            "ship_to_address1": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Ship-To Street Address"}
            ),
            "ship_to_address2": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Ship-To Suite, Apt, etc. (optional)",
                }
            ),
            "ship_to_city": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Ship-To City"}
            ),
            "ship_to_state": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Ship-To State"}
            ),
            "ship_to_zip": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Ship-To ZIP Code"}
            ),
            "ship_to_country": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Ship-To Country"}
            ),
            "verified_address1": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Verified Street Address",
                }
            ),
            "verified_address2": forms.TextInput(
                attrs={
                    "class": "form-control",
                    "placeholder": "Verified Suite, Apt, etc. (optional)",
                }
            ),
            "verified_city": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Verified City"}
            ),
            "verified_state": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Verified State"}
            ),
            "verified_postal": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Verified ZIP Code"}
            ),
            "verified_country": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Verified Country"}
            ),
            "contact_fname": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Contact First Name"}
            ),
            "contact_lname": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Contact Last Name"}
            ),
            "contact_email": forms.EmailInput(
                attrs={"class": "form-control", "placeholder": "<EMAIL>"}
            ),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default country
        if not self.instance.pk and not self.data:
            self.fields["country"].initial = "USA"

    def clean(self):
        cleaned_data = super().clean()

        # Validate that at least one address is provided
        has_primary_address = any(
            [
                cleaned_data.get("address1"),
                cleaned_data.get("city"),
                cleaned_data.get("state"),
            ]
        )

        has_shipping_address = any(
            [
                cleaned_data.get("ship_to_address1"),
                cleaned_data.get("ship_to_city"),
                cleaned_data.get("ship_to_state"),
            ]
        )

        has_verified_address = any(
            [
                cleaned_data.get("verified_address1"),
                cleaned_data.get("verified_city"),
                cleaned_data.get("verified_state"),
            ]
        )

        if not (has_primary_address or has_shipping_address or has_verified_address):
            raise forms.ValidationError(
                "At least one address (primary, shipping, or verified) must be provided."
            )

        # Validate contact information
        has_contact_info = any(
            [
                cleaned_data.get("contact_fname"),
                cleaned_data.get("contact_lname"),
                cleaned_data.get("contact_email"),
            ]
        )

        if not has_contact_info:
            raise forms.ValidationError(
                "At least one contact field (name or email) must be provided."
            )

        return cleaned_data

    def save(self, commit=True, user=None):
        customer = super().save(commit=False)

        # Associate with current user if provided
        if user:
            customer.member = user

        if commit:
            customer.save()

        return customer
