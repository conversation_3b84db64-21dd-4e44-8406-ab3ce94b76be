# Generated by Django 5.1 on 2025-07-16 14:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('member', '0007_remove_member_manager'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='memberteam',
            name='role',
            field=models.Char<PERSON>ield(choices=[('admin', 'Admin'), ('contributor', 'Contributor'), ('manager', 'Manager'), ('member', 'Member'), ('observer', 'Observer'), ('other', 'Other'), ('salesrep', 'Sales Rep'), ('lead', 'Team Lead')], default='member', max_length=20),
        ),
        migrations.AlterField(
            model_name='membertype',
            name='dashboard_layout',
            field=models.JSONField(blank=True, default=dict, help_text='JSON object defining dashboard widget layout', null=True, verbose_name='Dashboard Layout'),
        ),
        migrations.AlterField(
            model_name='membertype',
            name='theme_settings',
            field=models.JSONField(blank=True, default=dict, help_text='JSON object for UI theme customization', null=True, verbose_name='Theme Settings'),
        ),
    ]
