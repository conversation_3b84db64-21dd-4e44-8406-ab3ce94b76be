# Generated by Django 5.1 on 2025-07-15 19:40

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('member', '0005_memberhierarchy'),
    ]

    operations = [
        migrations.CreateModel(
            name='Team',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('team_type', models.CharField(choices=[('distributor', 'Distributor'), ('custom', 'Custom'), ('sales', 'Sales'), ('support', 'Support'), ('development', 'Development'), ('marketing', 'Marketing'), ('operations', 'Operations'), ('finance', 'Finance'), ('hr', 'Human Resources'), ('legal', 'Legal'), ('other', 'Other')], default='custom', max_length=20)),
                ('description', models.TextField(blank=True, help_text='Team description and purpose')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this team is currently active')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('team_lead', models.ForeignKey(blank=True, help_text='Team leader', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='led_teams', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Team',
                'verbose_name_plural': 'Teams',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='MemberTeam',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('member', 'Member'), ('lead', 'Team Lead'), ('manager', 'Manager'), ('contributor', 'Contributor'), ('observer', 'Observer')], default='member', max_length=20)),
                ('is_primary', models.BooleanField(default=False, help_text='Mark as primary team for this member')),
                ('start_date', models.DateField(blank=True, help_text='When this membership started', null=True)),
                ('end_date', models.DateField(blank=True, help_text='When this membership ended (if applicable)', null=True)),
                ('notes', models.TextField(blank=True, help_text='Additional notes about this team membership')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='team_memberships', to=settings.AUTH_USER_MODEL)),
                ('team', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='member_teams', to='member.team')),
            ],
            options={
                'verbose_name': 'Member Team',
                'verbose_name_plural': 'Member Teams',
                'ordering': ['-is_primary', 'role', 'created'],
                'unique_together': {('member', 'team')},
            },
        ),
    ]
