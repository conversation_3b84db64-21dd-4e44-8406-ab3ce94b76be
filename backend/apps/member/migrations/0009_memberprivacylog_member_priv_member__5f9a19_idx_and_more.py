# Generated by Django 5.1 on 2025-07-23 14:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('member', '0008_alter_memberteam_role_and_more'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='memberprivacylog',
            index=models.Index(fields=['member', '-accepted_at'], name='member_priv_member__5f9a19_idx'),
        ),
        migrations.AddIndex(
            model_name='memberprivacylog',
            index=models.Index(fields=['privacy', '-accepted_at'], name='member_priv_privacy_64de30_idx'),
        ),
        migrations.AddIndex(
            model_name='membertermslog',
            index=models.Index(fields=['member', '-accepted_at'], name='member_term_member__91c509_idx'),
        ),
        migrations.AddIndex(
            model_name='membertermslog',
            index=models.Index(fields=['terms', '-accepted_at'], name='member_term_terms_i_5a9a63_idx'),
        ),
    ]
