# Generated by Django 5.1 on 2025-07-11 19:00

import apps.member.models
import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import multiselectfield.db.fields
import re
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Classes',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tag', models.CharField(help_text='5 char max', max_length=5, verbose_name='Tag')),
                ('classname', models.Char<PERSON>ield(help_text='5 char max', max_length=35, verbose_name='Class')),
                ('description', models.Char<PERSON><PERSON>(help_text='5 char max', max_length=255, verbose_name='Description')),
                ('_req', models.CharField(blank=True, default='', max_length=200, null=True, validators=[django.core.validators.RegexValidator(re.compile('^\\d+(?:,\\d+)*\\Z'), code='invalid', message='Enter only digits separated by commas.')])),
                ('_perms', models.CharField(blank=True, default='', help_text='LOGIN,SUBMIT,VIEW,REVIEW,APPROVE,DISTRIBUTOR,REGIONAL,NATIONAL,SHELL', max_length=255, verbose_name='Permissions')),
                ('signup', models.BooleanField(verbose_name='Can Signup')),
                ('order', models.IntegerField(verbose_name='Signup Order')),
                ('allow_user_approvals', models.BooleanField(verbose_name='Can Approve/Deny New Users')),
                ('allow_messages', models.BooleanField(verbose_name='Allowed to Send Messages')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Member Class',
                'db_table': 'member_class',
                'permissions': (('member_class.manager', 'Member Class Manager'),),
            },
        ),
        migrations.CreateModel(
            name='Privacy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=50, verbose_name='Title')),
                ('current', models.BooleanField(default=0)),
                ('body_text', models.TextField(blank=True, null=True)),
                ('body_html', models.TextField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Privacy',
                'db_table': 'privacy',
                'permissions': (('privacy.manager', 'Privacy Manager'),),
            },
        ),
        migrations.CreateModel(
            name='Region',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=150, null=True, verbose_name='Title')),
                ('can_signup', models.BooleanField(default=0, verbose_name='Can Signup')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Region',
                'db_table': 'region',
                'permissions': (('region.manager', 'Region Manager'),),
            },
        ),
        migrations.CreateModel(
            name='Terms',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=50, verbose_name='Title')),
                ('current', models.BooleanField(default=0)),
                ('body_text', models.TextField(blank=True, null=True)),
                ('body_html', models.TextField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Terms',
                'db_table': 'terms',
                'permissions': (('terms.manager', 'Terms Manager'),),
            },
        ),
        migrations.CreateModel(
            name='Member',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('status', models.PositiveIntegerField(default=1, verbose_name='Status')),
                ('tier', models.PositiveIntegerField(blank=True, null=True, verbose_name='Member Tier')),
                ('work_address1', models.CharField(blank=True, max_length=150, null=True, verbose_name='Work Address 1')),
                ('work_address2', models.CharField(blank=True, max_length=150, null=True, verbose_name='Work Address 2')),
                ('work_city', models.CharField(blank=True, max_length=50, null=True, verbose_name='Work City')),
                ('work_state', models.CharField(blank=True, max_length=2, null=True, verbose_name='Work State/Province')),
                ('work_postal', models.CharField(blank=True, max_length=10, null=True, verbose_name='Work Postal')),
                ('work_country', models.CharField(blank=True, default='usa', max_length=20, null=True, verbose_name='Work Country')),
                ('home_address1', models.CharField(blank=True, max_length=150, null=True, verbose_name='Home Address 1')),
                ('home_address2', models.CharField(blank=True, max_length=150, null=True, verbose_name='Home Address 2')),
                ('home_city', models.CharField(blank=True, max_length=50, null=True, verbose_name='Home City')),
                ('home_state', models.CharField(blank=True, max_length=2, null=True, verbose_name='Home State/Province')),
                ('home_postal', models.CharField(blank=True, max_length=10, null=True, verbose_name='Home Postal')),
                ('home_country', models.CharField(blank=True, default='usa', max_length=20, null=True, verbose_name='Home Country')),
                ('contact_at_work', models.BooleanField(default=0, verbose_name='Contact At Work')),
                ('phone_work', models.CharField(blank=True, max_length=15, null=True, verbose_name='Work Phone')),
                ('phone_home', models.CharField(blank=True, max_length=15, null=True, verbose_name='Home Phone')),
                ('phone_cell', models.CharField(blank=True, max_length=15, null=True, verbose_name='Cell Phone')),
                ('approved_date', models.DateTimeField(blank=True, editable=False, null=True)),
                ('denied_date', models.DateTimeField(blank=True, editable=False, null=True)),
                ('lang', models.PositiveIntegerField(blank=True, default=1, null=True, verbose_name='Language')),
                ('two_factor_auth_method', models.PositiveIntegerField(blank=True, default=1, null=True, verbose_name='2 Factor Authentication')),
                ('feature_flags', multiselectfield.db.fields.MultiSelectField(blank=True, choices=[], max_length=200, null=True)),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='members_approved', to=settings.AUTH_USER_MODEL)),
                ('denied_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='members_denied', to=settings.AUTH_USER_MODEL)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='member_manager', to=settings.AUTH_USER_MODEL)),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
                ('cls', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='member.classes')),
                ('region', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='member.region')),
            ],
            options={
                'verbose_name_plural': 'Member',
                'db_table': 'member',
                'ordering': ('email',),
                'permissions': (('member.manager', 'Member Manager'),),
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='EmailTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Template Name')),
                ('template_type', models.CharField(choices=[('email', 'Email Template'), ('notification', 'Notification Template'), ('message', 'Message Template'), ('sms', 'SMS Template')], default='email', max_length=20, verbose_name='Type')),
                ('category', models.CharField(choices=[('welcome', 'Welcome'), ('password_reset', 'Password Reset'), ('notification', 'Notification'), ('marketing', 'Marketing'), ('system', 'System'), ('custom', 'Custom')], default='custom', max_length=20, verbose_name='Category')),
                ('subject', models.CharField(blank=True, max_length=200, null=True, verbose_name='Subject')),
                ('title', models.CharField(blank=True, max_length=200, null=True, verbose_name='Title')),
                ('content_text', models.TextField(blank=True, null=True, verbose_name='Plain Text Content')),
                ('content_html', models.TextField(blank=True, null=True, verbose_name='HTML Content')),
                ('variables', models.JSONField(blank=True, default=dict, help_text='Available variables for this template', verbose_name='Template Variables')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('is_system', models.BooleanField(default=False, help_text='System templates cannot be deleted', verbose_name='System Template')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Email Templates',
                'db_table': 'email_template',
                'ordering': ['name'],
                'permissions': (('template.manager', 'Template Manager'), ('template.create', 'Create Templates'), ('template.edit', 'Edit Templates')),
            },
        ),
        migrations.CreateModel(
            name='MemberAuthToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('auth_type', models.CharField(max_length=10, verbose_name='Auth Type')),
                ('token', models.CharField(blank=True, editable=False, max_length=255, null=True, verbose_name='Token')),
                ('issued', models.DateTimeField(blank=True, editable=False, null=True, verbose_name='Issued At')),
                ('expires', models.DateTimeField(blank=True, editable=False, null=True, verbose_name='Expires At')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='auth_tokens', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Member Auth Tokens',
                'db_table': 'member_auth_token',
                'ordering': ['-issued'],
            },
        ),
        migrations.CreateModel(
            name='MemberW9Upload',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to=apps.member.models.w9_upload_path)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='w9_uploads', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'W9 Uploads',
                'db_table': 'member_w9_upload',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='PasswordReset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('link', models.CharField(max_length=150)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Member Password Reset',
                'db_table': 'member_password_reset',
                'permissions': (('member_password_reset.manager', 'Member Password Reset Manager'),),
            },
        ),
        migrations.CreateModel(
            name='Communication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('communication_type', models.CharField(choices=[('email', 'Email'), ('notification', 'Notification'), ('message', 'Message'), ('sms', 'SMS'), ('push', 'Push Notification')], default='email', max_length=20, verbose_name='Type')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('delivered', 'Delivered'), ('read', 'Read'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20, verbose_name='Status')),
                ('priority', models.CharField(choices=[('low', 'Low'), ('normal', 'Normal'), ('high', 'High'), ('urgent', 'Urgent')], default='normal', max_length=10, verbose_name='Priority')),
                ('to_email', models.EmailField(blank=True, max_length=200, null=True, verbose_name='To Email')),
                ('to_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='To Phone')),
                ('from_email', models.EmailField(blank=True, max_length=200, null=True, verbose_name='From Email')),
                ('from_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='From Name')),
                ('subject', models.CharField(blank=True, max_length=200, null=True, verbose_name='Subject')),
                ('title', models.CharField(blank=True, max_length=200, null=True, verbose_name='Title')),
                ('content', models.TextField(verbose_name='Content')),
                ('content_html', models.TextField(blank=True, null=True, verbose_name='HTML Content')),
                ('template_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='Template')),
                ('category', models.CharField(blank=True, max_length=50, null=True, verbose_name='Category')),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags', max_length=255, null=True, verbose_name='Tags')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='Sent At')),
                ('delivered_at', models.DateTimeField(blank=True, null=True, verbose_name='Delivered At')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='Read At')),
                ('failed_at', models.DateTimeField(blank=True, null=True, verbose_name='Failed At')),
                ('failure_reason', models.TextField(blank=True, null=True, verbose_name='Failure Reason')),
                ('retry_count', models.PositiveIntegerField(default=0, verbose_name='Retry Count')),
                ('max_retries', models.PositiveIntegerField(default=3, verbose_name='Max Retries')),
                ('scheduled_for', models.DateTimeField(blank=True, null=True, verbose_name='Scheduled For')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('from_member', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sent_communications', to=settings.AUTH_USER_MODEL)),
                ('to_member', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='received_communications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Communications',
                'db_table': 'communication',
                'ordering': ['-created'],
                'permissions': (('communication.manager', 'Communication Manager'), ('communication.view_all', 'View All Communications'), ('communication.send', 'Send Communications')),
                'indexes': [models.Index(fields=['communication_type', 'status'], name='communicati_communi_db4004_idx'), models.Index(fields=['to_member', 'status'], name='communicati_to_memb_e36a67_idx'), models.Index(fields=['scheduled_for', 'status'], name='communicati_schedul_dd8725_idx'), models.Index(fields=['created'], name='communicati_created_981cee_idx')],
            },
        ),
        migrations.CreateModel(
            name='MemberPrivacyLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('accepted_at', models.DateTimeField(auto_now_add=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='privacy_logs', to=settings.AUTH_USER_MODEL)),
                ('privacy', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='member_logs', to='member.privacy')),
            ],
            options={
                'verbose_name_plural': 'Member Privacy Log',
                'db_table': 'member_privacy_log',
                'unique_together': {('member', 'privacy')},
            },
        ),
        migrations.CreateModel(
            name='TemplateBuilder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('component_type', models.CharField(choices=[('header', 'Header'), ('text', 'Text Block'), ('button', 'Button'), ('image', 'Image'), ('divider', 'Divider'), ('footer', 'Footer'), ('custom', 'Custom HTML')], max_length=20, verbose_name='Component Type')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='Order')),
                ('config', models.JSONField(default=dict, verbose_name='Configuration')),
                ('content', models.TextField(blank=True, null=True, verbose_name='Content')),
                ('css_classes', models.CharField(blank=True, max_length=255, null=True, verbose_name='CSS Classes')),
                ('inline_styles', models.TextField(blank=True, null=True, verbose_name='Inline Styles')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='builder_components', to='member.emailtemplate')),
            ],
            options={
                'verbose_name_plural': 'Template Builder Components',
                'db_table': 'template_builder',
                'ordering': ['template', 'order'],
                'unique_together': {('template', 'order')},
            },
        ),
        migrations.CreateModel(
            name='MemberTermsLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('accepted_at', models.DateTimeField(auto_now_add=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='terms_logs', to=settings.AUTH_USER_MODEL)),
                ('terms', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='member_logs', to='member.terms')),
            ],
            options={
                'verbose_name_plural': 'Member Terms Log',
                'db_table': 'member_terms_log',
                'unique_together': {('member', 'terms')},
            },
        ),
    ]
