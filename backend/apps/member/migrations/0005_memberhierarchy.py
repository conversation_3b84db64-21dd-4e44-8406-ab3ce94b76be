# Generated by Django 5.1 on 2025-07-15 17:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('member', '0004_remove_member_cls_delete_classes'),
    ]

    operations = [
        migrations.CreateModel(
            name='MemberHierarchy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('relationship_type', models.CharField(choices=[('direct_manager', 'Direct Manager'), ('indirect_manager', 'Indirect Manager'), ('mentor', 'Mentor'), ('supervisor', 'Supervisor'), ('team_lead', 'Team Lead'), ('project_manager', 'Project Manager')], default='direct_manager', max_length=20)),
                ('is_primary', models.BooleanField(default=False, help_text='Mark as primary manager for this member')),
                ('start_date', models.DateField(blank=True, help_text='When this relationship started', null=True)),
                ('end_date', models.DateField(blank=True, help_text='When this relationship ended (if applicable)', null=True)),
                ('notes', models.TextField(blank=True, help_text='Additional notes about this relationship')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('manager', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subordinates', to=settings.AUTH_USER_MODEL)),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='managers', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Member Hierarchy',
                'verbose_name_plural': 'Member Hierarchies',
                'ordering': ['-is_primary', 'relationship_type', 'created'],
                'unique_together': {('member', 'manager', 'relationship_type')},
            },
        ),
    ]
