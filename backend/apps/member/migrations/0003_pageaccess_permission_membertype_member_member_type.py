# Generated by Django 5.1 on 2025-07-14 21:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('member', '0002_alter_member_phone_cell_alter_member_phone_home_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PageAccess',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('slug', models.SlugField(unique=True, verbose_name='Slug')),
                ('url_pattern', models.CharField(help_text='URL pattern to match', max_length=200, verbose_name='URL Pattern')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('category', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=50, verbose_name='Category')),
                ('requires_permission', models.Char<PERSON>ield(blank=True, max_length=50, verbose_name='Required Permission')),
                ('is_active', models.<PERSON>olean<PERSON>ield(default=True, verbose_name='Active')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Page Access Rules',
                'db_table': 'page_access',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Name')),
                ('slug', models.SlugField(unique=True, verbose_name='Slug')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('category', models.CharField(blank=True, help_text='Group permissions by category', max_length=50, verbose_name='Category')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Permissions',
                'db_table': 'permission',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='MemberType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Name')),
                ('slug', models.SlugField(unique=True, verbose_name='Slug')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('permissions', models.JSONField(default=dict, help_text='JSON object of permissions', verbose_name='Permissions')),
                ('page_access', models.JSONField(default=dict, help_text='JSON object defining accessible pages and features', verbose_name='Page Access')),
                ('navigation', models.JSONField(default=dict, help_text='JSON object defining navigation menu structure', verbose_name='Navigation')),
                ('feature_flags', models.JSONField(default=dict, help_text='JSON object of enabled features', verbose_name='Feature Flags')),
                ('dashboard_layout', models.JSONField(default=dict, help_text='JSON object defining dashboard widget layout', verbose_name='Dashboard Layout')),
                ('theme_settings', models.JSONField(default=dict, help_text='JSON object for UI theme customization', verbose_name='Theme Settings')),
                ('can_signup', models.BooleanField(default=False, verbose_name='Can Signup')),
                ('requires_approval', models.BooleanField(default=True, verbose_name='Requires Approval')),
                ('auto_approve', models.BooleanField(default=False, verbose_name='Auto Approve')),
                ('max_subordinates', models.PositiveIntegerField(default=0, help_text='0 = unlimited', verbose_name='Max Subordinates')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='Display Order')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('can_manage_types', models.ManyToManyField(blank=True, help_text='Member types this type can manage', to='member.membertype')),
            ],
            options={
                'verbose_name_plural': 'Member Types',
                'db_table': 'member_type',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.AddField(
            model_name='member',
            name='member_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='members', to='member.membertype'),
        ),
    ]
