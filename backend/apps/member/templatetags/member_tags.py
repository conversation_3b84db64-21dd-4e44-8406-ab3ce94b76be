from django import template
from django.template.defaulttags import register


@register.filter
def has_permission(user, permission):
    """Template filter to check permissions"""
    return user.has_permission(permission)


@register.filter
def has_feature(user, feature):
    """Template filter to check features"""
    return user.has_feature(feature)


@register.filter
def can_access_page(user, page_slug):
    """Template filter to check page access"""
    return user.can_access_page(page_slug)
