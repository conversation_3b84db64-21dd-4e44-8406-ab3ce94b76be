from functools import wraps

from django.contrib import messages
from django.shortcuts import redirect


def require_permission(permission):
    """
    Decorator to require a specific permission for a view
    Usage: @require_permission('member.create')
    """

    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if request.user.is_authenticated and request.user.has_permission(
                permission
            ):
                return view_func(request, *args, **kwargs)
            else:
                messages.error(
                    request, f"You do not have permission to perform this action."
                )
                return redirect("member:member_list")

        return _wrapped_view

    return decorator


def require_page_access(page_slug):
    """
    Decorator to require page access for a view
    Usage: @require_page_access('member.create')
    """

    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if request.user.is_authenticated and request.user.can_access_page(
                page_slug
            ):
                return view_func(request, *args, **kwargs)
            else:
                messages.error(request, f"You do not have access to this page.")
                return redirect("member:member_list")

        return _wrapped_view

    return decorator


def require_feature(feature):
    """
    Decorator to require a specific feature for a view
    Usage: @require_feature('advanced_search')
    """

    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if request.user.is_authenticated and request.user.has_feature(feature):
                return view_func(request, *args, **kwargs)
            else:
                messages.error(
                    request, f"This feature is not available for your account type."
                )
                return redirect("member:member_list")

        return _wrapped_view

    return decorator
