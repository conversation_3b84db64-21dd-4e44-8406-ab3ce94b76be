from django.core.management.base import BaseCommand

from apps.member.models import MemberType, PageAccess, Permission


class Command(BaseCommand):
    help = (
        "Set up sample member types with comprehensive permissions and access control"
    )

    def handle(self, *args, **options):
        self.stdout.write("Setting up member types...")

        # Create permissions
        permissions = self._create_permissions()

        # Create page access rules
        page_access_rules = self._create_page_access_rules()

        # Create member types
        member_types = self._create_member_types(permissions, page_access_rules)

        self.stdout.write(
            self.style.SUCCESS(f"Successfully created {len(member_types)} member types")
        )

    def _create_permissions(self):
        """Create sample permissions"""
        permissions_data = [
            # Member Management
            {
                "name": "member.view",
                "slug": "member-view",
                "category": "Member Management",
                "description": "View member information",
            },
            {
                "name": "member.create",
                "slug": "member-create",
                "category": "Member Management",
                "description": "Create new members",
            },
            {
                "name": "member.edit",
                "slug": "member-edit",
                "category": "Member Management",
                "description": "Edit member information",
            },
            {
                "name": "member.delete",
                "slug": "member-delete",
                "category": "Member Management",
                "description": "Delete members",
            },
            {
                "name": "member.approve",
                "slug": "member-approve",
                "category": "Member Management",
                "description": "Approve new members",
            },
            # Communication
            {
                "name": "communication.send",
                "slug": "communication-send",
                "category": "Communication",
                "description": "Send messages",
            },
            {
                "name": "communication.view_all",
                "slug": "communication-view-all",
                "category": "Communication",
                "description": "View all communications",
            },
            {
                "name": "communication.manage",
                "slug": "communication-manage",
                "category": "Communication",
                "description": "Manage communications",
            },
            # Reports & Analytics
            {
                "name": "reports.view",
                "slug": "reports-view",
                "category": "Reports",
                "description": "View reports",
            },
            {
                "name": "reports.create",
                "slug": "reports-create",
                "category": "Reports",
                "description": "Create reports",
            },
            {
                "name": "analytics.view",
                "slug": "analytics-view",
                "category": "Analytics",
                "description": "View analytics",
            },
            # System Administration
            {
                "name": "admin.access",
                "slug": "admin-access",
                "category": "Administration",
                "description": "Access admin interface",
            },
            {
                "name": "settings.manage",
                "slug": "settings-manage",
                "category": "Administration",
                "description": "Manage system settings",
            },
            {
                "name": "member_types.manage",
                "slug": "member-types-manage",
                "category": "Administration",
                "description": "Manage member types",
            },
        ]

        permissions = {}
        for data in permissions_data:
            perm, created = Permission.objects.get_or_create(
                slug=data["slug"], defaults=data
            )
            permissions[data["slug"]] = perm
            if created:
                self.stdout.write(f"Created permission: {perm.name}")

        return permissions

    def _create_page_access_rules(self):
        """Create page access rules"""
        page_access_data = [
            {
                "name": "Member List",
                "slug": "member-list",
                "url_pattern": "/member/list/",
                "category": "Member Management",
                "requires_permission": "member.view",
            },
            {
                "name": "Create Member",
                "slug": "member-create",
                "url_pattern": "/member/create/",
                "category": "Member Management",
                "requires_permission": "member.create",
            },
            {
                "name": "Member Detail",
                "slug": "member-detail",
                "url_pattern": "/member/detail/*/",
                "category": "Member Management",
                "requires_permission": "member.view",
            },
            {
                "name": "Edit Member",
                "slug": "member-update",
                "url_pattern": "/member/update/*/",
                "category": "Member Management",
                "requires_permission": "member.edit",
            },
            {
                "name": "Admin Interface",
                "slug": "admin",
                "url_pattern": "/admin/",
                "category": "Administration",
                "requires_permission": "admin.access",
            },
            {
                "name": "Communication Center",
                "slug": "communication",
                "url_pattern": "/communication/",
                "category": "Communication",
                "requires_permission": "communication.send",
            },
            {
                "name": "Reports Dashboard",
                "slug": "reports",
                "url_pattern": "/reports/",
                "category": "Reports",
                "requires_permission": "reports.view",
            },
            {
                "name": "Analytics",
                "slug": "analytics",
                "url_pattern": "/analytics/",
                "category": "Analytics",
                "requires_permission": "analytics.view",
            },
        ]

        page_access = {}
        for data in page_access_data:
            rule, created = PageAccess.objects.get_or_create(
                slug=data["slug"], defaults=data
            )
            page_access[data["slug"]] = rule
            if created:
                self.stdout.write(f"Created page access rule: {rule.name}")

        return page_access

    def _create_member_types(self, permissions, page_access_rules):
        """Create member types with comprehensive configurations"""

        # Administrator Member Type
        admin_permissions = {
            "permissions": [
                "member.view",
                "member.create",
                "member.edit",
                "member.delete",
                "member.approve",
                "communication.send",
                "communication.view_all",
                "communication.manage",
                "reports.view",
                "reports.create",
                "analytics.view",
                "admin.access",
                "settings.manage",
                "member_types.manage",
            ]
        }

        admin_page_access = {
            "pages": {
                "member-list": {"access": True, "permissions": ["member.view"]},
                "member-create": {"access": True, "permissions": ["member.create"]},
                "member-detail": {"access": True, "permissions": ["member.view"]},
                "member-update": {"access": True, "permissions": ["member.edit"]},
                "admin": {"access": True, "permissions": ["admin.access"]},
                "communication": {
                    "access": True,
                    "permissions": ["communication.send"],
                },
                "reports": {"access": True, "permissions": ["reports.view"]},
                "analytics": {"access": True, "permissions": ["analytics.view"]},
            }
        }

        admin_navigation = {
            "menu": [
                {"label": "Dashboard", "url": "/", "icon": "home", "order": 1},
                {
                    "label": "Member Management",
                    "url": "/member/list/",
                    "icon": "users",
                    "order": 2,
                    "children": [
                        {"label": "All Members", "url": "/member/list/", "order": 1},
                        {
                            "label": "Create Member",
                            "url": "/member/create/",
                            "order": 2,
                        },
                    ],
                },
                {
                    "label": "Communication",
                    "url": "/communication/",
                    "icon": "mail",
                    "order": 3,
                },
                {
                    "label": "Reports",
                    "url": "/reports/",
                    "icon": "chart-bar",
                    "order": 4,
                },
                {
                    "label": "Analytics",
                    "url": "/analytics/",
                    "icon": "chart-pie",
                    "order": 5,
                },
                {
                    "label": "Administration",
                    "url": "/admin/",
                    "icon": "cog",
                    "order": 6,
                },
            ]
        }

        admin_features = {
            "features": [
                "advanced_search",
                "bulk_operations",
                "export_data",
                "import_data",
                "advanced_analytics",
                "custom_reports",
                "system_monitoring",
                "user_management",
                "role_management",
                "audit_logs",
            ]
        }

        admin_dashboard = {
            "layout": [
                {"widget": "member_stats", "position": "top-left", "size": "medium"},
                {
                    "widget": "recent_activity",
                    "position": "top-right",
                    "size": "medium",
                },
                {
                    "widget": "communication_summary",
                    "position": "bottom-left",
                    "size": "large",
                },
                {
                    "widget": "system_status",
                    "position": "bottom-right",
                    "size": "small",
                },
            ]
        }

        admin_theme = {
            "primary_color": "#1f2937",
            "secondary_color": "#374151",
            "accent_color": "#3b82f6",
            "sidebar_collapsed": False,
            "dark_mode": False,
        }

        # Manager Member Type
        manager_permissions = {
            "permissions": [
                "member.view",
                "member.create",
                "member.edit",
                "member.approve",
                "communication.send",
                "communication.view_all",
                "reports.view",
            ]
        }

        manager_page_access = {
            "pages": {
                "member-list": {"access": True, "permissions": ["member.view"]},
                "member-create": {"access": True, "permissions": ["member.create"]},
                "member-detail": {"access": True, "permissions": ["member.view"]},
                "member-update": {"access": True, "permissions": ["member.edit"]},
                "communication": {
                    "access": True,
                    "permissions": ["communication.send"],
                },
                "reports": {"access": True, "permissions": ["reports.view"]},
            }
        }

        manager_navigation = {
            "menu": [
                {"label": "Dashboard", "url": "/", "icon": "home", "order": 1},
                {
                    "label": "Member Management",
                    "url": "/member/list/",
                    "icon": "users",
                    "order": 2,
                    "children": [
                        {"label": "All Members", "url": "/member/list/", "order": 1},
                        {
                            "label": "Create Member",
                            "url": "/member/create/",
                            "order": 2,
                        },
                    ],
                },
                {
                    "label": "Communication",
                    "url": "/communication/",
                    "icon": "mail",
                    "order": 3,
                },
                {
                    "label": "Reports",
                    "url": "/reports/",
                    "icon": "chart-bar",
                    "order": 4,
                },
            ]
        }

        manager_features = {
            "features": [
                "advanced_search",
                "bulk_operations",
                "export_data",
                "basic_analytics",
                "team_reports",
            ]
        }

        # Regular Member Type
        regular_permissions = {"permissions": ["member.view", "communication.send"]}

        regular_page_access = {
            "pages": {
                "member-list": {"access": True, "permissions": ["member.view"]},
                "member-detail": {"access": True, "permissions": ["member.view"]},
                "communication": {
                    "access": True,
                    "permissions": ["communication.send"],
                },
            }
        }

        regular_navigation = {
            "menu": [
                {"label": "Dashboard", "url": "/", "icon": "home", "order": 1},
                {
                    "label": "Members",
                    "url": "/member/list/",
                    "icon": "users",
                    "order": 2,
                },
                {
                    "label": "Communication",
                    "url": "/communication/",
                    "icon": "mail",
                    "order": 3,
                },
            ]
        }

        regular_features = {"features": ["basic_search", "personal_dashboard"]}

        # Guest Member Type
        guest_permissions = {"permissions": ["member.view"]}

        guest_page_access = {
            "pages": {
                "member-list": {"access": True, "permissions": ["member.view"]},
                "member-detail": {"access": True, "permissions": ["member.view"]},
            }
        }

        guest_navigation = {
            "menu": [
                {"label": "Dashboard", "url": "/", "icon": "home", "order": 1},
                {
                    "label": "Members",
                    "url": "/member/list/",
                    "icon": "users",
                    "order": 2,
                },
            ]
        }

        guest_features = {"features": ["basic_search"]}

        # Create member types
        member_types_data = [
            {
                "name": "Administrator",
                "slug": "administrator",
                "description": "Full system access with all permissions",
                "permissions": admin_permissions,
                "page_access": admin_page_access,
                "navigation": admin_navigation,
                "feature_flags": admin_features,
                "dashboard_layout": admin_dashboard,
                "theme_settings": admin_theme,
                "can_signup": False,
                "requires_approval": False,
                "auto_approve": True,
                "max_subordinates": 0,
                "order": 1,
            },
            {
                "name": "Manager",
                "slug": "manager",
                "description": "Team management with limited administrative access",
                "permissions": manager_permissions,
                "page_access": manager_page_access,
                "navigation": manager_navigation,
                "feature_flags": manager_features,
                "can_signup": True,
                "requires_approval": True,
                "auto_approve": False,
                "max_subordinates": 50,
                "order": 2,
            },
            {
                "name": "Regular Member",
                "slug": "regular",
                "description": "Standard member with basic access",
                "permissions": regular_permissions,
                "page_access": regular_page_access,
                "navigation": regular_navigation,
                "feature_flags": regular_features,
                "can_signup": True,
                "requires_approval": True,
                "auto_approve": False,
                "max_subordinates": 0,
                "order": 3,
            },
            {
                "name": "Guest",
                "slug": "guest",
                "description": "Limited access for guest users",
                "permissions": guest_permissions,
                "page_access": guest_page_access,
                "navigation": guest_navigation,
                "feature_flags": guest_features,
                "can_signup": True,
                "requires_approval": True,
                "auto_approve": False,
                "max_subordinates": 0,
                "order": 4,
            },
        ]

        member_types = []
        for data in member_types_data:
            member_type, created = MemberType.objects.get_or_create(
                slug=data["slug"], defaults=data
            )
            member_types.append(member_type)
            if created:
                self.stdout.write(f"Created member type: {member_type.name}")

        return member_types
