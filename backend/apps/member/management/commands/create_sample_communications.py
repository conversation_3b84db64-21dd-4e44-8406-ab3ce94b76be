import random
from datetime import <PERSON><PERSON><PERSON>

from django.core.management.base import BaseCommand
from django.db.models import Count
from django.utils import timezone

from apps.member.models import Communication, Member


class Command(BaseCommand):
    help = "Create sample communications data for testing"

    def handle(self, *args, **options):
        # Get members 1-7 (or create them if they don't exist)
        members = []
        for i in range(1, 8):
            member, created = Member.objects.get_or_create(
                id=i,
                defaults={
                    "username": f"member{i}",
                    "email": f"member{i}@example.com",
                    "first_name": f"Member{i}",
                    "last_name": "Test",
                },
            )
            members.append(member)
            if created:
                self.stdout.write(f"Created member: {member.username}")

        # Sample communication data
        communication_data = [
            # Email communications
            {
                "communication_type": "email",
                "status": "sent",
                "priority": "normal",
                "subject": "Welcome to XD Incentives",
                "title": "Welcome Email",
                "content": "Welcome to XD Incentives! We're excited to have you on board.",
                "content_html": "<h2>Welcome to XD Incentives!</h2><p>We're excited to have you on board.</p>",
                "category": "welcome",
                "tags": "welcome,onboarding",
            },
            {
                "communication_type": "email",
                "status": "delivered",
                "priority": "high",
                "subject": "Important Update - Terms and Conditions",
                "title": "Terms Update",
                "content": "Please review and accept our updated terms and conditions.",
                "content_html": "<h2>Terms Update</h2><p>Please review and accept our updated terms and conditions.</p>",
                "category": "legal",
                "tags": "terms,legal,important",
            },
            {
                "communication_type": "email",
                "status": "read",
                "priority": "normal",
                "subject": "Monthly Newsletter - January 2025",
                "title": "Monthly Newsletter",
                "content": "Check out our latest updates and news for January 2025.",
                "content_html": "<h2>Monthly Newsletter</h2><p>Check out our latest updates and news for January 2025.</p>",
                "category": "newsletter",
                "tags": "newsletter,monthly",
            },
            # Notification communications
            {
                "communication_type": "notification",
                "status": "sent",
                "priority": "normal",
                "title": "New Team Assignment",
                "content": "You have been assigned to a new team.",
                "category": "team",
                "tags": "team,assignment",
            },
            {
                "communication_type": "notification",
                "status": "delivered",
                "priority": "high",
                "title": "System Maintenance",
                "content": "Scheduled maintenance will occur tonight at 2 AM.",
                "category": "system",
                "tags": "maintenance,system",
            },
            {
                "communication_type": "notification",
                "status": "read",
                "priority": "low",
                "title": "Profile Update",
                "content": "Your profile has been successfully updated.",
                "category": "profile",
                "tags": "profile,update",
            },
            # Message communications
            {
                "communication_type": "message",
                "status": "sent",
                "priority": "normal",
                "title": "Direct Message",
                "content": "Hello! How are you doing today?",
                "category": "personal",
                "tags": "personal,message",
            },
            {
                "communication_type": "message",
                "status": "delivered",
                "priority": "high",
                "title": "Urgent Message",
                "content": "Please contact support immediately regarding your account.",
                "category": "support",
                "tags": "urgent,support",
            },
            {
                "communication_type": "message",
                "status": "read",
                "priority": "normal",
                "title": "Team Message",
                "content": "Great work on the recent project!",
                "category": "team",
                "tags": "team,recognition",
            },
            # SMS communications
            {
                "communication_type": "sms",
                "status": "sent",
                "priority": "high",
                "title": "Security Alert",
                "content": "Your account was accessed from a new device.",
                "category": "security",
                "tags": "security,alert",
            },
            {
                "communication_type": "sms",
                "status": "delivered",
                "priority": "normal",
                "title": "Verification Code",
                "content": "Your verification code is: 123456",
                "category": "verification",
                "tags": "verification,code",
            },
            {
                "communication_type": "sms",
                "status": "failed",
                "priority": "urgent",
                "title": "Emergency Contact",
                "content": "Please call us immediately at **************.",
                "category": "emergency",
                "tags": "emergency,urgent",
            },
            # Push notification communications
            {
                "communication_type": "push",
                "status": "sent",
                "priority": "normal",
                "title": "New Feature Available",
                "content": "Check out our new dashboard features!",
                "category": "feature",
                "tags": "feature,update",
            },
            {
                "communication_type": "push",
                "status": "delivered",
                "priority": "high",
                "title": "Meeting Reminder",
                "content": "You have a meeting in 15 minutes.",
                "category": "reminder",
                "tags": "meeting,reminder",
            },
            {
                "communication_type": "push",
                "status": "read",
                "priority": "low",
                "title": "Daily Summary",
                "content": "Here's your daily activity summary.",
                "category": "summary",
                "tags": "daily,summary",
            },
        ]

        created_count = 0
        for member in members:
            # Create 2-3 communications of each type for each member
            for comm_data in communication_data:
                # Randomize some fields for variety
                status_choices = ["pending", "sent", "delivered", "read", "failed"]
                priority_choices = ["low", "normal", "high", "urgent"]

                # Create communication
                communication = Communication.objects.create(
                    communication_type=comm_data["communication_type"],
                    status=random.choice(status_choices),
                    priority=random.choice(priority_choices),
                    to_member=member,
                    from_member=random.choice(members),  # Random sender
                    subject=comm_data.get("subject", ""),
                    title=comm_data["title"],
                    content=comm_data["content"],
                    content_html=comm_data.get("content_html", ""),
                    category=comm_data["category"],
                    tags=comm_data["tags"],
                    sent_at=timezone.now() - timedelta(days=random.randint(1, 30)),
                    delivered_at=(
                        timezone.now() - timedelta(days=random.randint(0, 29))
                        if random.choice([True, False])
                        else None
                    ),
                    read_at=(
                        timezone.now() - timedelta(days=random.randint(0, 28))
                        if random.choice([True, False])
                        else None
                    ),
                    failed_at=(
                        timezone.now() - timedelta(days=random.randint(1, 30))
                        if random.choice([True, False])
                        else None
                    ),
                    failure_reason=(
                        "Delivery failed" if random.choice([True, False]) else None
                    ),
                    retry_count=random.randint(0, 3),
                    max_retries=3,
                    scheduled_for=(
                        timezone.now() + timedelta(days=random.randint(1, 7))
                        if random.choice([True, False])
                        else None
                    ),
                )
                created_count += 1

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully created {created_count} communications for {len(members)} members"
            )
        )

        # Print statistics
        total_communications = Communication.objects.count()
        status_counts = Communication.objects.values("status").annotate(
            count=Count("id")
        )
        type_counts = Communication.objects.values("communication_type").annotate(
            count=Count("id")
        )

        self.stdout.write(f"\nCommunication Statistics:")
        self.stdout.write(f"Total communications: {total_communications}")
        self.stdout.write(f"Status distribution:")
        for status in status_counts:
            self.stdout.write(f'  {status["status"]}: {status["count"]}')
        self.stdout.write(f"Type distribution:")
        for comm_type in type_counts:
            self.stdout.write(
                f'  {comm_type["communication_type"]}: {comm_type["count"]}'
            )
