from django.core.management.base import BaseCommand

from apps.member.models import Privacy


class Command(BaseCommand):
    help = "Create sample privacy policy data for testing"

    def handle(self, *args, **options):
        # Create sample privacy policies
        privacy_data = [
            {
                "title": "XD Incentives Privacy Policy v1.0",
                "current": True,
                "body_html": """
                <h2>XD Incentives Privacy Policy</h2>
                <p><strong>Effective Date:</strong> January 1, 2025</p>

                <h3>1. Information We Collect</h3>
                <p>We collect information you provide directly to us, such as when you create an account, participate in our programs, or contact us for support.</p>

                <h3>2. How We Use Your Information</h3>
                <p>We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.</p>

                <h3>3. Information Sharing</h3>
                <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>

                <h3>4. Data Security</h3>
                <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>

                <h3>5. Your Rights</h3>
                <p>You have the right to access, correct, or delete your personal information. You may also opt out of certain communications.</p>

                <h3>6. Cookies and Tracking</h3>
                <p>We use cookies and similar technologies to enhance your experience and analyze how our services are used.</p>

                <h3>7. Changes to This Policy</h3>
                <p>We may update this privacy policy from time to time. We will notify you of any material changes.</p>

                <h3>8. Contact Us</h3>
                <p>If you have any questions about this privacy policy, please contact <NAME_EMAIL></p>
                """,
                "body_text": """
XD Incentives Privacy Policy
Effective Date: January 1, 2025

1. Information We Collect
We collect information you provide directly to us, such as when you create an account, participate in our programs, or contact us for support.

2. How We Use Your Information
We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.

3. Information Sharing
We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.

4. Data Security
We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.

5. Your Rights
You have the right to access, correct, or delete your personal information. You may also opt out of certain communications.

6. Cookies and Tracking
We use cookies and similar technologies to enhance your experience and analyze how our services are used.

7. Changes to This Policy
We may update this privacy policy from time to time. We will notify you of any material changes.

8. Contact Us
If you have any questions about this privacy policy, please contact <NAME_EMAIL>
                """,
            },
            {
                "title": "XD Incentives Privacy Policy v1.1",
                "current": False,
                "body_html": """
                <h2>XD Incentives Privacy Policy v1.1</h2>
                <p><strong>Effective Date:</strong> March 15, 2025</p>

                <h3>1. Information We Collect</h3>
                <p>We collect information you provide directly to us, such as when you create an account, participate in our programs, or contact us for support.</p>

                <h3>2. How We Use Your Information</h3>
                <p>We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.</p>

                <h3>3. Information Sharing</h3>
                <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>

                <h3>4. Data Security</h3>
                <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>

                <h3>5. Your Rights</h3>
                <p>You have the right to access, correct, or delete your personal information. You may also opt out of certain communications.</p>

                <h3>6. Cookies and Tracking</h3>
                <p>We use cookies and similar technologies to enhance your experience and analyze how our services are used.</p>

                <h3>7. International Data Transfers</h3>
                <p>Your information may be transferred to and processed in countries other than your own, where privacy laws may be different.</p>

                <h3>8. Changes to This Policy</h3>
                <p>We may update this privacy policy from time to time. We will notify you of any material changes.</p>

                <h3>9. Contact Us</h3>
                <p>If you have any questions about this privacy policy, please contact <NAME_EMAIL></p>
                """,
                "body_text": """
XD Incentives Privacy Policy v1.1
Effective Date: March 15, 2025

1. Information We Collect
We collect information you provide directly to us, such as when you create an account, participate in our programs, or contact us for support.

2. How We Use Your Information
We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.

3. Information Sharing
We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.

4. Data Security
We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.

5. Your Rights
You have the right to access, correct, or delete your personal information. You may also opt out of certain communications.

6. Cookies and Tracking
We use cookies and similar technologies to enhance your experience and analyze how our services are used.

7. International Data Transfers
Your information may be transferred to and processed in countries other than your own, where privacy laws may be different.

8. Changes to This Policy
We may update this privacy policy from time to time. We will notify you of any material changes.

9. Contact Us
If you have any questions about this privacy policy, please contact <NAME_EMAIL>
                """,
            },
        ]

        created_count = 0
        for privacy_info in privacy_data:
            privacy, created = Privacy.objects.get_or_create(
                title=privacy_info["title"],
                defaults={
                    "current": privacy_info["current"],
                    "body_html": privacy_info["body_html"],
                    "body_text": privacy_info["body_text"],
                },
            )
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f"Created privacy policy: {privacy.title}")
                )
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f"Privacy policy already exists: {privacy.title}"
                    )
                )

        # Ensure only one current privacy policy exists
        current_privacy = Privacy.objects.filter(current=True)
        if current_privacy.count() > 1:
            # Keep the most recent one as current
            latest_current = current_privacy.order_by("-created").first()
            current_privacy.exclude(id=latest_current.id).update(current=False)
            self.stdout.write(
                self.style.SUCCESS(
                    f"Set {latest_current.title} as current privacy policy"
                )
            )

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully created {created_count} new privacy policy records"
            )
        )
