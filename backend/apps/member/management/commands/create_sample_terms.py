from django.core.management.base import BaseCommand

from apps.member.models import Terms


class Command(BaseCommand):
    help = "Create sample terms data for testing"

    def handle(self, *args, **options):
        # Create sample terms
        terms_data = [
            {
                "title": "XD Incentives Terms and Conditions v1.0",
                "current": True,
                "body_html": """
                <h2>XD Incentives Terms and Conditions</h2>
                <p><strong>Effective Date:</strong> January 1, 2025</p>

                <h3>1. Acceptance of Terms</h3>
                <p>By accessing and using XD Incentives services, you accept and agree to be bound by the terms and provision of this agreement.</p>

                <h3>2. Description of Service</h3>
                <p>XD Incentives provides member management, incentive programs, and related services to our clients and partners.</p>

                <h3>3. User Responsibilities</h3>
                <p>Users are responsible for maintaining the confidentiality of their account information and for all activities that occur under their account.</p>

                <h3>4. Privacy Policy</h3>
                <p>Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service.</p>

                <h3>5. Termination</h3>
                <p>We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever.</p>

                <h3>6. Changes to Terms</h3>
                <p>We reserve the right to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days notice prior to any new terms taking effect.</p>

                <h3>7. Contact Information</h3>
                <p>If you have any questions about these Terms, please contact <NAME_EMAIL></p>
                """,
                "body_text": """
XD Incentives Terms and Conditions
Effective Date: January 1, 2025

1. Acceptance of Terms
By accessing and using XD Incentives services, you accept and agree to be bound by the terms and provision of this agreement.

2. Description of Service
XD Incentives provides member management, incentive programs, and related services to our clients and partners.

3. User Responsibilities
Users are responsible for maintaining the confidentiality of their account information and for all activities that occur under their account.

4. Privacy Policy
Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service.

5. Termination
We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever.

6. Changes to Terms
We reserve the right to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days notice prior to any new terms taking effect.

7. Contact Information
If you have any questions about these Terms, please contact <NAME_EMAIL>
                """,
            },
            {
                "title": "XD Incentives Terms and Conditions v1.1",
                "current": False,
                "body_html": """
                <h2>XD Incentives Terms and Conditions v1.1</h2>
                <p><strong>Effective Date:</strong> March 15, 2025</p>

                <h3>1. Acceptance of Terms</h3>
                <p>By accessing and using XD Incentives services, you accept and agree to be bound by the terms and provision of this agreement.</p>

                <h3>2. Description of Service</h3>
                <p>XD Incentives provides member management, incentive programs, and related services to our clients and partners.</p>

                <h3>3. User Responsibilities</h3>
                <p>Users are responsible for maintaining the confidentiality of their account information and for all activities that occur under their account.</p>

                <h3>4. Privacy Policy</h3>
                <p>Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service.</p>

                <h3>5. Termination</h3>
                <p>We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever.</p>

                <h3>6. Changes to Terms</h3>
                <p>We reserve the right to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days notice prior to any new terms taking effect.</p>

                <h3>7. Data Protection</h3>
                <p>We are committed to protecting your data and will comply with all applicable data protection laws and regulations.</p>

                <h3>8. Contact Information</h3>
                <p>If you have any questions about these Terms, please contact <NAME_EMAIL></p>
                """,
                "body_text": """
XD Incentives Terms and Conditions v1.1
Effective Date: March 15, 2025

1. Acceptance of Terms
By accessing and using XD Incentives services, you accept and agree to be bound by the terms and provision of this agreement.

2. Description of Service
XD Incentives provides member management, incentive programs, and related services to our clients and partners.

3. User Responsibilities
Users are responsible for maintaining the confidentiality of their account information and for all activities that occur under their account.

4. Privacy Policy
Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service.

5. Termination
We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever.

6. Changes to Terms
We reserve the right to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days notice prior to any new terms taking effect.

7. Data Protection
We are committed to protecting your data and will comply with all applicable data protection laws and regulations.

8. Contact Information
If you have any questions about these Terms, please contact <NAME_EMAIL>
                """,
            },
        ]

        created_count = 0
        for terms_info in terms_data:
            terms, created = Terms.objects.get_or_create(
                title=terms_info["title"],
                defaults={
                    "current": terms_info["current"],
                    "body_html": terms_info["body_html"],
                    "body_text": terms_info["body_text"],
                },
            )
            if created:
                created_count += 1
                self.stdout.write(self.style.SUCCESS(f"Created terms: {terms.title}"))
            else:
                self.stdout.write(
                    self.style.WARNING(f"Terms already exist: {terms.title}")
                )

        # Ensure only one current terms exists
        current_terms = Terms.objects.filter(current=True)
        if current_terms.count() > 1:
            # Keep the most recent one as current
            latest_current = current_terms.order_by("-created").first()
            current_terms.exclude(id=latest_current.id).update(current=False)
            self.stdout.write(
                self.style.SUCCESS(f"Set {latest_current.title} as current terms")
            )

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully created {created_count} new terms records"
            )
        )
