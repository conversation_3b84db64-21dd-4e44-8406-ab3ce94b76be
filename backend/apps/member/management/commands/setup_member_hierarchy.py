from datetime import date

from django.core.management.base import BaseCommand
from django.utils import timezone

from apps.member.models import Member, MemberHierarchy


class Command(BaseCommand):
    help = "Set up sample member hierarchy relationships"

    def handle(self, *args, **options):
        self.stdout.write("Setting up member hierarchy relationships...")

        # Get existing members
        members = Member.objects.all()

        if members.count() < 3:
            self.stdout.write(
                self.style.WARNING(
                    "Need at least 3 members to create hierarchy. "
                    "Run create_sample_data first."
                )
            )
            return

        # Clear existing hierarchy
        MemberHierarchy.objects.all().delete()

        # Create hierarchy relationships
        hierarchy_data = [
            # Create a simple hierarchy with existing members
            {
                "member_name": "<PERSON>",
                "manager_name": "Admin User",
                "relationship_type": "direct_manager",
                "is_primary": True,
                "start_date": date(2024, 1, 1),
            },
            {
                "member_name": "<PERSON>",
                "manager_name": "<PERSON>",
                "relationship_type": "direct_manager",
                "is_primary": True,
                "start_date": date(2024, 1, 1),
            },
            {
                "member_name": "<PERSON>",
                "manager_name": "<PERSON>",
                "relationship_type": "direct_manager",
                "is_primary": True,
                "start_date": date(2024, 1, 1),
            },
            # Additional relationships
            {
                "member_name": "<PERSON> Doe",
                "manager_name": "Admin User",
                "relationship_type": "indirect_manager",
                "is_primary": False,
                "start_date": date(2024, 1, 1),
            },
            {
                "member_name": "<PERSON> <PERSON>",
                "manager_name": "Jane Smith",
                "relationship_type": "mentor",
                "is_primary": False,
                "start_date": date(2024, 2, 1),
            },
        ]

        created_count = 0
        for data in hierarchy_data:
            try:
                member = Member.objects.filter(
                    first_name__icontains=data["member_name"].split()[0],
                    last_name__icontains=data["member_name"].split()[1],
                ).first()

                manager = Member.objects.filter(
                    first_name__icontains=data["manager_name"].split()[0],
                    last_name__icontains=data["manager_name"].split()[1],
                ).first()

                if member and manager and member != manager:
                    hierarchy, created = MemberHierarchy.objects.get_or_create(
                        member=member,
                        manager=manager,
                        relationship_type=data["relationship_type"],
                        defaults={
                            "is_primary": data["is_primary"],
                            "start_date": data["start_date"],
                            "notes": f"Sample hierarchy relationship created by management command",
                        },
                    )

                    if created:
                        created_count += 1
                        self.stdout.write(
                            f'Created: {member} -> {manager} ({data["relationship_type"]})'
                        )
                    else:
                        self.stdout.write(
                            f'Updated: {member} -> {manager} ({data["relationship_type"]})'
                        )
                else:
                    self.stdout.write(
                        self.style.WARNING(
                            f'Could not find members for: {data["member_name"]} -> {data["manager_name"]}'
                        )
                    )

            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error creating hierarchy: {e}"))

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully created {created_count} hierarchy relationships"
            )
        )

        # Show hierarchy summary
        self.stdout.write("\nHierarchy Summary:")
        for member in members[:5]:  # Show first 5 members
            managers = member.get_all_managers()
            subordinates = member.get_subordinates()

            self.stdout.write(f"\n{member}:")
            if managers:
                self.stdout.write(
                    f'  Managers: {", ".join([f"{m.manager} ({m.relationship_type})" for m in managers])}'
                )
            if subordinates:
                self.stdout.write(
                    f'  Subordinates: {", ".join([f"{s.member} ({s.relationship_type})" for s in subordinates])}'
                )
            if not managers and not subordinates:
                self.stdout.write("  No hierarchy relationships")
