import hashlib
import random
import time
from datetime import timedelta

from django.core.management.base import BaseCommand
from django.utils import timezone

from apps.member.models import Member, PasswordReset


class Command(BaseCommand):
    help = "Create sample password reset data for testing"

    def handle(self, *args, **options):
        # Get or create some test members
        members = []
        for i in range(1, 8):
            member, created = Member.objects.get_or_create(
                id=i,
                defaults={
                    "username": f"member{i}",
                    "email": f"member{i}@example.com",
                    "first_name": f"Member{i}",
                    "last_name": "Test",
                },
            )
            members.append(member)
            if created:
                self.stdout.write(f"Created member: {member.username}")

        # Create sample password resets
        created_count = 0

        for member in members:
            # Create 1-3 password resets per member
            num_resets = random.randint(1, 3)

            for i in range(num_resets):
                # Generate a unique reset token
                token_data = f"{member.id}:{member.email}:{time.time()}:{i}"
                reset_token = hashlib.sha256(token_data.encode()).hexdigest()

                # Create reset link
                reset_link = f"http://localhost:8000/reset-password/{reset_token}/"

                # Randomize creation time (within last 30 days)
                days_ago = random.randint(0, 30)
                created_time = timezone.now() - timedelta(days=days_ago)

                password_reset = PasswordReset.objects.create(
                    member=member, link=reset_link, created=created_time
                )
                created_count += 1

                self.stdout.write(
                    self.style.SUCCESS(
                        f"Created password reset for {member.username}: {reset_link}"
                    )
                )

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully created {created_count} password reset records"
            )
        )

        # Print statistics
        total_resets = PasswordReset.objects.count()
        today_resets = PasswordReset.objects.filter(
            created__date=timezone.now().date()
        ).count()

        expired_resets = PasswordReset.objects.filter(
            created__lt=timezone.now() - timedelta(hours=24)
        ).count()

        active_resets = PasswordReset.objects.filter(
            created__gte=timezone.now() - timedelta(hours=24)
        ).count()

        self.stdout.write(f"\nPassword Reset Statistics:")
        self.stdout.write(f"Total resets: {total_resets}")
        self.stdout.write(f"Today resets: {today_resets}")
        self.stdout.write(f"Expired resets: {expired_resets}")
        self.stdout.write(f"Active resets: {active_resets}")

        # Show top members with most resets
        from django.db.models import Count

        top_members = (
            PasswordReset.objects.values("member__username")
            .annotate(reset_count=Count("id"))
            .order_by("-reset_count")[:5]
        )

        self.stdout.write(f"\nTop members with most resets:")
        for item in top_members:
            self.stdout.write(
                f'  {item["member__username"]}: {item["reset_count"]} resets'
            )
