from datetime import date

from django.core.management.base import BaseCommand
from django.utils import timezone

from apps.member.models import Member, MemberTeam, Team


class Command(BaseCommand):
    help = "Set up sample teams with multiple team memberships per member"

    def handle(self, *args, **options):
        self.stdout.write("Setting up teams with multiple memberships...")

        # Get existing members
        members = Member.objects.all()

        if members.count() < 2:
            self.stdout.write(
                self.style.WARNING(
                    "Need at least 2 members to create teams. "
                    "Run create_sample_data first."
                )
            )
            return

        # Clear existing teams and memberships
        MemberTeam.objects.all().delete()
        Team.objects.all().delete()

        # Create teams
        teams_data = [
            {
                "name": "Sales Team",
                "team_type": "sales",
                "description": "Primary sales and business development team",
                "team_lead_name": "Admin User",
            },
            {
                "name": "Support Team",
                "team_type": "support",
                "description": "Customer support and technical assistance team",
                "team_lead_name": "<PERSON>",
            },
            {
                "name": "Development Team",
                "team_type": "development",
                "description": "Software development and engineering team",
                "team_lead_name": "<PERSON>",
            },
            {
                "name": "Marketing Team",
                "team_type": "marketing",
                "description": "Marketing and communications team",
                "team_lead_name": "<PERSON> <PERSON>",
            },
            {
                "name": "Operations Team",
                "team_type": "operations",
                "description": "Business operations and process management",
                "team_lead_name": "Admin User",
            },
        ]

        created_teams = []
        for data in teams_data:
            try:
                # Find team lead
                team_lead = None
                if data["team_lead_name"]:
                    team_lead = Member.objects.filter(
                        first_name__icontains=data["team_lead_name"].split()[0],
                        last_name__icontains=data["team_lead_name"].split()[1],
                    ).first()

                team, created = Team.objects.get_or_create(
                    name=data["name"],
                    defaults={
                        "team_type": data["team_type"],
                        "description": data["description"],
                        "team_lead": team_lead,
                        "is_active": True,
                    },
                )

                if created:
                    created_teams.append(team)
                    self.stdout.write(
                        f"Created team: {team.name} ({team.get_team_type_display()})"
                    )
                else:
                    self.stdout.write(
                        f"Updated team: {team.name} ({team.get_team_type_display()})"
                    )

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error creating team {data["name"]}: {e}')
                )

        # Create team memberships with multiple teams per member
        memberships_data = [
            # Admin User - Primary: Sales, Secondary: Operations
            {
                "member_name": "Admin User",
                "team_name": "Sales Team",
                "role": "lead",
                "is_primary": True,
            },
            {
                "member_name": "Admin User",
                "team_name": "Operations Team",
                "role": "manager",
                "is_primary": False,
            },
            # Jane Smith - Primary: Support, Secondary: Sales
            {
                "member_name": "Jane Smith",
                "team_name": "Support Team",
                "role": "lead",
                "is_primary": True,
            },
            {
                "member_name": "Jane Smith",
                "team_name": "Sales Team",
                "role": "contributor",
                "is_primary": False,
            },
            # John Doe - Primary: Development, Secondary: Support
            {
                "member_name": "John Doe",
                "team_name": "Development Team",
                "role": "lead",
                "is_primary": True,
            },
            {
                "member_name": "John Doe",
                "team_name": "Support Team",
                "role": "contributor",
                "is_primary": False,
            },
            # Mike Wilson - Primary: Marketing, Secondary: Development
            {
                "member_name": "Mike Wilson",
                "team_name": "Marketing Team",
                "role": "lead",
                "is_primary": True,
            },
            {
                "member_name": "Mike Wilson",
                "team_name": "Development Team",
                "role": "observer",
                "is_primary": False,
            },
        ]

        created_memberships = 0
        for data in memberships_data:
            try:
                member = Member.objects.filter(
                    first_name__icontains=data["member_name"].split()[0],
                    last_name__icontains=data["member_name"].split()[1],
                ).first()

                team = Team.objects.filter(name=data["team_name"]).first()

                if member and team:
                    membership, created = MemberTeam.objects.get_or_create(
                        member=member,
                        team=team,
                        defaults={
                            "role": data["role"],
                            "is_primary": data["is_primary"],
                            "start_date": date(2024, 1, 1),
                            "notes": f"Sample team membership created by management command",
                        },
                    )

                    if created:
                        created_memberships += 1
                        self.stdout.write(
                            f'Created membership: {member} -> {team} ({data["role"]})'
                        )
                    else:
                        self.stdout.write(
                            f'Updated membership: {member} -> {team} ({data["role"]})'
                        )
                else:
                    self.stdout.write(
                        self.style.WARNING(
                            f'Could not find member or team for: {data["member_name"]} -> {data["team_name"]}'
                        )
                    )

            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error creating membership: {e}"))

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully created {len(created_teams)} teams and {created_memberships} memberships"
            )
        )

        # Show summary
        self.stdout.write("\nTeam Membership Summary:")
        for member in members[:5]:  # Show first 5 members
            memberships = member.get_team_memberships()
            self.stdout.write(f"\n{member}:")
            if memberships:
                for membership in memberships:
                    primary = " (Primary)" if membership.is_primary else ""
                    self.stdout.write(
                        f"  {membership.team.name} - {membership.get_role_display()}{primary}"
                    )
            else:
                self.stdout.write("  No team memberships")
