<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XD Incentives - Clerk Login Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            color: #666;
            margin-bottom: 2rem;
        }

        .login-section {
            margin-bottom: 2rem;
        }

        .login-button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
            width: 100%;
            margin-bottom: 1rem;
        }

        .login-button:hover {
            background: #3730a3;
        }

        .login-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-weight: 500;
        }

        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }

        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }

        .user-info {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: left;
        }

        .user-info h3 {
            margin-bottom: 0.5rem;
            color: #333;
        }

        .user-info p {
            margin: 0.25rem 0;
            color: #666;
        }

        .logout-button {
            background: #dc2626;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .logout-button:hover {
            background: #b91c1c;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">XD Incentives</div>
        <div class="subtitle">Clerk Authentication Test</div>

        <!-- Hidden data attribute for the key -->
        <div id="clerkKey" data-publishable-key="{{ clerk_publishable_key }}" style="display: none;"></div>

        <!-- Login Section -->
        <div id="loginSection" class="login-section">
            <button id="loginButton" class="login-button" disabled>
                Loading Clerk...
            </button>
            <div id="loginStatus" class="status hidden"></div>
        </div>

        <!-- User Info Section -->
        <div id="userSection" class="hidden">
            <div class="user-info">
                <h3>User Information</h3>
                <div id="userDetails"></div>
            </div>
            <button id="logoutButton" class="logout-button">Sign Out</button>
            <div id="logoutStatus" class="status hidden"></div>
        </div>

        <!-- Django API Test Section -->
        <div id="apiSection" class="hidden">
            <h3>Django API Test</h3>
            <button id="testApiButton" class="login-button">Test Django Authentication</button>
            <div id="apiStatus" class="status hidden"></div>
            <div id="apiResponse" class="user-info hidden"></div>
        </div>
    </div>

    <script>
        // Initialize Clerk
        const clerkKeyElement = document.getElementById('clerkKey');
        const publishableKey = clerkKeyElement.dataset.publishableKey;
        let clerk;

        async function initializeClerk() {
            try {
                // Check if we have a valid key first
                if (!publishableKey || publishableKey === '') {
                    throw new Error('Publishable key is not properly loaded. Please check your configuration.');
                }

                console.log('Loading Clerk manually...');
                await loadClerkManually();

                console.log('Clerk SDK loaded successfully!');
                console.log('Initializing Clerk with key:', publishableKey.substring(0, 10) + '...');

                // Debug: Check what's available
                console.log('window.Clerk type:', typeof window.Clerk);
                console.log('window.Clerk value:', window.Clerk);
                console.log('Available Clerk properties:', Object.keys(window).filter(key => key.toLowerCase().includes('clerk')));

                // Clerk is already initialized, use the existing instance
                if (window.Clerk && typeof window.Clerk === 'object') {
                    clerk = window.Clerk;
                    console.log('Using existing Clerk instance');
                } else {
                    throw new Error('Clerk is not properly initialized');
                }

                // Initialize Clerk properly
                console.log('Initializing Clerk...');
                if (clerk.load && typeof clerk.load === 'function') {
                    try {
                        await clerk.load();
                        console.log('Clerk initialized successfully!');
                    } catch (loadError) {
                        console.error('Error initializing Clerk:', loadError);
                        throw new Error('Failed to initialize Clerk: ' + loadError.message);
                    }
                } else {
                    console.log('Clerk load method not available, proceeding...');
                }

                // Enable the sign-in button
                document.getElementById('loginButton').disabled = false;
                document.getElementById('loginButton').textContent = 'Sign In with Clerk';

                // Listen for authentication state changes
                clerk.addListener(({ user }) => {
                    console.log('Auth state changed:', user ? 'User signed in' : 'User signed out');
                    if (user) {
                        showUserInfo(user);
                        document.getElementById('loginSection').classList.add('hidden');
                        document.getElementById('userSection').classList.remove('hidden');
                        document.getElementById('apiSection').classList.remove('hidden');
                    } else {
                        showLoginSection();
                    }
                });

                // Check if user is already signed in
                if (clerk.client && clerk.client.user) {
                    console.log('User already signed in');
                    showUserInfo(clerk.client.user);
                    document.getElementById('loginSection').classList.add('hidden');
                    document.getElementById('userSection').classList.remove('hidden');
                    document.getElementById('apiSection').classList.remove('hidden');
                }

            } catch (error) {
                console.error('Error initializing Clerk:', error);
                showStatus('Error initializing Clerk: ' + error.message, 'error');
            }
        }

        // Function to load Clerk manually
        async function loadClerkManually() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://useful-sailfish-19.clerk.accounts.dev/npm/@clerk/clerk-js@5/dist/clerk.browser.js';
                script.setAttribute('data-clerk-publishable-key', publishableKey);
                script.setAttribute('crossorigin', 'anonymous');
                script.setAttribute('async', 'true');

                script.onload = () => {
                    console.log('Clerk loaded manually');
                    // Wait a bit for the script to initialize
                    setTimeout(() => {
                        if (typeof window.Clerk !== 'undefined') {
                            console.log('Clerk is now available');
                            resolve();
                        } else {
                            reject(new Error('Clerk failed to initialize after loading'));
                        }
                    }, 1000);
                };
                script.onerror = () => {
                    console.error('Failed to load Clerk manually');
                    reject(new Error('Failed to load Clerk script'));
                };
                document.head.appendChild(script);
            });
        }

        function showLoginSection() {
            document.getElementById('loginSection').classList.remove('hidden');
            document.getElementById('userSection').classList.add('hidden');
            document.getElementById('apiSection').classList.add('hidden');
        }

        function showUserInfo(user) {
            const userDetails = document.getElementById('userDetails');
            userDetails.innerHTML = `
                <p><strong>Name:</strong> ${user.fullName || 'N/A'}</p>
                <p><strong>Email:</strong> ${user.primaryEmailAddress?.emailAddress || 'N/A'}</p>
                <p><strong>ID:</strong> ${user.id}</p>
                <p><strong>Created:</strong> ${new Date(user.createdAt).toLocaleDateString()}</p>
                <p><strong>Last Sign In:</strong> ${user.lastSignInAt ? new Date(user.lastSignInAt).toLocaleDateString() : 'N/A'}</p>
            `;
        }

        function showStatus(message, type = 'info') {
            const statusElement = document.getElementById('loginStatus');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
            statusElement.classList.remove('hidden');
        }

        function showApiStatus(message, type = 'info') {
            const statusElement = document.getElementById('apiStatus');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
            statusElement.classList.remove('hidden');
        }

        // Event Listeners
        document.getElementById('loginButton').addEventListener('click', async () => {
            const button = document.getElementById('loginButton');
            const originalText = button.textContent;
            button.textContent = 'Opening...';
            button.disabled = true;

            try {
                console.log('Attempting to open sign in...');
                await clerk.openSignIn();
                // If we get here, the modal was opened successfully
                console.log('Sign in modal opened successfully');
            } catch (error) {
                console.error('Error opening sign in:', error);
                if (error.message.includes('components are not ready')) {
                    showStatus('Clerk components are still loading. Retrying...', 'info');

                    // Multiple retry attempts with increasing delays
                    let retryCount = 0;
                    const maxRetries = 5;

                    const retrySignIn = async () => {
                        retryCount++;
                        console.log(`Retry attempt ${retryCount}/${maxRetries}`);

                        try {
                            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
                            await clerk.openSignIn();
                        } catch (retryError) {
                            console.error(`Retry ${retryCount} failed:`, retryError);

                            if (retryCount < maxRetries && retryError.message.includes('components are not ready')) {
                                showStatus(`Retrying... (${retryCount}/${maxRetries})`, 'info');
                                retrySignIn();
                            } else {
                                showStatus('Error opening sign in: ' + retryError.message, 'error');
                                button.textContent = originalText;
                                button.disabled = false;
                            }
                        }
                    };

                    retrySignIn();
                } else {
                    showStatus('Error opening sign in: ' + error.message, 'error');
                    button.textContent = originalText;
                    button.disabled = false;
                }
            }

            // Always reset the button after a short delay to handle modal close
            setTimeout(() => {
                button.textContent = originalText;
                button.disabled = false;
            }, 1000);
        });

        document.getElementById('logoutButton').addEventListener('click', async () => {
            try {
                await clerk.signOut();
                showLoginSection();
                showStatus('Successfully signed out', 'success');
            } catch (error) {
                console.error('Error signing out:', error);
                showStatus('Error signing out: ' + error.message, 'error');
            }
        });

        document.getElementById('testApiButton').addEventListener('click', async () => {
            try {
                const button = document.getElementById('testApiButton');
                const originalText = button.textContent;
                button.textContent = 'Testing...';
                button.disabled = true;

                // Get token from Clerk
                const token = await clerk.session.getToken();

                // Test Django authentication
                const response = await fetch('/api/clerk/auth/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token }),
                });

                const data = await response.json();

                if (data.success) {
                    showApiStatus('Successfully authenticated with Django!', 'success');

                    // Show Django user info
                    const apiResponse = document.getElementById('apiResponse');
                    apiResponse.innerHTML = `
                        <h4>Django User Info:</h4>
                        <p><strong>ID:</strong> ${data.user.id}</p>
                        <p><strong>Username:</strong> ${data.user.username}</p>
                        <p><strong>Email:</strong> ${data.user.email}</p>
                        <p><strong>Full Name:</strong> ${data.user.full_name}</p>
                        <p><strong>Clerk ID:</strong> ${data.user.clerk_id}</p>
                        <p><strong>Is Active:</strong> ${data.user.is_active ? 'Yes' : 'No'}</p>
                        <p><strong>Is Staff:</strong> ${data.user.is_staff ? 'Yes' : 'No'}</p>
                        <p><strong>Is Superuser:</strong> ${data.user.is_superuser ? 'Yes' : 'No'}</p>
                    `;
                    apiResponse.classList.remove('hidden');
                } else {
                    showApiStatus('Authentication failed: ' + (data.error || 'Unknown error'), 'error');
                }

            } catch (error) {
                console.error('Error testing API:', error);
                showApiStatus('Error testing API: ' + error.message, 'error');
            } finally {
                const button = document.getElementById('testApiButton');
                button.textContent = originalText;
                button.disabled = false;
            }
        });

        // Initialize when page loads
        console.log('Starting Clerk initialization...');
        initializeClerk();
    </script>
</body>
</html>
