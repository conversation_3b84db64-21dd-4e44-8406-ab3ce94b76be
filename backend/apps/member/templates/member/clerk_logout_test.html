<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clerk <PERSON>gout Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .primary { background: #007bff; color: white; }
        .secondary { background: #6c757d; color: white; }
        .danger { background: #dc3545; color: white; }
        .user-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>Clerk <PERSON>go<PERSON> Test</h1>

    <div id="clerkKey" data-publishable-key="{{ clerk_publishable_key }}" style="display: none;"></div>

    <div id="status"></div>

    <!-- Authentication Section -->
    <div id="authSection">
        <h2>Authentication</h2>
        <button id="signInButton" class="primary" onclick="signIn()">Sign In with Clerk</button>
        <div id="userInfo" class="user-info hidden">
            <h3>Current User</h3>
            <div id="userDetails"></div>
        </div>
    </div>

    <!-- Logout Section -->
    <div id="logoutSection" class="hidden">
        <h2>Force Logout Options</h2>

        <h3>Method 1: Clerk Sign Out</h3>
        <p>Uses Clerk's built-in signOut() method</p>
        <button id="clerkSignOutButton" class="danger" onclick="clerkSignOut()">Clerk Sign Out</button>

        <h3>Method 2: Clear Session Token</h3>
        <p>Manually clears the session token</p>
        <button id="clearTokenButton" class="secondary" onclick="clearSessionToken()">Clear Session Token</button>

        <h3>Method 3: Force Reload</h3>
        <p>Reloads the page to clear all state</p>
        <button id="forceReloadButton" class="secondary" onclick="forceReload()">Force Page Reload</button>

        <h3>Method 4: Clear Local Storage</h3>
        <p>Clears all Clerk-related local storage</p>
        <button id="clearStorageButton" class="secondary" onclick="clearLocalStorage()">Clear Local Storage</button>

        <h3>Method 5: Django + Clerk Logout</h3>
        <p>Logs out from both Django and Clerk</p>
        <button id="djangoLogoutButton" class="danger" onclick="djangoAndClerkLogout()">Django + Clerk Logout</button>
    </div>

    <script>
        let clerk = null;
        const publishableKey = document.getElementById('clerkKey').dataset.publishableKey;

        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function loadClerk() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://useful-sailfish-19.clerk.accounts.dev/npm/@clerk/clerk-js@5/dist/clerk.browser.js';
                script.setAttribute('data-clerk-publishable-key', publishableKey);
                script.setAttribute('crossorigin', 'anonymous');
                script.setAttribute('async', 'true');

                script.onload = () => {
                    setTimeout(() => {
                        if (window.Clerk) {
                            clerk = window.Clerk;
                            resolve();
                        } else {
                            reject(new Error('Clerk not available after loading'));
                        }
                    }, 1000);
                };

                script.onerror = () => reject(new Error('Failed to load Clerk script'));
                document.head.appendChild(script);
            });
        }

        async function initializeClerk() {
            try {
                showStatus('Loading Clerk...', 'info');
                await loadClerk();

                if (clerk.load && typeof clerk.load === 'function') {
                    await clerk.load();
                }

                showStatus('Clerk loaded successfully!', 'success');

                        // Listen for auth state changes
        clerk.addListener(({ user }) => {
            if (user) {
                showUserInfo(user);
                document.getElementById('logoutSection').classList.remove('hidden');
                // Automatically link to Django account
                linkToDjangoAccount(user);
            } else {
                hideUserInfo();
                document.getElementById('logoutSection').classList.add('hidden');
            }
        });

        // Check if user is already signed in
        if (clerk.client && clerk.client.user) {
            showUserInfo(clerk.client.user);
            document.getElementById('logoutSection').classList.remove('hidden');
            // Automatically link to Django account
            linkToDjangoAccount(clerk.client.user);
        }

            } catch (error) {
                showStatus('Error loading Clerk: ' + error.message, 'error');
            }
        }

        async function signIn() {
            try {
                showStatus('Opening sign in...', 'info');
                await clerk.openSignIn();
            } catch (error) {
                showStatus('Error opening sign in: ' + error.message, 'error');
            }
        }

        function showUserInfo(user) {
            const userInfo = document.getElementById('userInfo');
            const userDetails = document.getElementById('userDetails');

            userDetails.innerHTML = `
                <p><strong>Name:</strong> ${user.fullName || 'N/A'}</p>
                <p><strong>Email:</strong> ${user.primaryEmailAddress?.emailAddress || 'N/A'}</p>
                <p><strong>ID:</strong> ${user.id}</p>
                <p><strong>Created:</strong> ${new Date(user.createdAt).toLocaleDateString()}</p>
            `;

            userInfo.classList.remove('hidden');
        }

        function hideUserInfo() {
            document.getElementById('userInfo').classList.add('hidden');
        }

        async function linkToDjangoAccount(user) {
            try {
                showStatus('Linking to Django account...', 'info');

                // Get the session token
                const token = await clerk.session.getToken();

                // Prepare user data
                const userData = {
                    token: token,
                    clerk_id: user.id,
                    email: user.primaryEmailAddress?.emailAddress || '',
                    first_name: user.firstName || '',
                    last_name: user.lastName || '',
                    username: user.username || user.primaryEmailAddress?.emailAddress || '',
                };

                console.log('Sending user data to Django:', userData);

                // Send to Django backend
                const response = await fetch('/member/clerk-auth/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken'),
                    },
                    body: JSON.stringify(userData),
                });

                const data = await response.json();

                if (data.success) {
                    console.log('Account linked successfully:', data.user);
                    showStatus('Successfully linked to Django account!', 'success');

                    // Update user info to show Django details
                    const userInfo = document.getElementById('userInfo');
                    const userDetails = document.getElementById('userDetails');

                    userDetails.innerHTML = `
                        <h4>Clerk User</h4>
                        <p><strong>Name:</strong> ${user.fullName || 'N/A'}</p>
                        <p><strong>Email:</strong> ${user.primaryEmailAddress?.emailAddress || 'N/A'}</p>
                        <p><strong>Clerk ID:</strong> ${user.id}</p>
                        <p><strong>Created:</strong> ${new Date(user.createdAt).toLocaleDateString()}</p>

                        <h4>Django User</h4>
                        <p><strong>Name:</strong> ${data.user.full_name}</p>
                        <p><strong>Email:</strong> ${data.user.email}</p>
                        <p><strong>Username:</strong> ${data.user.username}</p>
                        <p><strong>Django ID:</strong> ${data.user.id}</p>
                        <p><strong>Status:</strong> ${data.user.is_active ? 'Active' : 'Inactive'}</p>
                    `;

                } else {
                    throw new Error(data.error || 'Unknown error');
                }

            } catch (error) {
                console.error('Error linking account:', error);
                showStatus('Error linking to Django: ' + error.message, 'error');
            }
        }

        // Method 1: Clerk Sign Out
        async function clerkSignOut() {
            try {
                showStatus('Signing out from Clerk...', 'info');
                await clerk.signOut();
                showStatus('Successfully signed out from Clerk!', 'success');
            } catch (error) {
                showStatus('Error signing out from Clerk: ' + error.message, 'error');
            }
        }

        // Method 2: Clear Session Token
        async function clearSessionToken() {
            try {
                showStatus('Clearing session token...', 'info');

                // Clear the session token
                if (clerk.session) {
                    // Force clear the session
                    await clerk.session.destroy();
                }

                // Also try to sign out
                await clerk.signOut();

                showStatus('Session token cleared!', 'success');
            } catch (error) {
                showStatus('Error clearing session token: ' + error.message, 'error');
            }
        }

        // Method 3: Force Reload
        function forceReload() {
            showStatus('Reloading page...', 'info');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }

        // Method 4: Clear Local Storage
        function clearLocalStorage() {
            try {
                showStatus('Clearing local storage...', 'info');

                // Clear all Clerk-related items
                const keysToRemove = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.toLowerCase().includes('clerk')) {
                        keysToRemove.push(key);
                    }
                }

                keysToRemove.forEach(key => {
                    localStorage.removeItem(key);
                });

                // Also clear session storage
                sessionStorage.clear();

                showStatus(`Cleared ${keysToRemove.length} Clerk-related items from storage!`, 'success');

                // Force reload to apply changes
                setTimeout(() => {
                    window.location.reload();
                }, 1000);

            } catch (error) {
                showStatus('Error clearing local storage: ' + error.message, 'error');
            }
        }

        // Method 5: Django + Clerk Logout
        async function djangoAndClerkLogout() {
            try {
                showStatus('Logging out from Django and Clerk...', 'info');

                // First, sign out from Clerk
                await clerk.signOut();

                // Then, call Django logout endpoint
                const response = await fetch('/member/django-logout/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken'),
                    },
                });

                const data = await response.json();

                if (data.success) {
                    showStatus('Successfully logged out from both Django and Clerk!', 'success');
                    // Redirect to home page
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                } else {
                    showStatus('Django logout failed: ' + (data.error || 'Unknown error'), 'error');
                }

            } catch (error) {
                showStatus('Error during logout: ' + error.message, 'error');
            }
        }

        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Initialize when page loads
        window.onload = initializeClerk;
    </script>
</body>
</html>
