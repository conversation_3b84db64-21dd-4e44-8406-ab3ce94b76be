{% extends "member/base.html" %}
{% load member_tags %}

{% block title %}Advanced Search{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Advanced Search</h1>
        <p class="text-gray-600 mt-2">Advanced search features available for your account type</p>
    </div>

    <!-- Search Form -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Search Parameters</h2>
        <form method="POST" class="space-y-4">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="search_params" class="block text-sm font-medium text-gray-700">Search Parameters</label>
                    <textarea name="search_params" id="search_params" rows="4"
                              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Enter your search parameters..."></textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Available Features</label>
                    <div class="mt-2 space-y-2">
                        {% for feature in search_features %}
                        <div class="flex items-center">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                            <span class="text-sm text-gray-600">{{ feature|title }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="flex justify-end">
                <button type="submit"
                        class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Perform Advanced Search
                </button>
            </div>
        </form>
    </div>

    <!-- Navigation Menu -->
    {% if user_navigation %}
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Your Navigation Menu</h2>
        <div class="space-y-2">
            {% for item in user_navigation %}
            <div class="border border-gray-200 rounded-lg overflow-hidden">
                <!-- Main Navigation Item -->
                <div class="flex items-center justify-between p-3 bg-gray-50">
                    <div class="flex items-center">
                        {% if item.icon %}
                        <span class="text-gray-400 mr-3">
                            <i class="fas fa-{{ item.icon }}"></i>
                        </span>
                        {% endif %}
                        <span class="font-medium text-gray-900">
                            <a href="{{ item.url }}" class="hover:text-blue-600">{{ item.label }}</a>
                        </span>
                    </div>
                    <span class="text-sm text-gray-500">{{ item.url }}</span>
                </div>

                <!-- Children/Sub-navigation -->
                {% if item.children %}
                <div class="bg-gray-25 border-t border-gray-100">
                    {% for child in item.children %}
                    <div class="flex items-center justify-between p-3 pl-8 border-b border-gray-100 last:border-b-0">
                        <div class="flex items-center">
                            {% if child.icon %}
                            <span class="text-gray-400 mr-3">
                                <i class="fas fa-{{ child.icon }}"></i>
                            </span>
                            {% endif %}
                            <span class="text-sm text-gray-700">
                                <a href="{{ child.url }}" class="hover:text-blue-600">{{ child.label }}</a>
                            </span>
                        </div>
                        <span class="text-xs text-gray-400">{{ child.url }}</span>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
