{% if tree %}
    <div class="ml-4 border-l-2 border-gray-200 pl-4">
        <div class="flex items-center mb-2">
            <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
            <a href="{% url 'member:member_detail' tree.member.id %}"
               class="font-medium text-gray-900 hover:text-blue-600">
                {{ tree.member }}
            </a>
            <span class="text-sm text-gray-500 ml-2">({{ tree.member.member_type.name|default:"No Type" }})</span>
        </div>

        {% if tree.subordinates %}
            {% for subordinate in tree.subordinates %}
                {% include "member/hierarchy_tree.html" with tree=subordinate %}
            {% endfor %}
        {% endif %}
    </div>
{% endif %}
