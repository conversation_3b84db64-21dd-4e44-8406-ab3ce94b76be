<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XD Incentives - Account Setup</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            width: 100%;
            max-width: 500px;
            text-align: center;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            color: #666;
            margin-bottom: 2rem;
        }

        .step {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }

        .step h3 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .step p {
            color: #666;
            margin-bottom: 1rem;
        }

        .auth-button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
            width: 100%;
            margin-bottom: 1rem;
        }

        .auth-button:hover {
            background: #3730a3;
        }

        .auth-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-weight: 500;
        }

        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }

        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }

        .user-info {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: left;
        }

        .user-info h3 {
            margin-bottom: 0.5rem;
            color: #333;
        }

        .user-info p {
            margin: 0.25rem 0;
            color: #666;
        }

        .hidden {
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">XD Incentives</div>
        <div class="subtitle">Account Setup & Linking</div>

        <!-- Hidden data attribute for the key -->
        <div id="clerkKey" data-publishable-key="{{ clerk_publishable_key }}" style="display: none;"></div>

        <!-- Step 1: Authentication -->
        <div id="step1" class="step">
            <h3>Step 1: Sign In with Clerk</h3>
            <p>First, please sign in or create an account using Clerk authentication.</p>
            <button id="authButton" class="auth-button">Sign In / Sign Up</button>
            <div id="authStatus" class="status hidden"></div>
        </div>

        <!-- Step 2: Account Linking -->
        <div id="step2" class="step hidden">
            <h3>Step 2: Link Your Account</h3>
            <p>We'll automatically create or link your Django account.</p>
            <button id="linkButton" class="auth-button" disabled>
                <span class="loading"></span> Processing...
            </button>
            <div id="linkStatus" class="status hidden"></div>
        </div>

        <!-- Step 3: Success -->
        <div id="step3" class="step hidden">
            <h3>Step 3: Welcome!</h3>
            <p>Your account has been successfully set up and linked.</p>
            <div id="userInfo" class="user-info hidden"></div>
            <button id="continueButton" class="auth-button" onclick="window.location.href='/member/dashboard/'">
                Continue to Dashboard
            </button>
        </div>
    </div>

    <script>
        // Initialize Clerk
        const clerkKeyElement = document.getElementById('clerkKey');
        const publishableKey = clerkKeyElement.dataset.publishableKey;
        let clerk;

        async function initializeClerk() {
            try {
                // Check if we have a valid key first
                if (!publishableKey || publishableKey === '') {
                    throw new Error('Publishable key is not properly loaded. Please check your configuration.');
                }

                console.log('Loading Clerk manually...');
                await loadClerkManually();

                console.log('Clerk SDK loaded successfully!');
                console.log('Initializing Clerk with key:', publishableKey.substring(0, 10) + '...');

                // Clerk is already initialized, use the existing instance
                if (window.Clerk && typeof window.Clerk === 'object') {
                    clerk = window.Clerk;
                    console.log('Using existing Clerk instance');
                } else {
                    throw new Error('Clerk is not properly initialized');
                }

                // Initialize Clerk properly
                console.log('Initializing Clerk...');
                if (clerk.load && typeof clerk.load === 'function') {
                    try {
                        await clerk.load();
                        console.log('Clerk initialized successfully!');
                    } catch (loadError) {
                        console.error('Error initializing Clerk:', loadError);
                        throw new Error('Failed to initialize Clerk: ' + loadError.message);
                    }
                } else {
                    console.log('Clerk load method not available, proceeding...');
                }

                // Enable the auth button
                document.getElementById('authButton').disabled = false;
                document.getElementById('authButton').textContent = 'Sign In / Sign Up';

                // Listen for authentication state changes
                clerk.addListener(({ user }) => {
                    console.log('Auth state changed:', user ? 'User signed in' : 'User signed out');
                    if (user) {
                        handleUserAuthenticated(user);
                    }
                });

                // Check if user is already signed in
                if (clerk.client && clerk.client.user) {
                    console.log('User already signed in');
                    handleUserAuthenticated(clerk.client.user);
                }

            } catch (error) {
                console.error('Error initializing Clerk:', error);
                showStatus('Error initializing Clerk: ' + error.message, 'error', 'authStatus');
            }
        }

        // Function to load Clerk manually
        async function loadClerkManually() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://useful-sailfish-19.clerk.accounts.dev/npm/@clerk/clerk-js@5/dist/clerk.browser.js';
                script.setAttribute('data-clerk-publishable-key', publishableKey);
                script.setAttribute('crossorigin', 'anonymous');
                script.setAttribute('async', 'true');

                script.onload = () => {
                    console.log('Clerk loaded manually');
                    setTimeout(() => {
                        if (typeof window.Clerk !== 'undefined') {
                            console.log('Clerk is now available');
                            resolve();
                        } else {
                            reject(new Error('Clerk failed to initialize after loading'));
                        }
                    }, 1000);
                };
                script.onerror = () => {
                    console.error('Failed to load Clerk manually');
                    reject(new Error('Failed to load Clerk script'));
                };
                document.head.appendChild(script);
            });
        }

        function showStatus(message, type = 'info', elementId = 'authStatus') {
            const statusElement = document.getElementById(elementId);
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
            statusElement.classList.remove('hidden');
        }

        async function handleUserAuthenticated(user) {
            console.log('User authenticated:', user);
            showStatus('User authenticated successfully!', 'success', 'authStatus');

            // Move to step 2
            document.getElementById('step1').classList.add('hidden');
            document.getElementById('step2').classList.remove('hidden');

            // Start linking process
            await linkUserAccount(user);
        }

        async function linkUserAccount(user) {
            try {
                console.log('Linking user account...');

                // Get the session token
                const token = await clerk.session.getToken();

                // Prepare user data
                const userData = {
                    token: token,
                    clerk_id: user.id,
                    email: user.primaryEmailAddress?.emailAddress || '',
                    first_name: user.firstName || '',
                    last_name: user.lastName || '',
                    username: user.username || user.primaryEmailAddress?.emailAddress || '',
                };

                console.log('Sending user data to Django:', userData);

                // Send to Django backend
                const response = await fetch('/member/clerk-auth/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken'),
                    },
                    body: JSON.stringify(userData),
                });

                const data = await response.json();

                if (data.success) {
                    console.log('Account linked successfully:', data.user);
                    showStatus('Account linked successfully!', 'success', 'linkStatus');

                    // Move to step 3
                    document.getElementById('step2').classList.add('hidden');
                    document.getElementById('step3').classList.remove('hidden');

                    // Show user info
                    const userInfo = document.getElementById('userInfo');
                    userInfo.innerHTML = `
                        <h3>Account Information</h3>
                        <p><strong>Name:</strong> ${data.user.full_name}</p>
                        <p><strong>Email:</strong> ${data.user.email}</p>
                        <p><strong>Username:</strong> ${data.user.username}</p>
                        <p><strong>Django ID:</strong> ${data.user.id}</p>
                        <p><strong>Clerk ID:</strong> ${data.user.clerk_id}</p>
                        <p><strong>Status:</strong> ${data.user.is_active ? 'Active' : 'Inactive'}</p>
                    `;
                    userInfo.classList.remove('hidden');

                } else {
                    throw new Error(data.error || 'Unknown error');
                }

            } catch (error) {
                console.error('Error linking account:', error);
                showStatus('Error linking account: ' + error.message, 'error', 'linkStatus');
                document.getElementById('linkButton').disabled = false;
                document.getElementById('linkButton').textContent = 'Retry Linking';
            }
        }

        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Event Listeners
        document.getElementById('authButton').addEventListener('click', async () => {
            try {
                console.log('Opening sign in...');
                await clerk.openSignIn();
            } catch (error) {
                console.error('Error opening sign in:', error);
                showStatus('Error opening sign in: ' + error.message, 'error', 'authStatus');
            }

            // Reset button after a short delay
            setTimeout(() => {
                const button = document.getElementById('authButton');
                button.textContent = 'Sign In / Sign Up';
                button.disabled = false;
            }, 1000);
        });

        // Initialize when page loads
        console.log('Starting Clerk initialization...');
        initializeClerk();
    </script>
</body>
</html>
