{% extends "member/base.html" %}
{% load member_tags %}

{% block title %}Organization Chart{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Organization Chart</h1>
        <p class="text-gray-600 mt-2">Complete organizational hierarchy and reporting structure</p>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-2xl font-bold text-blue-600">{{ all_members.count }}</div>
            <div class="text-sm text-gray-600">Total Members</div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-2xl font-bold text-green-600">{{ top_level_members.count }}</div>
            <div class="text-sm text-gray-600">Top Level</div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-2xl font-bold text-purple-600">{{ all_members|length }}</div>
            <div class="text-sm text-gray-600">With Hierarchy</div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-2xl font-bold text-orange-600">{{ all_members|length }}</div>
            <div class="text-sm text-gray-600">Active Relationships</div>
        </div>
    </div>

    <!-- Organization Tree -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <svg class="h-5 w-5 text-purple-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
            </svg>
            Organization Structure
        </h2>

        {% if top_level_members %}
            <div class="space-y-6">
                {% for member in top_level_members %}
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                                <a href="{% url 'member:member_detail' member.id %}"
                                   class="text-lg font-semibold text-gray-900 hover:text-blue-600">
                                    {{ member }}
                                </a>
                                <span class="ml-2 text-sm text-gray-500">({{ member.member_type.name|default:"No Type" }})</span>
                            </div>
                            <div class="flex space-x-2">
                                <a href="{% url 'member:member_hierarchy' member.id %}"
                                   class="text-sm text-blue-600 hover:text-blue-800">
                                    View Hierarchy
                                </a>
                                <span class="text-sm text-gray-500">
                                    {{ member.get_direct_subordinates.count }} direct reports
                                </span>
                            </div>
                        </div>

                        <!-- Subordinates -->
                        {% with subordinates=member.get_direct_subordinates %}
                            {% if subordinates %}
                                <div class="ml-6 space-y-2">
                                    {% for relationship in subordinates %}
                                        <div class="flex items-center justify-between py-2 border-l-2 border-gray-200 pl-4">
                                            <div class="flex items-center">
                                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                                <a href="{% url 'member:member_detail' relationship.member.id %}"
                                                   class="font-medium text-gray-900 hover:text-blue-600">
                                                    {{ relationship.member }}
                                                </a>
                                                <span class="ml-2 text-sm text-gray-500">({{ relationship.member.member_type.name|default:"No Type" }})</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <span class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                                                    {{ relationship.get_relationship_type_display }}
                                                </span>
                                                <a href="{% url 'member:member_hierarchy' relationship.member.id %}"
                                                   class="text-xs text-blue-600 hover:text-blue-800">
                                                    Hierarchy
                                                </a>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        {% endwith %}
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-12 text-gray-500">
                <svg class="h-16 w-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <p class="text-lg font-medium">No hierarchy relationships found</p>
                <p class="text-sm">Create member hierarchy relationships to see the organization chart</p>
            </div>
        {% endif %}
    </div>

    <!-- Action Buttons -->
    <div class="mt-8 flex space-x-4">
        <a href="{% url 'member:member_list' %}"
           class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
            Back to Member List
        </a>
        <a href="{% url 'admin:member_memberhierarchy_changelist' %}"
           class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
            Manage Hierarchy
        </a>
    </div>
</div>
{% endblock %}
