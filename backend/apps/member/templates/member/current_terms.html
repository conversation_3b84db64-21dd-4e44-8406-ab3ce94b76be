{% extends "member/base.html" %}
{% load static %}

{% block title %}Current Terms & Conditions - XD Incentives{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white shadow-sm rounded-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Terms & Conditions</h1>
                    <p class="text-gray-600 mt-2">Current version as of {% if terms %}{{ terms.created|date:"F j, Y" }}{% else %}No terms available{% endif %}</p>
                </div>
                <div class="flex items-center space-x-4">
                    {% if terms %}
                    <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                        Current Version
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Error Message -->
        {% if error %}
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Error Loading Terms</h3>
                    <div class="mt-2 text-sm text-red-700">
                        {{ error }}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Terms Content -->
        {% if terms %}
        <div class="bg-white shadow-sm rounded-lg overflow-hidden">
            <!-- Terms Header -->
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">{{ terms.title }}</h2>
                <div class="mt-2 flex items-center space-x-4 text-sm text-gray-600">
                    <span>Version: {{ terms.created|date:"Y-m-d" }}</span>
                    <span>•</span>
                    <span>Last Updated: {{ terms.modified|date:"F j, Y" }}</span>
                    {% if terms.current %}
                    <span>•</span>
                    <span class="text-green-600 font-medium">Active</span>
                    {% endif %}
                </div>
            </div>

            <!-- Terms Body -->
            <div class="p-6">
                {% if terms.body_html %}
                <div class="prose prose-lg max-w-none">
                    {{ terms.body_html|safe }}
                </div>
                {% elif terms.body_text %}
                <div class="prose prose-lg max-w-none">
                    <pre class="whitespace-pre-wrap font-sans text-gray-900">{{ terms.body_text }}</pre>
                </div>
                {% else %}
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No content available</h3>
                    <p class="mt-1 text-sm text-gray-500">The terms document exists but has no content.</p>
                </div>
                {% endif %}
            </div>

            <!-- Footer -->
            <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between text-sm text-gray-600">
                    <span>Document ID: {{ terms.id }}</span>
                    <span>Generated on {{ terms.modified|date:"F j, Y \a\t g:i A" }}</span>
                </div>
            </div>
        </div>

        <!-- Terms Acceptance Section -->
        {% if user.is_authenticated %}
        <div class="mt-8 bg-white shadow-sm rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium text-gray-900">Terms Acceptance</h3>
                    <p class="text-sm text-gray-600 mt-1">
                        {% if has_accepted %}
                        ✅ You have accepted these terms and conditions.
                        {% else %}
                        Please review and accept the terms and conditions to continue using our services.
                        {% endif %}
                    </p>
                </div>
                <div>
                    {% if has_accepted %}
                    <div class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600">
                        <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Accepted
                    </div>
                    {% else %}
                    <form method="post" action="{% url 'member:accept_terms' %}" class="inline">
                        {% csrf_token %}
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Accept Terms
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Additional Information -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Important Information</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p>This document represents the current terms and conditions for XD Incentives. By using our services, you agree to be bound by these terms. If you have any questions about these terms, please contact our support team.</p>
                    </div>
                </div>
            </div>
        </div>

        {% else %}
        <!-- No Terms Available -->
        <div class="bg-white shadow-sm rounded-lg p-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No Terms Available</h3>
            <p class="mt-1 text-sm text-gray-500">No terms and conditions have been published yet.</p>
            <div class="mt-6">
                <a href="{% url 'member:member_list' %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Back to Members
                </a>
            </div>
        </div>
        {% endif %}

        <!-- Navigation -->
        {% if user.is_authenticated %}
        <div class="mt-8 flex justify-between">
            <a href="{% url 'member:member_list' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Members
            </a>

            {% if user.is_staff %}
            <a href="{% url 'admin:member_terms_changelist' %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Manage Terms
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
