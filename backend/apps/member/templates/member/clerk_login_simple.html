<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XD Incentives - Simple Clerk Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .debug { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>XD Incentives - Simple Clerk Test</h1>

        <!-- Hidden data attribute for the key -->
        <div id="clerkKey" data-publishable-key="{{ clerk_publishable_key }}" style="display: none;"></div>

        <!-- Clerk Script (using Javascript template) -->
        <script
          async
          crossorigin="anonymous"
          data-clerk-publishable-key="{{ clerk_publishable_key }}"
          src="https://useful-sailfish-19.clerk.accounts.dev/npm/@clerk/clerk-js@5/dist/clerk.browser.js"
          type="text/javascript">
        </script>

        <div class="debug">
            <h3>Debug Info</h3>
            <p><strong>Key Length:</strong> <span id="keyLength">Loading...</span></p>
            <p><strong>Key Prefix:</strong> <span id="keyPrefix">Loading...</span></p>
            <p><strong>Key Set:</strong> <span id="keySet">Loading...</span></p>
            <p><strong>Raw Key:</strong> <span id="rawKey">Loading...</span></p>
        </div>

        <div id="status" class="status info" style="display: none;"></div>

        <div id="loginSection">
            <h3>Test Options</h3>
            <button onclick="testKeyFormat()">Test Key Format</button>
            <button onclick="testClerkCDN()">Test Clerk CDN</button>
            <button onclick="testManualInit()">Test Manual Init</button>
            <button onclick="testClerkAutoInit()">Test Clerk Auto-Init</button>
            <button onclick="testDirectAPI()">Test Direct API</button>
        </div>

        <div id="results" style="display: none;">
            <h3>Test Results</h3>
            <div id="resultsContent"></div>
        </div>
    </div>

    <script>
        // Get the key from Django using data attribute
        const clerkKeyElement = document.getElementById('clerkKey');
        const publishableKey = clerkKeyElement.dataset.publishableKey;

        // Update debug info
        document.getElementById('keyLength').textContent = publishableKey.length;
        document.getElementById('keyPrefix').textContent = publishableKey.substring(0, 10) + '...';
        document.getElementById('keySet').textContent = publishableKey ? 'Yes' : 'No';
        document.getElementById('rawKey').textContent = publishableKey;

        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
        }

        function showResults(content) {
            document.getElementById('resultsContent').innerHTML = content;
            document.getElementById('results').style.display = 'block';
        }

        // Test 1: Check key format
        function testKeyFormat() {
            showStatus('Testing key format...', 'info');

            const key = publishableKey;
            const results = [];

            results.push(`<p><strong>Key Length:</strong> ${key.length}</p>`);
            results.push(`<p><strong>Key Prefix:</strong> ${key.substring(0, 10)}...</p>`);
            results.push(`<p><strong>Starts with pk_test:</strong> ${key.startsWith('pk_test_') ? 'Yes' : 'No'}</p>`);
            results.push(`<p><strong>Starts with pk_live:</strong> ${key.startsWith('pk_live_') ? 'Yes' : 'No'}</p>`);
            results.push(`<p><strong>Contains special chars:</strong> ${/[^a-zA-Z0-9_-]/.test(key) ? 'Yes' : 'No'}</p>`);
            results.push(`<p><strong>Key is empty:</strong> ${key === '' ? 'Yes' : 'No'}</p>`);

            showResults(results.join(''));
            showStatus('Key format test completed', 'success');
        }

        // Test 2: Test Clerk CDN accessibility
        async function testClerkCDN() {
            showStatus('Testing Clerk CDN accessibility...', 'info');

            try {
                const response = await fetch('https://unpkg.com/@clerk/clerk-js@4.67.0/dist/clerk.browser.js', {
                    method: 'HEAD'
                });

                if (response.ok) {
                    showResults('<p><strong>CDN Status:</strong> ✅ Accessible</p>');
                    showStatus('Clerk CDN is accessible', 'success');
                } else {
                    showResults('<p><strong>CDN Status:</strong> ❌ Not accessible (Status: ' + response.status + ')</p>');
                    showStatus('Clerk CDN is not accessible', 'error');
                }
            } catch (error) {
                showResults(`<p><strong>CDN Status:</strong> ❌ Error: ${error.message}</p>`);
                showStatus('Clerk CDN test failed: ' + error.message, 'error');
            }
        }

        // Test 3: Test manual initialization
        async function testManualInit() {
            showStatus('Testing manual Clerk initialization...', 'info');

            try {
                // Check if Clerk is already available (from the script tag)
                if (typeof window.Clerk !== 'undefined') {
                    showResults('<p><strong>Manual Init:</strong> ✅ Clerk already available</p>');
                    showStatus('Clerk is already loaded and available', 'success');
                    return;
                }

                // Wait for Clerk to be available from the script tag
                let attempts = 0;
                while (typeof window.Clerk === 'undefined' && attempts < 20) {
                    await new Promise(resolve => setTimeout(resolve, 250));
                    attempts++;
                }

                if (typeof window.Clerk === 'undefined') {
                    throw new Error('Clerk failed to load from script tag after 5 seconds');
                }

                showResults('<p><strong>Manual Init:</strong> ✅ Clerk loaded from script tag</p>');
                showStatus('Clerk loaded successfully from script tag', 'success');

            } catch (error) {
                showResults(`<p><strong>Manual Init:</strong> ❌ Error: ${error.message}</p>`);
                showStatus('Manual Clerk initialization failed: ' + error.message, 'error');
            }
        }

        // Test 4: Test Clerk auto-initialization
        async function testClerkAutoInit() {
            showStatus('Testing Clerk auto-initialization...', 'info');

            try {
                // Wait for Clerk to be available
                let attempts = 0;
                while (typeof window.Clerk === 'undefined' && attempts < 20) {
                    await new Promise(resolve => setTimeout(resolve, 250));
                    attempts++;
                }

                if (typeof window.Clerk === 'undefined') {
                    throw new Error('Clerk failed to load after 5 seconds');
                }

                // Check if Clerk is auto-initialized
                if (window.Clerk.isReady && window.Clerk.isReady()) {
                    showResults('<p><strong>Auto-Init:</strong> ✅ Clerk is ready</p>');
                    showStatus('Clerk auto-initialization successful', 'success');
                } else {
                    // Try to initialize manually
                    const clerk = window.Clerk({
                        publishableKey: publishableKey
                    });

                    await clerk.load();

                    showResults('<p><strong>Auto-Init:</strong> ✅ Manual initialization successful</p>');
                    showStatus('Manual Clerk initialization successful', 'success');
                }

            } catch (error) {
                showResults(`<p><strong>Auto-Init:</strong> ❌ Error: ${error.message}</p>`);
                showStatus('Clerk auto-initialization failed: ' + error.message, 'error');
            }
        }

        // Test 5: Test direct API call
        async function testDirectAPI() {
            showStatus('Testing direct API call...', 'info');

            try {
                const response = await fetch('/api/clerk/auth/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        token: 'test_token',
                        test: true
                    }),
                });

                const data = await response.json();

                showResults(`
                    <p><strong>API Status:</strong> ${response.status}</p>
                    <p><strong>Response:</strong> ${JSON.stringify(data, null, 2)}</p>
                `);

                if (response.ok) {
                    showStatus('Direct API test successful', 'success');
                } else {
                    showStatus('Direct API test failed', 'error');
                }

            } catch (error) {
                showResults(`<p><strong>API Error:</strong> ${error.message}</p>`);
                showStatus('Direct API test failed: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
