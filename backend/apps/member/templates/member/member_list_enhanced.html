{% extends "member/base.html" %}
{% load member_tags %}

{% block title %}Member Management{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Member Management</h1>

        {% if user|has_permission:"member.create" %}
        <a href="{% url 'member_create' %}"
           class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Create New Member
        </a>
        {% endif %}
    </div>

    <!-- Search and Filter Section -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
        <form method="GET" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <input type="text" name="search" id="search" value="{{ search }}"
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="member_type" class="block text-sm font-medium text-gray-700">Member Type</label>
                    <select name="member_type" id="member_type"
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Types</option>
                        {% for member_type in member_types %}
                        <option value="{{ member_type.slug }}"
                                {% if member_type_filter == member_type.slug %}selected{% endif %}>
                            {{ member_type.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="flex items-end">
                    <button type="submit"
                            class="w-full bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                        Filter
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Member List -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">
                Members ({{ page_obj.paginator.count }} total)
            </h2>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Member
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Type
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Contact
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for member in page_obj %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-700">
                                            {{ member.first_name|first }}{{ member.last_name|first }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ member.get_full_name }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        {{ member.username }}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if member.member_type %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ member.member_type.name }}
                            </span>
                            {% else %}
                            <span class="text-gray-400">No Type</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                       {% if member.status == 'active' %}bg-green-100 text-green-800
                                       {% elif member.status == 'pending' %}bg-yellow-100 text-yellow-800
                                       {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ member.get_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ member.email }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                {% if user|has_permission:"member.view" %}
                                <a href="{% url 'member_detail' member.pk %}"
                                   class="text-blue-600 hover:text-blue-900">View</a>
                                {% endif %}

                                {% if user|has_permission:"member.edit" %}
                                <a href="{% url 'member_update' member.pk %}"
                                   class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                {% endif %}

                                {% if user|has_permission:"member.delete" %}
                                <button onclick="deleteMember({{ member.pk }})"
                                        class="text-red-600 hover:text-red-900">Delete</button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                            No members found.
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if member_type_filter %}&member_type={{ member_type_filter }}{% endif %}"
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                </a>
                {% endif %}
                {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if member_type_filter %}&member_type={{ member_type_filter }}{% endif %}"
                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing <span class="font-medium">{{ page_obj.start_index }}</span> to
                        <span class="font-medium">{{ page_obj.end_index }}</span> of
                        <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if member_type_filter %}&member_type={{ member_type_filter }}{% endif %}"
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Previous
                        </a>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                {{ num }}
                            </span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if member_type_filter %}&member_type={{ member_type_filter }}{% endif %}"
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                {{ num }}
                            </a>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if member_type_filter %}&member_type={{ member_type_filter }}{% endif %}"
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Next
                        </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Feature-specific sections -->
{% if user|has_feature:"advanced_search" %}
<div class="mt-8 bg-white shadow rounded-lg p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Advanced Search</h3>
    <p class="text-gray-600">Advanced search features are available for your account type.</p>
    <a href="{% url 'advanced_search' %}"
       class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
        Use Advanced Search
    </a>
</div>
{% endif %}

<!-- User-specific features display -->
{% if user_features %}
<div class="mt-8 bg-white shadow rounded-lg p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Your Available Features</h3>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        {% for feature in user_features %}
        <div class="bg-gray-50 rounded-lg p-3">
            <div class="text-sm font-medium text-gray-900">{{ feature|title }}</div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<script>
function deleteMember(memberId) {
    if (confirm('Are you sure you want to delete this member?')) {
        // Implement delete functionality
        console.log('Delete member:', memberId);
    }
}
</script>
{% endblock %}
