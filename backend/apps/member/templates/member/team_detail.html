{% extends "member/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">{{ team.name }}</h1>
            <p class="text-gray-600 mt-2">{{ team.get_team_type_display }} • {{ memberships.count }} members</p>
        </div>
        <div class="flex space-x-3">
            <a href="{% url 'member:team_list' %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Teams
            </a>
            <a href="{% url 'member:member_list' %}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                View All Members
            </a>
        </div>
    </div>

    <!-- Team Information -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Team Information</h3>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Team Type</dt>
                        <dd class="text-sm text-gray-900">{{ team.get_team_type_display }}</dd>
                    </div>
                    {% if team.team_lead %}
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Team Lead</dt>
                        <dd class="text-sm text-gray-900">
                            <a href="{% url 'member:member_detail' team.team_lead.id %}" class="text-blue-600 hover:text-blue-800">
                                {{ team.team_lead }}
                            </a>
                        </dd>
                    </div>
                    {% endif %}
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Status</dt>
                        <dd class="text-sm text-gray-900">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if team.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                {% if team.is_active %}Active{% else %}Inactive{% endif %}
                            </span>
                        </dd>
                    </div>
                </dl>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Description</h3>
                {% if team.description %}
                    <p class="text-gray-700">{{ team.description }}</p>
                {% else %}
                    <p class="text-gray-500 italic">No description available</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Team Members -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Team Members</h3>
        </div>

        {% if members_by_role %}
            <div class="p-6">
                {% for role, role_memberships in members_by_role.items %}
                <div class="mb-6 last:mb-0">
                    <h4 class="text-md font-medium text-gray-900 mb-3">{{ role }}s ({{ role_memberships|length }})</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {% for membership in role_memberships %}
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors duration-150">
                            <div class="flex items-center justify-between mb-2">
                                <a href="{% url 'member:member_detail' membership.member.id %}"
                                   class="font-medium text-gray-900 hover:text-blue-600">
                                    {{ membership.member }}
                                </a>
                                {% if membership.is_primary %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Primary
                                    </span>
                                {% endif %}
                            </div>

                            <div class="text-sm text-gray-600 space-y-1">
                                {% if membership.start_date %}
                                    <div>Started: {{ membership.start_date }}</div>
                                {% endif %}
                                {% if membership.end_date %}
                                    <div>Ends: {{ membership.end_date }}</div>
                                {% endif %}
                                {% if membership.notes %}
                                    <div class="text-gray-500 italic">{{ membership.notes|truncatewords:10 }}</div>
                                {% endif %}
                            </div>

                            <div class="mt-3 flex space-x-2">
                                <a href="{% url 'member:member_detail' membership.member.id %}"
                                   class="text-blue-600 hover:text-blue-800 text-sm">
                                    View Member
                                </a>
                                <a href="{% url 'member:member_teams' membership.member.id %}"
                                   class="text-green-600 hover:text-green-800 text-sm">
                                    View Teams
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="p-6 text-center">
                <div class="text-gray-500 mb-4">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Members</h3>
                <p class="text-gray-500">This team doesn't have any members yet.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
