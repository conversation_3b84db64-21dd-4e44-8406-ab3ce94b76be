{% extends "member/base.html" %}
{% load member_tags %}

{% block title %}Admin Only Page{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
        <div class="flex items-center mb-4">
            <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h1 class="text-2xl font-bold text-green-800">Admin Access Granted</h1>
                <p class="text-green-700">You have successfully accessed this restricted page!</p>
            </div>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Administrator Features</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Your Permissions</h3>
                    <ul class="space-y-2">
                        {% for permission in user_features %}
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                            <span class="text-sm text-gray-700">{{ permission|title }}</span>
                        </li>
                        {% endfor %}
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Available Actions</h3>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                            <span class="text-sm text-gray-700">Manage all member types</span>
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                            <span class="text-sm text-gray-700">Access system settings</span>
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                            <span class="text-sm text-gray-700">View all user data</span>
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                            <span class="text-sm text-gray-700">Generate system reports</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Access Control Demo</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>This page requires the 'admin.access' permission. Regular users and managers cannot access this page.</p>
                        <p class="mt-1">Try accessing this page with a different user account to see the access denied message.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
