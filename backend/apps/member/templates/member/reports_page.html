{% extends "member/base.html" %}
{% load member_tags %}

{% block title %}Reports Dashboard{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-center mb-4">
            <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h1 class="text-2xl font-bold text-blue-800">Reports Dashboard</h1>
                <p class="text-blue-700">You have access to the reports feature!</p>
            </div>
        </div>

        <div class="bg-white rounded-lg p-6 shadow-sm">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Available Reports</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Member Statistics</h3>
                    <p class="text-sm text-gray-600 mb-3">View member growth, activity, and engagement metrics.</p>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white text-sm px-4 py-2 rounded">
                        Generate Report
                    </button>
                </div>

                <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Activity Logs</h3>
                    <p class="text-sm text-gray-600 mb-3">Track user actions, login history, and system events.</p>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white text-sm px-4 py-2 rounded">
                        Generate Report
                    </button>
                </div>

                <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Permission Analysis</h3>
                    <p class="text-sm text-gray-600 mb-3">Analyze permission usage and access patterns.</p>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white text-sm px-4 py-2 rounded">
                        Generate Report
                    </button>
                </div>
            </div>
        </div>

        <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Feature Flag Demo</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>This page requires the 'reports' feature flag. Users without this feature cannot access this page.</p>
                        <p class="mt-1">Try accessing this page with a user account that doesn't have the 'reports' feature enabled.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
