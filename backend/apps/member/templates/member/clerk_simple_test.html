<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clerk Simple Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .primary { background: #007bff; color: white; }
        .secondary { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <h1>Clerk Simple Test</h1>

    <div id="clerkKey" data-publishable-key="{{ clerk_publishable_key }}" style="display: none;"></div>

    <div id="status"></div>

    <button id="testButton" class="primary" onclick="testClerk()">Test Clerk</button>
    <button id="signInButton" class="secondary" onclick="signIn()" disabled>Sign In</button>

    <div id="userInfo" style="display: none;">
        <h3>User Info</h3>
        <div id="userDetails"></div>
    </div>

    <script>
        let clerk = null;
        const publishableKey = document.getElementById('clerkKey').dataset.publishableKey;

        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function loadClerk() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://useful-sailfish-19.clerk.accounts.dev/npm/@clerk/clerk-js@5/dist/clerk.browser.js';
                script.setAttribute('data-clerk-publishable-key', publishableKey);
                script.setAttribute('crossorigin', 'anonymous');
                script.setAttribute('async', 'true');

                script.onload = () => {
                    setTimeout(() => {
                        if (window.Clerk) {
                            clerk = window.Clerk;
                            resolve();
                        } else {
                            reject(new Error('Clerk not available after loading'));
                        }
                    }, 1000);
                };

                script.onerror = () => reject(new Error('Failed to load Clerk script'));
                document.head.appendChild(script);
            });
        }

        async function testClerk() {
            try {
                showStatus('Loading Clerk...', 'info');
                await loadClerk();

                showStatus('Clerk loaded successfully!', 'success');

                // Initialize Clerk properly
                if (clerk.load && typeof clerk.load === 'function') {
                    showStatus('Initializing Clerk...', 'info');
                    try {
                        await clerk.load();
                        showStatus('Clerk initialized successfully!', 'success');
                    } catch (loadError) {
                        showStatus('Error initializing Clerk: ' + loadError.message, 'error');
                        return;
                    }
                }

                document.getElementById('testButton').textContent = 'Clerk Loaded ✓';
                document.getElementById('signInButton').disabled = false;

                // Check if user is already signed in
                if (clerk.client && clerk.client.user) {
                    showUserInfo(clerk.client.user);
                }

            } catch (error) {
                showStatus('Error loading Clerk: ' + error.message, 'error');
            }
        }

        async function signIn() {
            const button = document.getElementById('signInButton');
            const originalText = button.textContent;
            button.textContent = 'Opening...';
            button.disabled = true;

            try {
                showStatus('Opening sign in...', 'info');
                await clerk.openSignIn();
                showStatus('Sign in opened successfully!', 'success');
            } catch (error) {
                console.error('Error opening sign in:', error);
                showStatus('Error opening sign in: ' + error.message, 'error');
            }

            // Always reset the button after a short delay to handle modal close
            setTimeout(() => {
                button.textContent = originalText;
                button.disabled = false;
            }, 1000);
        }

        function showUserInfo(user) {
            const userInfo = document.getElementById('userInfo');
            const userDetails = document.getElementById('userDetails');

            userDetails.innerHTML = `
                <p><strong>Name:</strong> ${user.fullName || 'N/A'}</p>
                <p><strong>Email:</strong> ${user.primaryEmailAddress?.emailAddress || 'N/A'}</p>
                <p><strong>ID:</strong> ${user.id}</p>
            `;

            userInfo.style.display = 'block';
        }

        // Auto-test on page load
        window.onload = testClerk;
    </script>
</body>
</html>
