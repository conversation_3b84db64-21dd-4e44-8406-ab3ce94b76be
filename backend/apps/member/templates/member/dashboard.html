{% extends "member/base.html" %}
{% load member_tags %}

{% block title %}Member Dashboard{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Welcome, {{ user.get_full_name }}</h1>
        <p class="text-gray-600 mt-2">Your personalized dashboard based on your member type</p>
    </div>

    <!-- Member Type Information -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Your Member Type</h2>
        {% if user.member_type %}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <h3 class="text-lg font-medium text-gray-900">{{ user.member_type.name }}</h3>
                <p class="text-gray-600">{{ user.member_type.description }}</p>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    {{ user.member_type.slug }}
                </span>
            </div>
        </div>
        {% else %}
        <p class="text-gray-500">No member type assigned</p>
        {% endif %}
    </div>

    <!-- Dashboard Layout -->
    {% if dashboard_layout %}
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {% for widget in dashboard_layout.layout %}
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ widget.widget|title }} Widget</h3>
            <div class="text-gray-600">
                <p><strong>Position:</strong> {{ widget.position }}</p>
                <p><strong>Size:</strong> {{ widget.size }}</p>
                <div class="mt-4 p-4 bg-gray-50 rounded">
                    <p class="text-sm text-gray-500">Widget content would be rendered here based on the configuration.</p>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Theme Settings -->
    {% if theme_settings %}
    <div class="bg-white shadow rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Theme Settings</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            {% if theme_settings.primary_color %}
            <div>
                <label class="block text-sm font-medium text-gray-700">Primary Color</label>
                <div class="mt-1 flex items-center">
                    <div class="w-6 h-6 rounded border" style="background-color: {{ theme_settings.primary_color }};"></div>
                    <span class="ml-2 text-sm text-gray-600">{{ theme_settings.primary_color }}</span>
                </div>
            </div>
            {% endif %}

            {% if theme_settings.accent_color %}
            <div>
                <label class="block text-sm font-medium text-gray-700">Accent Color</label>
                <div class="mt-1 flex items-center">
                    <div class="w-6 h-6 rounded border" style="background-color: {{ theme_settings.accent_color }};"></div>
                    <span class="ml-2 text-sm text-gray-600">{{ theme_settings.accent_color }}</span>
                </div>
            </div>
            {% endif %}

            {% if theme_settings.dark_mode is not None %}
            <div>
                <label class="block text-sm font-medium text-gray-700">Dark Mode</label>
                <span class="mt-1 text-sm text-gray-600">
                    {% if theme_settings.dark_mode %}Enabled{% else %}Disabled{% endif %}
                </span>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- User Features -->
    {% if user_features %}
    <div class="bg-white shadow rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Your Available Features</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            {% for feature in user_features %}
            <div class="bg-gray-50 rounded-lg p-3">
                <div class="text-sm font-medium text-gray-900">{{ feature|title }}</div>
                <div class="text-xs text-gray-500 mt-1">Feature enabled</div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Navigation Menu -->
    {% if user_navigation %}
    <div class="bg-white shadow rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Your Navigation Menu</h2>
        <div class="space-y-2">
            {% for item in user_navigation %}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div class="flex items-center">
                    {% if item.icon %}
                    <span class="text-gray-400 mr-3">
                        <i class="fas fa-{{ item.icon }}"></i>
                    </span>
                    {% endif %}
                    <span class="font-medium text-gray-900">{{ item.label }}</span>
                </div>
                <span class="text-sm text-gray-500">{{ item.url }}</span>
            </div>
            {% if item.children %}
            <div class="ml-6 space-y-1">
                {% for child in item.children %}
                <div class="flex items-center justify-between p-2 bg-gray-25 rounded">
                    <span class="text-sm text-gray-700">{{ child.label }}</span>
                    <span class="text-xs text-gray-500">{{ child.url }}</span>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            {% if user|has_permission:"member.view" %}
            <a href="{% url 'member:member_list' %}"
               class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="flex-shrink-0">
                    <i class="fas fa-users text-blue-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">View Members</p>
                    <p class="text-xs text-gray-500">Browse all members</p>
                </div>
            </a>
            {% endif %}

            {% if user|has_permission:"member.create" %}
            <a href="{% url 'member:member_create' %}"
               class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="flex-shrink-0">
                    <i class="fas fa-user-plus text-green-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Create Member</p>
                    <p class="text-xs text-gray-500">Add new member</p>
                </div>
            </a>
            {% endif %}

            {% if user|has_feature:"advanced_search" %}
            <a href="{% url 'member:advanced_search' %}"
               class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="flex-shrink-0">
                    <i class="fas fa-search text-purple-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Advanced Search</p>
                    <p class="text-xs text-gray-500">Search with filters</p>
                </div>
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- JavaScript for dynamic features -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Example of how you might use the dashboard layout data
    const dashboardLayout = {{ dashboard_layout|safe }};
    const themeSettings = {{ theme_settings|safe }};

    console.log('Dashboard Layout:', dashboardLayout);
    console.log('Theme Settings:', themeSettings);

    // You could apply theme settings dynamically here
    if (themeSettings.primary_color) {
        document.documentElement.style.setProperty('--primary-color', themeSettings.primary_color);
    }
});
</script>
{% endblock %}
