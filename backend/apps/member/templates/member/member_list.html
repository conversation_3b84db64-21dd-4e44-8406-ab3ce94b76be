{% extends 'member/base.html' %}

{% block title %}Member Management{% endblock %}

{% block content %}
<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Member Management</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Manage all members in the system.
                </p>
            </div>
            <a href="{% url 'member:member_create' %}" class="btn-primary">
                Create New Member
            </a>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <form method="get" class="flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                <label for="search" class="block text-sm font-medium text-gray-700">Search Members</label>
                <input type="text" name="search" id="search" value="{{ search }}"
                       class="form-input mt-1" placeholder="Search by name, email, or username...">
            </div>
            <div class="sm:w-48">
                <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                <select name="status" id="status" class="form-select mt-1">
                    <option value="">All Statuses</option>
                    <option value="1" {% if status_filter == '1' %}selected{% endif %}>Active</option>
                    <option value="0" {% if status_filter == '0' %}selected{% endif %}>Inactive</option>
                    <option value="2" {% if status_filter == '2' %}selected{% endif %}>Pending</option>
                    <option value="3" {% if status_filter == '3' %}selected{% endif %}>Suspended</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="submit" class="btn-primary">
                    Search
                </button>
                {% if search or status_filter %}
                    <a href="{% url 'member:member_list' %}" class="btn-secondary ml-2">
                        Clear
                    </a>
                {% endif %}
            </div>
        </form>
    </div>

    <!-- Member List -->
    <div class="border-t border-gray-200">
        {% if page_obj %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Member
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Email
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Class
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Created
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for member in page_obj %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                            <span class="text-sm font-medium text-gray-700">
                                                {{ member.first_name|first }}{{ member.last_name|first }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ member.get_full_name }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            @{{ member.username }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ member.email }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {% if member.member_type %}
                                        {{ member.member_type.name }}
                                    {% else %}
                                        <span class="text-gray-400">-</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    {% if member.status == 1 %}bg-green-100 text-green-800
                                    {% elif member.status == 0 %}bg-red-100 text-red-800
                                    {% elif member.status == 2 %}bg-yellow-100 text-yellow-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {% if member.status == 1 %}Active
                                    {% elif member.status == 0 %}Inactive
                                    {% elif member.status == 2 %}Pending
                                    {% else %}Suspended{% endif %}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ member.created|date:"M d, Y" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{% url 'member:member_detail' member.pk %}" class="text-blue-600 hover:text-blue-900 mr-3">
                                    View
                                </a>
                                <a href="{% url 'member:member_update' member.pk %}" class="text-indigo-600 hover:text-indigo-900">
                                    Edit
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No members found</h3>
                <p class="mt-1 text-sm text-gray-500">
                    {% if search or status_filter %}
                        Try adjusting your search criteria.
                    {% else %}
                        Get started by creating a new member.
                    {% endif %}
                </p>
                <div class="mt-6">
                    <a href="{% url 'member:member_create' %}" class="btn-primary">
                        Create New Member
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
