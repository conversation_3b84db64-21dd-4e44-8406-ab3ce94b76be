<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clerk Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .primary { background: #007bff; color: white; }
        .secondary { background: #6c757d; color: white; }
        .debug {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Clerk Debug Test</h1>

    <div id="clerkKey" data-publishable-key="{{ clerk_publishable_key }}" style="display: none;"></div>

    <div id="status"></div>

    <button id="testButton" class="primary" onclick="testClerk()">Test Clerk Loading</button>
    <button id="signInButton" class="secondary" onclick="signIn()" disabled>Sign In</button>
    <button id="debugButton" class="secondary" onclick="debugClerk()" disabled>Debug Clerk</button>

    <div id="debugInfo" class="debug" style="display: none;">
        <h3>Debug Information</h3>
        <div id="debugDetails"></div>
    </div>

    <script>
        let clerk = null;
        const publishableKey = document.getElementById('clerkKey').dataset.publishableKey;

        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function showDebug(message) {
            const debugInfo = document.getElementById('debugInfo');
            const debugDetails = document.getElementById('debugDetails');
            debugDetails.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
            debugInfo.style.display = 'block';
        }

        async function loadClerk() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://useful-sailfish-19.clerk.accounts.dev/npm/@clerk/clerk-js@5/dist/clerk.browser.js';
                script.setAttribute('data-clerk-publishable-key', publishableKey);
                script.setAttribute('crossorigin', 'anonymous');
                script.setAttribute('async', 'true');

                script.onload = () => {
                    showDebug('Script loaded successfully');
                    setTimeout(() => {
                        if (window.Clerk) {
                            clerk = window.Clerk;
                            showDebug('Clerk object found: ' + typeof clerk);
                            resolve();
                        } else {
                            showDebug('Clerk object not found after loading');
                            reject(new Error('Clerk not available after loading'));
                        }
                    }, 1000);
                };

                script.onerror = () => {
                    showDebug('Script failed to load');
                    reject(new Error('Failed to load Clerk script'));
                };
                document.head.appendChild(script);
            });
        }

        async function testClerk() {
            try {
                showStatus('Loading Clerk...', 'info');
                showDebug('Starting Clerk load test');
                await loadClerk();

                showStatus('Clerk loaded successfully!', 'success');
                showDebug('Clerk loaded, checking properties...');

                // Try to initialize Clerk if load method exists
                if (clerk.load && typeof clerk.load === 'function') {
                    showDebug('Calling clerk.load()...');
                    try {
                        await clerk.load();
                        showDebug('✓ clerk.load() completed');
                    } catch (loadError) {
                        showDebug('✗ clerk.load() failed: ' + loadError.message);
                    }
                }

                // Debug: Check what's available
                const clerkProps = Object.keys(clerk).filter(key => !key.startsWith('_'));
                showDebug('Available Clerk properties: ' + clerkProps.join(', '));

                // Check for specific methods
                const methods = ['openSignIn', 'signOut', 'user', 'session', 'assertComponentsReady'];
                methods.forEach(method => {
                    if (clerk[method]) {
                        showDebug(`✓ ${method} is available`);
                    } else {
                        showDebug(`✗ ${method} is NOT available`);
                    }
                });

                // Check if user and session are available through client
                if (clerk.client) {
                    showDebug('✓ client is available');
                    if (clerk.client.user) {
                        showDebug('✓ client.user is available');
                    } else {
                        showDebug('✗ client.user is NOT available');
                    }
                    if (clerk.client.session) {
                        showDebug('✓ client.session is available');
                    } else {
                        showDebug('✗ client.session is NOT available');
                    }
                } else {
                    showDebug('✗ client is NOT available');
                }

                document.getElementById('testButton').textContent = 'Clerk Loaded ✓';
                document.getElementById('signInButton').disabled = false;
                document.getElementById('debugButton').disabled = false;

                // Check if user is already signed in
                if (clerk.user) {
                    showDebug('User already signed in: ' + clerk.user.id);
                }

            } catch (error) {
                showStatus('Error loading Clerk: ' + error.message, 'error');
                showDebug('Error: ' + error.message);
            }
        }

        async function signIn() {
            try {
                showStatus('Opening sign in...', 'info');
                showDebug('Attempting to open sign in...');
                await clerk.openSignIn();
                showDebug('Sign in opened successfully');
            } catch (error) {
                showStatus('Error opening sign in: ' + error.message, 'error');
                showDebug('Sign in error: ' + error.message);
            }
        }

        function debugClerk() {
            showDebug('=== CLERK DEBUG INFO ===');
            showDebug('Clerk type: ' + typeof clerk);
            showDebug('Clerk value: ' + JSON.stringify(clerk, null, 2).substring(0, 500) + '...');

            // Check window object
            const windowClerkProps = Object.keys(window).filter(key => key.toLowerCase().includes('clerk'));
            showDebug('Window Clerk properties: ' + windowClerkProps.join(', '));

            // Check if components are ready
            if (clerk.assertComponentsReady) {
                try {
                    clerk.assertComponentsReady();
                    showDebug('✓ Components are ready');
                } catch (error) {
                    showDebug('✗ Components not ready: ' + error.message);
                }
            } else {
                showDebug('✗ assertComponentsReady method not available');
            }

            // Check for other Clerk methods
            const testMethods = ['openSignIn', 'signOut', 'user', 'session', 'loadClerkManually'];
            testMethods.forEach(method => {
                if (typeof clerk[method] === 'function') {
                    showDebug(`✓ ${method} is a function`);
                } else if (clerk[method] !== undefined) {
                    showDebug(`✓ ${method} exists (not a function)`);
                } else {
                    showDebug(`✗ ${method} does not exist`);
                }
            });

            // Check client properties
            if (clerk.client) {
                showDebug('=== CLIENT PROPERTIES ===');
                const clientProps = Object.keys(clerk.client).filter(key => !key.startsWith('_'));
                showDebug('Client properties: ' + clientProps.join(', '));

                if (clerk.client.user) {
                    showDebug('✓ client.user exists');
                    showDebug('User ID: ' + clerk.client.user.id);
                } else {
                    showDebug('✗ client.user does not exist');
                }

                if (clerk.client.session) {
                    showDebug('✓ client.session exists');
                    showDebug('Session ID: ' + clerk.client.session.id);
                } else {
                    showDebug('✗ client.session does not exist');
                }
            }
        }

        // Auto-test on page load
        window.onload = testClerk;
    </script>
</body>
</html>
