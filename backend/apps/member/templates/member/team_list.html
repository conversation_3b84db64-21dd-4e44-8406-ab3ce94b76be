{% extends "member/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">{{ page_title }}</h1>
        <a href="{% url 'member:member_list' %}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Back to Members
        </a>
    </div>

    {% if teams %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for team in teams %}
            <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">
                                <a href="{% url 'member:team_detail' team.id %}" class="hover:text-blue-600">
                                    {{ team.name }}
                                </a>
                            </h3>
                            <p class="text-sm text-gray-600 mb-2">{{ team.get_team_type_display }}</p>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ team.member_count }} members
                        </span>
                    </div>

                    {% if team.description %}
                        <p class="text-gray-700 text-sm mb-4">{{ team.description|truncatewords:20 }}</p>
                    {% endif %}

                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-500">
                            {% if team.team_lead %}
                                <span class="font-medium">Lead:</span> {{ team.team_lead }}
                            {% else %}
                                <span class="text-gray-400">No lead assigned</span>
                            {% endif %}
                        </div>

                        <div class="flex space-x-2">
                            <a href="{% url 'member:team_detail' team.id %}"
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-12">
            <div class="text-gray-500 mb-4">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Teams Found</h3>
            <p class="text-gray-500">No teams have been created yet.</p>
        </div>
    {% endif %}
</div>
{% endblock %}
