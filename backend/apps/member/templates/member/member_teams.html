{% extends "member/base.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Teams for {{ member }}</h1>
            <p class="text-gray-600 mt-2">{{ memberships.count }} team memberships</p>
        </div>
        <div class="flex space-x-3">
            <a href="{% url 'member:member_detail' member.id %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Member
            </a>
            <a href="{% url 'member:team_list' %}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                View All Teams
            </a>
        </div>
    </div>

    {% if memberships %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for membership in memberships %}
            <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">
                                <a href="{% url 'member:team_detail' membership.team.id %}" class="hover:text-blue-600">
                                    {{ membership.team.name }}
                                </a>
                            </h3>
                            <p class="text-sm text-gray-600 mb-2">{{ membership.team.get_team_type_display }}</p>
                        </div>
                        <div class="flex flex-col items-end space-y-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{ membership.get_role_display }}
                            </span>
                            {% if membership.is_primary %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Primary
                                </span>
                            {% endif %}
                        </div>
                    </div>

                    {% if membership.team.description %}
                        <p class="text-gray-700 text-sm mb-4">{{ membership.team.description|truncatewords:15 }}</p>
                    {% endif %}

                    <div class="space-y-2 text-sm text-gray-600">
                        {% if membership.start_date %}
                            <div><span class="font-medium">Started:</span> {{ membership.start_date }}</div>
                        {% endif %}
                        {% if membership.end_date %}
                            <div><span class="font-medium">Ends:</span> {{ membership.end_date }}</div>
                        {% endif %}
                        {% if membership.notes %}
                            <div><span class="font-medium">Notes:</span> {{ membership.notes|truncatewords:8 }}</div>
                        {% endif %}
                    </div>

                    <div class="mt-4 flex justify-between items-center">
                        <div class="text-sm text-gray-500">
                            {% if membership.team.team_lead %}
                                <span class="font-medium">Lead:</span> {{ membership.team.team_lead }}
                            {% else %}
                                <span class="text-gray-400">No lead</span>
                            {% endif %}
                        </div>

                        <div class="flex space-x-2">
                            <a href="{% url 'member:team_detail' membership.team.id %}"
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                View Team
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Summary -->
        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Team Membership Summary</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ memberships.count }}</div>
                    <div class="text-sm text-gray-600">Total Teams</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">
                        {{ memberships|dictsort:"is_primary"|first|yesno:"1,0" }}
                    </div>
                    <div class="text-sm text-gray-600">Primary Teams</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">
                        {{ memberships|dictsort:"role"|length }}
                    </div>
                    <div class="text-sm text-gray-600">Different Roles</div>
                </div>
            </div>
        </div>
    {% else %}
        <div class="text-center py-12">
            <div class="text-gray-500 mb-4">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Team Memberships</h3>
            <p class="text-gray-500">{{ member }} is not a member of any teams yet.</p>
        </div>
    {% endif %}
</div>
{% endblock %}
