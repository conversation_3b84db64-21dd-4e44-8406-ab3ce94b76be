{% extends "member/base.html" %}
{% load member_tags %}

{% block title %}{{ member }} - Hierarchy{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">{{ member }} - Hierarchy</h1>
        <p class="text-gray-600 mt-2">Organizational relationships and reporting structure</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Managers Section -->
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="h-5 w-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Managers ({{ managers.count }})
            </h2>

            {% if managers %}
                <div class="space-y-3">
                    {% for relationship in managers %}
                        <div class="border border-gray-200 rounded-lg p-4 {% if relationship.is_primary %}bg-blue-50 border-blue-200{% endif %}">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    {% if relationship.is_primary %}
                                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full mr-2">
                                            Primary
                                        </span>
                                    {% endif %}
                                    <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2 py-1 rounded-full mr-2">
                                        {{ relationship.get_relationship_type_display }}
                                    </span>
                                </div>
                                <div class="text-sm text-gray-500">
                                    {% if relationship.is_active %}
                                        <span class="text-green-600">Active</span>
                                    {% else %}
                                        <span class="text-red-600">Inactive</span>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mt-2">
                                <a href="{% url 'member:member_detail' relationship.manager.id %}"
                                   class="text-lg font-medium text-gray-900 hover:text-blue-600">
                                    {{ relationship.manager }}
                                </a>
                                <p class="text-sm text-gray-600">{{ relationship.manager.email }}</p>
                            </div>

                            {% if relationship.start_date or relationship.end_date %}
                                <div class="mt-2 text-sm text-gray-500">
                                    {% if relationship.start_date %}
                                        <span>Started: {{ relationship.start_date }}</span>
                                    {% endif %}
                                    {% if relationship.end_date %}
                                        <span class="ml-2">Ended: {{ relationship.end_date }}</span>
                                    {% endif %}
                                </div>
                            {% endif %}

                            {% if relationship.notes %}
                                <div class="mt-2 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                                    {{ relationship.notes }}
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8 text-gray-500">
                    <svg class="h-12 w-12 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    <p>No managers assigned</p>
                </div>
            {% endif %}
        </div>

        <!-- Subordinates Section -->
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                </svg>
                Subordinates ({{ subordinates.count }})
            </h2>

            {% if subordinates %}
                <div class="space-y-3">
                    {% for relationship in subordinates %}
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2 py-1 rounded-full">
                                    {{ relationship.get_relationship_type_display }}
                                </span>
                                <div class="text-sm text-gray-500">
                                    {% if relationship.is_active %}
                                        <span class="text-green-600">Active</span>
                                    {% else %}
                                        <span class="text-red-600">Inactive</span>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mt-2">
                                <a href="{% url 'member:member_detail' relationship.member.id %}"
                                   class="text-lg font-medium text-gray-900 hover:text-blue-600">
                                    {{ relationship.member }}
                                </a>
                                <p class="text-sm text-gray-600">{{ relationship.member.email }}</p>
                            </div>

                            {% if relationship.start_date or relationship.end_date %}
                                <div class="mt-2 text-sm text-gray-500">
                                    {% if relationship.start_date %}
                                        <span>Started: {{ relationship.start_date }}</span>
                                    {% endif %}
                                    {% if relationship.end_date %}
                                        <span class="ml-2">Ended: {{ relationship.end_date }}</span>
                                    {% endif %}
                                </div>
                            {% endif %}

                            {% if relationship.notes %}
                                <div class="mt-2 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                                    {{ relationship.notes }}
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8 text-gray-500">
                    <svg class="h-12 w-12 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    <p>No subordinates assigned</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Hierarchy Tree Section -->
    {% if hierarchy_tree %}
        <div class="mt-8 bg-white shadow rounded-lg p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="h-5 w-5 text-purple-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                </svg>
                Organization Tree
            </h2>

            <div class="bg-gray-50 rounded-lg p-4">
                <div class="text-sm text-gray-600 mb-4">
                    This shows the hierarchical structure starting from {{ member }}
                </div>

                <div class="space-y-2">
                    {% include "member/hierarchy_tree.html" with tree=hierarchy_tree %}
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Action Buttons -->
    <div class="mt-8 flex space-x-4">
        <a href="{% url 'member:member_detail' member.id %}"
           class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
            Back to Member Details
        </a>
        <a href="{% url 'member:organization_chart' %}"
           class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
            View Full Organization Chart
        </a>
    </div>
</div>
{% endblock %}
