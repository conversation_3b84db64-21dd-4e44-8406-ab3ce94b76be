from django.urls import path

from . import views

app_name = "member"

urlpatterns = [
    # Function-based views
    path("create/", views.member_create, name="member_create"),
    path("update/<int:pk>/", views.member_update, name="member_update"),
    path("list/", views.member_list, name="member_list"),
    path("detail/<int:pk>/", views.member_detail, name="member_detail"),
    # Enhanced member type system views
    path("dashboard/", views.member_dashboard, name="member_dashboard"),
    path("advanced-search/", views.advanced_search, name="advanced_search"),
    path(
        "api/member-type/<int:member_type_id>/",
        views.get_member_type_info,
        name="get_member_type_info",
    ),
    # Hierarchy views
    path("hierarchy/<int:member_id>/", views.member_hierarchy, name="member_hierarchy"),
    path("organization-chart/", views.organization_chart, name="organization_chart"),
    # Test pages for demonstrating access control
    path("admin-only/", views.admin_only_page, name="admin_only_page"),
    path("reports/", views.reports_page, name="reports_page"),
    # Team URLs
    path("teams/", views.team_list, name="team_list"),
    path("teams/<int:team_id>/", views.team_detail, name="team_detail"),
    path("member/<int:member_id>/teams/", views.member_teams, name="member_teams"),
    # Terms and Legal
    path("terms/", views.current_terms, name="current_terms"),
    path("terms/accept/", views.accept_terms, name="accept_terms"),
    # Privacy Policy
    path("privacy/", views.current_privacy, name="current_privacy"),
    path("privacy/accept/", views.accept_privacy, name="accept_privacy"),
    # Clerk Test URL
    path("clerk-test/", views.clerk_login_test, name="clerk_login_test"),
    path("clerk-simple/", views.clerk_simple_test, name="clerk_simple_test"),
    path("clerk-debug/", views.clerk_debug_test, name="clerk_debug_test"),
    path("clerk-auth/", views.clerk_auth_callback, name="clerk_auth_callback"),
    path("clerk-setup/", views.clerk_account_linking, name="clerk_account_linking"),
    path("clerk-logout-test/", views.clerk_logout_test, name="clerk_logout_test"),
    path("django-logout/", views.django_logout, name="django_logout"),
]
