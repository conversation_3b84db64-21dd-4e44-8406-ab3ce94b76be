import json
import logging

import django.db.models as models
from django.contrib import messages
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth import authenticate, login
from django.contrib.auth.backends import ModelBackend
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render

from .decorators import require_feature, require_page_access, require_permission
from .forms import MemberCreationForm, MemberUpdateForm
from .models import Member, MemberType, Privacy, Region, Team, Terms

logger = logging.getLogger(__name__)


def handle_view_exception(exception, context="view operation"):
    """
    Securely handle view exceptions by logging the full error but returning generic messages.

    Args:
        exception: The caught exception
        context: String describing the operation that failed

    Returns:
        JsonResponse with generic error message
    """
    logger.exception(f"Unexpected error in {context}: {str(exception)}")
    return JsonResponse(
        {
            "success": False,
            "error": "An unexpected error occurred. Please try again later.",
        }
    )


@login_required
@require_permission("member.view")
def member_list(request):
    """List all members with permission checking"""
    members = Member.objects.all().order_by("email")

    # Apply search filter if provided
    search = request.GET.get("search", "")
    if search:
        members = members.filter(
            Q(email__icontains=search)
            | Q(username__icontains=search)
            | Q(first_name__icontains=search)
            | Q(last_name__icontains=search)
        )

    # Apply member type filter if provided
    member_type_filter = request.GET.get("member_type", "")
    if member_type_filter:
        members = members.filter(member_type__slug=member_type_filter)

    # Apply status filter if provided
    status_filter = request.GET.get("status", "")
    if status_filter:
        members = members.filter(status=status_filter)

    # Pagination
    paginator = Paginator(members, 20)
    page_number = request.GET.get("page")
    page_obj = paginator.get_page(page_number)

    # Get available member types for filter
    member_types = MemberType.objects.filter(is_active=True).order_by("order", "name")

    context = {
        "page_obj": page_obj,
        "search": search,
        "member_type_filter": member_type_filter,
        "status_filter": status_filter,
        "member_types": member_types,
        "user_navigation": request.user.get_navigation(),
        "user_features": request.user.get_member_type_features(),
    }

    return render(request, "member/member_list.html", context)


@login_required
@require_permission("member.create")
@require_page_access("member-create")
def member_create(request):
    """Create a new member with permission checking"""
    if request.method == "POST":
        form = MemberCreationForm(request.POST)
        if form.is_valid():
            member = form.save()
            messages.success(
                request, f'Member "{member.get_full_name()}" was created successfully!'
            )
            return redirect("member:member_detail", pk=member.pk)
    else:
        form = MemberCreationForm()

    # Get available member types for the form
    member_types = MemberType.objects.filter(is_active=True, can_signup=True).order_by(
        "order", "name"
    )

    context = {
        "form": form,
        "member_types": member_types,
        "title": "Create New Member",
        "submit_text": "Create Member",
        "user_navigation": request.user.get_navigation(),
    }

    return render(request, "member/member_form.html", context)


@login_required
@require_permission("member.view")
def member_detail(request, pk):
    """View member details with permission checking"""
    member = get_object_or_404(Member, pk=pk)

    # Check if user can view this specific member
    if not request.user.has_permission("member.view"):
        messages.error(request, "You do not have permission to view member details.")
        return redirect("member:member_list")

    context = {
        "member": member,
        "user_navigation": request.user.get_navigation(),
        "can_edit": request.user.has_permission("member.edit"),
        "can_delete": request.user.has_permission("member.delete"),
        "title": f"Member Details: {member.get_full_name()}",
    }

    return render(request, "member/member_detail.html", context)


@login_required
@require_permission("member.edit")
@require_page_access("member-update")
def member_update(request, pk):
    """Update member with permission checking"""
    member = get_object_or_404(Member, pk=pk)

    if request.method == "POST":
        form = MemberUpdateForm(request.POST, instance=member)
        if form.is_valid():
            member = form.save()
            messages.success(
                request, f'Member "{member.get_full_name()}" was updated successfully!'
            )
            return redirect("member:member_detail", pk=member.pk)
    else:
        form = MemberUpdateForm(instance=member)

    # Get available member types for the form
    member_types = MemberType.objects.filter(is_active=True).order_by("order", "name")

    context = {
        "form": form,
        "member": member,
        "member_types": member_types,
        "title": f"Update Member: {member.get_full_name()}",
        "submit_text": "Update Member",
        "user_navigation": request.user.get_navigation(),
    }

    return render(request, "member/member_form.html", context)


@login_required
@require_feature("advanced_search")
def advanced_search(request):
    """Advanced search feature - requires specific feature flag"""
    if request.method == "POST":
        # Handle advanced search
        # TODO: Implement advanced search functionality
        pass

    context = {
        "user_navigation": request.user.get_navigation(),
        "search_features": request.user.get_member_type_features(),
    }

    return render(request, "member/advanced_search.html", context)


@login_required
def member_dashboard(request):
    """Member dashboard with dynamic layout based on member type"""
    # Get dashboard layout from member type
    dashboard_layout = request.user.get_dashboard_layout()
    theme_settings = request.user.get_theme_settings()

    context = {
        "dashboard_layout": dashboard_layout,
        "theme_settings": theme_settings,
        "user_navigation": request.user.get_navigation(),
        "user_features": request.user.get_member_type_features(),
    }

    return render(request, "member/dashboard.html", context)


# API endpoint to get member type information
@login_required
def get_member_type_info(request, member_type_id):
    """Get member type information for AJAX requests"""
    member_type = get_object_or_404(MemberType, id=member_type_id)

    return JsonResponse(
        {
            "id": member_type.id,
            "name": member_type.name,
            "description": member_type.description,
            "permissions": list(member_type.get_permissions()),
            "features": list(member_type.get_feature_flags()),
            "navigation": member_type.get_navigation(),
            "page_access": member_type.get_page_access(),
        }
    )


@login_required
@require_permission("admin.access")
def admin_only_page(request):
    """Test page that only administrators can access"""
    context = {
        "user_navigation": request.user.get_navigation(),
        "user_features": request.user.get_member_type_features(),
    }
    return render(request, "member/admin_only_page.html", context)


@login_required
@require_feature("reports")
def reports_page(request):
    """Test page that requires the 'reports' feature flag"""
    context = {
        "user_navigation": request.user.get_navigation(),
        "user_features": request.user.get_member_type_features(),
    }
    return render(request, "member/reports_page.html", context)


@login_required
def member_hierarchy(request, member_id):
    """Display hierarchy information for a specific member"""
    try:
        member = Member.objects.get(id=member_id)
    except Member.DoesNotExist:
        messages.error(request, "Member not found.")
        return redirect("member:member_list")

    context = {
        "member": member,
        "managers": member.get_all_managers(),
        "subordinates": member.get_subordinates(),
        "hierarchy_tree": member.get_hierarchy_tree(max_depth=6),
        "user_navigation": request.user.get_navigation(),
        "user_features": request.user.get_member_type_features(),
    }
    return render(request, "member/member_hierarchy.html", context)


@login_required
def organization_chart(request):
    """Display organization chart for all members"""
    # Get all members with hierarchy relationships
    members_with_hierarchy = Member.objects.filter(
        models.Q(managers__isnull=False) | models.Q(subordinates__isnull=False)
    ).distinct()

    # Build organization tree starting from top-level (those with no managers)
    top_level = []
    for member in members_with_hierarchy:
        if not member.get_all_managers().exists():
            top_level.append(member)

    context = {
        "top_level_members": top_level,
        "all_members": members_with_hierarchy,
        "user_navigation": request.user.get_navigation(),
        "user_features": request.user.get_member_type_features(),
    }
    return render(request, "member/organization_chart.html", context)


@login_required
def team_list(request):
    """Display all teams with member counts"""
    teams = (
        Team.objects.filter(is_active=True)
        .annotate(member_count=models.Count("member_teams"))
        .order_by("name")
    )

    context = {
        "teams": teams,
        "page_title": "Teams",
        "user_navigation": request.user.get_navigation(),
        "user_features": request.user.get_member_type_features(),
    }
    return render(request, "member/team_list.html", context)


@login_required
def team_detail(request, team_id):
    """Display team details with all members"""
    try:
        team = Team.objects.get(id=team_id)
        memberships = team.get_memberships(active_only=True).select_related("member")

        # Group members by role
        members_by_role = {}
        for membership in memberships:
            role = membership.get_role_display()
            if role not in members_by_role:
                members_by_role[role] = []
            members_by_role[role].append(membership)

        context = {
            "team": team,
            "memberships": memberships,
            "members_by_role": members_by_role,
            "page_title": f"Team: {team.name}",
            "user_navigation": request.user.get_navigation(),
            "user_features": request.user.get_member_type_features(),
        }
        return render(request, "member/team_detail.html", context)
    except Team.DoesNotExist:
        messages.error(request, "Team not found.")
        return redirect("member:team_list")


@login_required
def member_teams(request, member_id):
    """Display all teams a member belongs to"""
    try:
        member = Member.objects.get(id=member_id)
        memberships = member.get_team_memberships(active_only=True).select_related(
            "team"
        )

        context = {
            "member": member,
            "memberships": memberships,
            "page_title": f"Teams for {member}",
            "user_navigation": request.user.get_navigation(),
            "user_features": request.user.get_member_type_features(),
        }
        return render(request, "member/member_teams.html", context)
    except Member.DoesNotExist:
        messages.error(request, "Member not found.")
        return redirect("member:member_list")


def current_terms(request):
    """Display the current terms version"""
    try:
        current_terms = Terms.objects.filter(current=True).first()
        if not current_terms:
            # If no current terms, get the most recent one
            current_terms = Terms.objects.order_by("-created").first()

        # Check if user has accepted the current terms
        has_accepted = False
        if request.user.is_authenticated:
            has_accepted = request.user.has_accepted_terms(current_terms)

        context = {
            "terms": current_terms,
            "has_accepted": has_accepted,
            "user_navigation": (
                request.user.get_navigation() if request.user.is_authenticated else []
            ),
            "user_features": (
                request.user.get_member_type_features()
                if request.user.is_authenticated
                else {}
            ),
        }

        return render(request, "member/current_terms.html", context)

    except Exception:
        logger.exception("Error loading current terms")
        context = {
            "error": "Unable to load terms. Please try again later.",
            "user_navigation": (
                request.user.get_navigation() if request.user.is_authenticated else []
            ),
            "user_features": (
                request.user.get_member_type_features()
                if request.user.is_authenticated
                else {}
            ),
        }
        return render(request, "member/current_terms.html", context)


@login_required
def accept_terms(request):
    """Handle terms acceptance"""
    if request.method == "POST":
        try:
            # Get current terms
            current_terms = Terms.objects.filter(current=True).first()
            if not current_terms:
                messages.error(request, "No current terms available to accept.")
                return redirect("member:current_terms")

            # Accept the terms
            log, created = request.user.accept_terms(current_terms)

            if created:
                messages.success(
                    request,
                    f"You have successfully accepted the terms and conditions ({current_terms.title}).",
                )
            else:
                messages.info(
                    request,
                    f'You have already accepted these terms on {log.accepted_at.strftime("%B %d, %Y")}.',
                )

            return redirect("member:current_terms")

        except Exception:
            logger.exception("Error accepting terms")
            messages.error(request, "Error accepting terms. Please try again later.")
            return redirect("member:current_terms")

    # If not POST, redirect to terms page
    return redirect("member:current_terms")


def current_privacy(request):
    """Display the current privacy policy version"""
    try:
        current_privacy = Privacy.objects.filter(current=True).first()
        if not current_privacy:
            # If no current privacy, get the most recent one
            current_privacy = Privacy.objects.order_by("-created").first()

        # Check if user has accepted the current privacy policy
        has_accepted = False
        if request.user.is_authenticated:
            has_accepted = request.user.has_accepted_privacy(current_privacy)

        context = {
            "privacy": current_privacy,
            "has_accepted": has_accepted,
            "user_navigation": (
                request.user.get_navigation() if request.user.is_authenticated else []
            ),
            "user_features": (
                request.user.get_member_type_features()
                if request.user.is_authenticated
                else {}
            ),
        }

        return render(request, "member/current_privacy.html", context)

    except Exception:
        logger.exception("Error loading current privacy policy")
        context = {
            "error": "Unable to load privacy policy. Please try again later.",
            "user_navigation": (
                request.user.get_navigation() if request.user.is_authenticated else []
            ),
            "user_features": (
                request.user.get_member_type_features()
                if request.user.is_authenticated
                else {}
            ),
        }
        return render(request, "member/current_privacy.html", context)


@login_required
def accept_privacy(request):
    """Handle privacy policy acceptance"""
    if request.method == "POST":
        try:
            # Get current privacy policy
            current_privacy = Privacy.objects.filter(current=True).first()
            if not current_privacy:
                messages.error(
                    request, "No current privacy policy available to accept."
                )
                return redirect("member:current_privacy")

            # Accept the privacy policy
            log, created = request.user.accept_privacy(current_privacy)

            if created:
                messages.success(
                    request,
                    f"You have successfully accepted the privacy policy ({current_privacy.title}).",
                )
            else:
                messages.info(
                    request,
                    f'You have already accepted this privacy policy on {log.accepted_at.strftime("%B %d, %Y")}.',
                )

            return redirect("member:current_privacy")

        except Exception:
            logger.exception("Error accepting privacy policy")
            messages.error(
                request, "Error accepting privacy policy. Please try again later."
            )
            return redirect("member:current_privacy")

    # If not POST, redirect to privacy page
    return redirect("member:current_privacy")


def clerk_login_test(request):
    """Test page for Clerk authentication"""
    from django.conf import settings

    publishable_key = getattr(settings, "CLERK_PUBLISHABLE_KEY", "")

    # Debug: Check if key is available
    if not publishable_key:
        logger.warning("CLERK_PUBLISHABLE_KEY is not set in environment or settings")

    context = {
        "clerk_publishable_key": publishable_key,
        "debug_info": {
            "key_length": len(publishable_key),
            "key_prefix": publishable_key[:10] + "..." if publishable_key else "None",
        },
    }

    return render(request, "member/clerk_login.html", context)


def clerk_simple_test(request):
    """Simple test page for Clerk authentication"""
    from django.conf import settings

    context = {
        "clerk_publishable_key": getattr(settings, "CLERK_PUBLISHABLE_KEY", ""),
    }

    return render(request, "member/clerk_simple_test.html", context)


def clerk_debug_test(request):
    """Debug test page for Clerk authentication"""
    from django.conf import settings

    context = {
        "clerk_publishable_key": getattr(settings, "CLERK_PUBLISHABLE_KEY", ""),
    }

    return render(request, "member/clerk_debug.html", context)


def clerk_auth_callback(request):
    """Handle Clerk authentication and create/link Django user accounts"""
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            clerk_token = data.get("token")

            if not clerk_token:
                return JsonResponse({"success": False, "error": "No token provided"})

            # Verify the token with Clerk (you'll need to implement this)
            # For now, we'll assume the token is valid and extract user info
            # In production, you should verify the JWT token with Clerk's API

            # Extract user information from the token
            # This is a simplified example - you'll need to decode the JWT properly
            user_info = {
                "clerk_id": data.get("clerk_id"),
                "email": data.get("email"),
                "first_name": data.get("first_name", ""),
                "last_name": data.get("last_name", ""),
                "username": data.get("username", data.get("email")),
            }

            # Check if a Member already exists with this Clerk ID
            try:
                member = Member.objects.get(clerk_id=user_info["clerk_id"])
                # User exists, log them in
                login(
                    request, member, backend="django.contrib.auth.backends.ModelBackend"
                )
                return JsonResponse(
                    {
                        "success": True,
                        "user": {
                            "id": member.id,
                            "username": member.username,
                            "email": member.email,
                            "full_name": member.get_full_name(),
                            "clerk_id": member.clerk_id,
                            "is_active": member.is_active,
                            "is_staff": member.is_staff,
                            "is_superuser": member.is_superuser,
                        },
                    }
                )
            except Member.DoesNotExist:
                # User doesn't exist, create a new one
                # Check if a Member with this email already exists
                try:
                    existing_member = Member.objects.get(email=user_info["email"])
                    # Update the existing member with Clerk ID
                    existing_member.clerk_id = user_info["clerk_id"]
                    existing_member.save()
                    member = existing_member
                except Member.DoesNotExist:
                    # Create a new Member
                    member = Member.objects.create(
                        clerk_id=user_info["clerk_id"],
                        email=user_info["email"],
                        first_name=user_info["first_name"],
                        last_name=user_info["last_name"],
                        username=user_info["username"],
                    )

                # Log the user in
                login(
                    request, member, backend="django.contrib.auth.backends.ModelBackend"
                )

                return JsonResponse(
                    {
                        "success": True,
                        "user": {
                            "id": member.id,
                            "username": member.username,
                            "email": member.email,
                            "full_name": member.get_full_name(),
                            "clerk_id": member.clerk_id,
                            "is_active": member.is_active,
                            "is_staff": member.is_staff,
                            "is_superuser": member.is_superuser,
                        },
                    }
                )

        except json.JSONDecodeError:
            return JsonResponse({"success": False, "error": "Invalid JSON"})
        except Exception as e:
            return handle_view_exception(e, "clerk account operation")

    return JsonResponse({"success": False, "error": "Invalid request method"})


def clerk_account_linking(request):
    """Account setup and linking page for Clerk authentication"""
    from django.conf import settings

    context = {
        "clerk_publishable_key": getattr(settings, "CLERK_PUBLISHABLE_KEY", ""),
    }

    return render(request, "member/clerk_account_linking.html", context)


def clerk_logout_test(request):
    """Test page for Clerk logout functionality"""
    from django.conf import settings

    context = {
        "clerk_publishable_key": getattr(settings, "CLERK_PUBLISHABLE_KEY", ""),
    }

    return render(request, "member/clerk_logout_test.html", context)


def django_logout(request):
    """Handle Django logout and redirect"""
    from django.contrib import messages
    from django.contrib.auth import logout
    from django.shortcuts import redirect

    if request.method == "POST":
        try:
            logout(request)
            return JsonResponse(
                {"success": True, "message": "Successfully logged out from Django"}
            )
        except Exception:
            logger.exception("Error during Django logout")
            return JsonResponse(
                {"success": False, "error": "An internal error has occurred."}
            )

    # Handle GET request - perform logout and redirect
    if request.user.is_authenticated:
        logout(request)
        messages.success(request, "You have been successfully logged out.")
        return redirect("home")
    else:
        messages.info(request, "You are not currently logged in.")
        return redirect("home")
