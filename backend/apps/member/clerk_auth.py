"""
Clerk Authentication Backend for Django
"""

import json
import logging
from datetime import datetime, timedelta

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.backends import BaseBackend
from django.core.exceptions import ValidationError
from django.utils import timezone

import jwt
import requests

User = get_user_model()
logger = logging.getLogger(__name__)


class ClerkAuthenticationBackend(BaseBackend):
    """
    Custom authentication backend for Clerk
    """

    def authenticate(self, request, token=None):
        """
        Authenticate a user using Clerk JWT token
        """
        if not token:
            return None

        try:
            # Verify the JWT token with Clerk
            user_data = self.verify_clerk_token(token)
            if not user_data:
                return None

            # Get or create user from Clerk data
            user = self.get_or_create_user(user_data)
            return user

        except Exception as e:
            logger.error(f"Clerk authentication error: {e}")
            return None

    def get_user(self, user_id):
        """
        Get user by ID
        """
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None

    def verify_clerk_token(self, token):
        """
        Verify JWT token with Clerk
        """
        try:
            # Decode JWT without verification first to get the issuer
            unverified_payload = jwt.decode(token, options={"verify_signature": False})

            # Get the issuer from the token
            issuer = unverified_payload.get("iss", "")

            # Fetch Clerk's public keys
            jwks_url = f"{issuer}/.well-known/jwks.json"
            response = requests.get(jwks_url, timeout=10)
            jwks = response.json()

            # Find the correct key
            kid = unverified_payload.get("kid")
            public_key = None

            for key in jwks["keys"]:
                if key["kid"] == kid:
                    public_key = jwt.algorithms.RSAAlgorithm.from_jwk(json.dumps(key))
                    break

            if not public_key:
                raise ValidationError("Invalid token: no matching key found")

            # Verify the token
            payload = jwt.decode(
                token,
                public_key,
                algorithms=["RS256"],
                audience=unverified_payload.get("aud"),
                issuer=issuer,
            )

            return payload

        except Exception as e:
            logger.error(f"Token verification error: {e}")
            return None

    def get_or_create_user(self, user_data):
        """
        Get or create user from Clerk user data
        """
        clerk_id = user_data.get("sub")  # Clerk user ID

        try:
            # Try to find existing user by Clerk ID
            user = User.objects.get(clerk_id=clerk_id)

            # Update user data from Clerk
            self.update_user_from_clerk(user, user_data)

        except User.DoesNotExist:
            # Create new user from Clerk data
            user = self.create_user_from_clerk(user_data)

        return user

    def create_user_from_clerk(self, user_data):
        """
        Create a new user from Clerk user data
        """
        clerk_id = user_data.get("sub")
        email = user_data.get("email", "")
        first_name = user_data.get("given_name", "")
        last_name = user_data.get("family_name", "")

        # Generate username from email if not provided
        username = user_data.get("preferred_username", "")
        if not username and email:
            username = email.split("@")[0]

        # Ensure unique username
        base_username = username
        counter = 1
        while User.objects.filter(username=username).exists():
            username = f"{base_username}{counter}"
            counter += 1

        # Create user
        user = User.objects.create(
            clerk_id=clerk_id,
            username=username,
            email=email,
            first_name=first_name,
            last_name=last_name,
            is_active=True,
            date_joined=timezone.now(),
        )

        # Set additional fields if available
        if user_data.get("phone_number"):
            user.phone_cell = user_data.get("phone_number")

        if user_data.get("picture"):
            user.profile_image = user_data.get("picture")

        user.save()

        return user

    def update_user_from_clerk(self, user, user_data):
        """
        Update existing user with latest Clerk data
        """
        # Update basic fields
        if user_data.get("email") and user_data.get("email") != user.email:
            user.email = user_data.get("email")

        if (
            user_data.get("given_name")
            and user_data.get("given_name") != user.first_name
        ):
            user.first_name = user_data.get("given_name")

        if (
            user_data.get("family_name")
            and user_data.get("family_name") != user.last_name
        ):
            user.last_name = user_data.get("family_name")

        if (
            user_data.get("phone_number")
            and user_data.get("phone_number") != user.phone_cell
        ):
            user.phone_cell = user_data.get("phone_number")

        if user_data.get("picture") and user_data.get("picture") != user.profile_image:
            user.profile_image = user_data.get("picture")

        user.modified = timezone.now()
        user.save()

        return user


class ClerkMiddleware:
    """
    Middleware to handle Clerk authentication
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Extract token from Authorization header
        auth_header = request.META.get("HTTP_AUTHORIZATION", "")
        if auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]

            # Authenticate user with Clerk
            backend = ClerkAuthenticationBackend()
            user = backend.authenticate(request, token=token)

            if user:
                request.user = user

        response = self.get_response(request)
        return response
