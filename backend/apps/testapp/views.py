import logging
import sys
import time
from datetime import datetime, timedelta

import django
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.cache import cache
from django.db import DatabaseError, connection
from django.shortcuts import get_object_or_404, redirect, render

from .forms import ContactForm, UserRegistrationForm
from .models import Article, Page

# Removed unused imports: reverse_lazy, CreateView


# Import Member and Team models at module level with safe fallback
try:
    from apps.member.models import Member, Team
except ImportError:
    Member = None
    Team = None


def home(request):
    """Enhanced home page with system statistics and status"""
    # Initialize default values
    member_count = 0
    team_count = 0
    active_users = 0
    system_status = {
        "overall": "status-online",
        "database": "status-online",
        "cache": "status-online",
    }

    # Get system statistics if models are available
    if Member and Team:
        try:
            member_count = Member.objects.count()
            team_count = Team.objects.count()

            # Calculate active users (logged in within last 30 days)
            thirty_days_ago = datetime.now() - timedelta(days=30)
            active_users = Member.objects.filter(
                last_login__gte=thirty_days_ago
            ).count()
        except DatabaseError as e:
            logging.getLogger(__name__).error(
                f"Database error getting member statistics: {e}"
            )
            system_status["database"] = "status-offline"
            system_status["overall"] = "status-warning"
    else:
        # Fallback if member models aren't available
        system_status = {
            "overall": "status-warning",
            "database": "status-warning",
            "cache": "status-warning",
        }

    # Test database connection
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
    except DatabaseError as e:
        logging.getLogger(__name__).error(f"Database connection test failed: {e}")
        system_status["database"] = "status-offline"
        system_status["overall"] = "status-warning"

    # Test cache connection
    try:
        cache.set("test_key", "test_value", 1)
        cache.get("test_key")
    except Exception as e:
        logging.getLogger(__name__).error(f"Cache connection test failed: {e}")
        system_status["cache"] = "status-offline"
        system_status["overall"] = "status-warning"

    context = {
        "member_count": member_count,
        "team_count": team_count,
        "active_users": active_users,
        "system_status": system_status,
    }

    return render(request, "testapp/home.html", context)


@login_required
def status(request):
    """Comprehensive system status page with health checks and metrics"""
    logger = logging.getLogger(__name__)

    # Security check: only allow staff members to access status page
    if not request.user.is_staff:
        # Log unauthorized access attempt
        logger.warning(
            f'Unauthorized status page access attempt by user: {request.user.username} from IP: {request.META.get("REMOTE_ADDR", "unknown")}'
        )
        messages.error(
            request,
            "Access denied. You need staff privileges to view the system status.",
        )
        return redirect("home")

    # Log authorized access for audit trail
    logger.info(
        f'Status page accessed by staff user: {request.user.username} from IP: {request.META.get("REMOTE_ADDR", "unknown")}'
    )

    # Initialize default values
    member_count = 0
    team_count = 0
    active_users = 0
    online_users = 0

    # Get basic statistics if models are available
    if Member and Team:
        try:
            member_count = Member.objects.count()
            team_count = Team.objects.count()

            # Calculate active users (logged in within last 30 days)
            thirty_days_ago = datetime.now() - timedelta(days=30)
            active_users = Member.objects.filter(
                last_login__gte=thirty_days_ago
            ).count()

            # Calculate online users (active in last hour) - simplified version
            one_hour_ago = datetime.now() - timedelta(hours=1)
            online_users = Member.objects.filter(last_login__gte=one_hour_ago).count()
        except DatabaseError as e:
            logger.error(f"Database error getting member statistics: {e}")
            member_count = 0
            team_count = 0
            active_users = 0
            online_users = 0

    # System health checks
    system_status = {
        "overall": "status-online",
        "database": "status-online",
        "cache": "status-online",
        "api": "status-online",
        "auth": "status-online",
    }

    # Test database connection with timing
    db_response_time = None
    try:
        start_time = time.time()
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        db_response_time = round((time.time() - start_time) * 1000, 2)
    except DatabaseError as e:
        logger.error(f"Database connection test failed: {e}")
        system_status["database"] = "status-offline"
        system_status["overall"] = "status-warning"

    # Test cache connection
    cache_memory_usage = None
    try:
        start_time = time.time()
        cache.set("status_test_key", "test_value", 1)
        result = cache.get("status_test_key")
        if result == "test_value":
            cache_memory_usage = 25  # Placeholder percentage
        else:
            raise ValueError("Cache test failed")
    except Exception as e:
        logger.error(f"Cache connection test failed: {e}")
        system_status["cache"] = "status-offline"
        system_status["overall"] = "status-warning"

    # API health check (simplified)
    api_response_time = 45  # Placeholder

    # Active sessions count (simplified)
    active_sessions = online_users if online_users else 1

    # Performance metrics (simplified/placeholder)
    avg_response_time = 120
    requests_per_minute = 45
    error_rate = 0.2
    db_queries_per_sec = 15

    # Environment information
    server_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
    django_version = django.get_version()
    python_version = (
        f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    )

    # Calculate simple uptime (placeholder)
    server_uptime = "2 days, 14 hours"

    # API endpoint status (placeholder)
    api_members_status = "200 OK"
    api_teams_status = "200 OK"
    api_org_status = "200 OK"
    api_auth_status = "200 OK"

    # Last updated timestamp
    last_updated = datetime.now().strftime("%H:%M:%S")

    context = {
        # Basic metrics
        "member_count": member_count,
        "team_count": team_count,
        "active_users": active_users,
        "online_users": online_users,
        # System status
        "system_status": system_status,
        # Performance metrics
        "db_response_time": db_response_time,
        "cache_memory_usage": cache_memory_usage,
        "api_response_time": api_response_time,
        "active_sessions": active_sessions,
        "avg_response_time": avg_response_time,
        "requests_per_minute": requests_per_minute,
        "error_rate": error_rate,
        "db_queries_per_sec": db_queries_per_sec,
        # Environment info
        "server_time": server_time,
        "django_version": django_version,
        "python_version": python_version,
        "server_uptime": server_uptime,
        # API status
        "api_members_status": api_members_status,
        "api_teams_status": api_teams_status,
        "api_org_status": api_org_status,
        "api_auth_status": api_auth_status,
        # Timestamps
        "last_updated": last_updated,
        "last_db_check": "Now",
        "last_cache_check": "Now",
    }

    return render(request, "testapp/status.html", context)


def redistest(request):
    return render(request, "testapp/redistest.html")


def websocket_sender(request):
    """View for the WebSocket sender page"""
    return render(request, "testapp/websocket_sender.html")


def contact_form(request):
    """View to demonstrate HTML builder with contact form"""
    if request.method == "POST":
        form = ContactForm(request.POST)
        if form.is_valid():
            # Process the form data
            name = form.cleaned_data["name"]
            # Here you would typically save to database or send email
            # Form data available in form.cleaned_data if needed
            messages.success(
                request,
                f"Thank you {name}! Your message has been submitted successfully.",
            )
            return redirect("contact_form")
    else:
        form = ContactForm()

    return render(
        request,
        "testapp/contact_form.html",
        {"form": form, "title": "Contact Form - HTML Builder Demo"},
    )


def registration_form(request):
    """View to demonstrate HTML builder with user registration form"""
    if request.method == "POST":
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(
                request, f"Account created successfully for {user.username}!"
            )
            return redirect("home")
    else:
        form = UserRegistrationForm()

    return render(
        request,
        "testapp/registration_form.html",
        {"form": form, "title": "User Registration - HTML Builder Demo"},
    )


def html_builder_demo(request):
    """Main demo page showing all HTML builder features"""
    return render(
        request, "testapp/html_builder_demo.html", {"title": "Django HTML Builder Demo"}
    )


def wysiwyg_demo(request):
    """Demo page showing WYSIWYG editor content"""
    articles = Article.objects.filter(is_published=True).order_by("-created_at")[:5]
    pages = Page.objects.filter(is_active=True).order_by("title")

    return render(
        request,
        "testapp/wysiwyg_demo.html",
        {"articles": articles, "pages": pages, "title": "WYSIWYG Editor Demo"},
    )


def article_detail(request, slug):
    """Display a single article"""
    article = get_object_or_404(Article, slug=slug, is_published=True)
    return render(
        request,
        "testapp/article_detail.html",
        {"article": article, "title": article.title},
    )


def page_detail(request, slug):
    """Display a single page"""
    page = get_object_or_404(Page, slug=slug, is_active=True)
    return render(
        request, "testapp/page_detail.html", {"page": page, "title": page.title}
    )
