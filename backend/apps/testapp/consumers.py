import json

from asgiref.sync import async_to_sync
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.layers import get_channel_layer


class EchoConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        # Join the "chat" group
        await self.channel_layer.group_add("chat", self.channel_name)
        await self.accept()
        await self.send(
            text_data=json.dumps(
                {"type": "connection_established", "message": "WebSocket connected!"}
            )
        )

    async def disconnect(self, close_code):
        # Leave the "chat" group
        await self.channel_layer.group_discard("chat", self.channel_name)

    async def receive(self, text_data):
        # Send message to the "chat" group
        await self.channel_layer.group_send(
            "chat", {"type": "chat_message", "message": text_data}
        )

    # Receive message from the "chat" group
    async def chat_message(self, event):
        message = event["message"]
        # Send message to WebSocket
        await self.send(text_data=message)
