# testapp/urls.py
from django.urls import path

from . import views

urlpatterns = [
    path("", views.home, name="home"),
    path("status/", views.status, name="status"),
    path("redistest/", views.redistest, name="redistest"),
    path("websocket-sender/", views.websocket_sender, name="websocket_sender"),
    # HTML Builder Demo URLs
    path("html-builder-demo/", views.html_builder_demo, name="html_builder_demo"),
    path("contact-form/", views.contact_form, name="contact_form"),
    path("registration-form/", views.registration_form, name="registration_form"),
    # WYSIWYG Editor Demo URLs
    path("wysiwyg-demo/", views.wysiwyg_demo, name="wysiwyg_demo"),
    path("article/<slug:slug>/", views.article_detail, name="article_detail"),
    path("page/<slug:slug>/", views.page_detail, name="page_detail"),
]
