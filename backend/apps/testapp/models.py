# accounts/models.py
from django.core.validators import RegexValidator
from django.db import models

from ckeditor.fields import RichText<PERSON>ield
from ckeditor_uploader.fields import RichTextUploadingField
from phonenumber_field.modelfields import PhoneNumberField


class Article(models.Model):
    """Model to demonstrate CKEditor WYSIWYG HTML editor"""

    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True)
    summary = models.TextField(max_length=500, help_text="Brief summary of the article")

    # Simple rich text field (no file uploads)
    content = RichTextField(
        config_name="default",
        help_text="Main article content with rich text formatting",
    )

    # Rich text field with file uploads
    full_content = RichTextUploadingField(
        config_name="full",
        help_text="Full content with image uploads and advanced formatting",
    )

    # Simple rich text field
    simple_content = RichTextField(
        config_name="simple", help_text="Simple content with basic formatting"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_published = models.BooleanField(default=False)

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return f"/article/{self.slug}/"


class Page(models.Model):
    """Model for static pages with WYSIWYG content"""

    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True)
    content = RichTextUploadingField(
        config_name="default",
        help_text="Page content with rich text formatting and image uploads",
    )
    meta_description = models.TextField(
        max_length=160, blank=True, help_text="SEO meta description"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["title"]

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return f"/page/{self.slug}/"
