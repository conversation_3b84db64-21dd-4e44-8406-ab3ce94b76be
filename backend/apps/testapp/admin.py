# accounts/admin.py
from django.contrib import admin

from .models import Article, Page


@admin.register(Article)
class ArticleAdmin(admin.ModelAdmin):
    list_display = ("title", "slug", "is_published", "created_at", "updated_at")
    search_fields = ("title", "slug")
    list_filter = ("is_published", "created_at")
    prepopulated_fields = {"slug": ("title",)}


@admin.register(Page)
class PageAdmin(admin.ModelAdmin):
    list_display = ("title", "slug", "is_active", "created_at", "updated_at")
    search_fields = ("title", "slug")
    list_filter = ("is_active", "created_at")
    prepopulated_fields = {"slug": ("title",)}
