# Generated by Django 5.1 on 2025-07-11 17:26

import ckeditor.fields
import ckeditor_uploader.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Article',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(max_length=200, unique=True)),
                ('summary', models.TextField(help_text='Brief summary of the article', max_length=500)),
                ('content', ckeditor.fields.RichTextField(help_text='Main article content with rich text formatting')),
                ('full_content', ckeditor_uploader.fields.RichTextUploadingField(help_text='Full content with image uploads and advanced formatting')),
                ('simple_content', ckeditor.fields.RichTextField(help_text='Simple content with basic formatting')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_published', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Page',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(max_length=200, unique=True)),
                ('content', ckeditor_uploader.fields.RichTextUploadingField(help_text='Page content with rich text formatting and image uploads')),
                ('meta_description', models.TextField(blank=True, help_text='SEO meta description', max_length=160)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['title'],
            },
        ),
    ]
