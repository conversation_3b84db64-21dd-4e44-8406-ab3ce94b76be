{% extends 'testapp/base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="card">
        <div class="card-header">
            <h1 class="text-2xl font-bold text-gray-900">Contact Us</h1>
            <p class="mt-1 text-sm text-gray-600">
                Send us a message and we'll get back to you as soon as possible.
            </p>
        </div>
        <div class="card-body">
            <form method="post" class="space-y-6">
                {% csrf_token %}

                <!-- Using Django Crispy Forms to automatically render the form -->
                {{ form|crispy }}

                <div class="flex items-center justify-between pt-4">
                    <button type="submit" class="btn-primary">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                        </svg>
                        Send Message
                    </button>

                    <a href="{% url 'html_builder_demo' %}" class="btn-secondary">
                        Back to Demo
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Form Features Demo -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="text-center p-4 bg-blue-50 rounded-lg">
            <svg class="h-8 w-8 text-blue-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <h3 class="font-medium text-blue-900">Automatic Styling</h3>
            <p class="text-sm text-blue-700">Forms are styled automatically with Tailwind CSS</p>
        </div>

        <div class="text-center p-4 bg-green-50 rounded-lg">
            <svg class="h-8 w-8 text-green-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
            </svg>
            <h3 class="font-medium text-green-900">Responsive Design</h3>
            <p class="text-sm text-green-700">Works perfectly on all device sizes</p>
        </div>

        <div class="text-center p-4 bg-purple-50 rounded-lg">
            <svg class="h-8 w-8 text-purple-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
            </svg>
            <h3 class="font-medium text-purple-900">Easy Customization</h3>
            <p class="text-sm text-purple-700">Simple to modify and extend</p>
        </div>
    </div>
</div>
{% endblock %}
