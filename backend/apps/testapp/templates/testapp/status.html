{% extends 'testapp/base.html' %}

{% block title %}System Status - XD Incentives{% endblock %}

{% block content %}
<!-- Security Notice -->
<div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <p class="text-sm text-red-700">
                <strong>Restricted Access:</strong> This system status dashboard contains sensitive information and is only accessible to authorized staff members. Access is logged and monitored.
            </p>
        </div>
    </div>
</div>

<!-- Status Header ---->
<div class="bg-white shadow">
    <div class="px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">
                System Status Dashboard
            </h1>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto mb-6">
                Real-time monitoring of XD Incentives system health and performance metrics.
            </p>
            <div class="inline-flex items-center">
                <span class="status-indicator {{ system_status.overall|default:'status-warning' }}"></span>
                <span class="text-lg font-medium text-gray-900">
                    Overall Status:
                    {% if system_status.overall == 'status-online' %}
                        <span class="text-green-600">Operational</span>
                    {% elif system_status.overall == 'status-warning' %}
                        <span class="text-yellow-600">Degraded</span>
                    {% else %}
                        <span class="text-red-600">Outage</span>
                    {% endif %}
                </span>
            </div>
        </div>
    </div>
</div>

<!-- System Health Cards -->
<div class="mt-8">
    <h2 class="text-2xl font-bold text-gray-900 mb-6">System Health</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">

        <!-- Database Status -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="status-indicator {{ system_status.database|default:'status-warning' }}"></span>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-gray-900">Database</h3>
                        <p class="text-sm text-gray-500">MySQL Connection</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-500">Status:</span>
                            <span class="font-medium {% if system_status.database == 'status-online' %}text-green-600{% elif system_status.database == 'status-warning' %}text-yellow-600{% else %}text-red-600{% endif %}">
                                {% if system_status.database == 'status-online' %}Online{% elif system_status.database == 'status-warning' %}Warning{% else %}Offline{% endif %}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Response Time:</span>
                            <span class="font-medium">{{ db_response_time|default:"N/A" }}ms</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cache Status -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="status-indicator {{ system_status.cache|default:'status-warning' }}"></span>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-gray-900">Cache</h3>
                        <p class="text-sm text-gray-500">Redis Connection</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-500">Status:</span>
                            <span class="font-medium {% if system_status.cache == 'status-online' %}text-green-600{% elif system_status.cache == 'status-warning' %}text-yellow-600{% else %}text-red-600{% endif %}">
                                {% if system_status.cache == 'status-online' %}Online{% elif system_status.cache == 'status-warning' %}Warning{% else %}Offline{% endif %}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Memory Usage:</span>
                            <span class="font-medium">{{ cache_memory_usage|default:"N/A" }}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Status -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="status-indicator {{ system_status.api|default:'status-online' }}"></span>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-gray-900">API</h3>
                        <p class="text-sm text-gray-500">REST Endpoints</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-500">Status:</span>
                            <span class="font-medium {% if system_status.api == 'status-online' %}text-green-600{% elif system_status.api == 'status-warning' %}text-yellow-600{% else %}text-red-600{% endif %}">
                                {% if system_status.api == 'status-online' %}Online{% elif system_status.api == 'status-warning' %}Warning{% else %}Offline{% endif %}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Avg Response:</span>
                            <span class="font-medium">{{ api_response_time|default:"N/A" }}ms</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Authentication Status -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="status-indicator {{ system_status.auth|default:'status-online' }}"></span>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-gray-900">Authentication</h3>
                        <p class="text-sm text-gray-500">Clerk & Django Auth</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-500">Status:</span>
                            <span class="font-medium {% if system_status.auth == 'status-online' %}text-green-600{% elif system_status.auth == 'status-warning' %}text-yellow-600{% else %}text-red-600{% endif %}">
                                {% if system_status.auth == 'status-online' %}Online{% elif system_status.auth == 'status-warning' %}Warning{% else %}Offline{% endif %}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Active Sessions:</span>
                            <span class="font-medium">{{ active_sessions|default:"N/A" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Metrics -->
<div class="mt-12">
    <h2 class="text-2xl font-bold text-gray-900 mb-6">System Metrics</h2>
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

        <!-- Performance Metrics -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">Performance Metrics</h3>
            </div>
            <div class="card-body">
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Average Response Time</span>
                        <span class="text-sm font-medium">{{ avg_response_time|default:"N/A" }}ms</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Requests per Minute</span>
                        <span class="text-sm font-medium">{{ requests_per_minute|default:"N/A" }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Error Rate</span>
                        <span class="text-sm font-medium">{{ error_rate|default:"N/A" }}%</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Database Queries/sec</span>
                        <span class="text-sm font-medium">{{ db_queries_per_sec|default:"N/A" }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Metrics -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">User Metrics</h3>
            </div>
            <div class="card-body">
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Total Members</span>
                        <span class="text-sm font-medium">{{ member_count|default:"N/A" }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Active Users (30 days)</span>
                        <span class="text-sm font-medium">{{ active_users|default:"N/A" }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Online Now</span>
                        <span class="text-sm font-medium">{{ online_users|default:"N/A" }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Total Teams</span>
                        <span class="text-sm font-medium">{{ team_count|default:"N/A" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Service Status -->
<div class="mt-12">
    <h2 class="text-2xl font-bold text-gray-900 mb-6">Service Status</h2>
    <div class="card">
        <div class="card-body">
            <div class="space-y-4">
                {% for service in services_status %}
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <span class="status-indicator {{ service.status|default:'status-warning' }}"></span>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-gray-900">{{ service.name }}</h4>
                            <p class="text-sm text-gray-500">{{ service.description }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium {% if service.status == 'status-online' %}text-green-600{% elif service.status == 'status-warning' %}text-yellow-600{% else %}text-red-600{% endif %}">
                            {{ service.status_text|default:"Unknown" }}
                        </div>
                        <div class="text-xs text-gray-500">{{ service.last_check|default:"Never" }}</div>
                    </div>
                </div>
                {% empty %}
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <span class="status-indicator status-online"></span>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-gray-900">Web Application</h4>
                            <p class="text-sm text-gray-500">Django web server</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium text-green-600">Operational</div>
                        <div class="text-xs text-gray-500">Now</div>
                    </div>
                </div>
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <span class="status-indicator {{ system_status.database|default:'status-warning' }}"></span>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-gray-900">Database Service</h4>
                            <p class="text-sm text-gray-500">MySQL 8.4.5 database server</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium {% if system_status.database == 'status-online' %}text-green-600{% elif system_status.database == 'status-warning' %}text-yellow-600{% else %}text-red-600{% endif %}">
                            {% if system_status.database == 'status-online' %}Operational{% elif system_status.database == 'status-warning' %}Degraded{% else %}Outage{% endif %}
                        </div>
                        <div class="text-xs text-gray-500">{{ last_db_check|default:"Now" }}</div>
                    </div>
                </div>
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <span class="status-indicator {{ system_status.cache|default:'status-warning' }}"></span>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-gray-900">Cache Service</h4>
                            <p class="text-sm text-gray-500">Redis cache and message broker</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium {% if system_status.cache == 'status-online' %}text-green-600{% elif system_status.cache == 'status-warning' %}text-yellow-600{% else %}text-red-600{% endif %}">
                            {% if system_status.cache == 'status-online' %}Operational{% elif system_status.cache == 'status-warning' %}Degraded{% else %}Outage{% endif %}
                        </div>
                        <div class="text-xs text-gray-500">{{ last_cache_check|default:"Now" }}</div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="mt-12">
    <h2 class="text-2xl font-bold text-gray-900 mb-6">System Information</h2>
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

        <!-- Environment Details -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">Environment</h3>
            </div>
            <div class="card-body">
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Django Version:</span>
                        <span class="text-sm font-medium">{{ django_version|default:"5.2.4" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Python Version:</span>
                        <span class="text-sm font-medium">{{ python_version|default:"3.12" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Server Time:</span>
                        <span class="text-sm font-medium">{{ server_time|default:"N/A" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Uptime:</span>
                        <span class="text-sm font-medium">{{ server_uptime|default:"N/A" }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Endpoints -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">API Endpoints Health</h3>
            </div>
            <div class="card-body">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm text-gray-700">/api/members/</span>
                        </div>
                        <span class="text-xs text-gray-500">{{ api_members_status|default:"OK" }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm text-gray-700">/api/teams/</span>
                        </div>
                        <span class="text-xs text-gray-500">{{ api_teams_status|default:"OK" }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm text-gray-700">/api/organization-chart/</span>
                        </div>
                        <span class="text-xs text-gray-500">{{ api_org_status|default:"OK" }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm text-gray-700">/api/token/</span>
                        </div>
                        <span class="text-xs text-gray-500">{{ api_auth_status|default:"OK" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Refresh Controls -->
<div class="mt-12 text-center">
    <div class="inline-flex items-center space-x-4">
        <button onclick="location.reload()" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            Refresh Status
        </button>
        <span class="text-sm text-gray-500">
            Last updated: {{ last_updated|default:"Now" }}
        </span>
    </div>
</div>
{% endblock %}
