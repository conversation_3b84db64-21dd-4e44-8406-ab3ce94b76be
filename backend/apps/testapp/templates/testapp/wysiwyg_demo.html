{% extends 'testapp/base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="space-y-8">
    <!-- Hero Section -->
    <div class="text-center">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">WYSIWYG HTML Editor Demo</h1>
        <p class="text-xl text-gray-600 mb-8">Create rich content with CKEditor in Django Admin</p>
    </div>

    <!-- Admin Access -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-center">
            <svg class="h-8 w-8 text-blue-600 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            <div>
                <h2 class="text-xl font-bold text-blue-900">Django Admin Access</h2>
                <p class="text-blue-800">Create and edit content using the WYSIWYG editor</p>
            </div>
        </div>
        <div class="mt-4 space-x-4">
            <a href="/admin/testapp/article/add/" class="btn-primary" target="_blank">
                Create New Article
            </a>
            <a href="/admin/testapp/page/add/" class="btn-primary" target="_blank">
                Create New Page
            </a>
            <a href="/admin/" class="btn-secondary" target="_blank">
                Go to Admin
            </a>
        </div>
    </div>

    <!-- Published Articles -->
    {% if articles %}
    <div class="card">
        <div class="card-header">
            <h2 class="text-2xl font-bold text-gray-900">Published Articles</h2>
            <p class="text-gray-600">Articles created with the WYSIWYG editor</p>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for article in articles %}
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ article.title }}</h3>
                    <p class="text-gray-600 text-sm mb-3">{{ article.summary|truncatewords:20 }}</p>
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <span>{{ article.created_at|date:"M d, Y" }}</span>
                        <a href="{% url 'article_detail' article.slug %}" class="text-blue-600 hover:text-blue-800">
                            Read More →
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-header">
            <h2 class="text-2xl font-bold text-gray-900">No Articles Yet</h2>
        </div>
        <div class="card-body text-center">
            <p class="text-gray-600 mb-4">Create your first article using the WYSIWYG editor in Django Admin</p>
            <a href="/admin/testapp/article/add/" class="btn-primary" target="_blank">
                Create Your First Article
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Static Pages -->
    {% if pages %}
    <div class="card">
        <div class="card-header">
            <h2 class="text-2xl font-bold text-gray-900">Static Pages</h2>
            <p class="text-gray-600">Pages created with the WYSIWYG editor</p>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for page in pages %}
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ page.title }}</h3>
                    {% if page.meta_description %}
                    <p class="text-gray-600 text-sm mb-3">{{ page.meta_description|truncatewords:15 }}</p>
                    {% endif %}
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <span>{{ page.updated_at|date:"M d, Y" }}</span>
                        <a href="{% url 'page_detail' page.slug %}" class="text-blue-600 hover:text-blue-800">
                            View Page →
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Features -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">Rich Text Editing</h3>
            </div>
            <div class="card-body">
                <p class="text-gray-600">Full-featured WYSIWYG editor with bold, italic, lists, links, and more.</p>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">Image Uploads</h3>
            </div>
            <div class="card-body">
                <p class="text-gray-600">Upload and insert images directly into your content.</p>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">HTML Source</h3>
            </div>
            <div class="card-body">
                <p class="text-gray-600">Switch to HTML source mode for advanced customization.</p>
            </div>
        </div>
    </div>

    <!-- How to Use -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900">How to Use the WYSIWYG Editor</h3>
        </div>
        <div class="card-body">
            <div class="space-y-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <span class="text-blue-600 font-semibold text-sm">1</span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Go to Django Admin</h4>
                        <p class="text-gray-600">Navigate to /admin/ and log in with your credentials</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <span class="text-blue-600 font-semibold text-sm">2</span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Create New Content</h4>
                        <p class="text-gray-600">Click on "Articles" or "Pages" and then "Add Article" or "Add Page"</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <span class="text-blue-600 font-semibold text-sm">3</span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Use the Editor</h4>
                        <p class="text-gray-600">Use the toolbar to format text, add links, insert images, and more</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <span class="text-blue-600 font-semibold text-sm">4</span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Save and View</h4>
                        <p class="text-gray-600">Save your content and view it on the frontend</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
