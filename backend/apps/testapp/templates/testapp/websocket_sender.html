<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Sender</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .messages {
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
        }
        .message.sent {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .message.received {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .timestamp {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Message Sender</h1>

        <div class="instructions">
            <strong>Instructions:</strong>
            <ol>
                <li>Open <a href="/redistest/" target="_blank">http://localhost:8000/redistest/</a> in another tab</li>
                <li>Open the browser console on that page to see incoming messages</li>
                <li>Use this page to send messages to the WebSocket connection</li>
            </ol>
        </div>

        <div class="form-group">
            <label for="messageInput">Message to Send:</label>
            <input type="text" id="messageInput" placeholder="Enter your message here..." />
        </div>

        <div class="form-group">
            <label for="messageTextarea">Or send a longer message:</label>
            <textarea id="messageTextarea" placeholder="Enter a longer message here..."></textarea>
        </div>

        <div class="form-group">
            <button id="sendButton" onclick="sendMessage()">Send Message</button>
            <button id="connectButton" onclick="connectWebSocket()">Connect</button>
            <button id="disconnectButton" onclick="disconnectWebSocket()" disabled>Disconnect</button>
        </div>

        <div id="status" class="status disconnected">
            Status: Disconnected
        </div>

        <div class="messages" id="messages">
            <h3>Message Log:</h3>
        </div>
    </div>

    <script>
        let ws = null;
        const messageInput = document.getElementById('messageInput');
        const messageTextarea = document.getElementById('messageTextarea');
        const sendButton = document.getElementById('sendButton');
        const connectButton = document.getElementById('connectButton');
        const disconnectButton = document.getElementById('disconnectButton');
        const status = document.getElementById('status');
        const messages = document.getElementById('messages');

        function connectWebSocket() {
            ws = new WebSocket('ws://localhost:8000/ws/echo/');

            ws.onopen = () => {
                updateStatus('connected', 'Status: Connected');
                connectButton.disabled = true;
                disconnectButton.disabled = false;
                sendButton.disabled = false;
                addMessage('System', 'WebSocket connected!', 'received');
            };

            ws.onmessage = (e) => {
                addMessage('Server', e.data, 'received');
            };

            ws.onerror = (e) => {
                addMessage('System', 'WebSocket error: ' + e, 'received');
            };

            ws.onclose = () => {
                updateStatus('disconnected', 'Status: Disconnected');
                connectButton.disabled = false;
                disconnectButton.disabled = true;
                sendButton.disabled = true;
                addMessage('System', 'WebSocket disconnected', 'received');
            };
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('WebSocket is not connected!');
                return;
            }

            const textMessage = messageInput.value.trim();
            const textareaMessage = messageTextarea.value.trim();

            let messageToSend = '';

            if (textMessage) {
                messageToSend = textMessage;
                messageInput.value = '';
            } else if (textareaMessage) {
                messageToSend = textareaMessage;
                messageTextarea.value = '';
            } else {
                alert('Please enter a message to send!');
                return;
            }

            ws.send(messageToSend);
            addMessage('You', messageToSend, 'sent');
        }

        function updateStatus(className, text) {
            status.className = `status ${className}`;
            status.textContent = text;
        }

        function addMessage(sender, message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `
                <strong>${sender}:</strong> ${message}
                <div class="timestamp">${timestamp}</div>
            `;

            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        // Allow Enter key to send message
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        messageTextarea.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                sendMessage();
            }
        });

        // Auto-connect when page loads
        window.onload = () => {
            connectWebSocket();
        };
    </script>
</body>
</html>
