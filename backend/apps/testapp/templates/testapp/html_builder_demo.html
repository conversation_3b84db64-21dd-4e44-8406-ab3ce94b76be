{% extends 'testapp/base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="space-y-8">
    <!-- Hero Section -->
    <div class="text-center">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">Django HTML Builder Demo</h1>
        <p class="text-xl text-gray-600 mb-8">See how Django Crispy Forms + Tailwind CSS creates beautiful, responsive forms automatically</p>
    </div>

    <!-- Features Grid -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">Django Crispy Forms</h3>
            </div>
            <div class="card-body">
                <p class="text-gray-600">Automatically generates beautiful HTML forms from Django form classes with minimal template code.</p>
                <div class="mt-4">
                    <a href="{% url 'contact_form' %}" class="btn-primary">View Contact Form</a>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">Tailwind CSS</h3>
            </div>
            <div class="card-body">
                <p class="text-gray-600">Utility-first CSS framework that makes building responsive designs fast and easy.</p>
                <div class="mt-4">
                    <a href="{% url 'registration_form' %}" class="btn-primary">View Registration Form</a>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">Responsive Design</h3>
            </div>
            <div class="card-body">
                <p class="text-gray-600">All forms and components are fully responsive and work perfectly on mobile devices.</p>
                <div class="mt-4">
                    <a href="{% url 'html_builder_demo' %}" class="btn-primary">View Demo</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Code Examples -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Django Form Example -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">Django Form Definition</h3>
            </div>
            <div class="card-body">
                <pre class="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto"><code>class ContactForm(forms.Form):
    name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Enter your name'
        })
    )
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-input',
            'placeholder': 'Enter your email'
        })
    )
    message = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-textarea',
            'rows': 4
        })
    )</code></pre>
            </div>
        </div>

        <!-- Template Example -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">Template Code</h3>
            </div>
            <div class="card-body">
                <pre class="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto"><code>{% load crispy_forms_tags %}

&lt;form method="post"&gt;
    {% csrf_token %}
    {{ form|crispy }}
    &lt;button type="submit" class="btn-primary"&gt;
        Submit
    &lt;/button&gt;
&lt;/form&gt;</code></pre>
            </div>
        </div>
    </div>

    <!-- Benefits Section -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 class="text-2xl font-bold text-blue-900 mb-4">Benefits of This Setup</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ul class="space-y-2 text-blue-800">
                <li class="flex items-center">
                    <svg class="h-5 w-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Automatic form styling
                </li>
                <li class="flex items-center">
                    <svg class="h-5 w-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Responsive design out of the box
                </li>
                <li class="flex items-center">
                    <svg class="h-5 w-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Minimal template code
                </li>
            </ul>
            <ul class="space-y-2 text-blue-800">
                <li class="flex items-center">
                    <svg class="h-5 w-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Consistent styling across forms
                </li>
                <li class="flex items-center">
                    <svg class="h-5 w-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Easy to customize and extend
                </li>
                <li class="flex items-center">
                    <svg class="h-5 w-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Built-in form validation styling
                </li>
            </ul>
        </div>
    </div>

    <!-- Quick Start Guide -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900">Quick Start Guide</h3>
        </div>
        <div class="card-body">
            <div class="space-y-4">
                <div>
                    <h4 class="font-medium text-gray-900">1. Install Packages</h4>
                    <p class="text-gray-600">Add django-crispy-forms and crispy-tailwind to your requirements.txt</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900">2. Configure Settings</h4>
                    <p class="text-gray-600">Add 'crispy_forms' and 'crispy_tailwind' to INSTALLED_APPS and set CRISPY_TEMPLATE_PACK = "tailwind"</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900">3. Create Forms</h4>
                    <p class="text-gray-600">Define your Django forms with appropriate widget attributes</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900">4. Use in Templates</h4>
                    <p class="text-gray-600">Load crispy_forms_tags and use {{ form|crispy }} to render beautiful forms</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
