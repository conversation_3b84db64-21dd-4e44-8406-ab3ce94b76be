{% extends 'testapp/base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="card">
        <div class="card-header">
            <h1 class="text-2xl font-bold text-gray-900">Create Your Account</h1>
            <p class="mt-1 text-sm text-gray-600">
                Join us and start building amazing things with our platform.
            </p>
        </div>
        <div class="card-body">
            <form method="post" class="space-y-6">
                {% csrf_token %}

                <!-- Using Django Crispy Forms to automatically render the form -->
                {{ form|crispy }}

                <div class="flex items-center justify-between pt-4">
                    <button type="submit" class="btn-primary">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                        </svg>
                        Create Account
                    </button>

                    <a href="{% url 'html_builder_demo' %}" class="btn-secondary">
                        Back to Demo
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Form Features -->
    <div class="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">What You Get</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <div>
                    <h4 class="font-medium text-gray-900">Secure Authentication</h4>
                    <p class="text-sm text-gray-600">Secure authentication via Clerk with encrypted password storage</p>
                </div>
            </div>

            <div class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <div>
                    <h4 class="font-medium text-gray-900">Beautiful Forms</h4>
                    <p class="text-sm text-gray-600">Automatically styled forms with validation</p>
                </div>
            </div>

            <div class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <div>
                    <h4 class="font-medium text-gray-900">Responsive Design</h4>
                    <p class="text-sm text-gray-600">Works perfectly on desktop, tablet, and mobile</p>
                </div>
            </div>

            <div class="flex items-start">
                <svg class="h-5 w-5 text-green-500 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <div>
                    <h4 class="font-medium text-gray-900">Easy Customization</h4>
                    <p class="text-sm text-gray-600">Simple to modify styles and add new fields</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Code Example -->
    <div class="mt-8 card">
        <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900">Behind the Scenes</h3>
        </div>
        <div class="card-body">
            <p class="text-gray-600 mb-4">This form is rendered with just one line of template code:</p>
            <pre class="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto"><code>{{ form|crispy }}</code></pre>
            <p class="text-gray-600 mt-4">Django Crispy Forms automatically generates all the HTML structure and applies Tailwind CSS classes for consistent, beautiful styling.</p>
        </div>
    </div>
</div>
{% endblock %}
