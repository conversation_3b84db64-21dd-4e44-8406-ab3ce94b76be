{% extends 'testapp/base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Article Header -->
    <div class="mb-8">
        <nav class="mb-4">
            <a href="{% url 'wysiwyg_demo' %}" class="text-blue-600 hover:text-blue-800">
                ← Back to WYSIWYG Demo
            </a>
        </nav>

        <h1 class="text-4xl font-bold text-gray-900 mb-4">{{ article.title }}</h1>

        <div class="flex items-center text-gray-600 mb-6">
            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
            </svg>
            <span>{{ article.created_at|date:"F d, Y" }}</span>

            {% if article.updated_at != article.created_at %}
            <span class="mx-2">•</span>
            <span>Updated {{ article.updated_at|date:"F d, Y" }}</span>
            {% endif %}
        </div>

        {% if article.summary %}
        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
            <p class="text-blue-800 font-medium">{{ article.summary }}</p>
        </div>
        {% endif %}
    </div>

    <!-- Article Content -->
    <div class="prose prose-lg max-w-none">
        {% if article.content %}
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Main Content</h2>
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                {{ article.content|safe }}
            </div>
        </div>
        {% endif %}

        {% if article.full_content %}
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Full Content (with uploads)</h2>
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                {{ article.full_content|safe }}
            </div>
        </div>
        {% endif %}

        {% if article.simple_content %}
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Simple Content</h2>
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                {{ article.simple_content|safe }}
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Article Actions -->
    <div class="mt-8 pt-6 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex space-x-4">
                <a href="/admin/testapp/article/{{ article.id }}/change/" class="btn-secondary" target="_blank">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                    Edit in Admin
                </a>
            </div>

            <a href="{% url 'wysiwyg_demo' %}" class="btn-primary">
                Back to Demo
            </a>
        </div>
    </div>

    <!-- WYSIWYG Features Info -->
    <div class="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">WYSIWYG Editor Features Used</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center">
                <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <span class="text-gray-700">Rich text formatting (bold, italic, etc.)</span>
            </div>
            <div class="flex items-center">
                <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <span class="text-gray-700">Lists and indentation</span>
            </div>
            <div class="flex items-center">
                <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <span class="text-gray-700">Links and anchors</span>
            </div>
            <div class="flex items-center">
                <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <span class="text-gray-700">Image uploads and embedding</span>
            </div>
            <div class="flex items-center">
                <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <span class="text-gray-700">Text alignment and formatting</span>
            </div>
            <div class="flex items-center">
                <svg class="h-5 w-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <span class="text-gray-700">HTML source editing</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}
