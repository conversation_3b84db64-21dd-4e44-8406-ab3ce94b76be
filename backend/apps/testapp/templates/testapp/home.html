{% extends 'testapp/base.html' %}

{% block title %}XD Incentives - Home{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-white shadow">
    <div class="px-4 sm:px-6 lg:px-8 py-12">
        <div class="text-center">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                Welcome to XD Incentives
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                A comprehensive member management system with hierarchical organization structure,
                team management, and robust permission controls.
            </p>
            {% if user.is_authenticated %}
                <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-6 max-w-md mx-auto">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="status-indicator status-online"></span>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-green-800">
                                Welcome back, <strong>{{ user.username }}</strong>! You are logged in.
                            </p>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6 max-w-md mx-auto">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <span class="status-indicator status-warning"></span>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-800">
                                Please <a href="/account/login/" class="underline font-medium">log in</a> to access member features.
                            </p>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Quick Access Cards -->
<div class="mt-8">
    <h2 class="text-2xl font-bold text-gray-900 mb-6">Quick Access</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- System Status Card -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="status-indicator {{ system_status.overall|default:'status-warning' }}"></span>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-gray-900">System Status</h3>
                        <p class="text-sm text-gray-500">Monitor system health</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="/status/" class="btn-primary w-full">
                        View Status Dashboard
                    </a>
                </div>
            </div>
        </div>

        {% if user.is_authenticated %}
        <!-- Member Management Card -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-gray-900">Members</h3>
                        <p class="text-sm text-gray-500">Manage organization members</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="/member/" class="btn-primary w-full">
                        View Members
                    </a>
                </div>
            </div>
        </div>

        <!-- API Access Card -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-gray-900">API</h3>
                        <p class="text-sm text-gray-500">REST API endpoints</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="/api/" class="btn-primary w-full">
                        Explore API
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        {% if user.is_staff %}
        <!-- Admin Panel Card -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-gray-900">Admin Panel</h3>
                        <p class="text-sm text-gray-500">System administration</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="/admin/" class="btn-primary w-full">
                        Open Admin
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Site Map -->
<div class="mt-12">
    <h2 class="text-2xl font-bold text-gray-900 mb-6">Site Map</h2>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Public Features -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">Public Features</h3>
            </div>
            <div class="card-body">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm font-medium text-gray-900">Home Page</span>
                        </div>
                        <a href="/" class="text-sm text-indigo-600 hover:text-indigo-900">Visit</a>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm font-medium text-gray-900">System Status</span>
                        </div>
                        <a href="/status/" class="text-sm text-indigo-600 hover:text-indigo-900">Check</a>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator {% if user.is_authenticated %}status-online{% else %}status-warning{% endif %}"></span>
                            <span class="text-sm font-medium text-gray-900">User Authentication</span>
                        </div>
                        {% if user.is_authenticated %}
                            <a href="/account/logout/" class="text-sm text-indigo-600 hover:text-indigo-900">Logout</a>
                        {% else %}
                            <a href="/account/login/" class="text-sm text-indigo-600 hover:text-indigo-900">Login</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Member Features -->
        {% if user.is_authenticated %}
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">Member Features</h3>
            </div>
            <div class="card-body">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm font-medium text-gray-900">Member List</span>
                        </div>
                        <a href="/member/list/" class="text-sm text-indigo-600 hover:text-indigo-900">View</a>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm font-medium text-gray-900">Organization Chart</span>
                        </div>
                        <a href="/member/organization-chart/" class="text-sm text-indigo-600 hover:text-indigo-900">View</a>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm font-medium text-gray-900">Team Management</span>
                        </div>
                        <a href="/member/teams/" class="text-sm text-indigo-600 hover:text-indigo-900">Manage</a>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm font-medium text-gray-900">Member Dashboard</span>
                        </div>
                        <a href="/member/dashboard/" class="text-sm text-indigo-600 hover:text-indigo-900">Open</a>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- API Endpoints -->
        {% if user.is_authenticated %}
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">API Endpoints</h3>
            </div>
            <div class="card-body">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm font-medium text-gray-900">Member API</span>
                        </div>
                        <a href="/api/members/" class="text-sm text-indigo-600 hover:text-indigo-900">Explore</a>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm font-medium text-gray-900">Teams API</span>
                        </div>
                        <a href="/api/teams/" class="text-sm text-indigo-600 hover:text-indigo-900">Explore</a>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm font-medium text-gray-900">Organization API</span>
                        </div>
                        <a href="/api/organization-chart/" class="text-sm text-indigo-600 hover:text-indigo-900">Explore</a>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm font-medium text-gray-900">Authentication API</span>
                        </div>
                        <a href="/api/token/" class="text-sm text-indigo-600 hover:text-indigo-900">Get Token</a>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Administrative Features -->
        {% if user.is_staff %}
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900">Administrative Features</h3>
            </div>
            <div class="card-body">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm font-medium text-gray-900">Django Admin</span>
                        </div>
                        <a href="/admin/" class="text-sm text-indigo-600 hover:text-indigo-900">Open</a>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm font-medium text-gray-900">System Status</span>
                        </div>
                        <a href="/status/" class="text-sm text-indigo-600 hover:text-indigo-900">Monitor</a>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm font-medium text-gray-900">Member Reports</span>
                        </div>
                        <a href="/member/reports/" class="text-sm text-indigo-600 hover:text-indigo-900">View</a>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm font-medium text-gray-900">Admin Only Page</span>
                        </div>
                        <a href="/member/admin-only/" class="text-sm text-indigo-600 hover:text-indigo-900">Access</a>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- System Information -->
<div class="mt-12">
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900">System Information</h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-indigo-600">{{ member_count|default:"N/A" }}</div>
                    <div class="text-sm text-gray-500">Total Members</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-indigo-600">{{ team_count|default:"N/A" }}</div>
                    <div class="text-sm text-gray-500">Active Teams</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-indigo-600">{{ active_users|default:"N/A" }}</div>
                    <div class="text-sm text-gray-500">Active Users</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
