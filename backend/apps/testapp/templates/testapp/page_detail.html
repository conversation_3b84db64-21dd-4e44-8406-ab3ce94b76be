{% extends 'testapp/base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Page Header -->
    <div class="mb-8">
        <nav class="mb-4">
            <a href="{% url 'wysiwyg_demo' %}" class="text-blue-600 hover:text-blue-800">
                ← Back to WYSIWYG Demo
            </a>
        </nav>

        <h1 class="text-4xl font-bold text-gray-900 mb-4">{{ page.title }}</h1>

        <div class="flex items-center text-gray-600 mb-6">
            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
            </svg>
            <span>Last updated {{ page.updated_at|date:"F d, Y" }}</span>
        </div>

        {% if page.meta_description %}
        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
            <p class="text-blue-800">{{ page.meta_description }}</p>
        </div>
        {% endif %}
    </div>

    <!-- Page Content -->
    <div class="prose prose-lg max-w-none">
        <div class="bg-white border border-gray-200 rounded-lg p-6">
            {{ page.content|safe }}
        </div>
    </div>

    <!-- Page Actions -->
    <div class="mt-8 pt-6 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex space-x-4">
                <a href="/admin/testapp/page/{{ page.id }}/change/" class="btn-secondary" target="_blank">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                    Edit in Admin
                </a>
            </div>

            <a href="{% url 'wysiwyg_demo' %}" class="btn-primary">
                Back to Demo
            </a>
        </div>
    </div>

    <!-- Page Info -->
    <div class="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Page Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <span class="font-medium text-gray-700">Created:</span>
                <span class="text-gray-600 ml-2">{{ page.created_at|date:"F d, Y" }}</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">Last Updated:</span>
                <span class="text-gray-600 ml-2">{{ page.updated_at|date:"F d, Y" }}</span>
            </div>
            <div>
                <span class="font-medium text-gray-700">Status:</span>
                <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full {% if page.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                    {% if page.is_active %}Active{% else %}Inactive{% endif %}
                </span>
            </div>
            <div>
                <span class="font-medium text-gray-700">Slug:</span>
                <span class="text-gray-600 ml-2 font-mono text-sm">{{ page.slug }}</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}
