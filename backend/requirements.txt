Django==5.2.4
python-decouple==3.8
mysqlclient==2.2.7
argon2-cffi==23.1.0

# API Setup
djangorestframework==3.16.0
djangorestframework-simplejwt==5.3.0

# Clerk Authentication
clerk-backend-api==3.0.7
django-clerk==0.1.15



# Phone number handling
phonenumbers==8.13.49
django-phonenumber-field==7.3.0

# React Assistance
django-cors-headers==4.6.0

# Async Options
channels==4.2.0
channels-redis==4.2.0
daphne==4.1.2

# Celery (background tasks)
celery[redis]==5.5.3

# HTML Builder & Styling
django-crispy-forms==2.3.0
crispy-tailwind==0.5.0
django-tailwind==3.8.0

# WYSIWYG HTML Editor for Django Admin
django-ckeditor==6.7.1

# Multi-select field for Django
django-multiselectfield==0.1.12

# Data visualization and scientific computing
matplotlib==3.9.2
numpy==2.1.3

# Sample data generation
Faker==25.3.0

# HTTP requests for API calls
requests==2.31.0

# Optional: for email sending, you can use SendGrid or another email backend
# sendgrid

mysql-connector-python==9.1.0

# Development tools for hot-reloading
watchdog==5.0.3

# Celery monitoring dashboard
flower==2.0.1

# Celery Beat scheduler with Django integration
django-celery-beat==2.8.0

# Type stubs for MyPy
types-redis==4.6.0.20241004

# Pre-commit hooks for code quality
pre-commit==4.3.0
