<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - XD Incentives</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    Sign in to XD Incentives
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Enter your credentials to access your account
                </p>
            </div>

            <form class="mt-8 space-y-6" method="post">
                {% csrf_token %}

                {% if form.errors %}
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                    <p class="text-sm font-medium"><PERSON><PERSON> failed</p>
                    <p class="text-sm mt-1">Please check your username and password.</p>
                </div>
                {% endif %}

                <div class="space-y-4">
                    <div>
                        <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Username
                        </label>
                        <input id="{{ form.username.id_for_label }}"
                               name="{{ form.username.name }}"
                               type="text"
                               required
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                               placeholder="Enter your username">
                        {% if form.username.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.username.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.password.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Password
                        </label>
                        <input id="{{ form.password.id_for_label }}"
                               name="{{ form.password.name }}"
                               type="password"
                               required
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                               placeholder="Enter your password">
                        {% if form.password.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.password.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <div>
                    <button type="submit"
                            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Sign in
                    </button>
                </div>

                <div class="text-center space-y-2">
                    <div class="text-sm text-gray-600">
                        <a href="/" class="text-blue-600 hover:text-blue-500">Back to home</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
