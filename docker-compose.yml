services:
  # Frontend service with hot reloading for development
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: dev
    container_name: xd-frontend
    restart: unless-stopped
    stop_grace_period: 30s
    stop_signal: SIGTERM
    ports:
      - "3000:3000"
    volumes:
      # Optimized bind mounts for better performance
      - ./frontend/src:/app/src:cached
      - ./frontend/public:/app/public:cached
      - ./frontend/index.html:/app/index.html:cached
      - ./frontend/vite.config.ts:/app/vite.config.ts:cached
      - ./frontend/tsconfig.json:/app/tsconfig.json:cached
      - ./frontend/tsconfig.node.json:/app/tsconfig.node.json:cached
      - ./frontend/package.json:/app/package.json:cached
      # Named volume for node_modules to avoid performance issues
      - frontend_node_modules:/app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://backend:8000
      # Optimized file watching
      - CHOKIDAR_USEPOLLING=false
      - CHOKIDAR_INTERVAL=1000
      - WATCHPACK_POLLING=false
      - VITE_HMR_PORT=3000
      - VITE_HMR_HOST=localhost
    env_file:
      - .env
    stdin_open: true
    tty: true
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx reverse proxy for production-like setup (optional, can be enabled)
  nginx:
    image: nginx:alpine
    container_name: xd-nginx
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
    depends_on:
      - backend
      - frontend
    profiles:
      - production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  redis:
    image: redis:8
    container_name: xd-redis
    restart: always
    stop_grace_period: 10s
    stop_signal: SIGTERM
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./logs/redis:/var/log/redis
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --maxmemory 2G
      --maxmemory-policy allkeys-lru
      --maxmemory-samples 10
      --timeout 300
      --tcp-keepalive 60
      --tcp-backlog 511
      --databases 16
      --save 900 1
      --save 300 10
      --save 60 10000
      --rdbcompression yes
      --rdbchecksum yes
      --stop-writes-on-bgsave-error yes
      --lazyfree-lazy-eviction yes
      --lazyfree-lazy-expire yes
      --lazyfree-lazy-server-del yes
      --replica-lazy-flush yes
      --logfile /var/log/redis/redis.log
      --loglevel notice
      --protected-mode no
      --bind 0.0.0.0
      --maxclients 10000
      --io-threads 4
      --io-threads-do-reads yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  db:
    image: mysql:8.4.5
    container_name: xd-mysql
    restart: always
    stop_grace_period: 60s
    stop_signal: SIGTERM
    environment:
      MYSQL_DATABASE: xd_incentives_db
      MYSQL_USER: testu
      MYSQL_PASSWORD: testpw
      MYSQL_ROOT_PASSWORD: rootpw
      MYSQL_INNODB_BUFFER_POOL_SIZE: 1G
      MYSQL_INNODB_LOG_FILE_SIZE: 256M
    command: >
      --innodb-buffer-pool-size=1G
      --innodb-redo-log-capacity=268435456
      --innodb-buffer-pool-instances=4
      --innodb-log-buffer-size=64M
      --innodb-flush-log-at-trx-commit=2
      --innodb-io-capacity=1000
      --innodb-io-capacity-max=2000
      --innodb-read-io-threads=8
      --innodb-write-io-threads=8
      --max-connections=500
      --thread-cache-size=128
      --table-open-cache=4000
      --table-definition-cache=2000
      --tmp-table-size=128M
      --max-heap-table-size=128M
      --join-buffer-size=2M
      --sort-buffer-size=2M
      --read-buffer-size=1M
      --read-rnd-buffer-size=2M
      --binlog-cache-size=1M
      --slow-query-log=1
      --slow-query-log-file=/var/log/mysql/slow.log
      --long-query-time=1
      --log-queries-not-using-indexes=1
      --log-slow-admin-statements=1
      --log-slow-slave-statements=1
      --expire-logs-days=7
      --max-binlog-size=100M
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --sql-mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO
      --interactive-timeout=28800
      --wait-timeout=28800
      --max-allowed-packet=256M
      --connect-timeout=10
      --back-log=500
      --max-connect-errors=100000
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
      - ./logs/mysql:/var/log/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "--silent"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: xd-backend
    restart: unless-stopped
    stop_grace_period: 30s
    stop_signal: SIGTERM
    entrypoint: ["/wait-for-db.sh", "db"]
    command: python /app/scripts/run_with_reload.py
    volumes:
      # Optimized bind mounts for better development performance
      - ./backend:/app/backend:cached
      - ./scripts:/app/scripts:cached
      - ./config:/app/config:cached
      - ./logs/django:/app/logs:delegated
      # Named volume for Python cache to avoid permission issues
      - backend_cache:/app/backend/__pycache__
    ports:
      - "8000:8000"
      # Debugger ports for development
      - "5678:5678"  # Python debugger (ptvsd/debugpy)
      - "8001:8001"  # Django debug toolbar
    environment:
      - PYTHONPATH=/app/backend
      - DJANGO_SETTINGS_MODULE=config.settings.development
      - DJANGO_ALLOW_ASYNC_UNSAFE=true
      # Development optimizations
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - DJANGO_DEBUG=true
    env_file:
      - .env
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  celery:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: xd-celery
    restart: unless-stopped
    stop_grace_period: 30s
    stop_signal: SIGTERM
    entrypoint: ["/wait-for-db.sh", "db"]
    command: sh -c "celery -A config worker -l info --concurrency=4 --autoscale=10,3"
    volumes:
      # Optimized bind mounts matching backend service
      - ./backend:/app/backend:cached
      - ./scripts:/app/scripts:cached
      - ./config:/app/config:cached
      - ./logs/celery:/app/logs/celery:delegated
      - backend_cache:/app/backend/__pycache__
    environment:
      - PYTHONPATH=/app/backend
      - DJANGO_SETTINGS_MODULE=config.settings.development
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      # Celery optimizations
      - C_FORCE_ROOT=1
      - CELERY_TASK_ALWAYS_EAGER=false
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "celery", "-A", "config", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Celery Beat for scheduled tasks
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: xd-celery-beat
    restart: unless-stopped
    entrypoint: ["/wait-for-db.sh", "db"]
    command: sh -c "celery -A config beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler"
    volumes:
      # Optimized bind mounts matching backend service
      - ./backend:/app/backend:cached
      - ./scripts:/app/scripts:cached
      - ./config:/app/config:cached
      - ./logs/celery:/app/logs/celery:delegated
      - backend_cache:/app/backend/__pycache__
    environment:
      - PYTHONPATH=/app/backend
      - DJANGO_SETTINGS_MODULE=config.settings
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    profiles:
      - celery-beat
    healthcheck:
      test: ["CMD", "celery", "-A", "config", "inspect", "ping"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Flower for Celery monitoring
  flower:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: xd-flower
    restart: unless-stopped
    command: sh -c "celery -A config flower --port=5555 --broker_api=******************************/api/"
    volumes:
      # Minimal volumes needed for monitoring
      - ./backend:/app/backend:cached
      - ./config:/app/config:cached
    ports:
      - "5555:5555"
    environment:
      - PYTHONPATH=/app/backend
      - DJANGO_SETTINGS_MODULE=config.settings
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    depends_on:
      - redis
      - celery
    env_file:
      - .env
    profiles:
      - monitoring
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5555"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  db_data:
    driver: local
  redis_data:
    driver: local
  frontend_node_modules:
    driver: local
  backend_cache:
    driver: local
