# Multi-stage Dockerfile for XD Incentives Django Application
# Optimized for production with security and performance improvements

# Stage 1: Build dependencies and install packages
FROM python:3.12-slim as dependencies

# Set environment variables for build
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies needed for building Python packages
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    default-libmysqlclient-dev \
    build-essential \
    pkg-config \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/*

# Create working directory
WORKDIR /build

# Copy requirements and install Python dependencies
COPY backend/requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Stage 2: Runtime image
FROM python:3.12-slim as production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/home/<USER>/.local/bin:$PATH"

# Install only runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    default-libmysqlclient-dev \
    default-mysql-client \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd --gid 1000 django && \
    useradd --uid 1000 --gid django --shell /bin/bash --create-home django

# Copy Python packages from dependencies stage
COPY --from=dependencies --chown=django:django /root/.local /home/<USER>/.local

# Set working directory
WORKDIR /app

# Copy wait script and make it executable
COPY --chown=django:django scripts/wait-for-db.sh /wait-for-db.sh
RUN chmod +x /wait-for-db.sh

# Copy application code
COPY --chown=django:django . /app/

# Create necessary directories
RUN mkdir -p /app/logs /app/staticfiles /app/media && \
    chown -R django:django /app/logs /app/staticfiles /app/media

# Switch to non-root user
USER django

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/admin/login/ || exit 1

# Use wait-for-db.sh as the entrypoint
ENTRYPOINT ["/wait-for-db.sh", "db"]

# Default command
CMD ["sh", "-c", "cd backend && daphne -b 0.0.0.0 -p 8000 config.asgi:application"]
