# Django Settings
DEBUG=True
SECRET_KEY=django-insecure-development-key-only-for-local-dev-12345
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DB_NAME=xd_incentives_db
DB_USER=testu
DB_PASSWORD=testpw
DB_HOST=db
DB_PORT=3306

# Clerk Authentication
CLERK_SECRET_KEY=sk_test_xxxxx
CLERK_PUBLISHABLE_KEY=pk_test_xxxxx
CLERK_JWT_ISSUER=https://your-app.clerk.accounts.dev
CLERK_WEBHOOK_SECRET=whsec_xxxxx
VITE_CLERK_PUBLISHABLE_KEY=pk_test_xxxxx
VITE_CLERK_SIGN_IN_URL=/sign-in
VITE_CLERK_SIGN_UP_URL=/sign-up
VITE_CLERK_AFTER_SIGN_IN_URL=/dashboard
VITE_CLERK_AFTER_SIGN_UP_URL=/onboarding
