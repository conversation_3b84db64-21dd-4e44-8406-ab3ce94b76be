version: '3.8'

# XD Incentives Database Seeding Docker Compose
# Specialized compose file for database seeding operations
# Usage: docker-compose -f docker-compose.seed.yml up <service>

services:
  # Full database seeding with backup
  seed-full:
    image: alpine:latest
    container_name: xd-seed-full
    working_dir: /workspace
    volumes:
      - .:/workspace
      - ./databases:/workspace/databases
    depends_on:
      - db-seed
    command: >
      sh -c "
        apk add --no-cache bash &&
        cd databases &&
        ./seed_database.sh --full --backup --sample-data --force
      "
    networks:
      - default

  # Fresh database reset and seeding
  seed-fresh:
    image: alpine:latest
    container_name: xd-seed-fresh
    working_dir: /workspace
    volumes:
      - .:/workspace
      - ./databases:/workspace/databases
    depends_on:
      - db-seed
    command: >
      sh -c "
        apk add --no-cache bash &&
        cd databases &&
        ./seed_database.sh --fresh --backup --sample-data --force
      "
    networks:
      - default

  # Minimal schema seeding
  seed-minimal:
    image: alpine:latest
    container_name: xd-seed-minimal
    working_dir: /workspace
    volumes:
      - .:/workspace
      - ./databases:/workspace/databases
    depends_on:
      - db-seed
    command: >
      sh -c "
        apk add --no-cache bash &&
        cd databases &&
        ./seed_database.sh --minimal --sample-data --force
      "
    networks:
      - default

  # Test data seeding
  seed-test:
    image: alpine:latest
    container_name: xd-seed-test
    working_dir: /workspace
    volumes:
      - .:/workspace
      - ./databases:/workspace/databases
    depends_on:
      - db-seed
    command: >
      sh -c "
        apk add --no-cache bash &&
        cd databases &&
        ./seed_database.sh --test --sample-data --force
      "
    networks:
      - default

  # Backup creation service
  backup-db:
    image: alpine:latest
    container_name: xd-backup-db
    working_dir: /workspace
    volumes:
      - .:/workspace
      - ./databases:/workspace/databases
    depends_on:
      - db-seed
    command: >
      sh -c "
        apk add --no-cache bash &&
        cd databases &&
        ./dump_database.sh
      "
    networks:
      - default

  # Database service for seeding operations
  db-seed:
    image: mysql:8.4.5
    container_name: xd-mysql-seed
    restart: "no"
    environment:
      MYSQL_DATABASE: xd_incentives_db
      MYSQL_USER: testu
      MYSQL_PASSWORD: testpw
      MYSQL_ROOT_PASSWORD: rootpw
      MYSQL_INNODB_BUFFER_POOL_SIZE: 512M
      MYSQL_INNODB_LOG_FILE_SIZE: 128M
    command: >
      --innodb-buffer-pool-size=512M
      --innodb-redo-log-capacity=134217728
      --innodb-buffer-pool-instances=2
      --innodb-log-buffer-size=32M
      --innodb-flush-log-at-trx-commit=2
      --innodb-io-capacity=500
      --innodb-io-capacity-max=1000
      --max-connections=200
      --thread-cache-size=32
      --table-open-cache=2000
      --table-definition-cache=1000
      --tmp-table-size=64M
      --max-heap-table-size=64M
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --sql-mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO
    ports:
      - "3307:3306"  # Different port to avoid conflicts with main db
    volumes:
      - db_seed_data:/var/lib/mysql
      - ./logs/mysql-seed:/var/log/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "--silent"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - default

  # Redis service for seeding operations (if needed)
  redis-seed:
    image: redis:8
    container_name: xd-redis-seed
    restart: "no"
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      - redis_seed_data:/data
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --maxmemory 512M
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    networks:
      - default

  # Backend service for Django commands during seeding
  backend-seed:
    build: .
    container_name: xd-backend-seed
    restart: "no"
    working_dir: /app
    volumes:
      - .:/app
      - ./logs/django-seed:/app/logs
    ports:
      - "8001:8000"  # Different port to avoid conflicts
    environment:
      - DEBUG=True
      - DATABASE_URL=mysql://testu:testpw@db-seed:3306/xd_incentives_db
      - REDIS_URL=redis://redis-seed:6379/0
    depends_on:
      db-seed:
        condition: service_healthy
      redis-seed:
        condition: service_healthy
    command: >
      sh -c "
        python manage.py migrate &&
        echo 'Web service ready for Django commands' &&
        tail -f /dev/null
      "
    networks:
      - default

  # Interactive seeding service for manual operations
  seed-interactive:
    image: alpine:latest
    container_name: xd-seed-interactive
    working_dir: /workspace
    volumes:
      - .:/workspace
      - ./databases:/workspace/databases
    depends_on:
      - db-seed
    command: >
      sh -c "
        apk add --no-cache bash mysql-client &&
        echo 'Interactive seeding environment ready' &&
        echo 'Available commands:' &&
        echo '  ./databases/seed_database.sh --help' &&
        echo '  ./databases/dump_database.sh' &&
        echo '  mysql -h db-seed -u testu -ptestpw xd_incentives_db' &&
        tail -f /dev/null
      "
    networks:
      - default

volumes:
  db_seed_data:
    driver: local
  redis_seed_data:
    driver: local

networks:
  default:
    name: xd-seed-network
    driver: bridge
