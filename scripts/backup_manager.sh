#!/bin/bash

# XD Incentives Database Backup Manager
# Advanced backup management with retention, compression, and restoration

set -e  # Exit on any error

# Configuration
DB_NAME="xd_incentives_db"
DB_USER="testu"
DB_PASSWORD="testpw"
CONTAINER_NAME="xd-mysql"
# Get the project root directory (parent of scripts directory)
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DUMPS_DIR="${PROJECT_ROOT}/data"
BACKUPS_DIR="${DUMPS_DIR}/backups"
ARCHIVES_DIR="${DUMPS_DIR}/archives"

# Backup retention settings
DAILY_RETENTION=7    # Keep 7 daily backups
WEEKLY_RETENTION=4   # Keep 4 weekly backups
MONTHLY_RETENTION=12 # Keep 12 monthly backups

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Emoji indicators
SUCCESS="✅"
ERROR="❌"
WARNING="⚠️"
INFO="ℹ️"
BACKUP="💾"
ARCHIVE="📦"
CLEAN="🧹"

# Print colored output
print_status() { echo -e "${GREEN}${SUCCESS}${NC} $1"; }
print_error() { echo -e "${RED}${ERROR}${NC} $1"; }
print_warning() { echo -e "${YELLOW}${WARNING}${NC} $1"; }
print_info() { echo -e "${BLUE}${INFO}${NC} $1"; }
print_backup() { echo -e "${BLUE}${BACKUP}${NC} $1"; }
print_archive() { echo -e "${PURPLE}${ARCHIVE}${NC} $1"; }
print_clean() { echo -e "${YELLOW}${CLEAN}${NC} $1"; }

# Help function
show_help() {
    echo "XD Incentives Database Backup Manager"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Backup Operations:"
    echo "  --create              Create a new backup with timestamp"
    echo "  --create-daily        Create daily backup (auto-cleanup old daily)"
    echo "  --create-weekly       Create weekly backup (auto-cleanup old weekly)"
    echo "  --create-monthly      Create monthly backup (auto-cleanup old monthly)"
    echo "  --auto                Auto backup based on date (daily/weekly/monthly)"
    echo ""
    echo "Restoration Operations:"
    echo "  --restore [file]      Restore from specific backup file"
    echo "  --restore-latest      Restore from latest backup"
    echo "  --restore-interactive Interactive restoration menu"
    echo ""
    echo "Management Operations:"
    echo "  --list                List all backups by category"
    echo "  --list-detailed       Detailed backup information"
    echo "  --cleanup             Clean old backups based on retention policy"
    echo "  --archive [file]      Archive specific backup to long-term storage"
    echo "  --verify [file]       Verify backup file integrity"
    echo ""
    echo "Utility Operations:"
    echo "  --size                Show backup directory size"
    echo "  --status              Show backup status and statistics"
    echo "  --config              Show current configuration"
    echo "  --help                Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --create-daily           # Create daily backup"
    echo "  $0 --restore-interactive    # Interactive restore menu"
    echo "  $0 --cleanup                # Clean old backups"
    echo "  $0 --archive backup_file.gz # Archive specific backup"
    echo ""
}

# Ensure directories exist
ensure_directories() {
    for dir in "$BACKUPS_DIR" "$ARCHIVES_DIR"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_info "Created directory: $dir"
        fi
    done
}

# Check if Docker container is running
check_container() {
    if ! docker ps | grep -q "${CONTAINER_NAME}"; then
        print_error "MySQL container (${CONTAINER_NAME}) is not running!"
        print_info "Please start the Docker containers first:"
        print_info "  docker-compose up -d"
        exit 1
    fi
}

# Get timestamp in various formats
get_timestamp() {
    local format="$1"
    case "$format" in
        "daily")
            date +%Y_%m_%d
            ;;
        "weekly")
            date +%Y_W%U
            ;;
        "monthly")
            date +%Y_%m
            ;;
        "full")
            date +%Y_%m_%d_%H_%M_%S
            ;;
        *)
            date +%Y_%m_%d_%H_%M_%S
            ;;
    esac
}

# Create backup with specific type
create_backup() {
    local backup_type="$1"
    local custom_name="$2"
    local timestamp=$(get_timestamp "$backup_type")
    local backup_file=""

    case "$backup_type" in
        "daily")
            backup_file="${BACKUPS_DIR}/daily_${timestamp}.sql.gz"
            ;;
        "weekly")
            backup_file="${BACKUPS_DIR}/weekly_${timestamp}.sql.gz"
            ;;
        "monthly")
            backup_file="${BACKUPS_DIR}/monthly_${timestamp}.sql.gz"
            ;;
        "custom")
            if [ -n "$custom_name" ]; then
                backup_file="${BACKUPS_DIR}/${custom_name}_$(get_timestamp full).sql.gz"
            else
                backup_file="${BACKUPS_DIR}/backup_$(get_timestamp full).sql.gz"
            fi
            ;;
        *)
            backup_file="${BACKUPS_DIR}/backup_$(get_timestamp full).sql.gz"
            ;;
    esac

    print_backup "Creating ${backup_type} backup..."
    print_info "File: $(basename "$backup_file")"

    # Create the backup
    if docker exec ${CONTAINER_NAME} mysqldump \
        -u ${DB_USER} -p${DB_PASSWORD} \
        --single-transaction \
        --routines \
        --triggers \
        --lock-tables=false \
        ${DB_NAME} | gzip > "$backup_file"; then

        local file_size=$(du -h "$backup_file" | cut -f1)
        print_status "${backup_type} backup created successfully (${file_size})"
        echo "$backup_file"
    else
        print_error "${backup_type} backup creation failed!"
        rm -f "$backup_file" 2>/dev/null
        exit 1
    fi
}

# Auto backup based on current date
auto_backup() {
    local current_day=$(date +%u)  # 1=Monday, 7=Sunday
    local current_date=$(date +%d)

    # Monthly backup on 1st day of month
    if [ "$current_date" = "01" ]; then
        print_info "Creating monthly backup (1st of month)"
        create_backup "monthly"
        cleanup_old_backups "monthly"

    # Weekly backup on Sundays
    elif [ "$current_day" = "7" ]; then
        print_info "Creating weekly backup (Sunday)"
        create_backup "weekly"
        cleanup_old_backups "weekly"

    # Daily backup otherwise
    else
        print_info "Creating daily backup"
        create_backup "daily"
        cleanup_old_backups "daily"
    fi
}

# List backups by category
list_backups() {
    local detailed="$1"

    echo ""
    echo "📋 Database Backups Overview"
    echo "============================"

    for category in "daily" "weekly" "monthly" "backup"; do
        echo ""
        echo "📁 ${category^} Backups:"

        local files=$(ls "${BACKUPS_DIR}/${category}_"*.sql.gz 2>/dev/null || true)
        if [ -z "$files" ]; then
            echo "  (no ${category} backups found)"
            continue
        fi

        if [ "$detailed" = "true" ]; then
            ls -lah "${BACKUPS_DIR}/${category}_"*.sql.gz 2>/dev/null | while read -r line; do
                echo "  $line"
            done
        else
            ls -1 "${BACKUPS_DIR}/${category}_"*.sql.gz 2>/dev/null | while read -r file; do
                local size=$(du -h "$file" | cut -f1)
                echo "  $(basename "$file") (${size})"
            done
        fi
    done

    # Show archives
    if [ -d "$ARCHIVES_DIR" ] && [ "$(ls -A "$ARCHIVES_DIR" 2>/dev/null)" ]; then
        echo ""
        echo "📦 Archived Backups:"
        if [ "$detailed" = "true" ]; then
            ls -lah "${ARCHIVES_DIR}"/*.sql.gz 2>/dev/null | while read -r line; do
                echo "  $line"
            done
        else
            ls -1 "${ARCHIVES_DIR}"/*.sql.gz 2>/dev/null | while read -r file; do
                local size=$(du -h "$file" | cut -f1)
                echo "  $(basename "$file") (${size})"
            done
        fi
    fi

    echo ""
}

# Cleanup old backups based on retention policy
cleanup_old_backups() {
    local backup_type="$1"
    local retention=""

    case "$backup_type" in
        "daily")
            retention="$DAILY_RETENTION"
            ;;
        "weekly")
            retention="$WEEKLY_RETENTION"
            ;;
        "monthly")
            retention="$MONTHLY_RETENTION"
            ;;
        *)
            print_info "No cleanup needed for backup type: $backup_type"
            return 0
            ;;
    esac

    print_clean "Cleaning old ${backup_type} backups (keeping ${retention})..."

    # Find and remove old backups
    local old_files=$(ls -t "${BACKUPS_DIR}/${backup_type}_"*.sql.gz 2>/dev/null | tail -n +$((retention + 1)) || true)

    if [ -n "$old_files" ]; then
        echo "$old_files" | while read -r file; do
            if [ -f "$file" ]; then
                print_clean "Removing old backup: $(basename "$file")"
                rm "$file"
            fi
        done
        print_status "Cleanup completed for ${backup_type} backups"
    else
        print_info "No old ${backup_type} backups to clean"
    fi
}

# Archive backup to long-term storage
archive_backup() {
    local backup_file="$1"

    if [ ! -f "$backup_file" ]; then
        print_error "Backup file not found: $backup_file"
        exit 1
    fi

    local filename=$(basename "$backup_file")
    local archive_file="${ARCHIVES_DIR}/${filename}"

    print_archive "Archiving backup: $filename"

    if cp "$backup_file" "$archive_file"; then
        print_status "Backup archived successfully"
        print_info "Archive location: $archive_file"

        # Ask if user wants to remove original
        read -p "Remove original backup file? (y/N): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm "$backup_file"
            print_info "Original backup file removed"
        fi
    else
        print_error "Failed to archive backup"
        exit 1
    fi
}

# Verify backup file integrity
verify_backup() {
    local backup_file="$1"

    if [ ! -f "$backup_file" ]; then
        print_error "Backup file not found: $backup_file"
        exit 1
    fi

    print_info "Verifying backup file: $(basename "$backup_file")"

    # Check if file is a valid gzip
    if gzip -t "$backup_file" 2>/dev/null; then
        print_status "✓ File compression is valid"
    else
        print_error "✗ File compression is corrupted"
        return 1
    fi

    # Check if SQL content is valid
    local sql_check=$(gunzip -c "$backup_file" | head -n 10 | grep -c "mysqldump\|CREATE\|INSERT" || true)
    if [ "$sql_check" -gt 0 ]; then
        print_status "✓ SQL content appears valid"
    else
        print_warning "⚠ SQL content may be invalid"
    fi

    # Show file stats
    local file_size=$(du -h "$backup_file" | cut -f1)
    local file_date=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$backup_file" 2>/dev/null || stat -c "%y" "$backup_file" 2>/dev/null || echo "unknown")

    print_info "File size: $file_size"
    print_info "Date created: $file_date"

    print_status "Backup verification completed"
}

# Interactive restoration menu
interactive_restore() {
    echo ""
    echo "🔄 Interactive Database Restoration"
    echo "==================================="

    # List available backups
    local backup_files=$(ls "${BACKUPS_DIR}"/*.sql.gz "${ARCHIVES_DIR}"/*.sql.gz 2>/dev/null | sort -r || true)

    if [ -z "$backup_files" ]; then
        print_error "No backup files found!"
        exit 1
    fi

    echo ""
    echo "Available backup files:"
    echo ""

    local i=1
    echo "$backup_files" | while read -r file; do
        local size=$(du -h "$file" | cut -f1)
        local date=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M" "$file" 2>/dev/null || stat -c "%y" "$file" 2>/dev/null | cut -d' ' -f1-2 || echo "unknown")
        echo "  $i. $(basename "$file") (${size}, ${date})"
        i=$((i + 1))
    done

    echo ""
    read -p "Select backup number (or 'q' to quit): " selection

    if [ "$selection" = "q" ] || [ "$selection" = "Q" ]; then
        print_info "Restoration cancelled"
        exit 0
    fi

    # Get selected file
    local selected_file=$(echo "$backup_files" | sed -n "${selection}p")

    if [ -z "$selected_file" ] || [ ! -f "$selected_file" ]; then
        print_error "Invalid selection or file not found"
        exit 1
    fi

    print_warning "This will restore database from: $(basename "$selected_file")"
    print_warning "All current data will be replaced!"

    read -p "Are you sure you want to continue? (yes/no): " confirm

    if [ "$confirm" = "yes" ]; then
        restore_database "$selected_file"
    else
        print_info "Restoration cancelled"
    fi
}

# Restore database from backup
restore_database() {
    local backup_file="$1"

    if [ ! -f "$backup_file" ]; then
        print_error "Backup file not found: $backup_file"
        exit 1
    fi

    print_info "Restoring database from: $(basename "$backup_file")"

    # Verify backup before restore
    if ! verify_backup "$backup_file" >/dev/null 2>&1; then
        print_error "Backup verification failed! Restoration aborted."
        exit 1
    fi

    # Create backup before restore
    print_backup "Creating safety backup before restoration..."
    local safety_backup=$(create_backup "custom" "before_restore")

    # Restore database
    print_info "Restoring database..."
    if gunzip -c "$backup_file" | docker exec -i ${CONTAINER_NAME} mysql -u ${DB_USER} -p${DB_PASSWORD} ${DB_NAME}; then
        print_status "Database restoration completed successfully"
        print_info "Safety backup created: $(basename "$safety_backup")"
    else
        print_error "Database restoration failed!"
        print_info "Your original data is safe in: $(basename "$safety_backup")"
        exit 1
    fi
}

# Show backup status and statistics
show_status() {
    echo ""
    echo "📊 Backup Status & Statistics"
    echo "============================="

    # Directory sizes
    if [ -d "$BACKUPS_DIR" ]; then
        local backups_size=$(du -sh "$BACKUPS_DIR" | cut -f1)
        echo "Backups directory size: $backups_size"
    fi

    if [ -d "$ARCHIVES_DIR" ]; then
        local archives_size=$(du -sh "$ARCHIVES_DIR" | cut -f1)
        echo "Archives directory size: $archives_size"
    fi

    # Count backups by type
    echo ""
    echo "Backup counts:"
    for type in "daily" "weekly" "monthly"; do
        local count=$(ls "${BACKUPS_DIR}/${type}_"*.sql.gz 2>/dev/null | wc -l || echo 0)
        echo "  ${type^}: $count files"
    done

    local archive_count=$(ls "${ARCHIVES_DIR}"/*.sql.gz 2>/dev/null | wc -l || echo 0)
    echo "  Archived: $archive_count files"

    # Latest backup
    local latest=$(ls -t "${BACKUPS_DIR}"/*.sql.gz 2>/dev/null | head -1 || true)
    if [ -n "$latest" ]; then
        local latest_date=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$latest" 2>/dev/null || stat -c "%y" "$latest" 2>/dev/null || echo "unknown")
        echo ""
        echo "Latest backup: $(basename "$latest")"
        echo "Created: $latest_date"
    fi

    echo ""
}

# Show current configuration
show_config() {
    echo ""
    echo "⚙️ Backup Configuration"
    echo "======================="
    echo "Database: $DB_NAME"
    echo "Container: $CONTAINER_NAME"
    echo "Backups directory: $BACKUPS_DIR"
    echo "Archives directory: $ARCHIVES_DIR"
    echo ""
    echo "Retention Policy:"
    echo "  Daily backups: $DAILY_RETENTION days"
    echo "  Weekly backups: $WEEKLY_RETENTION weeks"
    echo "  Monthly backups: $MONTHLY_RETENTION months"
    echo ""
}

# Main execution
main() {
    local command="$1"
    local param="$2"

    # Ensure directories exist
    ensure_directories

    case "$command" in
        --create)
            check_container
            create_backup "custom"
            ;;
        --create-daily)
            check_container
            create_backup "daily"
            cleanup_old_backups "daily"
            ;;
        --create-weekly)
            check_container
            create_backup "weekly"
            cleanup_old_backups "weekly"
            ;;
        --create-monthly)
            check_container
            create_backup "monthly"
            cleanup_old_backups "monthly"
            ;;
        --auto)
            check_container
            auto_backup
            ;;
        --restore)
            check_container
            if [ -n "$param" ]; then
                restore_database "$param"
            else
                print_error "Please specify backup file to restore"
                exit 1
            fi
            ;;
        --restore-latest)
            check_container
            local latest=$(ls -t "${BACKUPS_DIR}"/*.sql.gz 2>/dev/null | head -1 || true)
            if [ -n "$latest" ]; then
                restore_database "$latest"
            else
                print_error "No backup files found"
                exit 1
            fi
            ;;
        --restore-interactive)
            check_container
            interactive_restore
            ;;
        --list)
            list_backups "false"
            ;;
        --list-detailed)
            list_backups "true"
            ;;
        --cleanup)
            cleanup_old_backups "daily"
            cleanup_old_backups "weekly"
            cleanup_old_backups "monthly"
            ;;
        --archive)
            if [ -n "$param" ]; then
                archive_backup "$param"
            else
                print_error "Please specify backup file to archive"
                exit 1
            fi
            ;;
        --verify)
            if [ -n "$param" ]; then
                verify_backup "$param"
            else
                print_error "Please specify backup file to verify"
                exit 1
            fi
            ;;
        --size)
            show_status
            ;;
        --status)
            show_status
            ;;
        --config)
            show_config
            ;;
        --help|"")
            show_help
            ;;
        *)
            print_error "Unknown option: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
