#!/usr/bin/env python3
"""
Integrity XD Incentives AWS Architecture Diagram Generator

This script generates comprehensive AWS architecture diagrams using the diagrams library.
It creates multiple diagram types for different audiences and use cases.

Requirements:
    pip install diagrams

Usage:
    python generate_aws_diagrams.py
"""

import os

from diagrams import Cluster, Diagram, Edge
from diagrams.aws.compute import ECS, Fargate
from diagrams.aws.database import RDS, ElasticacheForRedis
from diagrams.aws.integration import SNS
from diagrams.aws.management import Cloudwatch
from diagrams.aws.network import ELB, VPC, CloudFront, Route53
from diagrams.aws.security import IAM, SecretsManager
from diagrams.aws.storage import S3
from diagrams.onprem.client import Client, Users
from diagrams.programming.framework import Django
from diagrams.programming.language import Python


def create_modernized_architecture_diagram():
    """Generate diagram showing proposed AWS modernized architecture"""

    with Diagram(
        "Integrity XD Incentives - Modernized AWS Architecture",
        filename="docs/modernized_aws_architecture",
        show=False,
        direction="TB",
    ):

        # Users and CDN
        users = Users("Users")
        dns = Route53("Route 53\nDNS")
        cdn = CloudFront("CloudFront\nCDN")

        with Cluster("AWS VPC"):
            with Cluster("Public Subnets"):
                alb = ELB("Application\nLoad Balancer")

            with Cluster("Private Subnets - App Tier"):
                with Cluster("ECS Fargate Cluster"):
                    django_tasks = [
                        Fargate("Django Task 1"),
                        Fargate("Django Task 2"),
                        Fargate("Django Task 3"),
                    ]

            with Cluster("Private Subnets - Data Tier"):
                with Cluster("RDS Multi-AZ"):
                    rds_primary = RDS("MySQL Primary")
                    rds_standby = RDS("MySQL Standby")

                redis_cache = ElasticacheForRedis("Redis Cache\nSessions")

        with Cluster("Storage & Services"):
            s3_static = S3("S3 Static Files")
            s3_media = S3("S3 Media Files")
            secrets = SecretsManager("Secrets Manager\nDB Credentials")
            email_service = SNS("Email Service\n(SES)")

        with Cluster("Monitoring & Management"):
            cloudwatch = Cloudwatch("CloudWatch\nLogs & Metrics")

        # Connections
        users >> dns >> cdn >> alb
        alb >> django_tasks
        django_tasks >> rds_primary
        rds_primary >> rds_standby
        django_tasks >> redis_cache
        django_tasks >> s3_static
        django_tasks >> s3_media
        django_tasks >> secrets
        django_tasks >> email_service
        django_tasks >> cloudwatch


def create_data_flow_diagram():
    """Generate diagram showing data flow through the system"""

    with Diagram(
        "Integrity XD Incentives - Data Flow Architecture",
        filename="docs/data_flow_modernized",
        show=False,
        direction="LR",
    ):

        # Data Sources
        with Cluster("Data Input"):
            users = Users("Users")
            admins = Users("Admins")
            bulk_import = Client("Bulk Import\nCSV/Excel")

        # Application Layer
        with Cluster("Application Processing"):
            django_api = Django("Django REST API")
            validation = Python("Data Validation\nEngine")
            business_logic = Python("Business Logic\nProcessor")

        # Data Storage
        with Cluster("Data Storage"):
            transactional_db = RDS("Transactional DB\n(MySQL)")
            cache_layer = ElasticacheForRedis("Cache Layer\n(Redis)")
            file_storage = S3("File Storage\n(S3)")

        # Data Output
        with Cluster("Data Output"):
            reports = Client("Reports &\nDashboards")
            email_notifications = SNS("Email\nNotifications")
            api_responses = Client("API Responses\n(JSON)")

        # Flow connections
        users >> Edge(label="Sales Data") >> django_api
        admins >> Edge(label="Approvals") >> django_api
        bulk_import >> Edge(label="Batch Data") >> django_api

        django_api >> validation >> business_logic
        business_logic >> transactional_db
        business_logic >> cache_layer
        business_logic >> file_storage

        transactional_db >> reports
        business_logic >> email_notifications
        django_api >> api_responses


def create_security_architecture_diagram():
    """Generate diagram showing security architecture"""

    with Diagram(
        "Integrity XD Incentives - Security Architecture",
        filename="docs/security_architecture",
        show=False,
        direction="TB",
    ):

        # External Access
        internet = Client("Internet")

        with Cluster("Security Perimeter"):
            waf = CloudFront("WAF & CloudFront\nDDoS Protection")

            with Cluster("VPC Security"):
                with Cluster("Public Subnet"):
                    alb = ELB("ALB with\nSSL Termination")

                with Cluster("Private Subnet - App"):
                    with Cluster("ECS Security Groups"):
                        app_tasks = Fargate("Django Apps\n(Port 8000 only)")

                with Cluster("Private Subnet - Data"):
                    with Cluster("Database Security Groups"):
                        database = RDS("Encrypted RDS\n(Port 3306 only)")
                        cache = ElasticacheForRedis("Redis Cache\n(Port 6379 only)")

        with Cluster("Identity & Access"):
            iam_roles = IAM("IAM Roles\n& Policies")
            secrets_mgr = SecretsManager("Secrets Manager\nDB Passwords")

        with Cluster("Monitoring & Compliance"):
            cloudwatch_logs = Cloudwatch("CloudWatch\nSecurity Logs")

        # Security flow
        internet >> waf >> alb >> app_tasks
        app_tasks >> database
        app_tasks >> cache
        app_tasks >> secrets_mgr
        iam_roles >> app_tasks
        app_tasks >> cloudwatch_logs


def create_deployment_pipeline_diagram():
    """Generate diagram showing CI/CD deployment pipeline"""

    with Diagram(
        "Integrity XD Incentives - Deployment Pipeline",
        filename="docs/deployment_pipeline",
        show=False,
        direction="LR",
    ):

        # Source Control
        with Cluster("Source Control"):
            git_repo = Client("Git Repository\n(GitHub)")

        # CI/CD Pipeline
        with Cluster("CI/CD Pipeline - GitHub Actions"):
            build_stage = Python("Build & Test")
            docker_build = Client("Docker Image\nBuild")
            security_scan = IAM("Security\nScanning")

        # Container Registry
        with Cluster("Container Registry"):
            ecr = Client("Amazon ECR\nContainer Registry")

        # Deployment Environments
        with Cluster("Development"):
            dev_ecs = ECS("ECS Dev\nCluster")
            dev_rds = RDS("Dev Database")

        with Cluster("Staging"):
            staging_ecs = ECS("ECS Staging\nCluster")
            staging_rds = RDS("Staging Database")

        with Cluster("Production"):
            prod_ecs = ECS("ECS Production\nCluster")
            prod_rds = RDS("Production Database")

        # Infrastructure as Code
        with Cluster("Infrastructure"):
            terraform = Client("Terraform\nTemplates")

        # Pipeline flow
        git_repo >> build_stage >> docker_build >> security_scan >> ecr
        ecr >> dev_ecs
        dev_ecs >> staging_ecs
        staging_ecs >> prod_ecs
        terraform >> [dev_ecs, staging_ecs, prod_ecs]
        [dev_rds, staging_rds, prod_rds]


def create_cost_optimization_diagram():
    """Generate diagram showing cost optimization strategies"""

    with Diagram(
        "Integrity XD Incentives - Cost Optimization Architecture",
        filename="docs/cost_optimization",
        show=False,
        direction="TB",
    ):

        with Cluster("Compute Optimization"):
            fargate_spot = Fargate("Fargate Spot\n70% Cost Savings")
            auto_scaling = ECS("Auto Scaling\nBased on Metrics")

        with Cluster("Storage Optimization"):
            s3_intelligent = S3("S3 Intelligent\nTiering")
            s3_lifecycle = S3("S3 Lifecycle\nPolicies")

        with Cluster("Database Optimization"):
            rds_reserved = RDS("RDS Reserved\nInstances")
            read_replicas = RDS("Read Replicas\nfor Reports")

        with Cluster("Network Optimization"):
            cloudfront_cache = CloudFront("CloudFront\nCaching Strategy")
            vpc_endpoints = VPC("VPC Endpoints\nReduce NAT Costs")

        with Cluster("Monitoring & Alerts"):
            cost_monitoring = Cloudwatch("Cost Monitoring\n& Alerts")
            rightsizing = Cloudwatch("Right-sizing\nRecommendations")

        # Cost optimization connections
        auto_scaling >> fargate_spot
        s3_intelligent >> s3_lifecycle
        rds_reserved >> read_replicas
        cloudfront_cache >> vpc_endpoints
        cost_monitoring >> rightsizing


def main():
    """Generate all architecture diagrams"""

    print("Generating CITGO Go-For-Green Architecture Diagrams...")

    # Create docs directory if it doesn't exist
    os.makedirs("docs", exist_ok=True)

    # Generate all diagrams
    diagrams_to_generate = [
        ("Modernized AWS Architecture", create_modernized_architecture_diagram),
        ("Data Flow Architecture", create_data_flow_diagram),
        ("Security Architecture", create_security_architecture_diagram),
        ("Deployment Pipeline", create_deployment_pipeline_diagram),
        ("Cost Optimization", create_cost_optimization_diagram),
    ]

    for name, func in diagrams_to_generate:
        try:
            print(f"  Generating {name}...")
            func()
            print(f"  ✓ {name} completed")
        except Exception as e:
            print(f"  ✗ Error generating {name}: {str(e)}")

    print("\nDiagram generation completed!")
    print("\nGenerated files:")
    print("  - docs/modernized_aws_architecture.png")
    print("  - docs/data_flow_modernized.png")
    print("  - docs/security_architecture.png")
    print("  - docs/deployment_pipeline.png")
    print("  - docs/cost_optimization.png")

    print("\nNext steps:")
    print("1. Review generated PNG files")
    print("2. Customize diagrams as needed")
    print("3. Use Terraform template for infrastructure deployment")
    print("4. Integrate with your existing documentation")


if __name__ == "__main__":
    main()
