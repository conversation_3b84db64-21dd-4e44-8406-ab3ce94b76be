#!/usr/bin/env python3
"""
Generate Improved ERD Diagram for Member Models as PNG
Better spacing and layout to prevent text overlapping
"""

import matplotlib.patches as patches
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import ConnectionPatch, FancyBboxPatch


def create_improved_erd_diagram():
    """Create improved ERD diagram for member models with better spacing"""

    # Set up the figure with better proportions
    fig, ax = plt.subplots(1, 1, figsize=(24, 20))
    ax.set_xlim(0, 24)
    ax.set_ylim(0, 20)
    ax.axis("off")

    # Colors
    primary_color = "#2E86AB"
    secondary_color = "#A23B72"
    accent_color = "#F18F01"
    light_color = "#C73E1D"
    bg_color = "#F8F9FA"

    # Title with better spacing
    ax.text(
        12,
        19.5,
        "MEMBER MODELS ERD",
        fontsize=28,
        fontweight="bold",
        ha="center",
        va="center",
        color=primary_color,
    )
    ax.text(
        12,
        19,
        "XD Incentives System - Database Schema",
        fontsize=16,
        ha="center",
        va="center",
        color="gray",
    )

    # Core Models Section - Better spacing
    ax.text(2, 18, "CORE MODELS", fontsize=18, fontweight="bold", color=primary_color)

    # MemberType - Larger box with better text spacing
    member_type_box = FancyBboxPatch(
        (0.5, 16),
        5,
        1.5,
        boxstyle="round,pad=0.15",
        facecolor=primary_color,
        alpha=0.1,
        edgecolor=primary_color,
        linewidth=2,
    )
    ax.add_patch(member_type_box)
    ax.text(3, 17.2, "MemberType", fontsize=14, fontweight="bold", ha="center")
    ax.text(3, 16.9, "PK: id", fontsize=11, ha="center")
    ax.text(3, 16.6, "name, slug, description", fontsize=10, ha="center")
    ax.text(3, 16.3, "permissions, page_access, navigation", fontsize=10, ha="center")
    ax.text(
        3,
        16.0,
        "feature_flags, dashboard_layout, theme_settings",
        fontsize=10,
        ha="center",
    )

    # Member (AbstractUser) - Larger box
    member_box = FancyBboxPatch(
        (7, 16),
        5,
        1.5,
        boxstyle="round,pad=0.15",
        facecolor=secondary_color,
        alpha=0.1,
        edgecolor=secondary_color,
        linewidth=2,
    )
    ax.add_patch(member_box)
    ax.text(
        9.5, 17.2, "Member (AbstractUser)", fontsize=14, fontweight="bold", ha="center"
    )
    ax.text(9.5, 16.9, "PK: id (from AbstractUser)", fontsize=11, ha="center")
    ax.text(9.5, 16.6, "username, email, password", fontsize=10, ha="center")
    ax.text(9.5, 16.3, "status, tier, addresses, phones", fontsize=10, ha="center")
    ax.text(9.5, 16.0, "FK: region_id, member_type_id", fontsize=10, ha="center")

    # Region - Larger box
    region_box = FancyBboxPatch(
        (13.5, 16),
        4,
        1.5,
        boxstyle="round,pad=0.15",
        facecolor=accent_color,
        alpha=0.1,
        edgecolor=accent_color,
        linewidth=2,
    )
    ax.add_patch(region_box)
    ax.text(15.5, 17.2, "Region", fontsize=14, fontweight="bold", ha="center")
    ax.text(15.5, 16.9, "PK: id", fontsize=11, ha="center")
    ax.text(15.5, 16.6, "title, can_signup", fontsize=10, ha="center")
    ax.text(15.5, 16.3, "created, modified", fontsize=10, ha="center")

    # Relationships with better positioning
    # MemberType -> Member
    con1 = ConnectionPatch(
        (5.5, 16.75),
        (7, 16.75),
        "data",
        "data",
        arrowstyle="->",
        shrinkA=5,
        shrinkB=5,
        mutation_scale=20,
        fc=primary_color,
        linewidth=2,
    )
    ax.add_patch(con1)
    ax.text(6.25, 17.1, "1:N", fontsize=12, ha="center", fontweight="bold")

    # Member -> Region
    con2 = ConnectionPatch(
        (12, 16.75),
        (13.5, 16.75),
        "data",
        "data",
        arrowstyle="->",
        shrinkA=5,
        shrinkB=5,
        mutation_scale=20,
        fc=secondary_color,
        linewidth=2,
    )
    ax.add_patch(con2)
    ax.text(12.75, 17.1, "N:1", fontsize=12, ha="center", fontweight="bold")

    # Hierarchy Section - Better spacing
    ax.text(
        2, 15, "HIERARCHY SYSTEM", fontsize=18, fontweight="bold", color=primary_color
    )

    # MemberHierarchy - Larger box
    hierarchy_box = FancyBboxPatch(
        (6, 13),
        10,
        1.5,
        boxstyle="round,pad=0.15",
        facecolor=light_color,
        alpha=0.1,
        edgecolor=light_color,
        linewidth=2,
    )
    ax.add_patch(hierarchy_box)
    ax.text(
        11,
        14.2,
        "MemberHierarchy (Many-to-Many)",
        fontsize=14,
        fontweight="bold",
        ha="center",
    )
    ax.text(11, 13.9, "PK: id", fontsize=11, ha="center")
    ax.text(11, 13.6, "FK: member_id, manager_id", fontsize=10, ha="center")
    ax.text(11, 13.3, "relationship_type, is_primary", fontsize=10, ha="center")
    ax.text(11, 13.0, "start_date, end_date, notes", fontsize=10, ha="center")

    # Team Section - Better spacing
    ax.text(
        2, 12.5, "TEAM MANAGEMENT", fontsize=18, fontweight="bold", color=primary_color
    )

    # Team - Larger box
    team_box = FancyBboxPatch(
        (0.5, 10.5),
        5,
        1.5,
        boxstyle="round,pad=0.15",
        facecolor=accent_color,
        alpha=0.1,
        edgecolor=accent_color,
        linewidth=2,
    )
    ax.add_patch(team_box)
    ax.text(3, 11.7, "Team", fontsize=14, fontweight="bold", ha="center")
    ax.text(3, 11.4, "PK: id", fontsize=11, ha="center")
    ax.text(3, 11.1, "name, team_type, description", fontsize=10, ha="center")
    ax.text(3, 10.8, "FK: team_lead_id, is_active", fontsize=10, ha="center")

    # MemberTeam - Larger box
    member_team_box = FancyBboxPatch(
        (7, 10.5),
        10,
        1.5,
        boxstyle="round,pad=0.15",
        facecolor=light_color,
        alpha=0.1,
        edgecolor=light_color,
        linewidth=2,
    )
    ax.add_patch(member_team_box)
    ax.text(
        12,
        11.7,
        "MemberTeam (Many-to-Many)",
        fontsize=14,
        fontweight="bold",
        ha="center",
    )
    ax.text(12, 11.4, "PK: id", fontsize=11, ha="center")
    ax.text(12, 11.1, "FK: member_id, team_id", fontsize=10, ha="center")
    ax.text(
        12, 10.8, "role, is_primary, start_date, end_date", fontsize=10, ha="center"
    )

    # Team relationship
    con3 = ConnectionPatch(
        (5.5, 11.25),
        (7, 11.25),
        "data",
        "data",
        arrowstyle="->",
        shrinkA=5,
        shrinkB=5,
        mutation_scale=20,
        fc=accent_color,
        linewidth=2,
    )
    ax.add_patch(con3)
    ax.text(6.25, 11.6, "1:N", fontsize=12, ha="center", fontweight="bold")

    # Supporting Models Section - Better spacing
    ax.text(
        2, 10, "SUPPORTING MODELS", fontsize=18, fontweight="bold", color=primary_color
    )

    # First row of supporting models - Larger boxes
    # Terms
    terms_box = FancyBboxPatch(
        (0.5, 8.5),
        3.5,
        1.2,
        boxstyle="round,pad=0.1",
        facecolor="lightblue",
        alpha=0.1,
        edgecolor="lightblue",
        linewidth=1,
    )
    ax.add_patch(terms_box)
    ax.text(2.25, 9.4, "Terms", fontsize=12, fontweight="bold", ha="center")
    ax.text(2.25, 9.1, "PK: id, title, current", fontsize=9, ha="center")

    # Privacy
    privacy_box = FancyBboxPatch(
        (4.5, 8.5),
        3.5,
        1.2,
        boxstyle="round,pad=0.1",
        facecolor="lightgreen",
        alpha=0.1,
        edgecolor="lightgreen",
        linewidth=1,
    )
    ax.add_patch(privacy_box)
    ax.text(6.25, 9.4, "Privacy", fontsize=12, fontweight="bold", ha="center")
    ax.text(6.25, 9.1, "PK: id, title, current", fontsize=9, ha="center")

    # Communication
    comm_box = FancyBboxPatch(
        (8.5, 8.5),
        3.5,
        1.2,
        boxstyle="round,pad=0.1",
        facecolor="lightcoral",
        alpha=0.1,
        edgecolor="lightcoral",
        linewidth=1,
    )
    ax.add_patch(comm_box)
    ax.text(10.25, 9.4, "Communication", fontsize=12, fontweight="bold", ha="center")
    ax.text(10.25, 9.1, "PK: id, communication_type", fontsize=9, ha="center")

    # EmailTemplate
    template_box = FancyBboxPatch(
        (12.5, 8.5),
        3.5,
        1.2,
        boxstyle="round,pad=0.1",
        facecolor="lightyellow",
        alpha=0.1,
        edgecolor="lightyellow",
        linewidth=1,
    )
    ax.add_patch(template_box)
    ax.text(14.25, 9.4, "EmailTemplate", fontsize=12, fontweight="bold", ha="center")
    ax.text(14.25, 9.1, "PK: id, name, template_type", fontsize=9, ha="center")

    # Second row of supporting models
    # MemberAuthToken
    auth_box = FancyBboxPatch(
        (0.5, 7),
        3.5,
        1.2,
        boxstyle="round,pad=0.1",
        facecolor="lavender",
        alpha=0.1,
        edgecolor="lavender",
        linewidth=1,
    )
    ax.add_patch(auth_box)
    ax.text(2.25, 7.9, "MemberAuthToken", fontsize=12, fontweight="bold", ha="center")
    ax.text(2.25, 7.6, "PK: id, FK: member_id", fontsize=9, ha="center")

    # MemberTermsLog
    terms_log_box = FancyBboxPatch(
        (4.5, 7),
        3.5,
        1.2,
        boxstyle="round,pad=0.1",
        facecolor="lightcyan",
        alpha=0.1,
        edgecolor="lightcyan",
        linewidth=1,
    )
    ax.add_patch(terms_log_box)
    ax.text(6.25, 7.9, "MemberTermsLog", fontsize=12, fontweight="bold", ha="center")
    ax.text(6.25, 7.6, "FK: member_id, terms_id", fontsize=9, ha="center")

    # MemberPrivacyLog
    privacy_log_box = FancyBboxPatch(
        (8.5, 7),
        3.5,
        1.2,
        boxstyle="round,pad=0.1",
        facecolor="lightpink",
        alpha=0.1,
        edgecolor="lightpink",
        linewidth=1,
    )
    ax.add_patch(privacy_log_box)
    ax.text(10.25, 7.9, "MemberPrivacyLog", fontsize=12, fontweight="bold", ha="center")
    ax.text(10.25, 7.6, "FK: member_id, privacy_id", fontsize=9, ha="center")

    # MemberW9Upload
    w9_box = FancyBboxPatch(
        (12.5, 7),
        3.5,
        1.2,
        boxstyle="round,pad=0.1",
        facecolor="lightsteelblue",
        alpha=0.1,
        edgecolor="lightsteelblue",
        linewidth=1,
    )
    ax.add_patch(w9_box)
    ax.text(14.25, 7.9, "MemberW9Upload", fontsize=12, fontweight="bold", ha="center")
    ax.text(14.25, 7.6, "FK: member_id, file", fontsize=9, ha="center")

    # Third row of supporting models
    # PasswordReset
    pwd_box = FancyBboxPatch(
        (0.5, 5.5),
        3.5,
        1.2,
        boxstyle="round,pad=0.1",
        facecolor="mistyrose",
        alpha=0.1,
        edgecolor="mistyrose",
        linewidth=1,
    )
    ax.add_patch(pwd_box)
    ax.text(2.25, 6.4, "PasswordReset", fontsize=12, fontweight="bold", ha="center")
    ax.text(2.25, 6.1, "FK: member_id, link", fontsize=9, ha="center")

    # TemplateBuilder
    builder_box = FancyBboxPatch(
        (4.5, 5.5),
        3.5,
        1.2,
        boxstyle="round,pad=0.1",
        facecolor="honeydew",
        alpha=0.1,
        edgecolor="honeydew",
        linewidth=1,
    )
    ax.add_patch(builder_box)
    ax.text(6.25, 6.4, "TemplateBuilder", fontsize=12, fontweight="bold", ha="center")
    ax.text(6.25, 6.1, "FK: template_id, component_type", fontsize=9, ha="center")

    # Permission
    perm_box = FancyBboxPatch(
        (8.5, 5.5),
        3.5,
        1.2,
        boxstyle="round,pad=0.1",
        facecolor="seashell",
        alpha=0.1,
        edgecolor="seashell",
        linewidth=1,
    )
    ax.add_patch(perm_box)
    ax.text(10.25, 6.4, "Permission", fontsize=12, fontweight="bold", ha="center")
    ax.text(10.25, 6.1, "PK: id, name, slug, category", fontsize=9, ha="center")

    # PageAccess
    page_box = FancyBboxPatch(
        (12.5, 5.5),
        3.5,
        1.2,
        boxstyle="round,pad=0.1",
        facecolor="oldlace",
        alpha=0.1,
        edgecolor="oldlace",
        linewidth=1,
    )
    ax.add_patch(page_box)
    ax.text(14.25, 6.4, "PageAccess", fontsize=12, fontweight="bold", ha="center")
    ax.text(14.25, 6.1, "PK: id, name, slug, url_pattern", fontsize=9, ha="center")

    # Key Relationships Section - Better spacing
    ax.text(
        2, 4.5, "KEY RELATIONSHIPS", fontsize=18, fontweight="bold", color=primary_color
    )

    relationships = [
        "1. Member → MemberType (N:1) - Each member has one member type",
        "2. Member → Region (N:1) - Each member belongs to one region",
        "3. Member → Member (Self-referencing) - Approval relationships",
        "4. Member ↔ Member (Many-to-Many via MemberHierarchy) - Manager/subordinate",
        "5. Member ↔ Team (Many-to-Many via MemberTeam) - Team memberships with roles",
        "6. MemberType → MemberType (Many-to-Many) - Which types can manage other types",
        "7. Member → Various Log Models (1:N) - Authentication, terms, privacy, W9 uploads",
        "8. Member → Communication (1:N) - Sent and received communications",
        "9. EmailTemplate → TemplateBuilder (1:N) - Template components",
        "10. Member → PasswordReset (1:N) - Password reset tokens",
    ]

    for i, rel in enumerate(relationships):
        ax.text(0.5, 4.2 - i * 0.3, rel, fontsize=11, ha="left", va="center")

    # JSON Fields Section - Much more space from relationships
    ax.text(2, 0.8, "JSON FIELDS", fontsize=18, fontweight="bold", color=primary_color)

    json_info = [
        "MemberType JSON Fields:",
        '• permissions: {"permissions": ["admin.access", "member.view", ...]}',
        '• page_access: {"pages": {"dashboard": {"access": true}, "reports": {"access": false}}}',
        '• navigation: {"menu": [{"label": "Dashboard", "url": "/dashboard"}, ...]}',
        '• feature_flags: {"features": ["reports", "analytics", "export"]}',
        '• dashboard_layout: {"widgets": [{"type": "chart", "position": "top-left"}, ...]}',
        '• theme_settings: {"primary_color": "#007bff", "font_family": "Arial"}',
        "",
        "EmailTemplate JSON Fields:",
        '• variables: {"user_name": "string", "company": "string", ...}',
        "",
        "TemplateBuilder JSON Fields:",
        '• config: {"button_text": "Click Here", "button_color": "#007bff", ...}',
    ]

    for i, info in enumerate(json_info):
        ax.text(0.5, 0.5 - i * 0.25, info, fontsize=10, ha="left", va="center")

    # Footer
    ax.text(
        12,
        0.5,
        "Generated on 2025-07-16 | XD Incentives System",
        fontsize=12,
        ha="center",
        va="center",
        color="gray",
        style="italic",
    )

    return fig


def main():
    """Generate and save the improved ERD diagram"""
    print("Generating improved ERD diagram...")

    # Create the diagram
    fig = create_improved_erd_diagram()

    # Save as PNG with better DPI
    output_file = "member_models_erd_improved.png"
    fig.savefig(
        output_file, dpi=200, bbox_inches="tight", facecolor="white", edgecolor="none"
    )

    print(f"Improved ERD diagram saved as: {output_file}")

    # Also save as PDF for better quality
    pdf_file = "member_models_erd_improved.pdf"
    fig.savefig(pdf_file, bbox_inches="tight", facecolor="white", edgecolor="none")
    print(f"Improved ERD diagram also saved as: {pdf_file}")

    plt.close(fig)
    print("Done!")


if __name__ == "__main__":
    main()
