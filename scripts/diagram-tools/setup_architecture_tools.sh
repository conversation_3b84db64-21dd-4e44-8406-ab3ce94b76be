#!/bin/bash

# CITGO Go-For-Green Architecture Tools Setup Script
# This script sets up the architecture diagram generation tools

set -e

echo "🚀 Setting up CITGO Go-For-Green Architecture Tools..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed."
    echo "Please install Python 3 and try again."
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is required but not installed."
    echo "Please install pip3 and try again."
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv_diagrams" ]; then
    echo "📦 Creating virtual environment for diagram tools..."
    python3 -m venv venv_diagrams
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv_diagrams/bin/activate

# Install required packages
echo "📥 Installing required packages..."
pip install --upgrade pip
pip install diagrams
pip install boto3
pip install pyyaml
pip install graphviz

# Check if Graphviz system package is installed (required for diagrams library)
if ! command -v dot &> /dev/null; then
    echo "⚠️  Graphviz system package is required for diagram generation."
    echo "Please install it using:"
    echo "  macOS: brew install graphviz"
    echo "  Ubuntu/Debian: sudo apt-get install graphviz"
    echo "  CentOS/RHEL: sudo yum install graphviz"
    echo ""
    echo "After installing Graphviz, run this script again."
    exit 1
fi

# Create docs directory if it doesn't exist
mkdir -p docs

# Make the diagram generation script executable
chmod +x generate_aws_diagrams.py 2>/dev/null || echo "Note: generate_aws_diagrams.py not found"

echo "✅ Setup completed successfully!"
echo ""
echo "🎯 Next steps:"
echo "1. Generate architecture diagrams:"
echo "   python generate_aws_diagrams.py"
echo ""
echo ""
echo "🔍 Available diagram types:"
echo "   - Current State Architecture"
echo "   - Modernized AWS Architecture"
echo "   - Data Flow Architecture"
echo "   - Security Architecture"
echo "   - Deployment Pipeline"
echo "   - Cost Optimization"
echo ""
echo "📁 Generated files will be saved in the docs/ directory as PNG images."
echo ""
echo "💡 Pro tip: Use 'source venv_diagrams/bin/activate' to activate the environment"
echo "   when you want to run the diagram generation scripts."

# Test if we can generate a simple diagram
echo ""
echo "🧪 Testing diagram generation..."
python3 -c "
from diagrams import Diagram
from diagrams.aws.compute import ECS
from diagrams.aws.database import RDS

with Diagram('Test Diagram', filename='docs/test_diagram', show=False):
    app = ECS('Django App')
    db = RDS('MySQL DB')
    app >> db

print('✅ Test diagram generated successfully: docs/test_diagram.png')
"

echo ""
echo "🎉 All systems ready! You can now generate comprehensive architecture diagrams."
echo "   Run: python generate_aws_diagrams.py"
