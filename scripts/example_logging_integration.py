"""
Example integration of logging utilities in Django views and business logic
This file demonstrates how to use the logging system in real application code
"""

import logging

from django.contrib.auth import authenticate, login
from django.http import JsonResponse
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import csrf_exempt

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from config.logging_utils import (
    PerformanceTimer,
    log_authentication_event,
    log_business_event,
    log_member_activity,
    log_permission_event,
    log_security_event,
    performance_monitor,
)

# Get loggers
logger = logging.getLogger("apps.api")
business_logger = logging.getLogger("business")


class ExampleAPIView(View):
    """
    Example API view demonstrating logging integration
    """

    def get(self, request):
        """Example GET endpoint with logging"""
        # Log API access
        logger.info(
            "API endpoint accessed",
            extra={
                "request": request,
                "extra_data": {
                    "endpoint": "example_api",
                    "method": "GET",
                },
            },
        )

        # Example business logic with performance monitoring
        with PerformanceTimer("database_query") as timer:
            # Simulate database operation
            import time

            time.sleep(0.05)  # 50ms simulated query

        return JsonResponse(
            {
                "message": "Example API response",
                "correlation_id": getattr(request, "correlation_id", "N/A"),
            }
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def example_member_action(request):
    """Example member action with business logging"""

    # Log the business action
    log_member_activity(
        member=request.user,
        action="profile_updated",
        details={
            "fields_updated": ["email", "phone"],
            "previous_email": "<EMAIL>",
            "new_email": request.data.get("email", ""),
        },
        request=request,
    )

    return Response({"status": "success"})


@csrf_exempt
def example_login_view(request):
    """Example login view with authentication logging"""
    if request.method == "POST":
        username = request.POST.get("username")
        password = request.POST.get("password")

        # Attempt authentication
        user = authenticate(request, username=username, password=password)

        if user is not None:
            if user.is_active:
                login(request, user)

                # Log successful login
                log_authentication_event(
                    event_type="login_success",
                    username=username,
                    success=True,
                    details={
                        "user_id": user.id,
                        "login_method": "password",
                        "is_staff": user.is_staff,
                    },
                    request=request,
                )

                return JsonResponse({"status": "success"})
            else:
                # Log inactive account access attempt
                log_security_event(
                    event_type="inactive_account_access",
                    severity="medium",
                    details={
                        "username": username,
                        "account_status": "inactive",
                    },
                    user=user,
                    request=request,
                )

                return JsonResponse({"error": "Account disabled"}, status=403)
        else:
            # Log failed login
            log_authentication_event(
                event_type="login_failed",
                username=username,
                success=False,
                details={
                    "failure_reason": "invalid_credentials",
                    "attempted_username": username,
                },
                request=request,
            )

            return JsonResponse({"error": "Invalid credentials"}, status=401)

    return JsonResponse({"error": "Method not allowed"}, status=405)


@performance_monitor("team_creation")
def create_team_example(name, description, created_by):
    """Example team creation function with performance monitoring"""

    # Simulate team creation logic
    import time

    time.sleep(0.1)  # 100ms simulated operation

    # Log business event
    log_business_event(
        action="team.created",
        details={
            "team_name": name,
            "description": description,
            "created_by_id": created_by.id,
            "created_by_username": created_by.username,
        },
        user=created_by,
    )

    return {"id": 123, "name": name, "description": description}


def example_permission_check(request, resource):
    """Example permission checking with logging"""

    # Check permission (simplified example)
    has_permission = request.user.is_staff

    # Log permission check
    log_permission_event(
        event_type="resource_access",
        resource=resource,
        permission="read",
        user=request.user,
        granted=has_permission,
        request=request,
    )

    if not has_permission:
        # Log security event for denied access
        log_security_event(
            event_type="access_denied",
            severity="medium",
            details={
                "resource": resource,
                "required_permission": "read",
                "user_permissions": "basic_user",
            },
            user=request.user,
            request=request,
        )

    return has_permission


def example_business_workflow(member, action_data):
    """Example business workflow with comprehensive logging"""

    # Start workflow
    log_business_event(
        action="workflow.started",
        details={
            "workflow_type": "member_enrollment",
            "member_id": member.id,
            "workflow_data": action_data,
        },
        user=member,
    )

    try:
        # Step 1: Validation
        with PerformanceTimer("validation_step", threshold_ms=50) as timer:
            # Simulate validation
            if not action_data.get("email"):
                raise ValueError("Email is required")

        # Step 2: Processing
        with PerformanceTimer("processing_step", threshold_ms=200) as timer:
            # Simulate processing
            import time

            time.sleep(0.1)

        # Log successful completion
        log_business_event(
            action="workflow.completed",
            details={
                "workflow_type": "member_enrollment",
                "member_id": member.id,
                "result": "success",
            },
            user=member,
        )

        return {"status": "success", "member_id": member.id}

    except Exception as e:
        # Log workflow failure
        log_business_event(
            action="workflow.failed",
            details={
                "workflow_type": "member_enrollment",
                "member_id": member.id,
                "error_type": type(e).__name__,
                "error_message": str(e),
            },
            user=member,
            level=logging.ERROR,
        )

        raise


# Example usage in a view
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def member_enrollment_api(request):
    """Example API endpoint using the logging workflow"""

    try:
        result = example_business_workflow(
            member=request.user, action_data=request.data
        )
        return Response(result)

    except ValueError as e:
        logger.warning(
            "Validation error in member enrollment: Invalid input provided",
            extra={"request": request},
            exc_info=False,
        )
        return Response({"error": "Invalid input"}, status=400)
    except Exception as e:
        logger.error(
            f"Unexpected error in member enrollment: {e}",
            extra={"request": request},
            exc_info=True,
        )
        return Response({"error": "Internal server error"}, status=500)
