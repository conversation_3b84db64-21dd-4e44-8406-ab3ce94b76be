#!/usr/bin/env python
"""Run Daphne ASGI server with automatic reload using watchdog.

This script monitors Python files for changes and restarts the server automatically.
Supports different Django settings modules via DJANGO_SETTINGS_MODULE environment variable.
"""

import logging
import os
import signal
import subprocess
import sys
import time
from pathlib import Path

from watchdog.events import FileSystemEventHandler
from watchdog.observers import Observer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger(__name__)


class ReloadHandler(FileSystemEventHandler):
    """Handler that restarts the server when Python files change."""

    def __init__(self, restart_callback):
        self.restart_callback = restart_callback
        self.last_restart = 0
        self.restart_delay = 1  # Minimum seconds between restarts

    def on_modified(self, event):
        if event.src_path.endswith(".py"):
            current_time = time.time()
            if current_time - self.last_restart > self.restart_delay:
                logger.info(f"✨ Detected change in {event.src_path}")
                logger.info("🔄 Reloading server...")
                self.last_restart = current_time
                self.restart_callback()


class DaphneRunner:
    """Manages the Daphne ASGI server process."""

    def __init__(self):
        self.process = None
        # Get Django settings module from environment or default to development
        self.settings_module = os.environ.get(
            "DJANGO_SETTINGS_MODULE", "config.settings.development"
        )
        self.command = [
            "daphne",
            "-b",
            "0.0.0.0",
            "-p",
            "8000",
            "config.asgi:application",
        ]

    def start(self):
        """Start the Daphne server."""
        if self.process:
            self.stop()

        logger.info(f"🚀 Starting Daphne server with settings: {self.settings_module}")
        # Set the environment variable for the subprocess
        env = os.environ.copy()
        env["DJANGO_SETTINGS_MODULE"] = self.settings_module
        self.process = subprocess.Popen(
            self.command,
            stdout=sys.stdout,
            stderr=sys.stderr,
            preexec_fn=os.setsid,
            env=env,
        )

    def stop(self):
        """Stop the Daphne server."""
        if self.process:
            logger.info("⏹️  Stopping server...")
            try:
                # Send SIGTERM to the process group
                os.killpg(os.getpgid(self.process.pid), signal.SIGTERM)
                self.process.wait(timeout=5)
            except (subprocess.TimeoutExpired, ProcessLookupError):
                # Force kill if graceful shutdown fails
                try:
                    os.killpg(os.getpgid(self.process.pid), signal.SIGKILL)
                except ProcessLookupError:
                    pass
            self.process = None

    def restart(self):
        """Restart the Daphne server."""
        self.stop()
        time.sleep(0.5)  # Brief pause before restart
        self.start()


def main():
    """Main function to set up file watching and run the server."""
    # Change to backend directory
    backend_dir = Path(__file__).parent.parent / "backend"
    os.chdir(backend_dir)

    # Run migrations first
    settings_module = os.environ.get(
        "DJANGO_SETTINGS_MODULE", "config.settings.development"
    )
    logger.info(f"📦 Running migrations with settings: {settings_module}")
    env = os.environ.copy()
    env["DJANGO_SETTINGS_MODULE"] = settings_module
    subprocess.run(["python", "manage.py", "migrate"], check=True, env=env)

    # Create server runner
    runner = DaphneRunner()

    # Set up file watcher
    event_handler = ReloadHandler(runner.restart)
    observer = Observer()

    # Watch the backend directory and subdirectories
    observer.schedule(event_handler, path=str(backend_dir), recursive=True)

    # Start watching
    observer.start()
    logger.info(f"👀 Watching for file changes in {backend_dir}")

    # Start the server
    runner.start()

    try:
        # Keep the script running
        while True:
            time.sleep(1)
            # Check if server process died unexpectedly
            if runner.process and runner.process.poll() is not None:
                logger.warning("⚠️  Server process died unexpectedly, restarting...")
                runner.start()
    except KeyboardInterrupt:
        logger.info("\n👋 Shutting down...")
        runner.stop()
        observer.stop()

    observer.join()


if __name__ == "__main__":
    main()
