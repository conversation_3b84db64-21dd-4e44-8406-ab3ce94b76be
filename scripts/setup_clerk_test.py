#!/usr/bin/env python3
"""
Clerk Test Setup Script
This script helps you set up Clerk for testing the authentication.
"""

import os
import sys
from pathlib import Path


def main():
    print("🔧 Clerk Test Setup")
    print("=" * 50)

    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found!")
        print("Please create a .env file with your Django settings first.")
        return

    print("📋 To test Clerk authentication, you need to:")
    print()
    print("1. 🎯 Sign up at https://clerk.com")
    print("2. 🔑 Create a new application")
    print("3. 📝 Get your keys from the Clerk dashboard")
    print("4. ⚙️  Add the keys to your .env file")
    print()

    # Check current .env file
    with open(".env", "r") as f:
        env_content = f.read()

    clerk_keys = []
    if "CLERK_PUBLISHABLE_KEY" in env_content:
        clerk_keys.append("✅ CLERK_PUBLISHABLE_KEY")
    else:
        clerk_keys.append("❌ CLERK_PUBLISHABLE_KEY")

    if "CLERK_SECRET_KEY" in env_content:
        clerk_keys.append("✅ CLERK_SECRET_KEY")
    else:
        clerk_keys.append("❌ CLERK_SECRET_KEY")

    if "CLERK_WEBHOOK_SECRET" in env_content:
        clerk_keys.append("✅ CLERK_WEBHOOK_SECRET")
    else:
        clerk_keys.append("❌ CLERK_WEBHOOK_SECRET")

    print("🔍 Current Clerk configuration:")
    for key in clerk_keys:
        print(f"   {key}")
    print()

    if all("✅" in key for key in clerk_keys):
        print("🎉 All Clerk keys are configured!")
        print("You can now test the authentication at:")
        print("   http://localhost:8000/member/clerk-test/")
    else:
        print("⚠️  Missing Clerk configuration!")
        print()
        print("Add these to your .env file:")
        print()
        print("# Clerk Configuration")
        print("CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key_here")
        print("CLERK_SECRET_KEY=sk_test_your_secret_key_here")
        print("CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret_here")
        print()
        print("Then restart your Docker containers:")
        print("   docker-compose restart web")

    print()
    print("📚 Next steps:")
    print("1. Configure JWT templates in Clerk dashboard")
    print("2. Set up webhooks (optional for testing)")
    print("3. Visit http://localhost:8000/member/clerk-test/")
    print("4. Try signing in with Clerk!")
    print()
    print("📖 For detailed setup instructions, see CLERK_SETUP.md")


if __name__ == "__main__":
    main()
