#!/bin/bash

# XD Incentives Database Seeding Script
# Comprehensive database seeding with multiple options and backup functionality

set -e  # Exit on any error

# Configuration
DB_NAME="xd_incentives_db"
DB_USER="testu"
DB_PASSWORD="testpw"
CONTAINER_NAME="xd-mysql"
BACKEND_CONTAINER="xd-backend"
# Get the project root directory (parent of scripts directory)
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DUMPS_DIR="${PROJECT_ROOT}/data"
BACKUPS_DIR="${DUMPS_DIR}/backups"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Emoji indicators
SUCCESS="✅"
ERROR="❌"
WARNING="⚠️"
INFO="ℹ️"
BACKUP="💾"
RESTORE="🔄"

# Print colored output
print_status() {
    echo -e "${GREEN}${SUCCESS}${NC} $1"
}

print_error() {
    echo -e "${RED}${ERROR}${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}${WARNING}${NC} $1"
}

print_info() {
    echo -e "${BLUE}${INFO}${NC} $1"
}

print_backup() {
    echo -e "${BLUE}${BACKUP}${NC} $1"
}

print_restore() {
    echo -e "${YELLOW}${RESTORE}${NC} $1"
}

# Help function
show_help() {
    echo "XD Incentives Database Seeding Script"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  --full            Restore full database dump with all data"
    echo "  --minimal         Restore minimal schema only"
    echo "  --test            Restore test data set"
    echo "  --fresh           Reset database completely and restore from dump"
    echo "  --backup          Create backup before seeding"
    echo "  --no-backup       Skip backup creation (default: create backup)"
    echo "  --sample-data     Add Django sample data after restore"
    echo "  --force           Skip confirmation prompts"
    echo "  --list-dumps      List available database dumps"
    echo "  --help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --full --backup      # Full restore with backup"
    echo "  $0 --fresh --force      # Fresh install without prompts"
    echo "  $0 --minimal --sample-data  # Minimal + sample data"
    echo ""
}

# Check if Docker containers are running
check_containers() {
    print_info "Checking Docker containers..."

    if ! docker ps | grep -q "${CONTAINER_NAME}"; then
        print_error "MySQL container (${CONTAINER_NAME}) is not running!"
        print_info "Please start the Docker containers first:"
        print_info "  docker-compose up -d"
        exit 1
    fi

    if ! docker ps | grep -q "${BACKEND_CONTAINER}"; then
        print_warning "Backend container (${BACKEND_CONTAINER}) is not running!"
        print_info "Starting backend container for Django commands..."
        docker-compose up -d backend
        sleep 10
    fi

    print_status "Docker containers are running"
}

# Create backups directory
ensure_backups_dir() {
    if [ ! -d "${BACKUPS_DIR}" ]; then
        mkdir -p "${BACKUPS_DIR}"
        print_info "Created backups directory: ${BACKUPS_DIR}"
    fi
}

# Create backup with timestamp
create_backup() {
    local timestamp=$(date +%Y_%m_%d_%H_%M_%S)
    local backup_file="${BACKUPS_DIR}/backup_before_seed_${timestamp}.sql.gz"

    print_backup "Creating backup before seeding..."
    print_info "Backup file: $(basename "${backup_file}")"

    if docker exec ${CONTAINER_NAME} mysqldump -u ${DB_USER} -p${DB_PASSWORD} ${DB_NAME} | gzip > "${backup_file}"; then
        local file_size=$(du -h "${backup_file}" | cut -f1)
        print_status "Backup created successfully (${file_size})"
        echo "${backup_file}"
    else
        print_error "Backup creation failed!"
        exit 1
    fi
}

# List available dumps
list_dumps() {
    print_info "Available database dumps:"
    echo ""
    ls -la "${DUMPS_DIR}"/*.sql.gz 2>/dev/null | while read -r line; do
        echo "  $line"
    done
    echo ""

    if [ -d "${BACKUPS_DIR}" ]; then
        print_info "Available backups:"
        echo ""
        ls -la "${BACKUPS_DIR}"/*.sql.gz 2>/dev/null | while read -r line; do
            echo "  $line"
        done
    fi
}

# Get the latest or specified dump file
get_dump_file() {
    local dump_type="$1"
    local dump_file=""

    case "$dump_type" in
        "full")
            # Use the existing full dump or latest
            dump_file="${DUMPS_DIR}/latest.sql.gz"
            if [ ! -f "$dump_file" ]; then
                # Find latest dump
                dump_file=$(ls -t "${DUMPS_DIR}"/database_dump_*.sql.gz 2>/dev/null | head -1)
            fi
            ;;
        "minimal")
            # Create minimal schema dump (structure only)
            print_info "Creating minimal schema dump..."
            local minimal_file="${DUMPS_DIR}/minimal_schema.sql"
            docker exec ${CONTAINER_NAME} mysqldump -u ${DB_USER} -p${DB_PASSWORD} --no-data ${DB_NAME} > "${minimal_file}"
            dump_file="${minimal_file}"
            ;;
        "test")
            # Use test dump if exists, otherwise create from full dump
            dump_file="${DUMPS_DIR}/test_data.sql.gz"
            if [ ! -f "$dump_file" ]; then
                print_warning "Test dump not found, using full dump"
                dump_file="${DUMPS_DIR}/database_dump_2025_07_18.sql.gz"
            fi
            ;;
    esac

    if [ -z "$dump_file" ] || [ ! -f "$dump_file" ]; then
        print_error "No suitable dump file found for type: $dump_type"
        print_info "Available dumps:"
        list_dumps
        exit 1
    fi

    echo "$dump_file"
}

# Reset database
reset_database() {
    print_warning "Resetting database completely..."

    # Drop and recreate database
    docker exec ${CONTAINER_NAME} mysql -u root -prootpw -e "
        DROP DATABASE IF EXISTS ${DB_NAME};
        CREATE DATABASE ${DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        GRANT ALL PRIVILEGES ON ${DB_NAME}.* TO '${DB_USER}'@'%';
        FLUSH PRIVILEGES;
    "

    print_status "Database reset completed"
}

# Restore database from dump
restore_database() {
    local dump_file="$1"
    local file_extension="${dump_file##*.}"

    print_restore "Restoring database from: $(basename "$dump_file")"

    if [ "$file_extension" = "gz" ]; then
        # Compressed file
        if gunzip -c "$dump_file" | docker exec -i ${CONTAINER_NAME} mysql -u ${DB_USER} -p${DB_PASSWORD} ${DB_NAME}; then
            print_status "Database restore completed successfully"
        else
            print_error "Database restore failed!"
            exit 1
        fi
    else
        # Uncompressed file
        if docker exec -i ${CONTAINER_NAME} mysql -u ${DB_USER} -p${DB_PASSWORD} ${DB_NAME} < "$dump_file"; then
            print_status "Database restore completed successfully"
        else
            print_error "Database restore failed!"
            exit 1
        fi
    fi
}

# Run Django migrations
run_migrations() {
    print_info "Running Django migrations..."
    if docker exec ${BACKEND_CONTAINER} sh -c "python manage.py migrate"; then
        print_status "Migrations completed successfully"
    else
        print_warning "Migrations failed or had warnings"
    fi
}

# Add sample data using Django management commands
add_sample_data() {
    print_info "Adding sample data using Django management commands..."

    local commands=(
        "setup_member_types"
        "create_sample_data"
        "setup_member_hierarchy"
        "setup_teams_multi"
        "create_sample_terms"
        "create_sample_privacy"
        "create_sample_communications"
        "create_sample_password_resets"
    )

    for cmd in "${commands[@]}"; do
        print_info "Running: $cmd"
        if docker exec ${BACKEND_CONTAINER} sh -c "python manage.py $cmd"; then
            print_status "✓ $cmd completed"
        else
            print_warning "⚠ $cmd failed or had warnings"
        fi
    done

    print_status "Sample data creation completed"
}

# Confirm action with user
confirm_action() {
    local message="$1"
    local force="$2"

    if [ "$force" = "true" ]; then
        return 0
    fi

    echo ""
    print_warning "$message"
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo ""

    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Operation cancelled by user"
        exit 0
    fi
}

# Main execution
main() {
    local seed_type="full"
    local create_backup="true"
    local add_samples="false"
    local force="false"
    local reset_db="false"

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --full)
                seed_type="full"
                shift
                ;;
            --minimal)
                seed_type="minimal"
                shift
                ;;
            --test)
                seed_type="test"
                shift
                ;;
            --fresh)
                reset_db="true"
                shift
                ;;
            --backup)
                create_backup="true"
                shift
                ;;
            --no-backup)
                create_backup="false"
                shift
                ;;
            --sample-data)
                add_samples="true"
                shift
                ;;
            --force)
                force="true"
                shift
                ;;
            --list-dumps)
                list_dumps
                exit 0
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # Display configuration
    echo ""
    echo "🎯 XD Incentives Database Seeding Configuration"
    echo "================================================"
    echo "Seed Type:      $seed_type"
    echo "Reset DB:       $reset_db"
    echo "Create Backup:  $create_backup"
    echo "Sample Data:    $add_samples"
    echo "Force Mode:     $force"
    echo ""

    # Check prerequisites
    check_containers
    ensure_backups_dir

    # Get dump file
    local dump_file=$(get_dump_file "$seed_type")
    print_info "Using dump file: $(basename "$dump_file")"

    # Confirm action
    if [ "$reset_db" = "true" ]; then
        confirm_action "This will COMPLETELY RESET the database and restore from dump." "$force"
    else
        confirm_action "This will restore the database from dump (existing data may be affected)." "$force"
    fi

    # Create backup if requested
    local backup_file=""
    if [ "$create_backup" = "true" ]; then
        backup_file=$(create_backup)
    fi

    # Reset database if requested
    if [ "$reset_db" = "true" ]; then
        reset_database
    fi

    # Restore database
    restore_database "$dump_file"

    # Run migrations
    run_migrations

    # Add sample data if requested
    if [ "$add_samples" = "true" ]; then
        add_sample_data
    fi

    # Final status
    echo ""
    echo "🎉 Database seeding completed successfully!"
    echo "=========================================="
    print_status "Seed Type: $seed_type"
    print_status "Database: $DB_NAME"
    if [ -n "$backup_file" ]; then
        print_status "Backup: $(basename "$backup_file")"
    fi

    echo ""
    print_info "You can now access your application:"
    print_info "  Web: http://localhost:8000"
    print_info "  Admin: http://localhost:8000/admin/ (admin/admin123)"
    echo ""
}

# Run main function
main "$@"
