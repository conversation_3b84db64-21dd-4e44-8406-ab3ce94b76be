#!/bin/bash

# Database dump script for XD Incentives
# This script dumps the database with a date stamp and compresses it

# Get current date in YYYY_MM_DD format
DATE=$(date +%Y_%m_%d)
TIMESTAMP=$(date +%Y_%m_%d_%H_%M_%S)

# Database configuration
DB_NAME="xd_incentives_db"
DB_USER="testu"
DB_PASSWORD="testpw"
CONTAINER_NAME="xd-mysql"

# Output filename
OUTPUT_FILE="database_dump_${DATE}.sql.gz"

echo "Starting database dump..."
echo "Date: ${DATE}"
echo "Output file: ${OUTPUT_FILE}"

# Check if Docker container is running
if ! docker ps | grep -q "${CONTAINER_NAME}"; then
    echo "Error: MySQL container (${CONTAINER_NAME}) is not running!"
    echo "Please start the Docker containers first:"
    echo "  docker-compose up -d"
    exit 1
fi

# Create the dump and compress it in one step
echo "Creating database dump..."
docker exec ${CONTAINER_NAME} mysqldump -u ${DB_USER} -p${DB_PASSWORD} ${DB_NAME} | gzip > "${OUTPUT_FILE}"

# Check if the dump was successful
if [ $? -eq 0 ]; then
    echo "✅ Database dump completed successfully!"
    echo "📁 File: ${OUTPUT_FILE}"

    # Get file size
    FILE_SIZE=$(du -h "${OUTPUT_FILE}" | cut -f1)
    echo "📊 Size: ${FILE_SIZE}"

    # List all database dumps
    echo ""
    echo "📋 All database dumps in this folder:"
    ls -la *.sql.gz 2>/dev/null || echo "No .sql.gz files found"

else
    echo "❌ Database dump failed!"
    exit 1
fi

echo ""
echo "💡 To restore this dump:"
echo "   gunzip ${OUTPUT_FILE}"
echo "   docker exec -i ${CONTAINER_NAME} mysql -u ${DB_USER} -p${DB_PASSWORD} ${DB_NAME} < database_dump_${DATE}.sql"
