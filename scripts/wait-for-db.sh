#!/bin/sh
set -e

host="$1"
shift

echo "Waiting for MySQL at $host..."

# Use database credentials from environment variables
# Fall back to docker-compose defaults if not set
DB_USER=${DB_USER:-testu}
DB_PASSWORD=${DB_PASSWORD:-testpw}

until mysql -h "$host" -u "$DB_USER" -p"$DB_PASSWORD" --skip-ssl -e "SELECT 1" >/dev/null 2>&1; do
  echo "MySQL is unavailable - sleeping"
  sleep 2
done

echo "MySQL is up - executing command"
exec "$@"
