{"*.py": ["black", "isort"], "*.{yml,yaml,json,md}": ["prettier --write"], ".github/**/*.{yml,yaml}": ["prettier --write"], ".github/scripts/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], ".github/scripts/**/*.{json,md}": ["prettier --write"], "backend/**/*.py": ["black", "isort"], "frontend/**/*.{css,scss,md}": ["prettier --write"], "frontend/**/*.json": ["prettier --write"], "frontend/**/*.{js,jsx,ts,tsx}": ["bash -c 'cd frontend && npx eslint --fix'", "prettier --write"], "!frontend/**/*.gen.{js,jsx,ts,tsx}": []}