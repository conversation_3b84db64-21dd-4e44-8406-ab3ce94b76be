# XD Incentives

> A comprehensive Django-based member management system designed to handle complex organizational hierarchies, team management, and permission-based access control.

[![Django](https://img.shields.io/badge/Django-5.2.4-green.svg)](https://djangoproject.com/)
[![Python](https://img.shields.io/badge/Python-3.12-blue.svg)](https://python.org/)
[![MySQL](https://img.shields.io/badge/MySQL-8.4.5-orange.svg)](https://mysql.com/)
[![Docker](https://img.shields.io/badge/Docker-Compose-blue.svg)](https://docker.com/)
[![License](https://img.shields.io/badge/License-Private-red.svg)](#)

## 🚀 Quick Start

Get the application running in under 5 minutes:

```bash
# Clone the repository
git clone https://github.com/integritystl/xd-incentives
cd xd-incentives

# Create environment file
cp .env.example .env
# Edit .env with your configuration

# Start with full database
make quick-start

# Or fresh install
make fresh-start

# Access the application
open http://localhost:8000
```

**Default Credentials:**

- **Admin:** `admin` / `admin123`
- **Database:** `testu` / `testpw`

## 📋 Table of Contents

1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Installation & Setup](#installation--setup)
4. [Development Guide](#development-guide)
5. [API Reference](#api-reference)
6. [Database Schema](#database-schema)
7. [Testing](#testing)
8. [Deployment](#deployment)
9. [Troubleshooting](#troubleshooting)
10. [Contributing](#contributing)

---

## Project Overview

XD Incentives is a comprehensive Django-based member management system designed to handle complex organizational hierarchies, team management, and permission-based access control.

### Key Features

- **Clerk Authentication Integration**: Primary authentication provider
- **Hierarchical Organization Structure**: Complex manager-subordinate relationships
- **Team Management**: Multi-role team assignments and hierarchies
- **Dynamic Permissions**: JSON-based configurable role permissions
- **Real-time Communication**: WebSocket support via Django Channels
- **Background Processing**: Celery-based task queue

### Technology Stack

| Category           | Technology      | Version | Purpose                    |
| ------------------ | --------------- | ------- | -------------------------- |
| **Backend**        | Django          | 5.2.4   | Web framework & API server |
| **Language**       | Python          | 3.12    | Backend runtime            |
| **Database**       | MySQL           | 8.4.5   | Primary database           |
| **Cache**          | Redis           | 8       | Caching & message broker   |
| **Task Queue**     | Celery          | Latest  | Background processing      |
| **WebSocket**      | Django Channels | Latest  | Real-time communication    |
| **Server**         | Daphne          | Latest  | ASGI server                |
| **Authentication** | Clerk SDK       | Latest  | Primary auth provider      |

| **Frontend** | React | 19+ | UI framework with Tanstack Start |
| **Frontend Language** | TypeScript | 5+ | Type-safe JavaScript |
| **Build Tool** | Vite | 6+ | Fast build tool & dev server |
| **Styling** | Tailwind CSS | 4+ | Utility-first CSS framework |
| **UI Components** | Shadcn UI | Latest | Component library |
| **State Management** | Tanstack Query + Zustand | Latest | Server & client state |
| **Routing** | Tanstack Router | Latest | Type-safe routing |
| **Component Development** | Storybook | 9+ | Component documentation |
| **Testing** | Vitest | Latest | Unit & integration testing |
| **Container** | Docker | Latest | Containerization |

---

## Architecture

### Current Application Structure

```
xd-incentives/
├── backend/                # Django backend application
│   ├── apps/               # Django applications
│   │   ├── api/            # REST API endpoints
│   │   ├── member/         # Core member management
│   │   ├── customer/       # Customer management
│   │   ├── testapp/        # WebSocket testing (TEMPORARY POC)
│   │   └── theme/          # Tailwind CSS theming (TEMPORARY POC)
│   ├── config/             # Django settings
│   │   ├── settings/       # Modular settings architecture
│   │   │   ├── base.py     # Base settings (shared configuration)
│   │   │   ├── development.py  # Development environment
│   │   │   ├── production.py   # Production environment
│   │   │   ├── staging.py      # Staging environment
│   │   │   └── local.py        # Local development
│   │   ├── middleware.py   # Custom middleware
│   │   ├── urls.py         # Main URL configuration
│   │   └── wsgi.py         # WSGI application
│   ├── manage.py           # Django management script
│   └── requirements.txt    # Python dependencies
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # React components
│   │   │   └── ui/         # Base UI components (Shadcn)
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API integration layer
│   │   ├── stores/         # Zustand stores
│   │   ├── utils/          # Utility functions
│   │   └── types/          # TypeScript definitions
│   ├── .storybook/         # Storybook configuration
│   └── stories/            # Component stories
├── data/                   # Database dumps
├── scripts/                # Utility scripts
├── static/                 # Static files
└── templates/              # Email templates
```

### Core Components

#### Backend (Django)

##### apps/member/

The heart of the backend system, handling all member-related functionality:

- **models.py**: Core data models (17 models)
- **views.py**: Member dashboard, profile management
- **clerk_auth.py**: Clerk SDK integration
- **admin.py**: Enhanced Django admin
- **forms.py**: Member forms and validation
- **management/commands/**: Data seeding commands

##### apps/api/

RESTful API implementation with JWT authentication:

- **v1/**: Versioned API endpoints
- **urls.py**: API routing (40+ endpoints)
- **views.py**: API view implementations
- **serializers**: Complex nested serialization

#### Frontend (React + TypeScript)

##### Component Architecture

- **src/components/ui/**: Base UI components built with Shadcn UI
  - Button, Input, Card, Modal, Loading components
  - TypeScript interfaces for all props
  - Accessible by default with ARIA attributes
- **src/components/layout/**: Layout components (Header, Sidebar)
- **src/components/forms/**: Form-specific components
- **src/components/data/**: Data display components

##### State Management

- **Tanstack Query**: Server state, caching, and API synchronization
- **Zustand**: Client-side state (auth, theme, app settings)
- **Context API**: Global state providers

##### Development Tools

- **Storybook**: Component development and documentation
- **Vite**: Fast development server and build tool
- **TypeScript**: Strict type checking for code quality
- **Vitest**: Unit and integration testing framework

#### Settings Architecture

The application uses a modular settings architecture with environment-specific configurations:

- **Base Settings** (`base.py`): Shared configuration, theme settings, installed apps
- **Environment-Specific**: Each environment has its own settings file that inherits from base
- **Environment Variables**: Managed via `python-decouple` for secure configuration
- **Theme Configuration**: Hardcoded in base settings, accessible via API endpoint

### Docker Services

| Service   | Container    | Port | Purpose                  |
| --------- | ------------ | ---- | ------------------------ |
| backend   | xd-backend   | 8000 | Django API server        |
| frontend  | xd-frontend  | 3000 | React development server |
| db        | xd-mysql     | 3306 | MySQL database           |
| redis     | xd-redis     | 6379 | Cache/message broker     |
| celery    | xd-celery    | -    | Background worker        |
| storybook | xd-storybook | 6006 | Component documentation  |

---

## Installation & Setup

### Prerequisites

- **Docker & Docker Compose** - Required for running the application
- **Git** - For cloning the repository
- **Make** - For using convenience commands
- **MySQL Client** (optional) - For direct database access

### Environment Setup

1. **Clone the Repository**

   ```bash
   git clone https://github.com/integritystl/xd-incentives
   cd xd-incentives
   ```

2. **Configure Environment**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

### Quick Start Commands

```bash
# Start with full database
make quick-start

# Fresh install (complete reset)
make fresh-start

# Daily development
make up       # Start services
make down     # Stop services
make logs     # View logs
```

### Database Setup

#### Database Seeding

```bash
make seed-full         # Full dump + sample data
make seed-fresh        # Complete reset + seed
make seed-minimal      # Schema only + sample data
```

#### Database Management

```bash
make backup-db         # Create timestamped backup
make restore-db        # Interactive restore
make mysql-cli         # Access MySQL directly
```

---

## Development Guide

### Common Commands

#### Docker Operations

```bash
make up         # Start services
make down       # Stop services
make logs       # View logs
make status     # Check service status
```

#### Environment Management

```bash
# Test different environments
docker-compose exec xd-backend python3 manage.py check --settings=config.settings.development
docker-compose exec xd-backend python3 manage.py check --settings=config.settings.production
docker-compose exec xd-backend python3 manage.py check --settings=config.settings.local
docker-compose exec xd-backend python3 manage.py check --settings=config.settings.staging

# Switch to production temporarily
docker-compose -f docker-compose.yml -f docker-compose.prod.yml --profile production up -d

# Test theme configuration API
curl http://localhost:8000/api/theme/config/
```

#### Backend (Django) Commands

```bash
# Run inside container
docker exec xd-backend sh -c "cd backend && python manage.py <command>"

# Common commands
make django-migrate         # Run migrations
make django-makemigrations  # Create migrations
make django-test           # Run all tests
make django-shell          # Django shell
make django-superuser      # Create superuser
make django-sample-data    # Create all sample data
```

#### Frontend (React) Commands

```bash
# Development
make frontend-dev           # Start React dev server
make frontend-build         # Build for production
make frontend-test          # Run frontend tests
make frontend-lint          # Lint TypeScript/React code

# Storybook
make storybook-dev          # Start Storybook dev server
make storybook-build        # Build Storybook for production

# Direct npm commands (inside container)
docker exec xd-frontend npm run dev        # Start Vite dev server
docker exec xd-frontend npm run build      # Production build
docker exec xd-frontend npm run test       # Run Vitest tests
docker exec xd-frontend npm run storybook  # Start Storybook
```

### Management Commands

Sample data generation:

- `create_sample_data` - Create sample members
- `setup_member_types` - Setup member types with permissions
- `setup_member_hierarchy` - Create hierarchy relationships
- `setup_teams_multi` - Create teams with memberships
- `create_sample_terms` - Create terms documents
- `create_sample_privacy` - Create privacy policies
- `create_sample_communications` - Create communications
- `create_sample_password_resets` - Create password resets

### Adding New Features

#### Backend Features

1. Check existing patterns in relevant app directory
2. Create/update models with proper relationships
3. Generate and apply migrations
4. Update serializers if API changes needed
5. Add management command for sample data
6. Write tests following existing patterns

#### Frontend Features

1. Design components in Storybook first
2. Create TypeScript interfaces for data structures
3. Build reusable UI components with Shadcn patterns
4. Implement API integration with Tanstack Query
5. Add routing with Tanstack Router
6. Write component tests with Vitest
7. Update component stories and documentation

### Working with Permissions

- Permissions are stored as JSON in MemberType model
- Use `member.member_type.permissions` to check permissions
- Page access controlled via `page_access` JSON field
- Feature flags in `feature_flags` field

---

## API Reference

### Authentication Endpoints

#### JWT Token

- `POST /api/token/` - Obtain JWT token pair
- `POST /api/token/refresh/` - Refresh access token

#### Clerk Integration

- `POST /api/v1/clerk/auth/` - Clerk authentication
- `POST /api/v1/clerk/sync/` - Sync Clerk user data
- `POST /api/v1/clerk/webhook/` - Clerk webhooks

## API Versioning

The XD Incentives API uses URL path versioning. All endpoints are versioned under `/api/v1/`.

### Version Information

- **Current Version**: v1
- **Base URL**: `http://localhost:8000/api/v1/`
- **Version Info**: `GET /api/` - Returns current API version information
- **Documentation**: `GET /api/docs/` - Returns API documentation links
- **Changelog**: `GET /api/changelog/` - Returns API changelog

### Configuration Endpoints

#### Theme Configuration

- `GET /api/v1/theme/config/` - Get theme configuration (colors, modes, fonts, logos)
  - **Public endpoint** - No authentication required
  - **Returns**: Theme colors, modes, fonts, logos, and timestamp
  - **Use case**: Frontend theming and branding

### Member Management

#### Member Operations

- `GET /api/v1/member/details/` - Current member details
- `GET /api/v1/members/` - List all members (admin)
- `GET /api/v1/members/search/` - Search members
- `GET /api/v1/member/profile/` - Member profile
- `PUT /api/v1/member/profile/<id>/` - Update profile

#### Hierarchy & Organization

- `GET /api/v1/organization-chart/` - Organization structure
- `GET /api/v1/member/<id>/hierarchy/` - Member hierarchy
- `POST /api/v1/member/<id>/hierarchy-management/` - Manage hierarchy
- `GET /api/v1/hierarchy/search/` - Search hierarchies

### Team Management

- `GET /api/v1/teams/` - List all teams
- `GET /api/v1/teams/<id>/members/` - Team members
- `GET /api/v1/teams/<id>/hierarchy/` - Team hierarchy
- `GET /api/v1/member/<id>/teams/` - Member's teams

### Member Types & Permissions

- `GET /api/v1/member-types/` - List member types
- `GET /api/v1/member-types/<id>/` - Member type details

### Legal & Compliance

#### Terms & Conditions

- `GET /api/v1/terms/` - Latest terms
- `POST /api/v1/terms/accept/` - Accept terms
- `GET /api/v1/terms/history/` - Acceptance history

#### Privacy Policy

- `GET /api/v1/privacy/` - Latest privacy policy
- `POST /api/v1/privacy/accept/` - Accept privacy policy
- `GET /api/v1/privacy/history/` - Acceptance history

### Communication System

- `GET /api/v1/communications/` - Member communications
- `GET /api/v1/communications/<id>/` - Communication details
- `GET /api/v1/communications/admin/` - Admin communications

### Password Management

- `POST /api/v1/password-reset/request/` - Request reset
- `POST /api/v1/password-reset/validate/` - Validate token
- `POST /api/v1/password-reset/confirm/` - Confirm reset
- `GET /api/v1/password-reset/history/` - Reset history
- `GET /api/v1/password-reset/stats/` - Reset statistics

---

## Database Schema

### Core Models

#### Member (extends AbstractUser)

Primary user model with enhanced fields:

- Authentication: username, email, password, clerk_user_id
- Profile: first_name, last_name, status, tier
- Contact: work/home addresses, phone numbers
- Relations: region, member_type
- Approval: approved_by, approved_date, denied_by, denied_date
- Settings: lang, feature_flags

#### MemberType

Dynamic permission system with JSON configuration:

- Basic: name, slug, description, is_active
- JSON Fields:
  - permissions: List of permission strings
  - page_access: Page-level access control
  - navigation: Menu structure
  - feature_flags: Enabled features
  - dashboard_layout: Widget configuration
  - theme_settings: UI customization
- Access Control: can_signup, requires_approval, max_subordinates
- Relations: can_manage_types (self-referencing M2M)

#### MemberHierarchy

Complex organizational relationships:

- Relations: member, manager (both FK to Member)
- Types: direct_manager, mentor, supervisor, team_lead, project_manager
- Temporal: start_date, end_date
- Flags: is_primary

#### Team & MemberTeam

Team management with role-based membership:

- Team: name, team_type, description, team_lead, is_active
- MemberTeam: member, team, role, is_primary, start_date, end_date
- Roles: member, lead, admin, manager, contributor, observer, salesrep

### Key Relationships

```
Member ─┬─→ MemberType (N:1)
        ├─→ Region (N:1)
        ├─↔ Member (M:M via MemberHierarchy)
        └─↔ Team (M:M via MemberTeam)

MemberType ↔ MemberType (M:M - management hierarchy)
```

### Permission Configuration

MemberType permissions use JSON fields:

```json
{
  "permissions": ["admin.access", "member.view", "reports.generate"],
  "page_access": {
    "dashboard": { "access": true, "features": ["widgets", "analytics"] },
    "reports": { "access": false, "reason": "tier_restriction" }
  },
  "navigation": {
    "menu": [
      { "label": "Dashboard", "url": "/dashboard", "icon": "home" },
      { "label": "Members", "url": "/members", "icon": "users" }
    ]
  },
  "feature_flags": ["reports", "analytics", "export"]
}
```

---

## Testing

### Test Structure

```
apps/
├── member/tests/
│   ├── test_models.py
│   ├── test_views.py
│   └── test_api.py
└── api/tests/
    ├── test_authentication.py
    └── test_serializers.py
```

### Running Tests

#### Backend Tests (Django)

```bash
# All Django tests
make django-test

# Specific app
make django-test-app APP=member

# Specific test
docker exec xd-backend sh -c "cd backend && python manage.py test apps.member.tests.test_models"

# With coverage
docker exec xd-backend sh -c "cd backend && coverage run --source='.' manage.py test"
docker exec xd-backend coverage report
```

#### Frontend Tests (React + TypeScript)

```bash
# All frontend tests
make frontend-test

# Watch mode for development
docker exec xd-frontend npm run test:watch

# Component tests with UI
docker exec xd-frontend npm run test:ui

# Coverage report
docker exec xd-frontend npm run test:coverage

# Specific test file
docker exec xd-frontend npm run test -- Button.test.tsx
```

#### Component Testing with Storybook

```bash
# Start Storybook for visual testing
make storybook-dev

# Run Storybook tests
docker exec xd-frontend npm run test-storybook

# Build and test stories
docker exec xd-frontend npm run build-storybook
```

---

## Deployment

### Production Checklist

1. **Environment**
   - Set `DEBUG=False`
   - Generate strong `SECRET_KEY`
   - Configure `ALLOWED_HOSTS`
   - Set production database credentials

2. **Database**
   - Use managed MySQL service
   - Configure backups
   - Set up read replicas if needed

3. **Static Files**
   - Configure CDN
   - Use production web server (nginx)
   - Run `collectstatic`

4. **Security**
   - Enable HTTPS
   - Configure CORS
   - Set up rate limiting
   - Use production Redis

### Docker Production

```bash
# Build production image
docker build -f Dockerfile.multi-stage -t xd-incentives:prod .

# Run production container
docker run -d \
  -p 8000:8000 \
  -e DEBUG=False \
  -e SECRET_KEY=<production-key> \
  xd-incentives:prod
```

### Health Checks

- Application: `http://localhost:8000/status/`
- Database: `docker exec xd-mysql mysqladmin ping`
- Redis: `docker exec xd-redis redis-cli ping`

---

## Troubleshooting

### Common Issues

#### Port Already in Use

```bash
# Check what's using port 8000
lsof -i :8000
# Kill the process
```

#### Database Connection Issues

```bash
# Check if MySQL is running
docker-compose ps
# Check MySQL logs
docker-compose logs db
```

#### Permission Issues

```bash
# Make wait script executable
chmod +x scripts/wait-for-db.sh
```

#### Static Files Not Loading

```bash
# Collect static files
docker exec xd-backend sh -c "cd backend && python manage.py collectstatic --noinput"
```

### Reset Everything

```bash
# Stop and remove everything
make clean-all && make fresh-start
```

### Debugging Tips

- Check service status: `make status`
- View specific logs: `docker-compose logs -f <service>`
- Database issues: Check `docker-compose logs -f db`
- Permission denied: Ensure `scripts/wait-for-db.sh` is executable
- Clear everything: `make clean-all && make fresh-start`

---

## Contributing

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch**

   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make your changes**
4. **Run tests**

   ```bash
   make django-test
   ```

5. **Commit your changes**

   ```bash
   git commit -m "Add your feature description"
   ```

6. **Push to your fork**

   ```bash
   git push origin feature/your-feature-name
   ```

7. **Create a Pull Request**

### Code Standards

- Follow PEP 8 for Python code
- Use meaningful variable and function names
- Add docstrings to functions and classes
- Write tests for new functionality
- Update documentation as needed

---

## Important URLs

### Development URLs

- **Frontend Application**: <http://localhost:3000/>
- **Backend API**: <http://localhost:8000/api/>
- **Django Admin**: <http://localhost:8000/admin/>
- **Storybook**: <http://localhost:6006/>
- **API Status**: <http://localhost:8000/status/>
- **WebSocket Test**: <http://localhost:8000/redistest/>

### Production URLs

- **Application**: TBD
- **API Documentation**: TBD
- **Component Library**: TBD (Storybook)

## Environment Configuration

### Settings Architecture

XD Incentives uses a modular settings architecture with environment-specific configurations:

```
backend/config/settings/
├── base.py          # Base settings (don't use directly)
├── development.py   # Development environment
├── production.py    # Production environment
├── staging.py       # Staging environment
└── local.py         # Local development
```

### Environment Switching

The application supports multiple environments with different configurations:

#### Available Environments

| Environment     | Settings Module               | Use Case               | Database | Debug   | Security |
| --------------- | ----------------------------- | ---------------------- | -------- | ------- | -------- |
| **Development** | `config.settings.development` | Team development       | MySQL    | `True`  | Relaxed  |
| **Production**  | `config.settings.production`  | Live deployment        | MySQL    | `False` | Strict   |
| **Staging**     | `config.settings.staging`     | Pre-production testing | MySQL    | `True`  | Medium   |
| **Local**       | `config.settings.local`       | Individual development | SQLite   | `True`  | Relaxed  |

#### Switching Environments

**Method 1: Edit Docker Compose Override (Recommended)**

```bash
# Edit docker-compose.override.yml
# Change this line:
DJANGO_SETTINGS_MODULE=config.settings.development  # Current
DJANGO_SETTINGS_MODULE=config.settings.production   # For production
DJANGO_SETTINGS_MODULE=config.settings.local        # For local
DJANGO_SETTINGS_MODULE=config.settings.staging      # For staging

# Restart containers
docker-compose down && docker-compose up -d
```

**Method 2: Use Production Override File**

```bash
# For production deployment
docker-compose -f docker-compose.yml -f docker-compose.prod.yml --profile production up -d

# For development (current)
docker-compose up -d
```

**Method 3: Temporary Testing**

```bash
# Test any environment without switching:
docker-compose exec xd-backend python3 manage.py check --settings=config.settings.production
docker-compose exec xd-backend python3 manage.py check --settings=config.settings.local
docker-compose exec xd-backend python3 manage.py check --settings=config.settings.staging
```

**Method 4: Environment Variable Override**

```bash
# Set environment variable and restart
export DJANGO_SETTINGS_MODULE=config.settings.production
docker-compose down && docker-compose up -d
```

#### Environment-Specific Features

**Development Environment**

- Debug mode enabled
- Console email backend
- Relaxed security settings
- MySQL database
- Detailed logging

**Production Environment**

- Debug mode disabled
- SMTP email backend
- Strict security settings (HTTPS, secure cookies)
- MySQL database with SSL
- Optimized logging

**Staging Environment**

- Debug mode enabled (for troubleshooting)
- Console email backend
- Medium security settings
- MySQL database
- Verbose logging

**Local Environment**

- Debug mode enabled
- SQLite database (faster setup)
- Console email backend
- Relaxed security settings
- Memory cache backend

### Required Environment Variables

**Core Django Settings**

- `SECRET_KEY` - Django secret key
- `DEBUG` - Debug mode (default: False)
- `ALLOWED_HOSTS` - Allowed hostnames

**Database Configuration**

- `DB_NAME` - Database name
- `DB_USER` - Database username
- `DB_PASSWORD` - Database password
- `DB_HOST` - Database host
- `DB_PORT` - Database port (default: 3306)

**Authentication & Security**

- `CLERK_PUBLISHABLE_KEY` - Clerk public key
- `CLERK_SECRET_KEY` - Clerk secret key
- `CLERK_WEBHOOK_SECRET` - Clerk webhook secret
- `JWT_ACCESS_TOKEN_LIFETIME` - JWT token lifetime (minutes)
- `JWT_REFRESH_TOKEN_LIFETIME` - JWT refresh token lifetime (days)

**Email Configuration**

- `EMAIL_HOST` - SMTP host
- `EMAIL_PORT` - SMTP port
- `EMAIL_USE_TLS` - Use TLS (default: True)
- `EMAIL_HOST_USER` - SMTP username
- `EMAIL_HOST_PASSWORD` - SMTP password
- `DEFAULT_FROM_EMAIL` - Default sender email

**External APIs**

- `GOOGLE_ADDRESS_VALIDATION_API_KEY` - Google Address Validation API key

**CORS & Security**

- `CORS_ALLOWED_ORIGINS` - Allowed CORS origins
- `CORS_ALLOW_ALL_ORIGINS` - Allow all origins (development only)

### Theme Configuration

Theme settings are hardcoded in `base.py` and accessible via API:

```json
{
  "theme_colors": {
    "primary": "#1A73E8",
    "secondary": "#34A853",
    "accent": "#FBBC05",
    "background": "#FFFFFF",
    "text": "#202124"
  },
  "theme_modes": {
    "light": { "background": "#FFFFFF", "text": "#000000" },
    "dark": { "background": "#202124", "text": "#E8EAED" }
  },
  "theme_fonts": {
    "primary": "Inter, sans-serif",
    "secondary": "Georgia, serif"
  },
  "logos": {
    "default": "/static/images/logo.svg",
    "small": "/static/images/logo-sm.svg",
    "dark_mode": "/static/images/logo-dark.svg",
    "favicon": "/static/images/favicon.ico"
  }
}
```

**Access Theme Configuration:**

```bash
curl http://localhost:8000/api/theme/config/
```

## Additional Resources

### Documentation

- [API Documentation](./docs/api/)
- [Architecture Overview](./docs/architecture/)
- [Frontend Specification](./docs/architecture/front-end-spec.md)
- [Authentication Guide](./docs/auth/)
- [Database Schema](./docs/tooling/database.md)
- [Development Best Practices](./docs/guides/development-best-practices.md)
- [PRD & Technical Requirements](./docs/prd/)
- [Claude Code Instructions](./CLAUDE.md)

### Component Development

- **Storybook**: <http://localhost:6006/> (when running)
- **Component Library**: Built with Shadcn UI + Tailwind CSS
- **Design System**: Following WCAG 2.1 AA accessibility standards
- **TypeScript**: Strict type checking enabled

### GitHub Resources

- **Repository**: [integritystl/xd-incentives](https://github.com/integritystl/xd-incentives)
- **Issues**: Report bugs and feature requests
- **Pull Requests**: Contribute code changes
- **Actions**: CI/CD pipeline status

---

## License

This project is proprietary and confidential. All rights reserved - Integrity XD.

## Support

For issues, questions, or support:

1. **Check the logs**: `make logs`
2. **Review this documentation**
3. **Check existing issues** in the project repository
4. **Contact the development team**
