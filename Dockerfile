# Multi-stage build for optimized production image
FROM python:3.12-alpine AS base

# Install system dependencies needed for Python packages
RUN apk add --no-cache \
    gcc \
    g++ \
    musl-dev \
    mariadb-dev \
    mariadb-client \
    libffi-dev \
    openssl-dev \
    curl \
    bash \
    pkgconfig \
    freetype-dev \
    libpng-dev \
    && rm -rf /var/cache/apk/*

# Set environment variables for Python optimization
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_DEFAULT_TIMEOUT=100

# Development stage with additional tools
FROM base AS development

# Add development dependencies
RUN apk add --no-cache \
    git \
    vim \
    htop \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app/backend

# Add backend to Python path for import resolution
ENV PYTHONPATH=/app/backend:$PYTHONPATH

# Install Python dependencies first for better caching
COPY backend/requirements.txt /app/backend/requirements.txt
RUN pip install --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# Copy wait script and make executable
COPY scripts/wait-for-db.sh /wait-for-db.sh
RUN chmod +x /wait-for-db.sh

# Copy application code (this layer changes most frequently)
COPY . /app/

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/api/health/ || exit 1

# Use wait-for-db.sh as the entrypoint
ENTRYPOINT ["/wait-for-db.sh", "db"]

# Default development command
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]

# Production stage with minimal dependencies
FROM base AS production

# Create non-root user for security
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# Set working directory
WORKDIR /app/backend

# Add backend to Python path
ENV PYTHONPATH=/app/backend:$PYTHONPATH \
    DJANGO_SETTINGS_MODULE=config.settings

# Install Python dependencies
COPY backend/requirements.txt /app/backend/requirements.txt
RUN pip install --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir gunicorn

# Copy wait script and make executable
COPY scripts/wait-for-db.sh /wait-for-db.sh
RUN chmod +x /wait-for-db.sh

# Copy application code
COPY . /app/

# Change ownership to non-root user
RUN chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/api/health/ || exit 1

# Use wait-for-db.sh as the entrypoint
ENTRYPOINT ["/wait-for-db.sh", "db"]

# Production command using Gunicorn
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "--timeout", "120", "config.wsgi:application"]
