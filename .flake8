[flake8]
max-line-length = 100
# Black compatibility + Django-specific ignores + Project-specific exceptions
extend-ignore = E203,E501,W503,DJ01,DJ08,D100,D101,D102,D103,D104,D105,D106,D107,D200,D205,D212,D415,Q000,I100,I101,I201,I202,E402,F401,W291,W292,W293,C901,F403,F405,E226,F841,D202,F541,B007,B018,E902
# Directories and files to exclude from linting
exclude =
    .git,           # Git version control directory
    __pycache__,    # Python bytecode cache directories
    .venv,          # Virtual environment directory (dot prefix)
    venv,           # Virtual environment directory
    env,            # Alternative virtual environment directory
    migrations,     # Django database migration files (auto-generated)
    node_modules,   # Node.js dependencies
    static,         # Static files (CSS, JS, images)
    .tox,           # Tox testing environment directory
    build,          # Build artifacts directory
    dist,           # Distribution packages directory
    *.egg-info      # Python package metadata directories
# Maximum cyclomatic complexity allowed (default is 10, we allow 12)
max-complexity = 12

# Use Google-style import ordering (stdlib, third-party, local)
import-order-style = google

# Define application-specific import names for import ordering
application-import-names = apps,config

# Use Google-style docstring convention
docstring-convention = google
# File-specific rule overrides
per-file-ignores =
    # Test files: allow longer lines, unused imports, and redefined names
    */tests/*:E501,F401,F811,D100,D101,D102,D103,D104,D105,D106,D107
    # Django settings files: allow longer lines for configuration
    */settings/*:E501,F401,F403,F405
    # Django management commands: allow longer lines for help text
    */management/commands/*:E501,D100,D101,D102,D103,D104,D105,D106,D107
    # Django migrations: ignore all rules (auto-generated code)
    */migrations/*:ALL
    # Package init files: allow unused imports (for public API exposure)
    */__init__.py:F401,F403
    # Django models: allow longer lines and model-specific patterns
    */models.py:E501,DJ01,DJ08
    # Django admin: allow model admin patterns
    */admin.py:E501,D100,D101,D102,D103
