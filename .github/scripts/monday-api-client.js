#!/usr/bin/env node

/**
 * Monday.com GraphQL API Client for Task Creation
 * Creates Monday.com tasks from parsed Claude code review issues
 * with proper error handling, rate limiting, and retry logic.
 */

const fs = require('fs');

/**
 * Configuration for Monday.com API integration
 */
const CONFIG = {
  API_URL: 'https://api.monday.com/v2',
  BOARD_ID: '9650366353',
  GROUP_ID: 'new_group29179',
  RATE_LIMIT: {
    MAX_REQUESTS_PER_MINUTE: 10, // Monday.com API rate limit is 5000/min, using conservative limit
    DELAY_BETWEEN_REQUESTS: 6000, // 6 seconds ensures we stay well under rate limit
  },
  RETRY: {
    MAX_RETRIES: 3,
    BACKOFF_DELAYS: [2000, 4000, 8000], // 2s, 4s, 8s
  },
  COLUMN_IDS: {
    STATUS: 'project_status',
    EPIC: 'dropdown_mkt463vs',
    LINK: 'link_mkt4nh1m'
  }
};

/**
 * GraphQL mutation for creating tasks in Monday.com
 */
const CREATE_TASK_MUTATION = `
  mutation CreateTaskItem($boardId: ID!, $groupId: String!, $itemName: String!, $columnValues: JSON!) {
    create_item(
      board_id: $boardId,
      group_id: $groupId,
      item_name: $itemName,
      column_values: $columnValues
    ) {
      id
      name
      url
    }
  }
`;

/**
 * @typedef {Object} TaskCreationResult
 * @property {boolean} success - Whether task creation was successful
 * @property {string} taskId - Monday.com task ID (if successful)
 * @property {string} taskUrl - Monday.com task URL (if successful)
 * @property {string} taskName - Task name that was created
 * @property {string} error - Error message (if failed)
 * @property {number} attempts - Number of attempts made
 */

/**
 * @typedef {Object} ParsedIssue
 * @property {'HIGH' | 'MEDIUM'} priority
 * @property {string} summary
 * @property {string} description
 * @property {'Must Fix' | 'Should Fix'} severity
 */

/**
 * Monday.com API Client class
 */
class MondayApiClient {
  constructor(apiToken) {
    if (!apiToken || typeof apiToken !== 'string' || apiToken.trim().length === 0) {
      throw new Error('Monday.com API token is required and must be a non-empty string');
    }
    // Consider basic format validation
    if (!apiToken.match(/^[a-zA-Z0-9_-]+$/)) {
      throw new Error('Invalid API token format');
    }
    this.apiToken = apiToken.trim();
    this.requestCount = 0;
    this.requestWindow = Date.now();
  }

  /**
   * Rate limiting implementation
   */
  async enforceRateLimit() {
    const now = Date.now();

    // Reset request count if minute has passed
    if (now - this.requestWindow > 60000) {
      this.requestCount = 0;
      this.requestWindow = now;
    }

    // If we've hit the limit, wait until next minute
    if (this.requestCount >= CONFIG.RATE_LIMIT.MAX_REQUESTS_PER_MINUTE) {
      const waitTime = 60000 - (now - this.requestWindow);
      console.log(`Rate limit reached, waiting ${waitTime}ms...`);
      await this.sleep(waitTime);
      this.requestCount = 0;
      this.requestWindow = Date.now();
    }

    // Add delay between requests
    if (this.requestCount > 0) {
      await this.sleep(CONFIG.RATE_LIMIT.DELAY_BETWEEN_REQUESTS);
    }

    this.requestCount++;
  }

  /**
   * Sleep utility function
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Execute GraphQL mutation with retry logic
   */
  async executeGraphQLMutation(mutation, variables, retryCount = 0) {
    await this.enforceRateLimit();

    const requestBody = {
      query: mutation,
      variables: variables
    };

    try {
      console.log(`Making API request (attempt ${retryCount + 1})...`);

      const response = await fetch(CONFIG.API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': this.apiToken
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // Check for GraphQL errors
      if (data.errors && data.errors.length > 0) {
        throw new Error(`GraphQL Error: ${data.errors[0].message}`);
      }

      return data;

    } catch (error) {
      console.error(`API request failed (attempt ${retryCount + 1}):`, error.message);

      // Determine if we should retry
      const shouldRetry = this.shouldRetryError(error, retryCount);

      if (shouldRetry && retryCount < CONFIG.RETRY.MAX_RETRIES) {
        const delay = CONFIG.RETRY.BACKOFF_DELAYS[retryCount];
        console.log(`Retrying in ${delay}ms...`);
        await this.sleep(delay);
        return this.executeGraphQLMutation(mutation, variables, retryCount + 1);
      }

      throw error;
    }
  }

  /**
   * Determine if an error should trigger a retry
   */
  shouldRetryError(error, retryCount) {
    if (retryCount >= CONFIG.RETRY.MAX_RETRIES) return false;

    const errorMessage = error.message.toLowerCase();

    // Retry conditions
    const retryConditions = [
      errorMessage.includes('timeout'),
      errorMessage.includes('503'),
      errorMessage.includes('502'),
      errorMessage.includes('500'),
      errorMessage.includes('rate limit'),
      errorMessage.includes('network'),
      errorMessage.includes('fetch')
    ];

    // Don't retry client errors (4xx)
    const skipRetryConditions = [
      errorMessage.includes('400'),
      errorMessage.includes('401'),
      errorMessage.includes('403'),
      errorMessage.includes('404'),
      errorMessage.includes('unauthorized'),
      errorMessage.includes('forbidden')
    ];

    if (skipRetryConditions.some(condition => condition)) {
      return false;
    }

    return retryConditions.some(condition => condition);
  }

  /**
   * Create a single task in Monday.com
   * @param {ParsedIssue} issue - Parsed issue from Claude output
   * @param {string} prUrl - Pull request URL
   * @param {number} prNumber - Pull request number
   * @returns {Promise<TaskCreationResult>}
   */
  async createTask(issue, prUrl, prNumber) {
    const taskName = `[${issue.priority}] ${issue.summary} - PR #${prNumber}`;
    const attempts = { count: 0 };

    try {
      // Prepare column values
      const columnValues = JSON.stringify({
        [CONFIG.COLUMN_IDS.STATUS]: { "label": "Working on it" },
        [CONFIG.COLUMN_IDS.EPIC]: "Infrastructure & Platform Foundation",
        [CONFIG.COLUMN_IDS.LINK]: {
          "url": prUrl,
          "text": `PR #${prNumber}`
        }
      });

      // Use the extracted GraphQL mutation constant
      const mutation = CREATE_TASK_MUTATION;

      const variables = {
        boardId: parseInt(CONFIG.BOARD_ID),
        groupId: CONFIG.GROUP_ID,
        itemName: taskName,
        columnValues: columnValues
      };

      console.log(`Creating task: "${taskName}"`);
      const result = await this.executeGraphQLMutation(mutation, variables);
      attempts.count = this.requestCount;

      if (result.data && result.data.create_item) {
        const task = result.data.create_item;
        console.log(`✅ Task created successfully: ${task.name} (ID: ${task.id})`);

        return {
          success: true,
          taskId: task.id,
          taskUrl: task.url,
          taskName: task.name,
          attempts: attempts.count
        };
      } else {
        throw new Error('Unexpected API response structure');
      }

    } catch (error) {
      console.error(`❌ Failed to create task: "${taskName}"`);
      console.error(`Error: ${error.message}`);

      return {
        success: false,
        taskName: taskName,
        error: error.message,
        attempts: attempts.count
      };
    }
  }

  /**
   * Create multiple tasks from parsed issues
   * @param {ParsedIssue[]} issues - Array of parsed issues
   * @param {string} prUrl - Pull request URL
   * @param {number} prNumber - Pull request number
   * @returns {Promise<TaskCreationResult[]>}
   */
  async createTasks(issues, prUrl, prNumber) {
    console.log(`Creating ${issues.length} tasks for PR #${prNumber}...`);

    if (!Array.isArray(issues) || issues.length === 0) {
      console.log('No issues to create tasks for');
      return [];
    }

    const results = [];

    // Process tasks sequentially to respect rate limits
    for (let i = 0; i < issues.length; i++) {
      const issue = issues[i];
      console.log(`\nProcessing issue ${i + 1}/${issues.length}: ${issue.severity}`);

      const result = await this.createTask(issue, prUrl, prNumber);
      results.push(result);

      // Small delay between tasks even within rate limits
      if (i < issues.length - 1) {
        await this.sleep(1000); // 1 second between tasks
      }
    }

    // Summary
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    console.log(`\n📊 Task Creation Summary:`);
    console.log(`✅ Successful: ${successful}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📋 Total: ${results.length}`);

    return results;
  }
}

/**
 * Main function for CLI and GitHub Actions integration
 */
async function main() {
  try {
    // Get environment variables
    const apiToken = process.env.MONDAY_API_TOKEN;
    const parsedIssuesJson = process.env.PARSED_ISSUES;
    const prUrl = process.env.PR_URL || process.env.GITHUB_EVENT_PULL_REQUEST_HTML_URL;
    const prNumber = process.env.PR_NUMBER || process.env.GITHUB_EVENT_NUMBER;

    // Validation
    if (!apiToken) {
      throw new Error('MONDAY_API_TOKEN environment variable is required');
    }

    if (!parsedIssuesJson) {
      throw new Error('PARSED_ISSUES environment variable is required');
    }

    if (!prUrl) {
      throw new Error('PR_URL or GITHUB_EVENT_PULL_REQUEST_HTML_URL environment variable is required');
    }

    if (!prNumber) {
      throw new Error('PR_NUMBER or GITHUB_EVENT_NUMBER environment variable is required');
    }

    // Parse issues
    let parsedIssues;
    try {
      parsedIssues = JSON.parse(parsedIssuesJson);
    } catch (error) {
      throw new Error(`Failed to parse PARSED_ISSUES JSON: ${error.message}`);
    }

    // Initialize client and create tasks
    const client = new MondayApiClient(apiToken);
    const results = await client.createTasks(parsedIssues, prUrl, parseInt(prNumber));

    // Output results for GitHub Actions
    const resultsOutput = `task-results=${JSON.stringify(results)}`;
    console.log(`\n${resultsOutput}`);
    if (process.env.GITHUB_OUTPUT) {
      fs.appendFileSync(process.env.GITHUB_OUTPUT, `${resultsOutput}\n`);
    }

    // Set summary output
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const summary = {
      total: results.length,
      successful,
      failed,
      success_rate: results.length > 0 ? (successful / results.length * 100).toFixed(1) : 0
    };

    const summaryOutput = `summary=${JSON.stringify(summary)}`;
    console.log(summaryOutput);
    if (process.env.GITHUB_OUTPUT) {
      fs.appendFileSync(process.env.GITHUB_OUTPUT, `${summaryOutput}\n`);
    }

    // Log failures but don't exit with error code (non-blocking design)
    if (failed > 0) {
      console.warn(`\n⚠️  ${failed} task(s) failed to create, but workflow continues`);
    }

    console.log(`\n✅ All ${successful} tasks created successfully!`);

  } catch (error) {
    console.error('❌ Script execution failed:', error.message);
    // Set empty outputs for non-blocking workflow
    if (process.env.GITHUB_OUTPUT) {
      fs.appendFileSync(process.env.GITHUB_OUTPUT, `task-results=[]\n`);
      fs.appendFileSync(process.env.GITHUB_OUTPUT, `summary={"total":0,"successful":0,"failed":0,"success_rate":0}\n`);
    }
    console.warn('⚠️  Monday.com integration failed, but workflow continues');
  }
}

// Export for testing
module.exports = {
  MondayApiClient,
  CONFIG
};

// Run main function if script is executed directly
if (require.main === module) {
  main();
}
