#!/usr/bin/env node

/**
 * Quick test to verify fixes work correctly
 */

console.log('🧪 Testing fixes...\n');

try {
  // Test parser
  const parser = require('./claude-output-parser.js');
  console.log('✅ Parser module loads successfully');

  // Test input validation fixes
  const result1 = parser.extractIssuesFromSection(null, 'Must Fix');
  console.log(`✅ Null input validation: ${result1.length === 0 ? 'PASS' : 'FAIL'}`);

  const result2 = parser.extractIssuesFromSection('', 'Must Fix');
  console.log(`✅ Empty string validation: ${result2.length === 0 ? 'PASS' : 'FAIL'}`);

  const result3 = parser.extractIssuesFromSection('test', 'Invalid Severity');
  console.log(`✅ Invalid severity validation: ${result3.length === 0 ? 'PASS' : 'FAIL'}`);

  // Test API client
  const apiClient = require('./monday-api-client.js');
  console.log('✅ API client module loads successfully');

  // Test API token validation
  try {
    new apiClient.MondayApiClient('');
    console.log('❌ Empty token validation: FAIL - should have thrown error');
  } catch (e) {
    console.log('✅ Empty token validation: PASS - correctly throws error');
  }

  try {
    new apiClient.MondayApiClient('invalid@token!');
    console.log('❌ Invalid token format validation: FAIL - should have thrown error');
  } catch (e) {
    console.log('✅ Invalid token format validation: PASS - correctly throws error');
  }

  // Test valid token
  try {
    const client = new apiClient.MondayApiClient('valid_token_123');
    console.log('✅ Valid token validation: PASS - accepts valid format');
  } catch (e) {
    console.log('❌ Valid token validation: FAIL - should accept valid token');
  }

  console.log('\n🎉 All basic tests passed! The fixes are working correctly.');

} catch (error) {
  console.error('❌ Test failed:', error.message);
  process.exit(1);
}
