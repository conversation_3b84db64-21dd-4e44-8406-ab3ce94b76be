# GitHub Actions Scripts

This directory contains scripts for the Monday.com integration with Claude code reviews. These scripts automatically create Monday.com tasks for critical code review feedback.

## Overview

The integration consists of three main components:

1. **Claude Output Parser** (`claude-output-parser.js`) - Parses Claude review output to extract Must Fix and Should Fix issues
2. **Monday.com API Client** (`monday-api-client.js`) - Creates tasks in Monday.com via GraphQL API
3. **PR Comment Generator** (`pr-comment-generator.js`) - Generates status comments for pull requests

## Architecture

```
Claude Review Output → Parser → API Client → Monday.com Tasks
                            ↓
                      Comment Generator → PR Comment
```

## Running Tests Locally

### Prerequisites
- Node.js 18 or higher
- npm

### Setup
```bash
cd .github/scripts
npm install
```

### Run Tests
```bash
# Run all tests
npm test

# Run specific test suites
npm run test:parser
npm run test:api
npm run test:comments
```

## Required Environment Variables

### For Testing
- `MONDAY_API_TOKEN` - Monday.com API token (for API client tests)

### For Production (GitHub Actions)
- `MONDAY_API_TOKEN` - Monday.com API token (stored in GitHub Secrets)
- `CLAUDE_OUTPUT` - Claude review output text
- `PR_URL` - Pull request URL
- `PR_NUMBER` - Pull request number

## Configuration

### Monday.com Settings
The API client is configured in `monday-api-client.js`:
- Board ID: `9650366353`
- Group ID: `new_group29179`
- Rate limit: 10 requests/minute (conservative)

### Task Creation
Tasks are created with:
- **Status**: "Working on it"
- **Epic**: "Infrastructure & Platform Foundation"
- **Link**: Direct link to the PR
- **Priority**: HIGH (Must Fix) or MEDIUM (Should Fix)

## Error Handling

The system includes comprehensive error handling:
- **Retry Logic**: 3 attempts with exponential backoff (2s, 4s, 8s)
- **Rate Limiting**: Conservative 10 req/min with 6-second delays
- **Graceful Degradation**: Non-blocking workflow design
- **User Feedback**: PR comments with status and recovery instructions

## Troubleshooting

### Common Issues

**"Invalid API token format"**
- Ensure the Monday.com API token only contains alphanumeric characters, underscores, and hyphens
- Check that the token is not empty or whitespace

**"Rate limit reached"**
- The system automatically handles rate limits with delays
- If persistent, check Monday.com API status

**"GraphQL Error"**
- Verify board ID and group ID are correct
- Check that the API token has proper permissions
- Ensure column IDs match your Monday.com board setup

### Debug Mode
Set `DEBUG=true` environment variable for verbose logging.

### Manual Testing
```bash
# Test parser with sample output
node claude-output-parser.js sample-output.txt

# Test API client (requires MONDAY_API_TOKEN)
MONDAY_API_TOKEN=your_token node monday-api-client.js
```

## Maintenance

### Updating Configuration
- Board/Group IDs: Update `CONFIG` in `monday-api-client.js`
- Column mappings: Update `COLUMN_IDS` in the same file
- Rate limits: Adjust `RATE_LIMIT` settings as needed

### Adding New Issue Types
1. Update section patterns in `claude-output-parser.js`
2. Add corresponding priority mappings
3. Update tests to cover new patterns

## Security Notes

- API tokens are handled securely via GitHub Secrets
- No sensitive data is logged in PR comments
- Input sanitization is performed on all Claude output
- Rate limiting prevents API abuse