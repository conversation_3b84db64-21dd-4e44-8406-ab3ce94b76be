#!/usr/bin/env node

/**
 * Comprehensive Test Suite for Monday.com API Client
 */

const { MondayApiClient, CONFIG } = require('./monday-api-client');

// Simple fetch mock for testing
class MockFetch {
  constructor() {
    this.reset();
  }

  reset() {
    this.calls = [];
    this.responses = [];
    this.currentIndex = 0;
  }

  mockResponseOnce(response) {
    this.responses.push({ type: 'resolve', data: response });
    return this;
  }

  mockRejectOnce(error) {
    this.responses.push({ type: 'reject', data: error });
    return this;
  }

  async mockImplementation(url, options) {
    this.calls.push({ url, options });

    if (this.currentIndex >= this.responses.length) {
      throw new Error('No more mocked responses available');
    }

    const response = this.responses[this.currentIndex++];

    if (response.type === 'reject') {
      throw response.data;
    }

    return {
      ok: true,
      status: 200,
      statusText: 'OK',
      json: async () => JSON.parse(response.data)
    };
  }
}

const mockFetch = new MockFetch();
global.fetch = mockFetch.mockImplementation.bind(mockFetch);

/**
 * Test data
 */
const mockIssues = [
  {
    priority: 'HIGH',
    severity: 'Must Fix',
    summary: 'SQL injection vulnerability in user query',
    description: 'The user input is not properly sanitized before being used in SQL queries, which could lead to SQL injection attacks.'
  },
  {
    priority: 'MEDIUM',
    severity: 'Should Fix',
    summary: 'Missing error handling in async function',
    description: 'The async function does not have proper error handling which could cause unhandled promise rejections.'
  }
];

const mockSuccessResponse = {
  data: {
    create_item: {
      id: '12345',
      name: '[HIGH] SQL injection vulnerability in user query - PR #123',
      url: 'https://mycompany.monday.com/boards/9650366353/pulses/12345'
    }
  }
};

const mockErrorResponse = {
  errors: [
    {
      message: 'Column not found',
      extensions: { code: 'ColumnNotFound' }
    }
  ]
};

/**
 * Test utility functions
 */
function createMockClient() {
  return new MondayApiClient('mock-token');
}

function resetMocks() {
  mockFetch.reset();
}

/**
 * Test Suite Runner
 */
class TestRunner {
  constructor() {
    this.passed = 0;
    this.failed = 0;
    this.results = [];
  }

  async runTest(testName, testFn) {
    console.log(`\n🧪 Running: ${testName}`);
    try {
      resetMocks();
      await testFn();
      console.log(`✅ PASSED: ${testName}`);
      this.passed++;
      this.results.push({ name: testName, status: 'PASSED' });
    } catch (error) {
      console.error(`❌ FAILED: ${testName}`);
      console.error(`   Error: ${error.message}`);
      this.failed++;
      this.results.push({ name: testName, status: 'FAILED', error: error.message });
    }
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(60));
    console.log(`✅ Passed: ${this.passed}`);
    console.log(`❌ Failed: ${this.failed}`);
    console.log(`📋 Total: ${this.passed + this.failed}`);

    if (this.failed > 0) {
      console.log('\nFailed Tests:');
      this.results
        .filter(r => r.status === 'FAILED')
        .forEach(r => console.log(`  - ${r.name}: ${r.error}`));
    }

    return this.failed === 0;
  }
}

/**
 * Individual Test Cases
 */

// Test 1: Single issue creation (Must Fix priority)
async function testSingleMustFixIssue() {
  const client = createMockClient();
  mockFetch.mockResponseOnce(JSON.stringify(mockSuccessResponse));

  const result = await client.createTask(mockIssues[0], 'https://github.com/test/repo/pull/123', 123);

  if (!result.success) throw new Error('Task creation should succeed');
  if (result.taskId !== '12345') throw new Error('Task ID mismatch');
  if (!result.taskName.includes('[HIGH]')) throw new Error('Task name should include priority');
  if (!result.taskName.includes('PR #123')) throw new Error('Task name should include PR number');
}

// Test 2: Multiple issues creation (mixed priorities)
async function testMultipleIssuesCreation() {
  const client = createMockClient();

  // Mock responses for both requests
  mockFetch
    .mockResponseOnce(JSON.stringify(mockSuccessResponse))
    .mockResponseOnce(JSON.stringify({
      data: {
        create_item: {
          id: '12346',
          name: '[MEDIUM] Missing error handling in async function - PR #123',
          url: 'https://mycompany.monday.com/boards/9650366353/pulses/12346'
        }
      }
    }));

  const results = await client.createTasks(mockIssues, 'https://github.com/test/repo/pull/123', 123);

  if (results.length !== 2) throw new Error('Should create 2 tasks');
  if (!results.every(r => r.success)) throw new Error('All tasks should succeed');
  if (results[0].taskName !== '[HIGH] SQL injection vulnerability in user query - PR #123') {
    throw new Error('First task name incorrect');
  }
}

// Test 3: API authentication failure
async function testAuthenticationFailure() {
  const client = createMockClient();
  mockFetch.mockRejectOnce(new Error('HTTP 401: Unauthorized'));

  const result = await client.createTask(mockIssues[0], 'https://github.com/test/repo/pull/123', 123);

  if (result.success) throw new Error('Task creation should fail with auth error');
  if (!result.error.includes('401')) throw new Error('Error should mention 401');
}

// Test 4: Rate limiting scenario
async function testRateLimitingBehavior() {
  const client = createMockClient();

  // Set up client to be at rate limit
  client.requestCount = CONFIG.RATE_LIMIT.MAX_REQUESTS_PER_MINUTE;
  client.requestWindow = Date.now();

  // Mock successful response
  mockFetch.mockResponseOnce(JSON.stringify(mockSuccessResponse));

  const startTime = Date.now();
  await client.createTask(mockIssues[0], 'https://github.com/test/repo/pull/123', 123);
  const endTime = Date.now();

  // Should have waited due to rate limiting (but not in our mocked environment)
  // This test verifies the rate limiting logic exists
  if (client.requestCount === 0) throw new Error('Request count should reset after limit');
}

// Test 5: Network timeout handling
async function testNetworkTimeoutHandling() {
  const client = createMockClient();
  // Set multiple timeout errors to exceed retry limit
  mockFetch
    .mockRejectOnce(new Error('Network timeout'))
    .mockRejectOnce(new Error('Network timeout'))
    .mockRejectOnce(new Error('Network timeout'))
    .mockRejectOnce(new Error('Network timeout'));

  const result = await client.createTask(mockIssues[0], 'https://github.com/test/repo/pull/123', 123);

  if (result.success) throw new Error('Task creation should fail with timeout');
  if (!result.error.toLowerCase().includes('timeout')) throw new Error(`Error should mention timeout, got: ${result.error}`);
}

// Test 6: Invalid board/group ID
async function testInvalidBoardId() {
  const client = createMockClient();
  mockFetch.mockResponseOnce(JSON.stringify({
    errors: [{ message: 'Board not found' }]
  }));

  const result = await client.createTask(mockIssues[0], 'https://github.com/test/repo/pull/123', 123);

  if (result.success) throw new Error('Task creation should fail with invalid board');
  if (!result.error.includes('Board not found')) throw new Error('Error should mention board not found');
}

// Test 7: Retry logic with exponential backoff
async function testRetryLogic() {
  const client = createMockClient();

  // Mock first two requests to fail, third to succeed
  mockFetch
    .mockRejectOnce(new Error('HTTP 500: Internal Server Error'))
    .mockRejectOnce(new Error('HTTP 503: Service Unavailable'))
    .mockResponseOnce(JSON.stringify(mockSuccessResponse));

  const result = await client.createTask(mockIssues[0], 'https://github.com/test/repo/pull/123', 123);

  if (!result.success) throw new Error('Task should succeed after retries');
  if (mockFetch.calls.length !== 3) throw new Error('Should make exactly 3 attempts');
}

// Test 8: Client error handling (no retry)
async function testClientErrorNoRetry() {
  const client = createMockClient();
  mockFetch.mockRejectOnce(new Error('HTTP 400: Bad Request'));

  const result = await client.createTask(mockIssues[0], 'https://github.com/test/repo/pull/123', 123);

  if (result.success) throw new Error('Task creation should fail with client error');
  if (mockFetch.calls.length !== 1) throw new Error('Should not retry on client error');
}

// Test 9: GraphQL errors
async function testGraphQLErrors() {
  const client = createMockClient();
  mockFetch.mockResponseOnce(JSON.stringify(mockErrorResponse));

  const result = await client.createTask(mockIssues[0], 'https://github.com/test/repo/pull/123', 123);

  if (result.success) throw new Error('Task creation should fail with GraphQL error');
  if (!result.error.includes('Column not found')) throw new Error('Error should include GraphQL error message');
}

// Test 10: Empty issues array
async function testEmptyIssuesArray() {
  const client = createMockClient();

  const results = await client.createTasks([], 'https://github.com/test/repo/pull/123', 123);

  if (results.length !== 0) throw new Error('Should return empty array for empty input');
}

// Test 11: Invalid JSON column values handling
async function testColumnValuesConstruction() {
  const client = createMockClient();
  mockFetch.mockResponseOnce(JSON.stringify(mockSuccessResponse));

  const result = await client.createTask(mockIssues[0], 'https://github.com/test/repo/pull/123', 123);

  // Check that the fetch was called with proper JSON structure
  const callBody = JSON.parse(mockFetch.calls[0].options.body);
  const columnValues = JSON.parse(callBody.variables.columnValues);

  if (!columnValues[CONFIG.COLUMN_IDS.STATUS]) throw new Error('Status column not set');
  if (!columnValues[CONFIG.COLUMN_IDS.EPIC]) throw new Error('Epic column not set');
  if (!columnValues[CONFIG.COLUMN_IDS.LINK]) throw new Error('Link column not set');
}

// Test 12: Constructor validation
async function testConstructorValidation() {
  try {
    new MondayApiClient(); // No token
    throw new Error('Should throw error for missing token');
  } catch (error) {
    if (!error.message.includes('token is required')) {
      throw new Error('Should throw specific error for missing token');
    }
  }

  try {
    new MondayApiClient(''); // Empty token
    throw new Error('Should throw error for empty token');
  } catch (error) {
    if (!error.message.includes('token is required')) {
      throw new Error('Should throw specific error for empty token');
    }
  }
}

/**
 * Main test execution
 */
async function runAllTests() {
  console.log('🚀 Starting Monday.com API Client Test Suite');
  console.log('='.repeat(60));

  const runner = new TestRunner();

  // Run all tests
  await runner.runTest('Single Must Fix Issue Creation', testSingleMustFixIssue);
  await runner.runTest('Multiple Issues Creation', testMultipleIssuesCreation);
  await runner.runTest('Authentication Failure', testAuthenticationFailure);
  await runner.runTest('Rate Limiting Behavior', testRateLimitingBehavior);
  await runner.runTest('Network Timeout Handling', testNetworkTimeoutHandling);
  await runner.runTest('Invalid Board ID', testInvalidBoardId);
  await runner.runTest('Retry Logic with Exponential Backoff', testRetryLogic);
  await runner.runTest('Client Error No Retry', testClientErrorNoRetry);
  await runner.runTest('GraphQL Errors', testGraphQLErrors);
  await runner.runTest('Empty Issues Array', testEmptyIssuesArray);
  await runner.runTest('Column Values Construction', testColumnValuesConstruction);
  await runner.runTest('Constructor Validation', testConstructorValidation);

  // Print summary and exit
  const allPassed = runner.printSummary();
  process.exit(allPassed ? 0 : 1);
}

// Run tests if script is executed directly
if (require.main === module) {
  runAllTests();
}
