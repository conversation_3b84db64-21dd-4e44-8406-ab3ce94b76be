#!/usr/bin/env node

/**
 * Test Suite for PR Comment Generator
 */

const {
    generateComment,
    generateSuccessComment,
    generatePartialFailureComment,
    generateCompleteFailureComment,
    extractSummaryFromTaskName,
    truncateError,
    generateErrorSummary
} = require('./pr-comment-generator.js');

// Test data fixtures
const testData = {
    successfulTasks: [
        {
            success: true,
            taskId: '12345',
            taskUrl: 'https://monday.com/boards/123/items/12345',
            taskName: '[HIGH] Fix authentication bypass vulnerability - PR #123',
            attempts: 1
        },
        {
            success: true,
            taskId: '12346',
            taskUrl: 'https://monday.com/boards/123/items/12346',
            taskName: '[MEDIUM] Improve error handling in API - PR #123',
            attempts: 1
        }
    ],

    failedTasks: [
        {
            success: false,
            taskName: '[HIGH] Fix SQL injection in query builder - PR #123',
            error: 'GraphQL Error: Column value validation failed',
            attempts: 3
        },
        {
            success: false,
            taskName: '[MEDIUM] Add input validation - PR #123',
            error: 'HTTP 503: Service temporarily unavailable',
            attempts: 2
        }
    ],

    parsedIssues: [
        {
            priority: 'HIGH',
            summary: 'Fix authentication bypass vulnerability',
            description: 'Critical security issue in auth middleware',
            severity: 'Must Fix'
        },
        {
            priority: 'MEDIUM',
            summary: 'Improve error handling in API',
            description: 'Add proper error responses',
            severity: 'Should Fix'
        },
        {
            priority: 'HIGH',
            summary: 'Fix SQL injection in query builder',
            description: 'Parameterize database queries',
            severity: 'Must Fix'
        },
        {
            priority: 'MEDIUM',
            summary: 'Add input validation',
            description: 'Validate user inputs',
            severity: 'Should Fix'
        }
    ],

    metadata: {
        prNumber: 123,
        prUrl: 'https://github.com/test/repo/pull/123',
        workflowUrl: 'https://github.com/test/repo/actions/runs/456',
        timestamp: '2025-01-08T12:00:00.000Z'
    },

    summary: {
        successful: {
            total: 2,
            successful: 2,
            failed: 0,
            success_rate: 100
        },
        partial: {
            total: 4,
            successful: 2,
            failed: 2,
            success_rate: 50
        },
        failed: {
            total: 2,
            successful: 0,
            failed: 2,
            success_rate: 0
        }
    }
};

// Test utilities
let testCount = 0;
let passedTests = 0;
let failedTests = 0;

function test(description, testFn) {
    testCount++;
    try {
        testFn();
        console.log(`✅ Test ${testCount}: ${description}`);
        passedTests++;
    } catch (error) {
        console.error(`❌ Test ${testCount}: ${description}`);
        console.error(`   Error: ${error.message}`);
        failedTests++;
    }
}

function assert(condition, message) {
    if (!condition) {
        throw new Error(message || 'Assertion failed');
    }
}

function assertContains(str, substring, message) {
    if (!str || !str.includes(substring)) {
        throw new Error(message || `Expected "${str}" to contain "${substring}"`);
    }
}

function assertNotContains(str, substring, message) {
    if (str && str.includes(substring)) {
        throw new Error(message || `Expected "${str}" to not contain "${substring}"`);
    }
}

// Test utility functions
test('extractSummaryFromTaskName - extracts summary correctly', () => {
    const result = extractSummaryFromTaskName('[HIGH] Fix authentication bypass vulnerability - PR #123');
    assert(result === 'Fix authentication bypass vulnerability', 'Should extract summary without priority and PR info');
});

test('extractSummaryFromTaskName - handles malformed task names', () => {
    const result = extractSummaryFromTaskName('Invalid task name format');
    assert(result === 'Invalid task name format', 'Should return original string if format doesn\'t match');
});

test('truncateError - truncates long errors', () => {
    const longError = 'This is a very long error message that should be truncated because it exceeds the maximum length limit and would make the comment too verbose for users to read effectively';
    const result = truncateError(longError);
    assert(result.length <= 100, 'Error should be truncated to 100 characters or less');
    assert(result.endsWith('...'), 'Truncated error should end with ellipsis');
});

test('truncateError - handles short errors', () => {
    const shortError = 'Short error';
    const result = truncateError(shortError);
    assert(result === 'Short error', 'Short errors should not be modified');
});

test('truncateError - removes common prefixes', () => {
    const result = truncateError('GraphQL Error: Invalid field specified');
    assert(result === 'Invalid field specified', 'Should remove GraphQL Error prefix');
});

test('generateErrorSummary - identifies auth errors', () => {
    const failedTasks = [
        { error: '401 Unauthorized access' },
        { error: 'Authentication failed' }
    ];
    const result = generateErrorSummary(failedTasks);
    assert(result === 'AUTH_ERROR', 'Should identify authentication errors');
});

test('generateErrorSummary - identifies server errors', () => {
    const failedTasks = [
        { error: 'HTTP 500: Internal server error' },
        { error: '503 Service unavailable' }
    ];
    const result = generateErrorSummary(failedTasks);
    assert(result === 'SERVER_ERROR', 'Should identify server errors');
});

// Test comment generation scenarios
test('generateSuccessComment - creates proper success comment', () => {
    const comment = generateSuccessComment(testData.successfulTasks, testData.parsedIssues);

    assertContains(comment, '✅ Monday.com Integration Success', 'Should have success header');
    assertContains(comment, 'Created **2** tasks', 'Should show correct task count');
    assertContains(comment, 'High Priority (Must Fix)', 'Should have high priority section');
    assertContains(comment, 'Medium Priority (Should Fix)', 'Should have medium priority section');
    assertContains(comment, 'Task #12345', 'Should include first task ID');
    assertContains(comment, 'Task #12346', 'Should include second task ID');
    assertContains(comment, 'Next Steps', 'Should include next steps');
    assertContains(comment, 'Auto-generated from Claude Code Review', 'Should have footer');
});

test('generatePartialFailureComment - creates proper partial failure comment', () => {
    const mixedResults = [...testData.successfulTasks, ...testData.failedTasks];
    const comment = generatePartialFailureComment(mixedResults, testData.summary.partial, testData.metadata);

    assertContains(comment, '⚠️ Monday.com Integration Partial Failure', 'Should have partial failure header');
    assertContains(comment, '2 tasks created successfully, 2 tasks failed', 'Should show correct counts');
    assertContains(comment, '✅ Successfully Created Tasks', 'Should have success section');
    assertContains(comment, '❌ Failed Task Creation', 'Should have failure section');
    assertContains(comment, '🔧 Next Steps', 'Should have next steps section');
    assertContains(comment, testData.metadata.workflowUrl, 'Should include workflow URL');
    assertContains(comment, testData.metadata.timestamp, 'Should include timestamp');
});

test('generateCompleteFailureComment - creates proper complete failure comment', () => {
    const comment = generateCompleteFailureComment(testData.failedTasks, testData.parsedIssues, testData.metadata);

    assertContains(comment, '❌ Monday.com Integration Failed', 'Should have failure header');
    assertContains(comment, 'Unable to create tasks for **4** critical', 'Should show correct issue count');
    assertContains(comment, 'Issues Requiring Manual Tracking', 'Should have manual tracking section');
    assertContains(comment, '🔧 Immediate Action Required', 'Should have action required section');
    assertContains(comment, 'Fix authentication bypass vulnerability', 'Should list issues');
    assertContains(comment, '*Error Code*:', 'Should include error summary');
});

// Test main generateComment function scenarios
test('generateComment - returns success comment for all successful tasks', () => {
    const comment = generateComment(
        testData.successfulTasks,
        testData.summary.successful,
        testData.parsedIssues.slice(0, 2), // Only first 2 issues
        testData.metadata
    );

    assert(comment !== null, 'Should generate a comment');
    assertContains(comment, '✅ Monday.com Integration Success', 'Should be success comment');
});

test('generateComment - returns partial failure comment for mixed results', () => {
    const mixedResults = [...testData.successfulTasks, ...testData.failedTasks];
    const comment = generateComment(
        mixedResults,
        testData.summary.partial,
        testData.parsedIssues,
        testData.metadata
    );

    assert(comment !== null, 'Should generate a comment');
    assertContains(comment, '⚠️ Monday.com Integration Partial Failure', 'Should be partial failure comment');
});

test('generateComment - returns complete failure comment for all failed tasks', () => {
    const comment = generateComment(
        testData.failedTasks,
        testData.summary.failed,
        testData.parsedIssues.slice(2), // Last 2 issues
        testData.metadata
    );

    assert(comment !== null, 'Should generate a comment');
    assertContains(comment, '❌ Monday.com Integration Failed', 'Should be failure comment');
});

test('generateComment - returns null for empty task results', () => {
    const comment = generateComment(
        [],
        { total: 0, successful: 0, failed: 0, success_rate: 0 },
        testData.parsedIssues,
        testData.metadata
    );

    assert(comment === null, 'Should return null for empty results');
});

test('generateComment - returns null for no parsed issues', () => {
    const comment = generateComment(
        testData.successfulTasks,
        testData.summary.successful,
        [],
        testData.metadata
    );

    assert(comment === null, 'Should return null for no parsed issues');
});

test('generateComment - returns null for invalid task results', () => {
    const comment = generateComment(
        null,
        testData.summary.successful,
        testData.parsedIssues,
        testData.metadata
    );

    assert(comment === null, 'Should return null for invalid task results');
});

// Test comment length requirements
test('success comment respects length requirements', () => {
    const comment = generateSuccessComment(testData.successfulTasks, testData.parsedIssues);
    const wordCount = comment.split(' ').length;
    assert(wordCount < 500, 'Comment should be under 500 words for readability');
});

test('partial failure comment respects length requirements', () => {
    const mixedResults = [...testData.successfulTasks, ...testData.failedTasks];
    const comment = generatePartialFailureComment(mixedResults, testData.summary.partial, testData.metadata);
    const wordCount = comment.split(' ').length;
    assert(wordCount < 500, 'Comment should be under 500 words for readability');
});

test('complete failure comment respects length requirements', () => {
    const comment = generateCompleteFailureComment(testData.failedTasks, testData.parsedIssues, testData.metadata);
    const wordCount = comment.split(' ').length;
    assert(wordCount < 500, 'Comment should be under 500 words for readability');
});

// Test visual indicators requirement
test('comments include proper visual indicators', () => {
    const successComment = generateSuccessComment(testData.successfulTasks, testData.parsedIssues);
    assertContains(successComment, '✅', 'Success comment should have success indicator');

    const mixedResults = [...testData.successfulTasks, ...testData.failedTasks];
    const partialComment = generatePartialFailureComment(mixedResults, testData.summary.partial, testData.metadata);
    assertContains(partialComment, '⚠️', 'Partial failure comment should have warning indicator');
    assertContains(partialComment, '✅', 'Should have success indicators for successful tasks');
    assertContains(partialComment, '❌', 'Should have failure indicators for failed tasks');
    assertContains(partialComment, '🔧', 'Should have action indicator');

    const failureComment = generateCompleteFailureComment(testData.failedTasks, testData.parsedIssues, testData.metadata);
    assertContains(failureComment, '❌', 'Failure comment should have failure indicator');
    assertContains(failureComment, '🔧', 'Should have action indicator');
});

// Test actionable content requirement
test('comments include actionable next steps', () => {
    const successComment = generateSuccessComment(testData.successfulTasks, testData.parsedIssues);
    assertContains(successComment, 'Next Steps', 'Success comment should have next steps');
    assertContains(successComment, 'Review and address', 'Should provide actionable guidance');

    const mixedResults = [...testData.successfulTasks, ...testData.failedTasks];
    const partialComment = generatePartialFailureComment(mixedResults, testData.summary.partial, testData.metadata);
    assertContains(partialComment, 'Next Steps', 'Partial failure comment should have next steps');
    assertContains(partialComment, 'Review failed tasks', 'Should provide specific actions');

    const failureComment = generateCompleteFailureComment(testData.failedTasks, testData.parsedIssues, testData.metadata);
    assertContains(failureComment, 'Immediate Action Required', 'Failure comment should have immediate actions');
    assertContains(failureComment, 'Manually create', 'Should provide specific manual steps');
});

// Run all tests
console.log('🧪 Running PR Comment Generator Tests\n');

// Execute all tests
console.log('Running test suite...\n');

console.log('\n📊 Test Results Summary:');
console.log(`Total Tests: ${testCount}`);
console.log(`✅ Passed: ${passedTests}`);
console.log(`❌ Failed: ${failedTests}`);
console.log(`Success Rate: ${testCount > 0 ? ((passedTests / testCount) * 100).toFixed(1) : 0}%`);

if (failedTests === 0) {
    console.log('\n🎉 All tests passed! The PR comment generator is working correctly.');
    process.exit(0);
} else {
    console.log('\n⚠️  Some tests failed. Please review the failures above.');
    process.exit(1);
}
