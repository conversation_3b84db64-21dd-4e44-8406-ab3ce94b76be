#!/usr/bin/env node

/**
 * PR Comment Generator for Monday.com Integration
 * Generates GitHub PR comments based on Monday.com task creation results
 */

/**
 * @typedef {Object} TaskResult
 * @property {boolean} success - Whether task creation was successful
 * @property {string} taskId - Monday.com task ID (if successful)
 * @property {string} taskUrl - Monday.com task URL (if successful)
 * @property {string} taskName - Task name that was created
 * @property {string} error - Error message (if failed)
 * @property {number} attempts - Number of attempts made
 */

/**
 * @typedef {Object} ParsedIssue
 * @property {'HIGH' | 'MEDIUM'} priority
 * @property {string} summary
 * @property {string} description
 * @property {'Must Fix' | 'Should Fix'} severity
 */

/**
 * @typedef {Object} Summary
 * @property {number} total - Total number of tasks attempted
 * @property {number} successful - Number of successful task creations
 * @property {number} failed - Number of failed task creations
 * @property {number} success_rate - Success rate percentage
 */

/**
 * @typedef {Object} Metadata
 * @property {number} prNumber - Pull request number
 * @property {string} prUrl - Pull request URL
 * @property {string} workflowUrl - GitHub Actions workflow run URL
 * @property {string} timestamp - ISO timestamp
 */

/**
 * Generate success comment for when all tasks are created successfully
 * @param {TaskResult[]} successfulTasks - Array of successful task results
 * @param {ParsedIssue[]} parsedIssues - Original parsed issues for priority mapping
 * @returns {string} Formatted success comment
 */
function generateSuccessComment(successfulTasks, parsedIssues) {
    // Create mapping of task names to issues for priority determination
    const issueMap = new Map();
    parsedIssues.forEach(issue => {
        issueMap.set(issue.summary, issue);
    });

    // Group tasks by priority
    const highPriorityTasks = [];
    const mediumPriorityTasks = [];

    successfulTasks.forEach(task => {
        // Extract summary from task name (remove priority prefix and PR suffix)
        const summaryMatch = task.taskName.match(/^\[(?:HIGH|MEDIUM)\]\s(.+?)\s-\sPR\s#\d+$/);
        const summary = summaryMatch ? summaryMatch[1] : task.taskName;

        const issue = issueMap.get(summary);
        const priority = issue?.priority || 'MEDIUM';

        const taskInfo = {
            id: task.taskId,
            url: task.taskUrl,
            summary: summary
        };

        if (priority === 'HIGH') {
            highPriorityTasks.push(taskInfo);
        } else {
            mediumPriorityTasks.push(taskInfo);
        }
    });

    let comment = `## ✅ Monday.com Integration Success\n\n`;
    comment += `Created **${successfulTasks.length}** tasks for critical code review issues:\n\n`;

    if (highPriorityTasks.length > 0) {
        comment += `### High Priority (Must Fix)\n`;
        highPriorityTasks.forEach(task => {
            comment += `- [Task #${task.id}](${task.url}): ${task.summary}\n`;
        });
        comment += `\n`;
    }

    if (mediumPriorityTasks.length > 0) {
        comment += `### Medium Priority (Should Fix)\n`;
        mediumPriorityTasks.forEach(task => {
            comment += `- [Task #${task.id}](${task.url}): ${task.summary}\n`;
        });
        comment += `\n`;
    }

    comment += `**Next Steps**: Review and address these tasks before merging.\n\n`;
    comment += `---\n*Auto-generated from Claude Code Review → Monday.com*`;

    return comment;
}

/**
 * Generate partial failure comment for mixed success/failure scenarios
 * @param {TaskResult[]} taskResults - All task results (success and failure)
 * @param {Summary} summary - Summary statistics
 * @param {Metadata} metadata - PR and workflow metadata
 * @returns {string} Formatted partial failure comment
 */
function generatePartialFailureComment(taskResults, summary, metadata) {
    const successfulTasks = taskResults.filter(r => r.success);
    const failedTasks = taskResults.filter(r => !r.success);

    let comment = `## ⚠️ Monday.com Integration Partial Failure\n\n`;
    comment += `**Status**: ${summary.successful} tasks created successfully, ${summary.failed} tasks failed\n\n`;

    if (successfulTasks.length > 0) {
        comment += `### ✅ Successfully Created Tasks\n`;
        successfulTasks.forEach(task => {
            comment += `- [Task #${task.taskId}](${task.taskUrl}): ${extractSummaryFromTaskName(task.taskName)}\n`;
        });
        comment += `\n`;
    }

    if (failedTasks.length > 0) {
        comment += `### ❌ Failed Task Creation\n`;
        failedTasks.forEach(task => {
            const summary = extractSummaryFromTaskName(task.taskName);
            const briefError = truncateError(task.error);
            comment += `- **${summary}**: ${briefError}\n`;
        });
        comment += `\n`;
    }

    comment += `### 🔧 Next Steps\n`;
    comment += `- Review failed tasks above for manual creation\n`;
    comment += `- Check [workflow run](${metadata.workflowUrl}) for detailed error logs\n`;
    comment += `- Contact DevOps if errors persist\n\n`;
    comment += `---\n`;
    comment += `*Workflow Run*: ${metadata.workflowUrl} | *Timestamp*: ${metadata.timestamp}`;

    return comment;
}

/**
 * Generate complete failure comment for when all tasks fail
 * @param {TaskResult[]} taskResults - All failed task results
 * @param {ParsedIssue[]} parsedIssues - Original parsed issues
 * @param {Metadata} metadata - PR and workflow metadata
 * @returns {string} Formatted complete failure comment
 */
function generateCompleteFailureComment(taskResults, parsedIssues, metadata) {
    let comment = `## ❌ Monday.com Integration Failed\n\n`;
    comment += `Unable to create tasks for **${parsedIssues.length}** critical code review issues.\n\n`;

    comment += `### Issues Requiring Manual Tracking\n`;
    parsedIssues.forEach((issue, index) => {
        comment += `${index + 1}. **[${issue.priority}]** ${issue.summary}\n`;
    });
    comment += `\n`;

    comment += `### 🔧 Immediate Action Required\n`;
    comment += `- Manually create Monday.com tasks for critical issues above\n`;
    comment += `- Check [workflow logs](${metadata.workflowUrl}) for technical details\n`;
    comment += `- Notify DevOps team if API issues persist\n\n`;
    comment += `---\n`;
    comment += `*Workflow Run*: ${metadata.workflowUrl} | *Error Code*: ${generateErrorSummary(taskResults)}`;

    return comment;
}

/**
 * Extract summary text from task name by removing priority prefix and PR suffix
 * @param {string} taskName - Full task name
 * @returns {string} Extracted summary
 */
function extractSummaryFromTaskName(taskName) {
    const match = taskName.match(/^\[(?:HIGH|MEDIUM)\]\s(.+?)\s-\sPR\s#\d+$/);
    return match ? match[1] : taskName;
}

/**
 * Truncate error message to keep comments concise
 * @param {string} error - Original error message
 * @returns {string} Truncated error message
 */
function truncateError(error) {
    if (!error) return 'Unknown error';

    // Remove common prefixes and keep core message
    let briefError = error
        .replace(/^(Error:|GraphQL Error:|HTTP \d+:)\s*/, '')
        .replace(/\s+/g, ' ')
        .trim();

    // Truncate if too long
    if (briefError.length > 100) {
        briefError = briefError.substring(0, 97) + '...';
    }

    return briefError;
}

/**
 * Generate a brief error summary from failed task results
 * @param {TaskResult[]} failedTasks - Array of failed task results
 * @returns {string} Brief error summary
 */
function generateErrorSummary(failedTasks) {
    if (!failedTasks || failedTasks.length === 0) {
        return 'UNKNOWN_ERROR';
    }

    // Look for common error patterns
    const errorMessages = failedTasks.map(t => t.error || '').join(' ');

    if (errorMessages.includes('401') || errorMessages.includes('unauthorized')) {
        return 'AUTH_ERROR';
    } else if (errorMessages.includes('403') || errorMessages.includes('forbidden')) {
        return 'PERMISSION_ERROR';
    } else if (errorMessages.includes('500') || errorMessages.includes('502') || errorMessages.includes('503')) {
        return 'SERVER_ERROR';
    } else if (errorMessages.includes('timeout')) {
        return 'TIMEOUT_ERROR';
    } else if (errorMessages.includes('rate limit')) {
        return 'RATE_LIMIT_ERROR';
    } else {
        return 'API_ERROR';
    }
}

/**
 * Main function to generate appropriate comment based on results
 * @param {TaskResult[]} taskResults - Array of task creation results
 * @param {Summary} summary - Summary statistics
 * @param {ParsedIssue[]} parsedIssues - Original parsed issues
 * @param {Metadata} metadata - PR and workflow metadata
 * @returns {string|null} Generated comment or null if no comment needed
 */
function generateComment(taskResults, summary, parsedIssues, metadata) {
    // Validate inputs
    if (!Array.isArray(taskResults)) {
        console.log('No task results provided - skipping comment generation');
        return null;
    }

    if (!Array.isArray(parsedIssues) || parsedIssues.length === 0) {
        console.log('No parsed issues provided - skipping comment generation');
        return null;
    }

    // Don't comment if no tasks were attempted
    if (taskResults.length === 0) {
        console.log('No tasks were attempted - skipping comment generation');
        return null;
    }

    const successfulTasks = taskResults.filter(r => r.success);
    const failedTasks = taskResults.filter(r => !r.success);

    // Determine comment type based on results
    if (failedTasks.length === 0) {
        // All tasks successful
        return generateSuccessComment(successfulTasks, parsedIssues);
    } else if (successfulTasks.length > 0) {
        // Partial failure
        return generatePartialFailureComment(taskResults, summary, metadata);
    } else {
        // Complete failure
        return generateCompleteFailureComment(taskResults, parsedIssues, metadata);
    }
}

module.exports = {
    generateComment,
    generateSuccessComment,
    generatePartialFailureComment,
    generateCompleteFailureComment,
    extractSummaryFromTaskName,
    truncateError,
    generateErrorSummary
};

// CLI support for testing
if (require.main === module) {
    console.log('PR Comment Generator - Test Mode');

    // Example usage for testing
    const testTaskResults = [
        {
            success: true,
            taskId: '12345',
            taskUrl: 'https://monday.com/boards/123/items/12345',
            taskName: '[HIGH] Fix security vulnerability in auth - PR #123',
            attempts: 1
        }
    ];

    const testSummary = {
        total: 1,
        successful: 1,
        failed: 0,
        success_rate: 100
    };

    const testIssues = [
        {
            priority: 'HIGH',
            summary: 'Fix security vulnerability in auth',
            description: 'Authentication bypass detected',
            severity: 'Must Fix'
        }
    ];

    const testMetadata = {
        prNumber: 123,
        prUrl: 'https://github.com/repo/pull/123',
        workflowUrl: 'https://github.com/repo/actions/runs/456',
        timestamp: new Date().toISOString()
    };

    const comment = generateComment(testTaskResults, testSummary, testIssues, testMetadata);
    console.log('\nGenerated Comment:');
    console.log('='.repeat(50));
    console.log(comment);
}
