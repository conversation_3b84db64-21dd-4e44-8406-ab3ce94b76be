#!/usr/bin/env node

/**
 * Test suite for Claude Output Parser
 */

const {
  parseClaudeOutput,
  extractIssuesFromSection,
  extractSectionContent,
  sectionHeaders,
  issuePattern
} = require('./claude-output-parser.js');

// Test data - various Claude output formats
const testCases = {
  standardOutput: `
PR Summary Assessment
Overall Quality: High-level evaluation of the PR's readiness

Must Fix (Blocking Issues)
- SQL injection vulnerability in user input handling - the current implementation directly concatenates user input
- Missing authentication check on admin endpoints allowing unauthorized access
- Memory leak in connection pooling that will crash the application under load

Should Fix (Strong Recommendations)
- Performance bottleneck in database query that could be optimized with proper indexing
- Missing error handling for API timeout scenarios that could leave users in inconsistent state
- Code duplication in validation logic that makes maintenance difficult

Consider Fixing (Suggestions)
- Variable naming could be more descriptive for better maintainability
- Additional logging would help with debugging production issues
`,

  mustFixOnly: `
Code Review Results

Must Fix (Blocking Issues)
- Critical security flaw: passwords stored in plaintext in database
- Breaking change without migration path will cause production failures

Consider Fixing (Suggestions)
- Code formatting inconsistencies
`,

  shouldFixOnly: `
Review Summary

Should Fix (Strong Recommendations)
- Performance regression detected in user authentication flow
- Missing unit tests for critical business logic functions
- Inconsistent error messaging across API endpoints

Praise & Recognition
- Excellent documentation and clear variable naming
`,

  noQualifyingIssues: `
PR Review Complete

Consider Fixing (Suggestions)
- Minor code style improvements for consistency
- Additional comments would be helpful for complex algorithms

Praise & Recognition
- Great implementation of the new feature
- Comprehensive test coverage
- Clear and maintainable code structure
`,

  malformedOutput: `
Random text without proper structure
Some content here
No proper sections or formatting
`,

  emptyOutput: '',

  complexIssues: `
Must Fix (Blocking Issues)
- XSS vulnerability in comment rendering system
  The current implementation renders user comments without proper sanitization,
  allowing malicious scripts to execute in other users' browsers.
  This violates our security requirements and could compromise user data.

- Database transaction deadlock in concurrent user updates
  Multiple users editing the same resource simultaneously causes database
  deadlocks and application timeouts. This will cause data corruption
  in production environments.

Should Fix (Strong Recommendations)
- N+1 query problem in user profile loading
  Current implementation loads user data with separate queries for each
  relationship, causing performance degradation as user count grows.
  Should be refactored to use proper joins or eager loading.
`
};

// Test results tracking
let totalTests = 0;
let passedTests = 0;

/**
 * Test assertion helper
 */
function assert(condition, message) {
  totalTests++;
  if (condition) {
    passedTests++;
    console.log(`✅ PASS: ${message}`);
  } else {
    console.log(`❌ FAIL: ${message}`);
  }
}

/**
 * Test section extraction functionality
 */
function testSectionExtraction() {
  console.log('\n=== Testing Section Extraction ===');

  // Test Must Fix section extraction
  const mustFixContent = extractSectionContent(testCases.standardOutput, sectionHeaders.mustFix);
  assert(mustFixContent.includes('SQL injection vulnerability'), 'Must Fix section extracted correctly');
  assert(mustFixContent.includes('Memory leak in connection pooling'), 'Must Fix section contains all issues');

  // Test Should Fix section extraction
  const shouldFixContent = extractSectionContent(testCases.standardOutput, sectionHeaders.shouldFix);
  assert(shouldFixContent.includes('Performance bottleneck'), 'Should Fix section extracted correctly');
  assert(shouldFixContent.includes('Code duplication'), 'Should Fix section contains all issues');

  // Test non-existent section
  const noSection = extractSectionContent(testCases.noQualifyingIssues, sectionHeaders.mustFix);
  assert(noSection === '', 'Non-existent section returns empty string');
}

/**
 * Test issue extraction from sections
 */
function testIssueExtraction() {
  console.log('\n=== Testing Issue Extraction ===');

  const sampleSection = `
- SQL injection vulnerability in user input handling - the current implementation directly concatenates user input
- Missing authentication check on admin endpoints allowing unauthorized access
- Memory leak in connection pooling that will crash the application under load
`;

  const issues = extractIssuesFromSection(sampleSection, 'Must Fix');

  assert(issues.length === 3, 'Extracted correct number of issues from section');
  assert(issues[0].severity === 'Must Fix', 'Issue severity set correctly');
  assert(issues[0].priority === 'HIGH', 'Must Fix mapped to HIGH priority');
  assert(issues[0].summary.includes('SQL injection'), 'Issue summary extracted correctly');
  assert(issues[0].description.includes('concatenates user input'), 'Issue description contains full text');
}

/**
 * Test complete parsing functionality
 */
function testCompleteParsing() {
  console.log('\n=== Testing Complete Parsing ===');

  // Test standard output with both sections
  const standardIssues = parseClaudeOutput(testCases.standardOutput);
  assert(standardIssues.length === 6, 'Standard output: correct total issue count');

  const highPriorityIssues = standardIssues.filter(i => i.priority === 'HIGH');
  const mediumPriorityIssues = standardIssues.filter(i => i.priority === 'MEDIUM');

  assert(highPriorityIssues.length === 3, 'Standard output: correct HIGH priority count');
  assert(mediumPriorityIssues.length === 3, 'Standard output: correct MEDIUM priority count');

  // Test priority ordering (HIGH should come first)
  assert(standardIssues[0].priority === 'HIGH', 'Issues sorted by priority (HIGH first)');

  // Test Must Fix only
  const mustFixOnlyIssues = parseClaudeOutput(testCases.mustFixOnly);
  assert(mustFixOnlyIssues.length === 2, 'Must Fix only: correct issue count');
  assert(mustFixOnlyIssues.every(i => i.priority === 'HIGH'), 'Must Fix only: all issues HIGH priority');

  // Test Should Fix only
  const shouldFixOnlyIssues = parseClaudeOutput(testCases.shouldFixOnly);
  assert(shouldFixOnlyIssues.length === 3, 'Should Fix only: correct issue count');
  assert(shouldFixOnlyIssues.every(i => i.priority === 'MEDIUM'), 'Should Fix only: all issues MEDIUM priority');
}

/**
 * Test edge cases and error handling
 */
function testEdgeCases() {
  console.log('\n=== Testing Edge Cases ===');

  // Test no qualifying issues
  const noIssues = parseClaudeOutput(testCases.noQualifyingIssues);
  assert(noIssues.length === 0, 'No qualifying issues: returns empty array');

  // Test malformed output
  const malformedIssues = parseClaudeOutput(testCases.malformedOutput);
  assert(malformedIssues.length === 0, 'Malformed output: returns empty array without crashing');

  // Test empty output
  const emptyIssues = parseClaudeOutput(testCases.emptyOutput);
  assert(emptyIssues.length === 0, 'Empty output: returns empty array');

  // Test null/undefined input
  const nullIssues = parseClaudeOutput(null);
  assert(nullIssues.length === 0, 'Null input: returns empty array');

  const undefinedIssues = parseClaudeOutput(undefined);
  assert(undefinedIssues.length === 0, 'Undefined input: returns empty array');

  // Test non-string input
  const numberIssues = parseClaudeOutput(123);
  assert(numberIssues.length === 0, 'Number input: returns empty array');
}

/**
 * Test complex multiline issues
 */
function testComplexIssues() {
  console.log('\n=== Testing Complex Issues ===');

  const complexIssues = parseClaudeOutput(testCases.complexIssues);
  assert(complexIssues.length === 3, 'Complex issues: correct total count');

  // Check that multiline issues are properly parsed
  const xssIssue = complexIssues.find(i => i.summary.includes('XSS vulnerability'));
  assert(xssIssue !== undefined, 'Complex issues: XSS issue found');
  assert(xssIssue.description.includes('malicious scripts'), 'Complex issues: multiline description captured');

  const deadlockIssue = complexIssues.find(i => i.summary.includes('Database transaction deadlock'));
  assert(deadlockIssue !== undefined, 'Complex issues: deadlock issue found');
  assert(deadlockIssue.description.includes('data corruption'), 'Complex issues: full multiline content captured');
}

/**
 * Test JSON serialization for GitHub Actions
 */
function testJSONSerialization() {
  console.log('\n=== Testing JSON Serialization ===');

  const issues = parseClaudeOutput(testCases.standardOutput);

  try {
    const jsonString = JSON.stringify(issues);
    const parsedBack = JSON.parse(jsonString);

    assert(parsedBack.length === issues.length, 'JSON serialization: round-trip preserves length');
    assert(parsedBack[0].severity === issues[0].severity, 'JSON serialization: round-trip preserves data');
    assert(typeof jsonString === 'string', 'JSON serialization: produces valid JSON string');

  } catch (error) {
    assert(false, `JSON serialization failed: ${error.message}`);
  }
}

/**
 * Run all tests
 */
function runAllTests() {
  console.log('🧪 Running Claude Output Parser Tests');
  console.log('=====================================');

  testSectionExtraction();
  testIssueExtraction();
  testCompleteParsing();
  testEdgeCases();
  testComplexIssues();
  testJSONSerialization();

  console.log('\n=== Test Results ===');
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${totalTests - passedTests}`);
  console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

  if (passedTests === totalTests) {
    console.log('🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('❌ Some tests failed!');
    process.exit(1);
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = { runAllTests };
