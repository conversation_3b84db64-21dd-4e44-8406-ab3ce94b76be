#!/usr/bin/env node

/**
 * Claude Review Output Parser for Monday.com Integration
 * Parses Claude code review output to extract Must Fix and Should Fix issues
 * for automated task creation in Monday.com.
 */

const fs = require('fs');

// Section header patterns for identifying Must Fix and Should Fix sections
const sectionHeaders = {
  mustFix: /Must Fix \(Blocking Issues\)/i,
  shouldFix: /Should Fix \(Strong Recommendations\)/i
};

// Pattern to extract individual issues from sections
const issuePattern = /^[-•]\s*(.+?)(?=\n[-•]|\n\n|\n[A-Z][^a-z]|\n$|$)/gms;

/**
 * Represents a parsed issue from Claude review output
 * @typedef {Object} ParsedIssue
 * @property {'Must Fix' | 'Should Fix'} severity - Issue severity level
 * @property {'HIGH' | 'MEDIUM'} priority - Priority mapping for Monday.com
 * @property {string} summary - First line of issue (short description)
 * @property {string} description - Full issue text
 * @property {string} fullText - Raw extracted text from <PERSON> output
 */

/**
 * Extracts issues from a specific section of <PERSON> output
 * @param {string} sectionText - Text content of the section
 * @param {'Must Fix' | 'Should Fix'} severity - Severity level for this section
 * @returns {ParsedIssue[]} Array of parsed issues
 */
function extractIssuesFromSection(sectionText, severity) {
  // Add validation
  if (!sectionText || typeof sectionText !== 'string') {
    return [];
  }
  if (!['Must Fix', 'Should Fix'].includes(severity)) {
    console.warn(`Invalid severity level: ${severity}`);
    return [];
  }

  const issues = [];

  // Split the section by bullet points, handling both - and •
  const bulletRegex = /^[-•]\s+/gm;
  const issueBlocks = sectionText.split(bulletRegex).filter(block => block.trim());

  for (const block of issueBlocks) {
    const fullText = block.trim();
    if (!fullText) continue;

    // Extract summary (first line) and full description
    const lines = fullText.split('\n').map(line => line.trim()).filter(line => line);
    const summary = lines[0] || fullText.substring(0, 100);

    // Join all lines for full description, preserving multiline content
    const description = lines.join(' ').replace(/\s+/g, ' ').trim();

    issues.push({
      severity,
      priority: severity === 'Must Fix' ? 'HIGH' : 'MEDIUM',
      summary,
      description,
      fullText: fullText
    });
  }

  return issues;
}

/**
 * Finds the content of a section between its header and the next section header
 * @param {string} text - Full Claude output text
 * @param {RegExp} headerPattern - Regex pattern for section header
 * @returns {string} Section content, or empty string if not found
 */
function extractSectionContent(text, headerPattern) {
  const headerMatch = text.match(headerPattern);
  if (!headerMatch) return '';

  const startIndex = headerMatch.index + headerMatch[0].length;

  // Find the end of this section (next section header starting with capital letter)
  const remainingText = text.substring(startIndex);

  // Look for next section: lines that start with capital letters and contain common section patterns
  const nextSectionMatch = remainingText.match(/\n(?:Must Fix|Should Fix|Consider Fixing|Praise|Security|Performance|Code Quality|Testing|Documentation|PR Summary|Review|Overall)/i);
  const endIndex = nextSectionMatch ? nextSectionMatch.index : remainingText.length;

  return remainingText.substring(0, endIndex).trim();
}

/**
 * Main parsing function to extract Must Fix and Should Fix issues from Claude output
 * @param {string} claudeOutput - Raw output text from Claude code review
 * @returns {ParsedIssue[]} Array of parsed issues sorted by priority
 */
function parseClaudeOutput(claudeOutput) {
  if (!claudeOutput || typeof claudeOutput !== 'string') {
    console.warn('Invalid Claude output provided');
    return [];
  }

  const allIssues = [];

  try {
    // Extract Must Fix issues
    const mustFixContent = extractSectionContent(claudeOutput, sectionHeaders.mustFix);
    if (mustFixContent) {
      const mustFixIssues = extractIssuesFromSection(mustFixContent, 'Must Fix');
      allIssues.push(...mustFixIssues);
    }

    // Extract Should Fix issues
    const shouldFixContent = extractSectionContent(claudeOutput, sectionHeaders.shouldFix);
    if (shouldFixContent) {
      const shouldFixIssues = extractIssuesFromSection(shouldFixContent, 'Should Fix');
      allIssues.push(...shouldFixIssues);
    }

    // Sort by priority (HIGH first, then MEDIUM)
    allIssues.sort((a, b) => {
      if (a.priority === 'HIGH' && b.priority === 'MEDIUM') return -1;
      if (a.priority === 'MEDIUM' && b.priority === 'HIGH') return 1;
      return 0;
    });

    console.log(`Parsed ${allIssues.length} issues: ${allIssues.filter(i => i.priority === 'HIGH').length} HIGH, ${allIssues.filter(i => i.priority === 'MEDIUM').length} MEDIUM`);

  } catch (error) {
    console.error('Error parsing Claude output:', error.message);
    // Return empty array on error to avoid breaking the workflow
    return [];
  }

  return allIssues;
}

/**
 * CLI interface for testing and GitHub Actions integration
 */
function main() {
  // Check if running in GitHub Actions environment
  const claudeOutput = process.env.CLAUDE_OUTPUT;

  if (claudeOutput) {
    // GitHub Actions mode - parse environment variable
    const issues = parseClaudeOutput(claudeOutput);

    // Output results for GitHub Actions
    const output = JSON.stringify(issues, null, 2);
    console.log('Parsed Issues:');
    console.log(output);

    // Use modern GitHub Actions output format
    if (process.env.GITHUB_OUTPUT) {
      const fs = require('fs');
      fs.appendFileSync(process.env.GITHUB_OUTPUT, `parsed-issues=${JSON.stringify(issues)}\n`);
    } else {
      // Fallback for local testing
      console.log(`parsed-issues=${JSON.stringify(issues)}`);
    }

  } else if (process.argv.length > 2) {
    // CLI mode - read from file
    const filePath = process.argv[2];

    try {
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const issues = parseClaudeOutput(fileContent);
      console.log(JSON.stringify(issues, null, 2));
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error.message);
      process.exit(1);
    }

  } else {
    console.error('Usage: node claude-output-parser.js <file-path>');
    console.error('Or set CLAUDE_OUTPUT environment variable for GitHub Actions');
    process.exit(1);
  }
}

// Export functions for testing
module.exports = {
  parseClaudeOutput,
  extractIssuesFromSection,
  extractSectionContent,
  sectionHeaders,
  issuePattern
};

// Run main function if script is executed directly
if (require.main === module) {
  main();
}
