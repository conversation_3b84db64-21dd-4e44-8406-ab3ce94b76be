{"author": "XD Incentives Development Team", "dependencies": {}, "description": "GitHub Actions scripts for Monday.com integration with Claude code reviews", "devDependencies": {}, "keywords": ["monday.com", "github-actions", "code-review", "claude"], "license": "PRIVATE", "main": "monday-api-client.js", "name": "monday-integration-scripts", "scripts": {"test": "npm run test:parser && npm run test:api && npm run test:comments", "test:api": "node test-monday-api.js", "test:comments": "node test-pr-comment-generator.js", "test:parser": "node test-claude-parser.js"}, "version": "1.0.0"}