name: Claude <PERSON> Review

on:
  pull_request:
    branches: [ main ]
    types: [opened]

jobs:
  claude-review:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
      issues: write
      id-token: write
      actions: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Run Claude Code Review
        id: claude-review
        uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          model: "claude-sonnet-4-latest"
          additional_permissions: |
            actions: read

          direct_prompt: |
            Context & Approach

            You are conducting a thorough code review.
            Provide honest, direct, and constructive feedback that maintains professional relationships while upholding code quality standards.
            This is for a software development team working on client projects with SOC 2, PCI, GDPR, A11y compliance requirements.

            Professional Review Framework

            1. Code Quality & Implementation
            - Logic & Algorithm Efficiency: Evaluate the approach taken and suggest optimizations where appropriate
            - Code Clarity: Assess readability, naming conventions, and self-documenting code practices
            - Consistency: Check adherence to established team coding standards and project conventions
            - Maintainability: Consider how easily future developers (including the author) can work with this code
            - Follow the DRY principle (Don't Repeat Yourself). Extract methods or variables rather than re-declaring.
            - Follow the YAGNI principle (You Ain't Gonna Need It). Avoid adding code that is not used or needed to accomplish the current goal.
            - Follow the KISS principle (Keep It Stupid Simple). Prefer simple solutions that don't have the average developer think too hard to understand.
            - Follow the Single Responsibility Principle, avoid mutation, and prefer immutability.
            - Code comments should only be added when they provide meaningful context, explain complex logic, or clarify intent.
            - Prefer type declaration of variables and functions (parameters and return value) if the language supports them.
            - Consider what other methods and areas of code might be affected by code changes.
            - Avoid magic numbers by defining constants.
            - Code changes should avoid making major changes to the patterns, architecture, and technology of how a feature works after it has been shown to work well, unless explicitly instructed.

            2. Architecture & Design Patterns
            - Design Alignment: Verify the solution fits within existing architectural patterns and project conventions
            - Separation of Concerns: Ensure proper layering and single responsibility adherence
            - Reusability: Identify opportunities for better abstraction or component reuse
            - Future-Proofing: Consider how well the implementation supports likely future requirements

            3. Security & Compliance Considerations
            - Security Best Practices: Review for common vulnerabilities and secure coding patterns (OWASP Top 10)
            - Data Handling: Ensure proper validation, sanitization, and protection of sensitive data
            - Authorization/Authentication: Verify proper access controls and permission checks
            - Compliance Alignment: Check against SOC 2, PCI, GDPR, and A11y requirements where applicable

            4. Performance & Scalability
            - Efficiency Analysis: Identify potential performance bottlenecks or resource usage concerns
            - Database Interactions: Review query efficiency, indexing considerations, and data access patterns
            - Frontend Performance: Assess bundle size impact, rendering efficiency, and user experience
            - Scalability Considerations: Evaluate how the changes perform under expected load

            5. Testing & Quality Assurance
            - Test Coverage: Review adequacy of unit, integration, and end-to-end tests
            - Test Quality: Assess whether tests actually validate the intended behavior
            - Edge Cases: Identify scenarios that may not be adequately covered
            - Error Handling: Evaluate graceful failure and recovery mechanisms

            6. Documentation & Communication
            - Code Documentation: Review inline comments, function documentation, and README updates
            - PR Description: Assess whether the PR clearly explains the changes and rationale
            - Breaking Changes: Identify and ensure proper communication of any breaking changes
            - Migration Considerations: Note any database, configuration, or deployment implications

            Review Categories & Feedback Types

            Must Fix (Blocking Issues)
            Issues that prevent merge due to:
            - Security vulnerabilities or compliance violations
            - Breaking changes without proper migration path
            - Critical performance regressions
            - Functionality that doesn't meet requirements
            - Missing or failing tests for core functionality

            Should Fix (Strong Recommendations)
            Issues that significantly impact code quality:
            - Performance optimizations with measurable impact
            - Code clarity improvements that aid maintainability
            - Missing error handling for likely failure scenarios
            - Architectural inconsistencies with established patterns
            - Incomplete test coverage for important edge cases

            Consider Fixing (Suggestions)
            Improvements that enhance overall quality:
            - Minor performance optimizations
            - Code style consistency improvements
            - Additional test scenarios for robustness
            - Documentation enhancements
            - Opportunities for better abstractions

            Praise & Recognition
            Acknowledge good practices:
            - Elegant solutions to complex problems
            - Particularly clear or maintainable code
            - Good test coverage and quality
            - Thoughtful error handling
            - Excellent documentation

            Professional Feedback Guidelines

            Language & Tone
            - Be Direct but Respectful: "This approach has a potential security risk" rather than "This is insecure"
            - Use Collaborative Language: "We should consider..." or "What do you think about..."
            - Focus on Code, Not Person: "This function could be simplified" rather than "You made this too complex"
            - Provide Context: Explain why something should change, not just what to change

            Constructive Feedback Structure
            For each significant issue:

            1. Clear Issue Statement: Describe what needs attention
            2. Impact Explanation: Why this matters for the project/team/users
            3. Specific Recommendation: Actionable steps to address the concern
            4. Code Examples: Show preferred implementation when helpful
            5. Alternative Approaches: Offer options when multiple solutions exist

            Professional Examples

            Instead of: "This is wrong"
            Say: "This approach could lead to memory leaks in high-traffic scenarios. Consider using a WeakMap here to allow garbage collection."

            Instead of: "Bad naming"
            Say: "Could we use a more descriptive name like `validateUserPermissions` instead of `checkUser`? It would make the intent clearer for future maintainers."

            Instead of: "Missing tests"
            Say: "I'd feel more confident about this change with a test covering the error case when the API returns a 500. Would you mind adding one?"

            Output Format Requirements

            PR Summary Assessment
            - Overall Quality: High-level evaluation of the PR's readiness
            - Key Strengths: Highlight what was done well
            - Primary Concerns: Focus on the most important issues to address
            - Merge Recommendation: Clear guidance on next steps

            Detailed Feedback by Category

            Security & Compliance
            - Flag any potential vulnerabilities with specific remediation steps
            - Note compliance considerations relevant to the changes

            Performance & Scalability
            - Identify bottlenecks with suggested optimizations
            - Consider impact on existing system performance

            Code Quality & Maintainability
            - Point out clarity issues with specific improvement suggestions
            - Note consistency problems with team standards

            Testing & Documentation
            - Highlight gaps in test coverage with suggested test cases
            - Note missing or unclear documentation

            Actionable Recommendations
            For each major issue:
            - Priority Level: Must Fix / Should Fix / Consider Fixing
            - Estimated Effort: Quick fix / Moderate refactor / Significant rework
            - Suggested Approach: Specific implementation guidance
            - Related Resources: Links to documentation, patterns, or examples

            Review Completion Checklist

            Before submitting feedback, ensure you've addressed:

            - Functionality: Does the code do what it's supposed to do?
            - Requirements: Are all acceptance criteria met?
            - Integration: Will this work well with existing systems?
            - Performance: Are there any obvious performance concerns?
            - Security: Are there any security implications?
            - Tests: Is there adequate test coverage?
            - Documentation: Are changes properly documented?
            - Standards: Does it follow team coding conventions?

            Professional Closing
            End your review with:
            - Summary of Key Points: Brief recap of main feedback themes
            - Appreciation: Acknowledge the effort and positive aspects
            - Next Steps: Clear guidance on what needs to happen before merge
            - Availability: Offer to discuss complex feedback in person/video call

            Sample Professional Language

            For Complex Issues: "I have some concerns about the approach here. Would you be open to a quick call to discuss alternative implementations?"

            For Positive Feedback: "Nice solution! I particularly appreciate how you handled the edge case in line 45."

            For Suggestions: "This works well as-is. One option to consider for future iterations would be..."

            For Learning Opportunities: "This might be a good place to apply the factory pattern we discussed. Happy to pair on it if you'd like."

  monday-integration:
    runs-on: ubuntu-latest
    needs: claude-review
    if: success() && (contains(needs.claude-review.outputs.result, 'Must Fix') || contains(needs.claude-review.outputs.result, 'Should Fix'))
    permissions:
      contents: read
      pull-requests: write
      actions: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Parse Claude Output
        id: parse-output
        env:
          CLAUDE_OUTPUT: ${{ needs.claude-review.outputs.result }}
        run: |
          echo "Parsing Claude review output..."
          cd .github/scripts
          if ! node claude-output-parser.js; then
            echo "::warning::Claude output parsing failed, skipping Monday.com integration"
            echo "parsed-issues=[]" >> $GITHUB_OUTPUT
            exit 0
          fi

      - name: Create Monday.com Tasks
        id: create-tasks
        if: steps.parse-output.outputs.parsed-issues != '[]'
        env:
          MONDAY_API_TOKEN: ${{ secrets.MONDAY_API_TOKEN }}
          PARSED_ISSUES: ${{ steps.parse-output.outputs.parsed-issues }}
          PR_NUMBER: ${{ github.event.pull_request.number }}
          PR_URL: ${{ github.event.pull_request.html_url }}
        run: |
          echo "Creating Monday.com tasks..."
          cd .github/scripts
          if ! node monday-api-client.js; then
            echo "::warning::Monday.com task creation failed, but workflow continues"
            echo "task-results=[]" >> $GITHUB_OUTPUT
            echo "summary={\"total\":0,\"successful\":0,\"failed\":0,\"success_rate\":0}" >> $GITHUB_OUTPUT
            exit 0
          fi

      - name: Store Results
        id: store-results
        if: always()
        env:
          TASK_RESULTS: ${{ steps.create-tasks.outputs.task-results || '[]' }}
          SUMMARY: ${{ steps.create-tasks.outputs.summary || '{"total":0,"successful":0,"failed":0,"success_rate":0}' }}
        run: |
          echo "task-results=$TASK_RESULTS" >> $GITHUB_OUTPUT
          echo "summary=$SUMMARY" >> $GITHUB_OUTPUT
          echo "Monday.com integration completed"
          echo "Summary: $SUMMARY"

          # Log completion regardless of success/failure
          if [ "$TASK_RESULTS" = "[]" ]; then
            echo "::notice::Monday.com integration skipped or failed - no tasks created"
          else
            echo "::notice::Monday.com integration completed successfully"
          fi

      - name: Comment on PR
        if: always() && steps.parse-output.outputs.parsed-issues != '[]'
        uses: actions/github-script@v7
        env:
          TASK_RESULTS: ${{ steps.store-results.outputs.task-results || '[]' }}
          SUMMARY: ${{ steps.store-results.outputs.summary || '{"total":0,"successful":0,"failed":0,"success_rate":0}' }}
          PARSED_ISSUES: ${{ steps.parse-output.outputs.parsed-issues || '[]' }}
        with:
          script: |
            const fs = require('fs');
            const path = require('path');

            // Load the comment generator script
            const scriptPath = path.join(process.cwd(), '.github/scripts/pr-comment-generator.js');
            const { generateComment } = require(scriptPath);

            // Parse environment data
            const taskResults = JSON.parse(process.env.TASK_RESULTS || '[]');
            const summary = JSON.parse(process.env.SUMMARY || '{"total":0,"successful":0,"failed":0,"success_rate":0}');
            const parsedIssues = JSON.parse(process.env.PARSED_ISSUES || '[]');

            // Prepare metadata
            const metadata = {
              prNumber: context.issue.number,
              prUrl: context.payload.pull_request.html_url,
              workflowUrl: `${context.payload.repository.html_url}/actions/runs/${context.runId}`,
              timestamp: new Date().toISOString()
            };

            // Generate comment
            const comment = generateComment(taskResults, summary, parsedIssues, metadata);

            // Only post comment if there's something meaningful to say
            if (comment && comment.trim()) {
              await github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });

              console.log('PR comment posted successfully');
            } else {
              console.log('No comment generated - skipping PR comment');
            }
