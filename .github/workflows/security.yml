name: "Security Scanning"

on:
  push:
    branches: [ "main", "develop" ]
  pull_request:
    branches: [ "main", "develop" ]
  schedule:
    - cron: '30 2 * * 1'

jobs:
  dependency-check:
    name: Dependency Vulnerability Check
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'

    - name: Install safety
      run: pip install safety bandit

    - name: Run safety check
      run: safety check -r backend/requirements.txt --json --output safety-report.json || true

    - name: Run Bandit
      run: bandit -r backend/apps/ backend/config/ backend/manage.py --json --output bandit-report.json || true

    - name: Upload safety report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: safety-report
        path: safety-report.json

    - name: Upload Bandit Report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: bandit-report
        path: bandit-report.json

  secrets-scan:
    name: Secrets Scanning
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Run TruffleHog OSS
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD
        extra_args: --debug --only-verified
