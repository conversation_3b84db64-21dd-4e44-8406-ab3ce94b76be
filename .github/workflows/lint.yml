name: "PythonCode Linter"

permissions:
  contents: read

on:
  push:
    branches: [ "main", "develop" ]
  pull_request:
    branches: [ "main", "develop" ]

env:
  PYTHON_VERSION: "3.12"

jobs:
  lint:
    name: Code Quality Checks
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r backend/requirements.txt -r backend/requirements-dev.txt

    - name: Validate configuration files
      run: |
        python -c "import tomllib; tomllib.load(open('backend/pyproject.toml', 'rb'))"
        echo "✅ pyproject.toml is valid"

        flake8 --version
        echo "✅ flake8 configuration is valid"

        black --version
        echo "✅ black configuration is valid"

    - name: <PERSON> <PERSON> (Code Formatting Check)
      run: |
        cd backend && black --check --diff apps/ config/ manage.py
      continue-on-error: false

    - name: Run isort (Import Sorting Check)
      run: |
        cd backend && isort --check-only --diff apps/ config/ manage.py
      continue-on-error: false

    - name: Run Flake8 (Style & Complexity)
      run: |
        cd backend && flake8 apps/ config/ manage.py
      continue-on-error: false

    - name: Run Pylint (Static Analysis)
      run: |
        cd backend && pylint apps/ config/ manage.py || true
      continue-on-error: true

    - name: Run MyPy (Type Checking)
      run: |
        cd backend && mypy apps/ config/ manage.py || true
      continue-on-error: true
