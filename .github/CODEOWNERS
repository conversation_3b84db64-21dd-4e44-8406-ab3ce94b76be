# Global Owners - Default reviewers for all files
* @integritystl/python-devs

# Documentation
*.md @dankhs21 @mkmorgan1 @alexrodriguezintegrityxd
/docs/ @dankhs21 @mkmorgan1 @alexrodriguezintegrityxd

# Configuration & Infrastructure
/config/ @dankhs21 @alexrodriguezintegrityxd
/docker-compose*.yml @dankhs21 @alexrodriguezintegrityxd
/Dockerfile* @dankhs21 @alexrodriguezintegrityxd
/scripts/ @dankhs21 @alexrodriguezintegrityxd
/.github/ @dankhs21 @alexrodriguezintegrityxd

# Python Dependencies & Project Configuration
/requirements*.txt @dankhs21 @alexrodriguezintegrityxd
/pyproject.toml @dankhs21 @alexrodriguezintegrityxd
/manage.py @dankhs21 @alexrodriguezintegrityxd

# Django Apps - Backend Team
/apps/api/ @dankhs21 @alexrodriguezintegrityxd
/apps/customer/ @dankhs21 @alexrodriguezintegrityxd
/apps/member/ @dankhs21 @alexrodriguezintegrityxd

# Frontend/Theme - Frontend Team
/apps/theme/ @mkmorgan1 @alexrodriguezintegrityxd
/static/ @mkmorgan1 @alexrodriguezintegrityxd
/templates/ @mkmorgan1 @alexrodriguezintegrityxd
*.css @mkmorgan1 @alexrodriguezintegrityxd
*.js @mkmorgan1 @alexrodriguezintegrityxd
*.html @mkmorgan1 @alexrodriguezintegrityxd
package.json @mkmorgan1 @alexrodriguezintegrityxd
tailwind.config.js @mkmorgan1 @alexrodriguezintegrityxd

# Database & Migrations - Database Team
*/migrations/ @dankhs21 @mkmorgan1 @alexrodriguezintegrityxd
/data/ @dankhs21 @mkmorgan1 @alexrodriguezintegrityxd

# Security & Authentication
/apps/member/clerk_auth.py @dankhs21 @alexrodriguezintegrityxd
*/middleware.py @dankhs21 @alexrodriguezintegrityxd

# Testing
*/tests/ @dankhs21 @alexrodriguezintegrityxd
*/tests.py @dankhs21 @alexrodriguezintegrityxd
/static/run_tests.js @dankhs21 @alexrodriguezintegrityxd

# Architecture & Planning Documents
/docs/architecture/ @dankhs21 @mkmorgan1 @alexrodriguezintegrityxd
/docs/prd/ @dankhs21 @mkmorgan1 @alexrodriguezintegrityxd
/docs/scope/ @dankhs21 @mkmorgan1 @alexrodriguezintegrityxd
