version: 2
updates:
  # Python dependencies - Combined configuration
  - package-ecosystem: 'pip'
    directory: '/backend'
    schedule:
      interval: 'weekly'
      day: 'monday'
      time: '08:00'
      timezone: 'America/Chicago'
    target-branch: "develop"
    labels:
      - "automated"
      - "dependencies"
    versioning-strategy: 'increase-if-necessary'
    groups:
      security-updates:
        patterns:
          - "*"
        dependency-type: "production"
        update-types: ["patch", "minor"]
      major-updates:
        update-types: ['major']
      minor-patch-updates:
        update-types: ['minor', 'patch']
        exclude-patterns:
          - "*"
    allow:
      - dependency-type: "all"
    commit-message:
      prefix: "chore(deps)"
      prefix-development: "chore(deps-dev)"
      include: "scope"
    reviewers: ["@integritystl/python-devs"]
    open-pull-requests-limit: 10
  - package-ecosystem: 'docker'
    directory: '/'
    schedule:
      interval: 'weekly'
      day: 'monday'
      time: '08:00'
      timezone: 'America/Chicago'
    target-branch: "develop"
    labels:
      - "automated"
      - "dependencies"
      - "docker"
    groups:
      major-updates:
        update-types: ['major']
      minor-patch-updates:
        update-types: ['minor', 'patch']
    commit-message:
      prefix: "chore(docker)"
      include: "scope"
    reviewers: ["@integritystl/python-devs"]

  # GitHub Actions
  - package-ecosystem: 'github-actions'
    directory: '/'
    schedule:
      interval: 'monthly'
      day: 'monday'
      time: '08:00'
      timezone: 'America/Chicago'
    target-branch: "develop"
    labels:
      - "automated"
      - "dependencies"
      - "actions"
    commit-message:
      prefix: "chore(actions)"
      include: "scope"
    reviewers: ["@integritystl/python-devs"]
