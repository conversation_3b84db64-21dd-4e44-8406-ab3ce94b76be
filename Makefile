# XD Incentives Development Makefile
# Comprehensive commands for database seeding and development workflow

# Docker container name configuration
# Override these with: make TARGET BACKEND_CONTAINER=my-backend-container
BACKEND_CONTAINER ?= xd-backend
DB_CONTAINER ?= xd-mysql
REDIS_CONTAINER ?= xd-redis
SEED_CONTAINER ?= xd-seed
FRONTEND_CONTAINER ?= xd-frontend
NGINX_CONTAINER ?= xd-nginx
CELERY_CONTAINER ?= xd-celery
CELERY_BEAT_CONTAINER ?= xd-celery-beat
FLOWER_CONTAINER ?= xd-flower

.PHONY: help build up down logs clean reset
.PHONY: seed-full seed-fresh seed-minimal seed-test seed-interactive
.PHONY: backup-db restore-db reset-db list-dumps
.PHONY: django-shell django-migrate django-test django-superuser
.PHONY: dev-up dev-down dev-logs dev-build
.PHONY: lint lint-check format lint-all pre-commit-install pre-commit-run

# Default target
help: ## Show this help message
	@echo "XD Incentives Development Commands"
	@echo "=================================="
	@echo ""
	@echo "🏗️  Development Services:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep "Development" | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "🎨 Frontend Services:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep "Frontend" | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[95m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "⚡ Celery & Task Management:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep "Celery\|Flower" | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[91m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "🌱 Database Seeding:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep "Seed\|Database\|Backup" | grep -v "Celery" | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[32m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "🐍 Django Commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep "Django" | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[33m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "🎨 Code Quality:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep "Lint\|Format\|Quality" | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[34m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "🔧 Utility Commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep -v "Development\|Frontend\|Celery\|Flower\|Seed\|Database\|Backup\|Django\|Lint\|Format\|Quality" | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[35m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "📦 Docker Container Configuration:"
	@echo "  Override container names with environment variables:"
	@echo "  BACKEND_CONTAINER=$(BACKEND_CONTAINER) (default)"
	@echo "  DB_CONTAINER=$(DB_CONTAINER) (default)"
	@echo "  REDIS_CONTAINER=$(REDIS_CONTAINER) (default)"
	@echo "  FRONTEND_CONTAINER=$(FRONTEND_CONTAINER) (default)"
	@echo "  CELERY_CONTAINER=$(CELERY_CONTAINER) (default)"
	@echo ""
	@echo "  Example: make django-shell BACKEND_CONTAINER=my-custom-backend"

# =============================================================================
# Development Services
# =============================================================================

dev-build: ## Development: Build all services
	@echo "🏗️ Building development services..."
	docker-compose build

dev-up: ## Development: Start all services in background
	@echo "🚀 Starting development services..."
	docker-compose up -d --build

dev-down: ## Development: Stop all services
	@echo "🛑 Stopping development services..."
	docker-compose down

dev-logs: ## Development: Show logs for all services
	@echo "📋 Showing development logs..."
	docker-compose logs -f

frontend-build: ## Frontend: Build frontend service
	@echo "🎨 Building frontend service..."
	docker-compose build frontend

frontend-up: ## Frontend: Start frontend service with hot reloading
	@echo "🚀 Starting frontend service..."
	docker-compose up -d frontend

frontend-down: ## Frontend: Stop frontend service
	@echo "🛑 Stopping frontend service..."
	docker-compose stop frontend

frontend-logs: ## Frontend: Show frontend service logs
	@echo "📋 Showing frontend logs..."
	docker-compose logs -f frontend

frontend-shell: ## Frontend: Open shell in frontend container
	@echo "🐚 Opening frontend shell..."
	docker exec -it $(FRONTEND_CONTAINER) sh

frontend-install: ## Frontend: Install npm dependencies
	@echo "📦 Installing frontend dependencies..."
	docker exec $(FRONTEND_CONTAINER) npm install

frontend-test: ## Frontend: Run frontend tests
	@echo "🧪 Running frontend tests..."
	docker exec $(FRONTEND_CONTAINER) npm test

frontend-lint: ## Frontend: Run frontend linting
	@echo "🔍 Running frontend linting..."
	docker exec $(FRONTEND_CONTAINER) npm run lint

frontend-build-prod: ## Frontend: Build frontend for production
	@echo "📦 Building frontend for production..."
	docker exec $(FRONTEND_CONTAINER) npm run build

build: dev-build ## Alias for dev-build

up: dev-up ## Alias for dev-up

down: dev-down ## Alias for dev-down

logs: dev-logs ## Alias for dev-logs

# =============================================================================
# Celery & Task Management
# =============================================================================

celery-up: ## Celery: Start Celery worker
	@echo "🔄 Starting Celery worker..."
	docker-compose up -d celery

celery-down: ## Celery: Stop Celery worker
	@echo "🛑 Stopping Celery worker..."
	docker-compose stop celery

celery-logs: ## Celery: Show Celery worker logs
	@echo "📋 Showing Celery worker logs..."
	docker-compose logs -f celery

celery-restart: ## Celery: Restart Celery worker
	@echo "🔄 Restarting Celery worker..."
	docker-compose restart celery

celery-beat-up: ## Celery Beat: Start scheduled task scheduler
	@echo "⏰ Starting Celery Beat scheduler..."
	docker-compose --profile celery-beat up -d celery-beat

celery-beat-down: ## Celery Beat: Stop scheduled task scheduler
	@echo "🛑 Stopping Celery Beat scheduler..."
	docker-compose --profile celery-beat stop celery-beat

celery-beat-logs: ## Celery Beat: Show scheduler logs
	@echo "📋 Showing Celery Beat logs..."
	docker-compose --profile celery-beat logs -f celery-beat

celery-beat-restart: ## Celery Beat: Restart scheduler
	@echo "🔄 Restarting Celery Beat..."
	docker-compose --profile celery-beat restart celery-beat

flower-up: ## Flower: Start Celery monitoring dashboard (port 5555)
	@echo "🌸 Starting Flower monitoring dashboard..."
	@docker-compose --profile monitoring up -d flower
	@echo "🌐 Flower dashboard available at: http://localhost:5555"

flower-down: ## Flower: Stop monitoring dashboard
	@echo "🛑 Stopping Flower monitoring..."
	docker-compose --profile monitoring stop flower

flower-logs: ## Flower: Show monitoring dashboard logs
	@echo "📋 Showing Flower logs..."
	docker-compose --profile monitoring logs -f flower

flower-restart: ## Flower: Restart monitoring dashboard
	@echo "🔄 Restarting Flower..."
	docker-compose --profile monitoring restart flower

celery-status: ## Celery: Check status of all Celery components
	@echo "📊 Celery Components Status:"
	@echo "=========================="
	@echo ""
	@echo "Celery Worker:"
	@docker-compose ps celery 2>/dev/null || echo "  Not running"
	@echo ""
	@echo "Celery Beat:"
	@docker-compose --profile celery-beat ps celery-beat 2>/dev/null || echo "  Not running"
	@echo ""
	@echo "Flower Monitor:"
	@docker-compose --profile monitoring ps flower 2>/dev/null || echo "  Not running"
	@echo ""
	@echo "Redis (Message Broker):"
	@docker-compose ps redis 2>/dev/null || echo "  Not running"

celery-purge: ## Celery: Purge all pending tasks from queue
	@echo "⚠️  WARNING: This will delete all pending tasks!"
	@read -p "Are you sure? Type 'yes' to continue: " confirm; \
	if [ "$$confirm" = "yes" ]; then \
		echo "🗑️ Purging all pending tasks..."; \
		docker exec $(BACKEND_CONTAINER) python manage.py shell -c "from celery import current_app; current_app.control.purge()"; \
		echo "✅ All pending tasks purged"; \
	else \
		echo "❌ Operation cancelled"; \
	fi

celery-inspect: ## Celery: Inspect active tasks and workers
	@echo "🔍 Inspecting Celery workers and tasks..."
	@docker exec $(BACKEND_CONTAINER) celery -A config inspect active

celery-stats: ## Celery: Show worker statistics
	@echo "📊 Celery worker statistics..."
	@docker exec $(BACKEND_CONTAINER) celery -A config inspect stats

# =============================================================================
# Database Seeding Commands
# =============================================================================

seed-full: ## Database: Seed with full database dump + sample data
	@echo "🌱 Seeding database with full dump and sample data..."
	cd data && ../scripts/seed_database.sh --full --backup --sample-data

seed-fresh: ## Database: Fresh install - reset DB completely and seed
	@echo "🔄 Fresh database installation..."
	cd data && ../scripts/seed_database.sh --fresh --backup --sample-data

seed-minimal: ## Seed: Minimal schema only + sample data
	@echo "🌿 Seeding database with minimal schema..."
	cd data && ../scripts/seed_database.sh --minimal --sample-data

seed-test: ## Seed: Test data set for development
	@echo "🧪 Seeding database with test data..."
	cd data && ../scripts/seed_database.sh --test --sample-data

seed-interactive: ## Seed: Interactive seeding environment
	@echo "🔧 Starting interactive seeding environment..."
	docker-compose -f docker-compose.seed.yml up -d seed-interactive
	@echo "Access the container with: docker exec -it $(SEED_CONTAINER) sh"

seed-docker-full: ## Database: Seed using Docker Compose (full)
	@echo "🐳 Seeding database using Docker Compose (full)..."
	docker-compose -f docker-compose.seed.yml up --build seed-full

seed-docker-fresh: ## Database: Fresh seed using Docker Compose
	@echo "🐳 Fresh seeding using Docker Compose..."
	docker-compose -f docker-compose.seed.yml up --build seed-fresh

# =============================================================================
# Database Management
# =============================================================================

backup-db: ## Backup: Create database backup with timestamp
	@echo "💾 Creating database backup..."
	cd data && ../scripts/dump_database.sh

backup-docker: ## Backup: Create backup using Docker Compose
	@echo "🐳 Creating backup using Docker Compose..."
	docker-compose -f docker-compose.seed.yml up --build backup-db

restore-db: ## Database: Interactive database restore (prompts for file)
	@echo "🔄 Available database dumps:"
	@ls -la data/*.sql.gz 2>/dev/null || echo "No dumps found"
	@echo ""
	@read -p "Enter dump filename (without path): " dumpfile; \
	if [ -f "data/$$dumpfile" ]; then \
		echo "Restoring from $$dumpfile..."; \
		gunzip -c "data/$$dumpfile" | docker exec -i $(DB_CONTAINER) mysql -u testu -ptestpw xd_incentives_db; \
		echo "✅ Restore completed"; \
	else \
		echo "❌ File not found: $$dumpfile"; \
	fi

reset-db: ## Database: Reset database (drop/create) - DESTRUCTIVE!
	@echo "⚠️  WARNING: This will completely reset the database!"
	@read -p "Are you sure? Type 'yes' to continue: " confirm; \
	if [ "$$confirm" = "yes" ]; then \
		echo "🔄 Resetting database..."; \
		docker exec $(DB_CONTAINER) mysql -u root -prootpw -e "DROP DATABASE IF EXISTS xd_incentives_db; CREATE DATABASE xd_incentives_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; GRANT ALL PRIVILEGES ON xd_incentives_db.* TO 'testu'@'%'; FLUSH PRIVILEGES;"; \
		echo "✅ Database reset completed"; \
	else \
		echo "❌ Operation cancelled"; \
	fi

list-dumps: ## Database: List all available database dumps
	@echo "📋 Available database dumps:"
	@cd data && ../scripts/seed_database.sh --list-dumps

# =============================================================================
# Django Management Commands
# =============================================================================

django-shell: ## Django: Open Django shell
	@echo "🐍 Opening Django shell..."
	docker exec -it $(BACKEND_CONTAINER) python manage.py shell

django-migrate: ## Django: Run database migrations
	@echo "🗄️ Running Django migrations..."
	docker exec $(BACKEND_CONTAINER) python manage.py migrate

django-makemigrations: ## Django: Create new migrations
	@echo "📝 Creating new migrations..."
	docker exec $(BACKEND_CONTAINER) python manage.py makemigrations

django-test: ## Django: Run all tests
	@echo "🧪 Running Django tests..."
	docker exec $(BACKEND_CONTAINER) python manage.py test

django-test-app: ## Django: Run tests for specific app (usage: make django-test-app APP=member)
	@echo "🧪 Running tests for app: $(APP)"
	docker exec $(BACKEND_CONTAINER) python manage.py test apps.$(APP)

django-superuser: ## Django: Create Django superuser
	@echo "👤 Creating Django superuser..."
	docker exec -it $(BACKEND_CONTAINER) python manage.py createsuperuser

django-collectstatic: ## Django: Collect static files
	@echo "📦 Collecting static files..."
	docker exec $(BACKEND_CONTAINER) python manage.py collectstatic --noinput

django-sample-data: ## Django: Create sample data using management commands
	@echo "🎭 Creating sample data..."
	docker exec $(BACKEND_CONTAINER) python manage.py setup_member_types
	docker exec $(BACKEND_CONTAINER) python manage.py create_sample_data
	docker exec $(BACKEND_CONTAINER) python manage.py setup_member_hierarchy
	docker exec $(BACKEND_CONTAINER) python manage.py setup_teams_multi
	docker exec $(BACKEND_CONTAINER) python manage.py create_sample_terms
	docker exec $(BACKEND_CONTAINER) python manage.py create_sample_privacy
	docker exec $(BACKEND_CONTAINER) python manage.py create_sample_communications
	docker exec $(BACKEND_CONTAINER) python manage.py create_sample_password_resets

# =============================================================================
# Utility Commands
# =============================================================================

clean: ## Clean up Docker containers and volumes
	@echo "🧹 Cleaning up Docker resources..."
	docker-compose down -v
	docker-compose -f docker-compose.seed.yml down -v
	docker system prune -f

clean-all: ## Clean up everything including images
	@echo "🧹 Cleaning up all Docker resources..."
	docker-compose down -v
	docker-compose -f docker-compose.seed.yml down -v
	docker system prune -af

status: ## Show status of all services
	@echo "📊 Service Status:"
	@echo "=================="
	@echo ""
	@echo "Main Services:"
	@docker-compose ps
	@echo ""
	@echo "Seed Services:"
	@docker-compose -f docker-compose.seed.yml ps

mysql-cli: ## Open MySQL command line interface
	@echo "🗄️ Opening MySQL CLI..."
	docker exec -it $(DB_CONTAINER) mysql -u testu -ptestpw xd_incentives_db

redis-cli: ## Open Redis command line interface
	@echo "📦 Opening Redis CLI..."
	docker exec -it $(REDIS_CONTAINER) redis-cli

# =============================================================================
# Task Management Workflows
# =============================================================================

tasks-up: ## Tasks: Start all task management services (Celery + Beat + Flower)
	@echo "🚀 Starting all task management services..."
	@make celery-up
	@make celery-beat-up
	@make flower-up
	@echo ""
	@echo "✅ All task services started!"
	@echo "📊 Monitor at: http://localhost:5555"

tasks-down: ## Tasks: Stop all task management services
	@echo "🛑 Stopping all task management services..."
	@make flower-down
	@make celery-beat-down
	@make celery-down
	@echo "✅ All task services stopped"

tasks-status: ## Tasks: Show status of all task services
	@make celery-status

monitoring-up: ## Monitoring: Start all monitoring tools (Flower)
	@make flower-up

monitoring-down: ## Monitoring: Stop all monitoring tools
	@make flower-down

# =============================================================================
# Quick Start Commands
# =============================================================================

quick-start: ## Quick start: Build, start services, and seed with full data
	@echo "🚀 Quick Start: Building and seeding database..."
	make dev-build
	make dev-up
	@echo "⏳ Waiting for services to be ready..."
	sleep 15
	make seed-full
	@echo ""
	@echo "🎉 Quick start completed!"
	@echo "🌐 Backend API: http://localhost:8000"
	@echo "🎨 Frontend App: http://localhost:3000"
	@echo "🔑 Admin login: admin / admin123"

fresh-start: ## Fresh start: Complete reset and fresh installation
	@echo "🔄 Fresh Start: Complete reset..."
	make clean
	make dev-build
	make dev-up
	@echo "⏳ Waiting for services to be ready..."
	sleep 15
	make seed-fresh
	@echo ""
	@echo "🎉 Fresh start completed!"
	@echo "🌐 Backend API: http://localhost:8000"
	@echo "🎨 Frontend App: http://localhost:3000"
	@echo "🔑 Admin login: admin / admin123"

# =============================================================================
# Development Workflows
# =============================================================================

dev-reset: ## Complete environment reset, rebuild, import DB, run tests
	@echo "🤖 Complete Development Environment Reset"
	@echo "======================================================"
	@echo ""
	@echo "⚠️  WARNING: This will completely reset your development environment!"
	@echo "- Stop all containers"
	@echo "- Remove all volumes and containers"
	@echo "- Rebuild all images from scratch"
	@echo "- Import latest database or create fresh"
	@echo "- Run comprehensive tests"
	@echo ""
	@read -p "Continue? Type 'yes' to proceed: " confirm; \
	if [ "$$confirm" != "yes" ]; then \
		echo "❌ Reset cancelled"; \
		exit 1; \
	fi
	@echo ""
	@echo "🛑 Step 1/8: Stopping all services..."
	make dev-down || true
	make tasks-down || true
	make monitoring-down || true
	@echo ""
	@echo "🧹 Step 2/8: Cleaning up Docker resources..."
	make clean-all
	@echo ""
	@echo "🏗️ Step 3/8: Rebuilding all images from scratch..."
	docker-compose build --no-cache --pull
	@echo ""
	@echo "🚀 Step 4/8: Starting services..."
	make dev-up
	@echo ""
	@echo "⏳ Step 5/8: Waiting for services to be ready..."
	@echo "Waiting 30 seconds for database initialization..."
	sleep 30
	make health-check
	@echo ""
	@echo "🗄️ Step 6/8: Setting up database..."
	@if [ -f "data/latest.sql.gz" ] || [ -f "data/xd_incentives_production.sql.gz" ]; then \
		echo "📥 Found database dump, importing..."; \
		make seed-fresh; \
	else \
		echo "🏗️ No dumps found, creating fresh database..."; \
		make django-migrate; \
		make django-sample-data; \
	fi
	@echo ""
	@echo "🧪 Step 7/8: Running comprehensive tests..."
	make django-test || (echo "❌ Tests failed, but continuing..."; true)
	make frontend-test || (echo "❌ Frontend tests failed, but continuing..."; true)
	@echo ""
	@echo "🏥 Step 8/8: Final health check..."
	make health-check
	@echo ""
	@echo "✅ Development Environment Reset Complete!"
	@echo "========================================"
	@echo "🌐 Backend API: http://localhost:8000"
	@echo "🎨 Frontend App: http://localhost:3000"
	@echo "🔑 Default login: admin / admin123"
	@echo "📊 Service status:"
	@make status

test-workflow: ## Test the complete seeding workflow
	@echo "🧪 Testing complete seeding workflow..."
	@echo "1. Testing backup creation..."
	make backup-db
	@echo "2. Testing minimal seed..."
	make seed-minimal
	@echo "3. Testing full seed..."
	make seed-full
	@echo "✅ Workflow test completed"

# =============================================================================
# Monitoring and Debugging
# =============================================================================

watch-logs: ## Watch logs from all services
	@echo "👀 Watching logs from all services..."
	docker-compose logs -f

watch-backend: ## Watch logs from backend service only
	@echo "👀 Watching backend service logs..."
	docker-compose logs -f backend

watch-db: ## Watch logs from database service only
	@echo "👀 Watching database service logs..."
	docker-compose logs -f db

watch-frontend: ## Watch logs from frontend service only
	@echo "👀 Watching frontend service logs..."
	docker-compose logs -f frontend

health-check: ## Check health of all services
	@echo "🏥 Health Check:"
	@echo "==============="
	@echo ""
	@echo "Docker Services:"
	@docker-compose ps
	@echo ""
	@echo "Backend Service Health:"
	@curl -s -o /dev/null -w "HTTP Status: %{http_code}\n" http://localhost:8000 || echo "Backend service not responding"
	@echo ""
	@echo "Frontend Service Health:"
	@curl -s -o /dev/null -w "HTTP Status: %{http_code}\n" http://localhost:3000 || echo "Frontend service not responding"
	@echo ""
	@echo "Database Connection:"
	@docker exec $(DB_CONTAINER) mysqladmin ping -h localhost --silent && echo "✅ Database: OK" || echo "❌ Database: Failed"
	@echo ""
	@echo "Redis Connection:"
	@docker exec $(REDIS_CONTAINER) redis-cli ping && echo "✅ Redis: OK" || echo "❌ Redis: Failed"

# =============================================================================
# Code Quality & Linting Commands
# =============================================================================

format: ## Format: Run all code formatters (black + isort)
	@echo "🎨 Running code formatters..."
	docker exec $(BACKEND_CONTAINER) black apps/ config/ manage.py
	docker exec $(BACKEND_CONTAINER) isort apps/ config/ manage.py
	@echo "✅ Code formatting completed"

lint-check: ## Lint: Check code style without making changes
	@echo "🔍 Checking code style..."
	docker exec $(BACKEND_CONTAINER) black --check --diff apps/ config/ manage.py
	docker exec $(BACKEND_CONTAINER) isort --check-only --diff apps/ config/ manage.py
	docker exec $(BACKEND_CONTAINER) flake8 apps/ config/ manage.py

lint: ## Lint: Run all linters (flake8, pylint, mypy, bandit)
	@echo "🔍 Running comprehensive linting..."
	@echo "📋 Flake8 (Style & Complexity):"
	docker exec $(BACKEND_CONTAINER) flake8 apps/ config/ manage.py
	@echo ""
	@echo "🔧 Pylint (Static Analysis):"
	docker exec $(BACKEND_CONTAINER) pylint apps/ config/ manage.py || true
	@echo ""
	@echo "🔍 MyPy (Type Checking):"
	docker exec $(BACKEND_CONTAINER) mypy apps/ config/ manage.py || true
	@echo ""
	@echo "🛡️ Bandit (Security Scan):"
	docker exec $(BACKEND_CONTAINER) bandit -r apps/ config/ manage.py || true

lint-fix: ## Lint: Run formatters and fix auto-fixable issues
	@echo "🔧 Running linters and fixing issues..."
	docker exec $(BACKEND_CONTAINER) black apps/ config/ manage.py
	docker exec $(BACKEND_CONTAINER) isort apps/ config/ manage.py
	docker exec $(BACKEND_CONTAINER) django-upgrade --target-version 5.2 apps/ config/ manage.py
	@echo "✅ Auto-fixable issues resolved"

lint-security: ## Quality: Run security-focused scans
	@echo "🛡️ Running security scans..."
	docker exec $(BACKEND_CONTAINER) bandit -r apps/ config/ manage.py -f json -o bandit-report.json
	docker exec $(BACKEND_CONTAINER) safety check -r requirements.txt
	@echo "✅ Security scan completed"

lint-all: ## Quality: Run complete code quality suite
	@echo "🎯 Running complete code quality suite..."
	$(MAKE) format
	$(MAKE) lint
	$(MAKE) lint-security
	docker exec $(BACKEND_CONTAINER) python manage.py check --deploy
	@echo "✅ Complete code quality check finished"

pre-commit-install: ## Quality: Install pre-commit hooks
	@echo "🪝 Installing pre-commit hooks..."
	pre-commit install
	@echo "✅ Pre-commit hooks installed"

pre-commit-run: ## Quality: Run pre-commit on all files
	@echo "🪝 Running pre-commit on all files..."
	docker exec $(BACKEND_CONTAINER) pre-commit run --all-files

django-check: ## Django: Run Django system check
	@echo "✅ Running Django system check..."
	docker exec $(BACKEND_CONTAINER) python manage.py check --deploy
