# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# Virtual environments
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Logs
logs/
*.log

# Environment files
.env
.env.local
.env.sample

# Testing
.coverage
.pytest_cache/
.tox/
htmlcov/

# Documentation
*.md
docs/

# Database dumps
databases/
data/
*.sql
*.sql.gz
*.dump

# Node modules (if any frontend build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Scripts (development only)
scripts/local/
local/

# Backup files
*.bak
*.backup

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Additional documentation
README*
CHANGELOG*
LICENSE*

# MacOS
.DS_Store
