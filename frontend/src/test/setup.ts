import '@testing-library/jest-dom'
import { cleanup } from '@testing-library/react'
import { afterEach, beforeAll, vi } from 'vitest'

// Make vi globally available
global.vi = vi

// Set up environment variables for tests
beforeAll(() => {
  // Ensure environment variables are available
  if (!import.meta.env.VITE_API_URL) {
    import.meta.env.VITE_API_URL = 'http://localhost:8000'
  }
})

afterEach(() => {
  cleanup()
})
