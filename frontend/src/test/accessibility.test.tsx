import { render } from '@testing-library/react'
import { axe, toHaveNoViolations } from 'jest-axe'
import { vi } from 'vitest'

import { Button } from '@components/ui/Button'
import { Form, FormField } from '@components/ui/Form'
import { Input } from '@components/ui/Input'
import { SkipLinks } from '@components/ui/SkipLink'

// Extend Jest matchers
expect.extend(toHaveNoViolations)

describe('Accessibility Tests', () => {
  describe('Button Component', () => {
    test('should not have accessibility violations', async () => {
      const { container } = render(
        <Button>Click me</Button>
      )
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    test('should be keyboard navigable', async () => {
      const { getByRole } = render(
        <Button>Click me</Button>
      )
      const button = getByRole('button')
      expect(button).toBeInTheDocument()
      expect(button).not.toHaveAttribute('tabIndex', '-1')
    })

    test('should have proper focus styles', async () => {
      const { container } = render(
        <Button>Click me</Button>
      )
      // Check if focus styles are defined in CSS
      const button = container.querySelector('button')
      expect(button).toHaveClass('focus-visible:ring-2')
    })
  })

  describe('Form Components', () => {
    test('FormField should not have accessibility violations', async () => {
      const { container } = render(
        <FormField
          label="Email"
          id="email"
          required
          helpText="Enter your email address"
        >
          <Input type="email" />
        </FormField>
      )
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    test('FormField should have proper ARIA attributes', () => {
      const { getByLabelText, getByRole } = render(
        <FormField
          label="Email"
          id="email"
          required
          error="Invalid email"
          helpText="Enter your email address"
        >
          <Input type="email" />
        </FormField>
      )

      const input = getByLabelText(/email/i)
      expect(input).toHaveAttribute('aria-required', 'true')
      expect(input).toHaveAttribute('aria-invalid', 'true')
      expect(input).toHaveAttribute('aria-describedby')

      // Check for error message
      const errorMessage = getByRole('alert')
      expect(errorMessage).toBeInTheDocument()
    })

    test('Form should be keyboard navigable', () => {
      const mockSubmit = vi.fn()
      const { getByLabelText, getByRole } = render(
        <Form onSubmit={mockSubmit}>
          <FormField label="First Name" id="firstName">
            <Input type="text" />
          </FormField>
          <FormField label="Last Name" id="lastName">
            <Input type="text" />
          </FormField>
          <Button type="submit">Submit</Button>
        </Form>
      )

      const firstInput = getByLabelText(/first name/i)
      const lastInput = getByLabelText(/last name/i)
      const submitButton = getByRole('button', { name: /submit/i })

      // All should be keyboard accessible
      expect(firstInput).not.toHaveAttribute('tabIndex', '-1')
      expect(lastInput).not.toHaveAttribute('tabIndex', '-1')
      expect(submitButton).not.toHaveAttribute('tabIndex', '-1')
    })
  })

  describe('Skip Links', () => {
    test('should not have accessibility violations', async () => {
      const { container } = render(
        <SkipLinks
          links={[
            { href: '#main', label: 'Skip to main content' },
            { href: '#nav', label: 'Skip to navigation' },
          ]}
        />
      )
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    test('should have proper navigation structure', () => {
      const { getByRole, getByText } = render(
        <SkipLinks
          links={[
            { href: '#main', label: 'Skip to main content' },
            { href: '#nav', label: 'Skip to navigation' },
          ]}
        />
      )

      const navigation = getByRole('navigation', { name: /skip navigation/i })
      expect(navigation).toBeInTheDocument()

      const skipLinks = getByText(/skip to main content/i)
      expect(skipLinks).toHaveAttribute('href', '#main')
    })
  })

  describe('Color Contrast', () => {
    test('should meet WCAG AA contrast requirements', async () => {
      // This test would ideally use a color contrast checker
      // For now, we'll test that our design system colors meet requirements
      const { container } = render(
        <div>
          <Button variant="default">Default Button</Button>
          <Button variant="destructive">Destructive Button</Button>
          <Button variant="outline">Outline Button</Button>
        </div>
      )

      const results = await axe(container, {
        rules: {
          'color-contrast': { enabled: true }
        }
      })
      expect(results).toHaveNoViolations()
    })
  })

  describe('Keyboard Navigation', () => {
    test('should support arrow key navigation in lists', () => {
      // This is more of an integration test that would need user interaction
      // For unit tests, we mainly check that elements are focusable
      const { getAllByRole } = render(
        <nav role="menubar">
          <a href="#" role="menuitem">Link 1</a>
          <a href="#" role="menuitem">Link 2</a>
          <a href="#" role="menuitem">Link 3</a>
        </nav>
      )

      const links = getAllByRole('menuitem')
      links.forEach(link => {
        expect(link).not.toHaveAttribute('tabIndex', '-1')
      })
    })
  })

  describe('Screen Reader Support', () => {
    test('should have proper heading hierarchy', () => {
      const { getByRole } = render(
        <div>
          <h1>Main Title</h1>
          <h2>Section Title</h2>
          <h3>Subsection Title</h3>
        </div>
      )

      expect(getByRole('heading', { level: 1 })).toBeInTheDocument()
      expect(getByRole('heading', { level: 2 })).toBeInTheDocument()
      expect(getByRole('heading', { level: 3 })).toBeInTheDocument()
    })

    test('should have proper landmark regions', () => {
      const { getByRole } = render(
        <div>
          <nav role="navigation">Navigation</nav>
          <main role="main">Main content</main>
          <aside role="complementary">Sidebar</aside>
        </div>
      )

      expect(getByRole('navigation')).toBeInTheDocument()
      expect(getByRole('main')).toBeInTheDocument()
      expect(getByRole('complementary')).toBeInTheDocument()
    })
  })
})
