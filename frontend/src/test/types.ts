// Common type definitions for tests

// Clerk User interface - matching their actual API
export interface MockClerkUser {
  id: string;
  firstName?: string | null;
  lastName?: string | null;
  emailAddresses: Array<{ emailAddress: string }>;
  phoneNumbers?: Array<{ phoneNumber: string }>;
  imageUrl?: string;
  updatedAt?: string;
  createdAt?: string;
}

// Clerk useUser hook return type
export interface MockUseUserReturn {
  user: MockClerkUser | null;
  isLoaded: boolean;
}

// Clerk useAuth hook return type
export interface MockUseAuthReturn {
  isLoaded: boolean;
  isSignedIn: boolean | null;
  user: MockClerkUser | null;
  sessionId: string | null;
  userId: string | null;
  orgId: string | null;
  orgRole: string | null;
  orgSlug: string | null;
}

// IntersectionObserver mock types
export interface MockIntersectionObserverEntry {
  isIntersecting: boolean;
  target?: Element;
  intersectionRatio?: number;
  boundingClientRect?: DOMRectReadOnly;
  intersectionRect?: DOMRectReadOnly;
  rootBounds?: DOMRectReadOnly | null;
  time?: number;
}

export interface MockIntersectionObserver {
  observe: (target: Element) => void;
  unobserve: (target: Element) => void;
  disconnect: () => void;
}

// Error handling types
export interface MockErrorInfo {
  componentStack: string;
  errorBoundary?: string;
}

// Generic promise resolver type for tests
export type PromiseResolver<T> = (value: T | PromiseLike<T>) => void;

// API response types
export interface MockApiResponse<T = unknown> {
  data?: T;
  success?: boolean;
  message?: string;
  error?: string;
}

// Component prop types for mocked UI components
export interface MockComponentProps {
  children?: React.ReactNode;
  className?: string;
  variant?: string;
  [key: string]: unknown;
}
