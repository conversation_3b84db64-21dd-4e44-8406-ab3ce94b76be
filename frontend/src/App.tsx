import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/clerk-react';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { RouterProvider, createRouter } from '@tanstack/react-router';

import { routeTree } from '@/routeTree.gen';

import { ErrorBoundary } from '@components/ErrorBoundary';
import { queryClient } from '@services/query-client';
import { getClerkPublishableKey, getClerkAfterSignInUrl, getClerkAfterSignUpUrl } from '@utils/env';

// Create a new router instance
const router = createRouter({ routeTree });

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}

function App() {
  const publishableKey = getClerkPublishableKey();

  if (!publishableKey) {
    throw new Error('Missing Clerk Publishable Key');
  }

  return (
    <ErrorBoundary>
      <ClerkProvider
        publishableKey={publishableKey}
        routerPush={(to) => router.navigate({ to })}
        routerReplace={(to) => router.navigate({ to, replace: true })}
        signInFallbackRedirectUrl={getClerkAfterSignInUrl()}
        signUpFallbackRedirectUrl={getClerkAfterSignUpUrl()}
        signInForceRedirectUrl={getClerkAfterSignInUrl()}
        signUpForceRedirectUrl={getClerkAfterSignUpUrl()}
        domain={window.location.hostname}
        isSatellite={false}
        appearance={{
          variables: {
            colorPrimary: '#1e40af',
            colorText: '#1f2937',
            colorBackground: '#ffffff',
            colorInputBackground: '#ffffff',
            colorInputText: '#1f2937',
            fontFamily: 'Inter, system-ui, sans-serif',
            borderRadius: '0.375rem',
          },
          elements: {
            formButtonPrimary:
              'bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors',
            card: 'shadow-lg border border-gray-200 rounded-lg',
            headerTitle: 'text-2xl font-bold text-gray-900',
            headerSubtitle: 'text-sm text-gray-600',
            formFieldLabel: 'text-sm font-medium text-gray-700',
            formFieldInput:
              'block w-full rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500',
            socialButtonsBlockButton: 'border border-gray-300 hover:bg-gray-50 transition-colors',
          },
        }}
      >
        <QueryClientProvider client={queryClient}>
          <RouterProvider router={router} />
          <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
      </ClerkProvider>
    </ErrorBoundary>
  );
}

export default App;
