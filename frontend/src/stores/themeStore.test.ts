import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'

import { useThemeStore } from './themeStore'

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
})

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock document.documentElement
const mockDocumentElement = {
  classList: {
    add: vi.fn(),
    remove: vi.fn(),
    contains: vi.fn(),
    toggle: vi.fn(),
  },
}
Object.defineProperty(document, 'documentElement', {
  value: mockDocumentElement,
  writable: true,
})

describe('themeStore', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset localStorage mock
    mockLocalStorage.getItem.mockReturnValue(null)
    // Reset theme store to initial state
    useThemeStore.setState({ theme: 'system' }, true)
  })

  describe('Initial state', () => {
    it('initializes with system theme by default', () => {
      const { result } = renderHook(() => useThemeStore())

      expect(result.current.theme).toBe('system')
    })

    it('provides setTheme function', () => {
      const { result } = renderHook(() => useThemeStore())

      expect(typeof result.current.setTheme).toBe('function')
    })

    it('provides toggleTheme function', () => {
      const { result } = renderHook(() => useThemeStore())

      expect(typeof result.current.toggleTheme).toBe('function')
    })
  })

  describe('setTheme', () => {
    it('updates theme state to light', () => {
      const { result } = renderHook(() => useThemeStore())

      act(() => {
        result.current.setTheme('light')
      })

      expect(result.current.theme).toBe('light')
    })

    it('updates theme state to dark', () => {
      const { result } = renderHook(() => useThemeStore())

      act(() => {
        result.current.setTheme('dark')
      })

      expect(result.current.theme).toBe('dark')
    })

    it('updates theme state to system', () => {
      const { result } = renderHook(() => useThemeStore())

      // Set to light first
      act(() => {
        result.current.setTheme('light')
      })

      // Then set to system
      act(() => {
        result.current.setTheme('system')
      })

      expect(result.current.theme).toBe('system')
    })

    it('applies theme to document element for light theme', () => {
      const { result } = renderHook(() => useThemeStore())

      act(() => {
        result.current.setTheme('light')
      })

      expect(mockDocumentElement.classList.remove).toHaveBeenCalledWith('light', 'dark')
      expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('light')
    })

    it('applies theme to document element for dark theme', () => {
      const { result } = renderHook(() => useThemeStore())

      act(() => {
        result.current.setTheme('dark')
      })

      expect(mockDocumentElement.classList.remove).toHaveBeenCalledWith('light', 'dark')
      expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('dark')
    })

    it('applies system theme based on media query', () => {
      // Mock system preference for dark mode
      window.matchMedia = vi.fn().mockImplementation((query) => ({
        matches: query === '(prefers-color-scheme: dark)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }))

      const { result } = renderHook(() => useThemeStore())

      act(() => {
        result.current.setTheme('system')
      })

      expect(mockDocumentElement.classList.remove).toHaveBeenCalledWith('light', 'dark')
      expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('dark')
    })

    it('applies light theme when system preference is light', () => {
      // Mock system preference for light mode
      window.matchMedia = vi.fn().mockImplementation((query) => ({
        matches: false, // Not dark mode, so light mode
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }))

      const { result } = renderHook(() => useThemeStore())

      act(() => {
        result.current.setTheme('system')
      })

      expect(mockDocumentElement.classList.remove).toHaveBeenCalledWith('light', 'dark')
      expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('light')
    })
  })

  describe('toggleTheme', () => {
    it('toggles from dark to light', () => {
      const { result } = renderHook(() => useThemeStore())

      // Set initial state to dark
      act(() => {
        result.current.setTheme('dark')
      })

      // Toggle theme
      act(() => {
        result.current.toggleTheme()
      })

      expect(result.current.theme).toBe('light')
    })

    it('toggles from light to dark', () => {
      const { result } = renderHook(() => useThemeStore())

      // Set initial state to light
      act(() => {
        result.current.setTheme('light')
      })

      // Toggle theme
      act(() => {
        result.current.toggleTheme()
      })

      expect(result.current.theme).toBe('dark')
    })

    it('toggles from system to dark', () => {
      const { result } = renderHook(() => useThemeStore())

      // Initial state is system
      expect(result.current.theme).toBe('system')

      // Toggle theme
      act(() => {
        result.current.toggleTheme()
      })

      expect(result.current.theme).toBe('dark')
    })

    it('applies the toggled theme to document element', () => {
      const { result } = renderHook(() => useThemeStore())

      // Set to light first
      act(() => {
        result.current.setTheme('light')
      })

      // Clear previous calls
      vi.clearAllMocks()

      // Toggle to dark
      act(() => {
        result.current.toggleTheme()
      })

      expect(mockDocumentElement.classList.remove).toHaveBeenCalledWith('light', 'dark')
      expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('dark')
    })
  })

  describe('Persistence', () => {
    it('persists theme changes to localStorage', () => {
      const { result } = renderHook(() => useThemeStore())

      act(() => {
        result.current.setTheme('dark')
      })

      // Check that setItem was called (zustand persist handles the exact format)
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
    })

    it('loads persisted theme from localStorage', () => {
      // Mock localStorage returning a persisted dark theme
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        state: { theme: 'dark' },
        version: 0
      }))

      // Create a new hook instance to trigger persistence loading
      const { result } = renderHook(() => useThemeStore())

      // Should load the persisted theme
      expect(result.current.theme).toBe('dark')
    })

    it('uses default theme when no persisted value exists', () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      const { result } = renderHook(() => useThemeStore())

      expect(result.current.theme).toBe('system')
    })

    it('handles invalid persisted data gracefully', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid-json')

      const { result } = renderHook(() => useThemeStore())

      // Should fall back to default
      expect(result.current.theme).toBe('system')
    })
  })

  describe('DOM manipulation', () => {
    it('removes previous theme classes before applying new ones', () => {
      const { result } = renderHook(() => useThemeStore())

      act(() => {
        result.current.setTheme('light')
      })

      expect(mockDocumentElement.classList.remove).toHaveBeenCalledWith('light', 'dark')
      expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('light')
    })

    it('handles missing document element gracefully', () => {
      // Temporarily remove documentElement
      const originalDocumentElement = document.documentElement
      delete (document as { documentElement?: Element }).documentElement

      const { result } = renderHook(() => useThemeStore())

      expect(() => {
        act(() => {
          result.current.setTheme('dark')
        })
      }).toThrow()

      // Restore documentElement
      Object.defineProperty(document, 'documentElement', {
        value: originalDocumentElement,
        writable: true,
      })
    })
  })

  describe('State management', () => {
    it('maintains state consistency across multiple hook instances', () => {
      const { result: result1 } = renderHook(() => useThemeStore())
      const { result: result2 } = renderHook(() => useThemeStore())

      act(() => {
        result1.current.setTheme('dark')
      })

      expect(result2.current.theme).toBe('dark')
    })

    it('updates all subscribers when theme changes', () => {
      const { result: result1 } = renderHook(() => useThemeStore())
      const { result: result2 } = renderHook(() => useThemeStore())

      expect(result1.current.theme).toBe('system')
      expect(result2.current.theme).toBe('system')

      act(() => {
        result1.current.setTheme('light')
      })

      expect(result1.current.theme).toBe('light')
      expect(result2.current.theme).toBe('light')
    })
  })

  describe('System theme detection', () => {
    it('detects dark system theme preference', () => {
      window.matchMedia = vi.fn().mockImplementation((query) => ({
        matches: query === '(prefers-color-scheme: dark)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }))

      const { result } = renderHook(() => useThemeStore())

      act(() => {
        result.current.setTheme('system')
      })

      expect(window.matchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)')
      expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('dark')
    })

    it('detects light system theme preference', () => {
      window.matchMedia = vi.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }))

      const { result } = renderHook(() => useThemeStore())

      act(() => {
        result.current.setTheme('system')
      })

      expect(window.matchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)')
      expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('light')
    })

    it('handles matchMedia not being available', () => {
      // Remove matchMedia temporarily
      const originalMatchMedia = window.matchMedia
      delete (window as { matchMedia?: (query: string) => MediaQueryList }).matchMedia

      const { result } = renderHook(() => useThemeStore())

      expect(() => {
        act(() => {
          result.current.setTheme('system')
        })
      }).toThrow()

      // Restore matchMedia
      window.matchMedia = originalMatchMedia
    })
  })

  describe('Edge cases', () => {
    it('handles rapid theme changes', () => {
      const { result } = renderHook(() => useThemeStore())

      act(() => {
        result.current.setTheme('light')
        result.current.setTheme('dark')
        result.current.setTheme('system')
      })

      expect(result.current.theme).toBe('system')
    })

    it('handles toggle on system theme', () => {
      const { result } = renderHook(() => useThemeStore())

      // Start with system theme
      expect(result.current.theme).toBe('system')

      act(() => {
        result.current.toggleTheme()
      })

      // Should toggle to dark since system is not light or dark
      expect(result.current.theme).toBe('dark')
    })
  })
})
