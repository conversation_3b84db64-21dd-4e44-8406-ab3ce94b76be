import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'

import { getClerkPublishableKey } from '@utils/env'

import App from './App'

// Mock environment utility
vi.mock('@utils/env', () => ({
  getClerkPublishableKey: vi.fn(),
}))

// Mock Clerk provider
vi.mock('@clerk/clerk-react', () => ({
  ClerkProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="clerk-provider">{children}</div>
  ),
}))

// Mock TanStack Query
vi.mock('@tanstack/react-query', () => ({
  QueryClientProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="query-client-provider">{children}</div>
  ),
}))

// Mock TanStack Query Devtools
vi.mock('@tanstack/react-query-devtools', () => ({
  ReactQueryDevtools: () => <div data-testid="react-query-devtools" />,
}))

// Mock TanStack Router
vi.mock('@tanstack/react-router', () => ({
  RouterProvider: ({ router }: { router: unknown }) => (
    <div data-testid="router-provider" data-router={router?.constructor?.name || 'router'} />
  ),
  createRouter: vi.fn(() => ({
    constructor: { name: 'MockRouter' }
  })),
}))

// Mock ErrorBoundary
vi.mock('@components/ErrorBoundary', () => ({
  ErrorBoundary: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="error-boundary">{children}</div>
  ),
}))

// Mock services
vi.mock('@services/query-client', () => ({
  queryClient: {
    mount: vi.fn(),
    unmount: vi.fn(),
  },
}))

// Mock route tree
vi.mock('@/routeTree.gen', () => ({
  routeTree: {
    children: [],
  },
}))


const mockGetClerkPublishableKey = getClerkPublishableKey as vi.MockedFunction<typeof getClerkPublishableKey>

describe('App', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockGetClerkPublishableKey.mockReturnValue('pk_test_12345')
  })

  describe('Successful initialization', () => {
    it('renders without crashing', () => {
      expect(() => render(<App />)).not.toThrow()
    })

    it('renders all provider components in correct order', () => {
      render(<App />)

      // ErrorBoundary should be the outermost
      expect(screen.getByTestId('error-boundary')).toBeInTheDocument()

      // ClerkProvider should be inside ErrorBoundary
      expect(screen.getByTestId('clerk-provider')).toBeInTheDocument()

      // QueryClientProvider should be inside ClerkProvider
      expect(screen.getByTestId('query-client-provider')).toBeInTheDocument()

      // RouterProvider should be inside QueryClientProvider
      expect(screen.getByTestId('router-provider')).toBeInTheDocument()

      // React Query Devtools should be present
      expect(screen.getByTestId('react-query-devtools')).toBeInTheDocument()
    })

    it('initializes with correct Clerk publishable key', () => {
      mockGetClerkPublishableKey.mockReturnValue('pk_test_specific_key')

      render(<App />)

      expect(mockGetClerkPublishableKey).toHaveBeenCalledTimes(1)
    })

    it('creates router instance', () => {
      render(<App />)

      const routerProvider = screen.getByTestId('router-provider')
      expect(routerProvider).toHaveAttribute('data-router', 'MockRouter')
    })
  })

  describe('Error handling', () => {
    it('throws error when Clerk publishable key is missing', () => {
      mockGetClerkPublishableKey.mockReturnValue('')

      expect(() => render(<App />)).toThrow('Missing Clerk Publishable Key')
    })

    it('throws error when Clerk publishable key is null', () => {
      mockGetClerkPublishableKey.mockReturnValue(null)

      expect(() => render(<App />)).toThrow('Missing Clerk Publishable Key')
    })

    it('throws error when Clerk publishable key is undefined', () => {
      mockGetClerkPublishableKey.mockReturnValue(undefined)

      expect(() => render(<App />)).toThrow('Missing Clerk Publishable Key')
    })
  })

  describe('Provider hierarchy', () => {
    it('maintains correct nesting structure', () => {
      render(<App />)

      const errorBoundary = screen.getByTestId('error-boundary')
      const clerkProvider = screen.getByTestId('clerk-provider')
      const queryProvider = screen.getByTestId('query-client-provider')
      const routerProvider = screen.getByTestId('router-provider')

      // Check nesting by DOM hierarchy
      expect(errorBoundary).toContainElement(clerkProvider)
      expect(clerkProvider).toContainElement(queryProvider)
      expect(queryProvider).toContainElement(routerProvider)
    })

    it('includes React Query Devtools alongside RouterProvider', () => {
      render(<App />)

      const queryProvider = screen.getByTestId('query-client-provider')
      const routerProvider = screen.getByTestId('router-provider')
      const devtools = screen.getByTestId('react-query-devtools')

      // Both should be children of QueryClientProvider
      expect(queryProvider).toContainElement(routerProvider)
      expect(queryProvider).toContainElement(devtools)
    })
  })

  describe('Clerk configuration', () => {
    it('uses correct Clerk publishable key from environment', () => {
      const testKey = 'pk_test_custom_key_12345'
      mockGetClerkPublishableKey.mockReturnValue(testKey)

      render(<App />)

      expect(mockGetClerkPublishableKey).toHaveBeenCalledTimes(1)
      // In a real test, we'd verify Clerk is initialized with the correct key
      // This would require additional mocking of the ClerkProvider
    })
  })

  describe('Development vs Production', () => {
    it('includes React Query Devtools in development', () => {
      // Mock development environment
      vi.stubEnv('NODE_ENV', 'development')

      render(<App />)

      expect(screen.getByTestId('react-query-devtools')).toBeInTheDocument()
    })

    // Note: In a real implementation, you might conditionally render devtools
    // based on environment. This test demonstrates how you'd test that.
    it('could conditionally include devtools based on environment', () => {
      // This is a placeholder for when devtools are conditionally rendered
      render(<App />)

      // Currently always rendered, but this shows the testing pattern
      expect(screen.getByTestId('react-query-devtools')).toBeInTheDocument()
    })
  })

  describe('Router initialization', () => {
    it('creates router with route tree', () => {
      render(<App />)

      // Verify router provider is rendered
      expect(screen.getByTestId('router-provider')).toBeInTheDocument()

      // In a more detailed test, we'd verify the router was created
      // with the correct route tree configuration
    })
  })

  describe('Error boundary integration', () => {
    it('wraps entire application in error boundary', () => {
      render(<App />)

      const errorBoundary = screen.getByTestId('error-boundary')
      const clerkProvider = screen.getByTestId('clerk-provider')

      expect(errorBoundary).toContainElement(clerkProvider)
      expect(errorBoundary).toBeInTheDocument()
    })
  })

  describe('Component integration', () => {
    it('integrates all major providers successfully', () => {
      render(<App />)

      // All core providers should be present
      expect(screen.getByTestId('error-boundary')).toBeInTheDocument()
      expect(screen.getByTestId('clerk-provider')).toBeInTheDocument()
      expect(screen.getByTestId('query-client-provider')).toBeInTheDocument()
      expect(screen.getByTestId('router-provider')).toBeInTheDocument()
      expect(screen.getByTestId('react-query-devtools')).toBeInTheDocument()
    })

    it('maintains provider order for proper dependency injection', () => {
      render(<App />)

      // The order is important for proper functionality:
      // ErrorBoundary -> ClerkProvider -> QueryClientProvider -> RouterProvider
      // This ensures auth context is available to queries, and queries are available to routes

      const providers = [
        'error-boundary',
        'clerk-provider',
        'query-client-provider',
        'router-provider'
      ]

      providers.forEach(testId => {
        expect(screen.getByTestId(testId)).toBeInTheDocument()
      })
    })
  })

  describe('Edge cases', () => {
    it('handles empty publishable key gracefully', () => {
      mockGetClerkPublishableKey.mockReturnValue('')

      expect(() => render(<App />)).toThrow('Missing Clerk Publishable Key')
    })

    it('handles whitespace-only publishable key', () => {
      mockGetClerkPublishableKey.mockReturnValue('   ')

      // Assuming the utility trims whitespace
      expect(() => render(<App />)).toThrow('Missing Clerk Publishable Key')
    })
  })

  describe('Type safety', () => {
    it('exports App component as default', () => {
      expect(App).toBeDefined()
      expect(typeof App).toBe('function')
    })

    it('renders as a React component', () => {
      const result = render(<App />)
      expect(result.container).toBeInstanceOf(HTMLElement)
    })
  })
})
