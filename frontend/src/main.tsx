import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';

import App from '@/App.tsx';

import './index.css';
// Validate environment variables at startup (this will throw on critical errors)
import '@utils/env';
import { initWebVitals } from '@utils/webVitals';

// Initialize Web Vitals monitoring
if (typeof window !== 'undefined') {
  initWebVitals();
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
);
