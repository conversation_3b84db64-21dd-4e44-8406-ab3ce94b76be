import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'


import { useErrorHandler } from './useErrorHandler'

// Mock console methods
const mockConsole = {
  group: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  info: vi.fn(),
  groupEnd: vi.fn(),
}

Object.assign(console, mockConsole)

// Mock import.meta.env
vi.stubGlobal('import.meta', {
  env: {
    DEV: true,
  },
})

describe('useErrorHandler', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset environment to development mode
    import.meta.env.DEV = true
  })

  describe('Basic error handling', () => {
    it('returns error handler function', () => {
      const { result } = renderHook(() => useErrorHandler())

      expect(typeof result.current).toBe('function')
    })

    it('processes Error objects correctly', () => {
      const { result } = renderHook(() => useErrorHandler())

      const error = new Error('Test error message')
      let errorInfo: { message: string; type: string; userFriendlyMessage: string; originalError: Error; stack?: string; timestamp?: string; userAgent?: string; url?: string }

      act(() => {
        errorInfo = result.current(error)
      })

      expect(errorInfo.message).toBe('Test error message')
      expect(errorInfo.type).toBe('unknown')
      expect(errorInfo.userFriendlyMessage).toBe('Something went wrong. Please try again.')
    })

    it('handles non-Error objects', () => {
      const { result } = renderHook(() => useErrorHandler())

      const errorObj = { status: 500, message: 'Server error' }
      let errorInfo: { message: string; type: string; userFriendlyMessage: string; originalError: Error; stack?: string; timestamp?: string; userAgent?: string; url?: string }

      act(() => {
        errorInfo = result.current(errorObj)
      })

      expect(errorInfo.message).toBe('Server error')
      expect(errorInfo.type).toBe('network')
      expect(errorInfo.userFriendlyMessage).toBe('Server error. Please try again later.')
    })

    it('handles primitive values', () => {
      const { result } = renderHook(() => useErrorHandler())

      let errorInfo: { message: string; type: string; userFriendlyMessage: string; originalError: Error; stack?: string; timestamp?: string; userAgent?: string; url?: string }

      act(() => {
        errorInfo = result.current('Simple string error')
      })

      expect(errorInfo.message).toBe('An unexpected error occurred')
      expect(errorInfo.type).toBe('unknown')
      expect(errorInfo.userFriendlyMessage).toBe('Something went wrong. Please try again.')
    })
  })

  describe('Error categorization', () => {
    it('categorizes network errors', () => {
      const { result } = renderHook(() => useErrorHandler())

      const networkError = new Error('fetch failed due to network issue')
      let errorInfo: { message: string; type: string; userFriendlyMessage: string; originalError: Error; stack?: string; timestamp?: string; userAgent?: string; url?: string }

      act(() => {
        errorInfo = result.current(networkError)
      })

      expect(errorInfo.type).toBe('network')
      expect(errorInfo.userFriendlyMessage).toBe('Network error. Please check your connection and try again.')
    })

    it('categorizes authentication errors by message', () => {
      const { result } = renderHook(() => useErrorHandler())

      const authError = new Error('401 unauthorized access')
      let errorInfo: { message: string; type: string; userFriendlyMessage: string; originalError: Error; stack?: string; timestamp?: string; userAgent?: string; url?: string }

      act(() => {
        errorInfo = result.current(authError)
      })

      expect(errorInfo.type).toBe('authentication')
      expect(errorInfo.userFriendlyMessage).toBe('Your session has expired. Please log in again.')
    })

    it('categorizes validation errors', () => {
      const { result } = renderHook(() => useErrorHandler())

      const validationError = new Error('validation failed for field')
      let errorInfo: { message: string; type: string; userFriendlyMessage: string; originalError: Error; stack?: string; timestamp?: string; userAgent?: string; url?: string }

      act(() => {
        errorInfo = result.current(validationError)
      })

      expect(errorInfo.type).toBe('validation')
      expect(errorInfo.userFriendlyMessage).toBe('Please check your input and try again.')
    })

    it('categorizes errors by status code', () => {
      const { result } = renderHook(() => useErrorHandler())

      const errorWith401 = { status: 401, message: 'Unauthorized' }
      let errorInfo: { message: string; type: string; userFriendlyMessage: string; originalError: Error; stack?: string; timestamp?: string; userAgent?: string; url?: string }

      act(() => {
        errorInfo = result.current(errorWith401)
      })

      expect(errorInfo.type).toBe('authentication')
      expect(errorInfo.userFriendlyMessage).toBe('Your session has expired. Please log in again.')
    })

    it('categorizes 400-level errors as validation', () => {
      const { result } = renderHook(() => useErrorHandler())

      const errorWith400 = { status: 400, message: 'Bad request' }
      let errorInfo: { message: string; type: string; userFriendlyMessage: string; originalError: Error; stack?: string; timestamp?: string; userAgent?: string; url?: string }

      act(() => {
        errorInfo = result.current(errorWith400)
      })

      expect(errorInfo.type).toBe('validation')
      expect(errorInfo.userFriendlyMessage).toBe('Please check your input and try again.')
    })

    it('categorizes 500-level errors as network', () => {
      const { result } = renderHook(() => useErrorHandler())

      const errorWith500 = { status: 500, statusText: 'Internal Server Error' }
      let errorInfo: { message: string; type: string; userFriendlyMessage: string; originalError: Error; stack?: string; timestamp?: string; userAgent?: string; url?: string }

      act(() => {
        errorInfo = result.current(errorWith500)
      })

      expect(errorInfo.type).toBe('network')
      expect(errorInfo.userFriendlyMessage).toBe('Server error. Please try again later.')
    })
  })

  describe('Console logging', () => {
    it('logs to console when logToConsole is true and in development', () => {
      const { result } = renderHook(() => useErrorHandler({ logToConsole: true }))

      const error = new Error('Test error')
      error.stack = 'Error stack trace'

      act(() => {
        result.current(error)
      })

      expect(mockConsole.group).toHaveBeenCalledWith('🚨 Application Error')
      expect(mockConsole.error).toHaveBeenCalledWith('Error:', error)
      expect(mockConsole.error).toHaveBeenCalledWith('Type:', 'unknown')
      expect(mockConsole.error).toHaveBeenCalledWith('Message:', 'Test error')
      expect(mockConsole.error).toHaveBeenCalledWith('User Message:', 'Something went wrong. Please try again.')
      expect(mockConsole.error).toHaveBeenCalledWith('Stack:', 'Error stack trace')
      expect(mockConsole.groupEnd).toHaveBeenCalled()
    })

    it('does not log to console when logToConsole is false', () => {
      const { result } = renderHook(() => useErrorHandler({ logToConsole: false }))

      act(() => {
        result.current(new Error('Test error'))
      })

      expect(mockConsole.group).not.toHaveBeenCalled()
      expect(mockConsole.error).not.toHaveBeenCalled()
    })

    it('does not log to console in production even when enabled', () => {
      import.meta.env.DEV = false

      const { result } = renderHook(() => useErrorHandler({ logToConsole: true }))

      act(() => {
        result.current(new Error('Test error'))
      })

      expect(mockConsole.group).not.toHaveBeenCalled()
      expect(mockConsole.error).not.toHaveBeenCalled()
    })

    it('handles errors without stack trace', () => {
      const { result } = renderHook(() => useErrorHandler({ logToConsole: true }))

      const errorWithoutStack = new Error('Test error')
      delete errorWithoutStack.stack

      act(() => {
        result.current(errorWithoutStack)
      })

      expect(mockConsole.group).toHaveBeenCalled()
      expect(mockConsole.error).toHaveBeenCalledWith('Error:', errorWithoutStack)
      // Should not try to log stack
      expect(mockConsole.error).not.toHaveBeenCalledWith('Stack:', expect.anything())
    })
  })

  describe('Toast notifications', () => {
    it('shows toast notification when showToast is true', () => {
      const { result } = renderHook(() => useErrorHandler({ showToast: true }))

      act(() => {
        result.current(new Error('Test error'))
      })

      // For now, it logs a warning since no toast system is integrated
      expect(mockConsole.warn).toHaveBeenCalledWith(
        'Toast notification:',
        'Something went wrong. Please try again.'
      )
    })

    it('does not show toast when showToast is false', () => {
      const { result } = renderHook(() => useErrorHandler({ showToast: false }))

      act(() => {
        result.current(new Error('Test error'))
      })

      expect(mockConsole.warn).not.toHaveBeenCalledWith(
        'Toast notification:',
        expect.anything()
      )
    })
  })

  describe('Error reporting', () => {
    it('reports to service when reportToService is true and in production', () => {
      import.meta.env.DEV = false

      const { result } = renderHook(() => useErrorHandler({ reportToService: true }))

      act(() => {
        result.current(new Error('Test error'))
      })

      expect(mockConsole.info).toHaveBeenCalledWith(
        'Would report to monitoring service:',
        expect.objectContaining({
          error: expect.objectContaining({
            message: 'Test error',
            type: 'unknown'
          }),
          timestamp: expect.any(String),
          userAgent: expect.any(String),
          url: expect.any(String),
        })
      )
    })

    it('does not report to service in development', () => {
      import.meta.env.DEV = true

      const { result } = renderHook(() => useErrorHandler({ reportToService: true }))

      act(() => {
        result.current(new Error('Test error'))
      })

      expect(mockConsole.info).not.toHaveBeenCalledWith(
        'Would report to monitoring service:',
        expect.anything()
      )
    })

    it('does not report when reportToService is false', () => {
      import.meta.env.DEV = false

      const { result } = renderHook(() => useErrorHandler({ reportToService: false }))

      act(() => {
        result.current(new Error('Test error'))
      })

      expect(mockConsole.info).not.toHaveBeenCalledWith(
        'Would report to monitoring service:',
        expect.anything()
      )
    })
  })

  describe('Error type handling', () => {
    it('provides specific warnings for authentication errors', () => {
      const { result } = renderHook(() => useErrorHandler())

      act(() => {
        result.current(new Error('401 unauthorized'))
      })

      expect(mockConsole.warn).toHaveBeenCalledWith(
        'Authentication error detected. Consider redirecting to login.'
      )
    })

    it('provides specific warnings for network errors', () => {
      const { result } = renderHook(() => useErrorHandler())

      act(() => {
        result.current(new Error('network connection failed'))
      })

      expect(mockConsole.warn).toHaveBeenCalledWith(
        'Network error detected. Consider showing retry option.'
      )
    })

    it('provides specific warnings for validation errors', () => {
      const { result } = renderHook(() => useErrorHandler())

      act(() => {
        result.current(new Error('validation failed'))
      })

      expect(mockConsole.warn).toHaveBeenCalledWith(
        'Validation error detected.'
      )
    })

    it('provides generic warning for unknown errors', () => {
      const { result } = renderHook(() => useErrorHandler())

      act(() => {
        result.current(new Error('Something weird happened'))
      })

      expect(mockConsole.warn).toHaveBeenCalledWith(
        'Unknown error type detected.'
      )
    })
  })

  describe('Default options', () => {
    it('uses default options when none provided', () => {
      const { result } = renderHook(() => useErrorHandler())

      act(() => {
        result.current(new Error('Test error'))
      })

      // Should log (default logToConsole: true, and we're in dev)
      expect(mockConsole.group).toHaveBeenCalled()

      // Should show toast (default showToast: true)
      expect(mockConsole.warn).toHaveBeenCalledWith(
        'Toast notification:',
        expect.any(String)
      )
    })

    it('applies custom options correctly', () => {
      const { result } = renderHook(() => useErrorHandler({
        showToast: false,
        logToConsole: false,
        reportToService: true
      }))

      act(() => {
        result.current(new Error('Test error'))
      })

      // Should not log
      expect(mockConsole.group).not.toHaveBeenCalled()

      // Should not show toast
      expect(mockConsole.warn).not.toHaveBeenCalledWith(
        'Toast notification:',
        expect.anything()
      )
    })
  })

  describe('Callback stability', () => {
    it('returns stable callback when options do not change', () => {
      const options = { showToast: true, logToConsole: true }
      const { result, rerender } = renderHook(() => useErrorHandler(options))

      const firstCallback = result.current

      rerender()

      expect(result.current).toBe(firstCallback)
    })

    it('returns new callback when options change', () => {
      const { result, rerender } = renderHook(
        ({ options }) => useErrorHandler(options),
        { initialProps: { options: { showToast: true } } }
      )

      const firstCallback = result.current

      rerender({ options: { showToast: false } })

      expect(result.current).not.toBe(firstCallback)
    })
  })

  describe('Complex error objects', () => {
    it('handles error objects with statusText fallback', () => {
      const { result } = renderHook(() => useErrorHandler())

      const errorObj = { status: 404, statusText: 'Not Found' }
      let errorInfo: { message: string; type: string; userFriendlyMessage: string; originalError: Error; stack?: string; timestamp?: string; userAgent?: string; url?: string }

      act(() => {
        errorInfo = result.current(errorObj)
      })

      expect(errorInfo.message).toBe('Not Found')
    })

    it('handles error objects with no message or statusText', () => {
      const { result } = renderHook(() => useErrorHandler())

      const errorObj = { status: 500 }
      let errorInfo: { message: string; type: string; userFriendlyMessage: string; originalError: Error; stack?: string; timestamp?: string; userAgent?: string; url?: string }

      act(() => {
        errorInfo = result.current(errorObj)
      })

      expect(errorInfo.message).toBe('An unexpected error occurred')
      expect(errorInfo.type).toBe('network')
    })

    it('handles null and undefined errors', () => {
      const { result } = renderHook(() => useErrorHandler())

      let errorInfo: { message: string; type: string; userFriendlyMessage: string; originalError: Error; stack?: string; timestamp?: string; userAgent?: string; url?: string }

      act(() => {
        errorInfo = result.current(null)
      })

      expect(errorInfo.message).toBe('An unexpected error occurred')
      expect(errorInfo.type).toBe('unknown')

      act(() => {
        errorInfo = result.current(undefined)
      })

      expect(errorInfo.message).toBe('An unexpected error occurred')
      expect(errorInfo.type).toBe('unknown')
    })
  })
})
