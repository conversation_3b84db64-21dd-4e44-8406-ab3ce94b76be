import { useCallback } from 'react';

interface ErrorHandlerOptions {
  showToast?: boolean;
  logToConsole?: boolean;
  reportToService?: boolean;
}

interface ErrorInfo {
  message: string;
  type: 'network' | 'validation' | 'authentication' | 'unknown';
  userFriendlyMessage: string;
}

// Enhanced error categorization
function categorizeError(error: Error | unknown): ErrorInfo {
  let message = 'An unexpected error occurred';
  let type: ErrorInfo['type'] = 'unknown';
  let userFriendlyMessage = 'Something went wrong. Please try again.';

  if (error instanceof Error) {
    message = error.message;

    // Network errors
    if (error.message.includes('fetch') || error.message.includes('network')) {
      type = 'network';
      userFriendlyMessage = 'Network error. Please check your connection and try again.';
    }
    // Authentication errors
    else if (error.message.includes('401') || error.message.includes('unauthorized')) {
      type = 'authentication';
      userFriendlyMessage = 'Your session has expired. Please log in again.';
    }
    // Validation errors
    else if (error.message.includes('validation') || error.message.includes('400')) {
      type = 'validation';
      userFriendlyMessage = 'Please check your input and try again.';
    }
  }
  // Handle non-Error objects
  else if (typeof error === 'object' && error !== null) {
    const errorObj = error as { status?: number; message?: string; statusText?: string };
    if (errorObj.status) {
      if (errorObj.status === 401) {
        type = 'authentication';
        userFriendlyMessage = 'Your session has expired. Please log in again.';
      } else if (errorObj.status >= 400 && errorObj.status < 500) {
        type = 'validation';
        userFriendlyMessage = 'Please check your input and try again.';
      } else if (errorObj.status >= 500) {
        type = 'network';
        userFriendlyMessage = 'Server error. Please try again later.';
      }
    }
    message = errorObj.message || errorObj.statusText || message;
  }

  return { message, type, userFriendlyMessage };
}

// Hook for functional components error handling
export function useErrorHandler(options: ErrorHandlerOptions = {}) {
  const { showToast = true, logToConsole = true, reportToService = false } = options;

  return useCallback(
    (error: Error | unknown) => {
      const errorInfo = categorizeError(error);

      // Log to console in development
      if (logToConsole && import.meta.env.DEV) {
        console.error('🚨 Application Error');
        console.error('Error:', error);
        console.error('Type:', errorInfo.type);
        console.error('Message:', errorInfo.message);
        console.error('User Message:', errorInfo.userFriendlyMessage);
        if (error instanceof Error && error.stack) {
          console.error('Stack:', error.stack);
        }
      }

      // Show user-friendly toast notification
      if (showToast) {
        // TODO: Integrate with toast notification system when available
        console.warn('Toast notification:', errorInfo.userFriendlyMessage);
      }

      // Report to error monitoring service
      if (reportToService && !import.meta.env.DEV) {
        // TODO: Integrate with error monitoring service (e.g., Sentry)
        console.warn('Would report to monitoring service:', {
          error: errorInfo,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href,
        });
      }

      // Handle specific error types
      switch (errorInfo.type) {
        case 'authentication':
          // Could trigger logout or redirect to login
          console.warn('Authentication error detected. Consider redirecting to login.');
          break;
        case 'network':
          // Could show offline indicator or retry mechanism
          console.warn('Network error detected. Consider showing retry option.');
          break;
        case 'validation':
          // Usually handled by form validation, but could highlight fields
          console.warn('Validation error detected.');
          break;
        default:
          console.warn('Unknown error type detected.');
      }

      return errorInfo;
    },
    [showToast, logToConsole, reportToService],
  );
}
