import { useUser } from '@clerk/clerk-react';
import type { Mo<PERSON><PERSON>lerkUser, MockUseUserReturn, PromiseResolver } from '@test/types';
import { renderHook, act, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';

import { useApiClient } from '@services/api-client';

import { useClerkSync } from './useClerkSync';

// Mock Clerk's useUser hook
vi.mock('@clerk/clerk-react', () => ({
  useUser: vi.fn(),
}));

// Mock the API client
vi.mock('@services/api-client', () => ({
  useApiClient: vi.fn(() => ({
    post: vi.fn(),
  })),
}));

const mockUseUser = useUser as vi.MockedFunction<typeof useUser>;
const mockUseApiClient = useApiClient as vi.MockedFunction<typeof useApiClient>;

describe('useClerkSync', () => {
  const mockUser: MockClerkUser = {
    id: 'user_123',
    firstName: 'John',
    lastName: 'Doe',
    emailAddresses: [{ emailAddress: '<EMAIL>' }],
    phoneNumbers: [{ phoneNumber: '+1234567890' }],
    imageUrl: 'https://example.com/avatar.jpg',
    updatedAt: new Date('2023-06-01').toISOString(),
    createdAt: new Date('2023-01-01').toISOString(),
  };

  const mockApiClient = {
    post: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseApiClient.mockReturnValue(mockApiClient);

    // Default mock values
    mockUseUser.mockReturnValue({
      user: null,
      isLoaded: false,
    } as MockUseUserReturn);
  });

  describe('Initial state', () => {
    it('initializes with correct default state', () => {
      const { result } = renderHook(() => useClerkSync());

      expect(result.current.syncStatus.isLoading).toBe(false);
      expect(result.current.syncStatus.error).toBeNull();
      expect(result.current.syncStatus.lastSyncAt).toBeNull();
      expect(result.current.syncStatus.isInitialSync).toBe(true);
      expect(result.current.needsSync).toBe(true);
    });
  });

  describe('Auto sync behavior', () => {
    it('automatically syncs when user is loaded and autoSync is true', async () => {
      mockApiClient.post.mockResolvedValue({ success: true });

      // Start with user not loaded
      const { rerender } = renderHook(() => useClerkSync(true));

      // Simulate user loading
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      } as MockUseUserReturn);

      rerender();

      await waitFor(() => {
        expect(mockApiClient.post).toHaveBeenCalledWith('/clerk/sync/', {
          clerkId: 'user_123',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          imageUrl: 'https://example.com/avatar.jpg',
          phoneNumber: '+1234567890',
        });
      });
    });

    it('does not auto sync when autoSync is false', async () => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      } as MockUseUserReturn);

      renderHook(() => useClerkSync(false));

      // Wait a bit to ensure no sync happens
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(mockApiClient.post).not.toHaveBeenCalled();
    });

    it('does not auto sync when user is not loaded', async () => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: false,
      } as MockUseUserReturn);

      renderHook(() => useClerkSync(true));

      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(mockApiClient.post).not.toHaveBeenCalled();
    });

    it('does not auto sync when no user is present', async () => {
      mockUseUser.mockReturnValue({
        user: null,
        isLoaded: true,
      } as MockUseUserReturn);

      renderHook(() => useClerkSync(true));

      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(mockApiClient.post).not.toHaveBeenCalled();
    });

    it('only syncs once initially', async () => {
      mockApiClient.post.mockResolvedValue({ success: true });

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      } as MockUseUserReturn);

      const { rerender } = renderHook(() => useClerkSync(true));

      await waitFor(() => {
        expect(mockApiClient.post).toHaveBeenCalledTimes(1);
      });

      // Trigger re-render
      rerender();

      // Should not sync again
      expect(mockApiClient.post).toHaveBeenCalledTimes(1);
    });
  });

  describe('Manual sync', () => {
    it('manually syncs user data', async () => {
      mockApiClient.post.mockResolvedValue({ success: true });
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      } as MockUseUserReturn);

      const { result } = renderHook(() => useClerkSync(false));

      let syncResult: boolean;
      await act(async () => {
        syncResult = await result.current.syncUser();
      });

      expect(mockApiClient.post).toHaveBeenCalledWith('/clerk/sync/', {
        clerkId: 'user_123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        imageUrl: 'https://example.com/avatar.jpg',
        phoneNumber: '+1234567890',
      });
      expect(syncResult!).toBe(true);
    });

    it('returns false when no user is available for manual sync', async () => {
      mockUseUser.mockReturnValue({
        user: null,
        isLoaded: true,
      } as MockUseUserReturn);

      const { result } = renderHook(() => useClerkSync(false));

      let syncResult: boolean;
      await act(async () => {
        syncResult = await result.current.syncUser();
      });

      expect(syncResult!).toBe(false);
      expect(mockApiClient.post).not.toHaveBeenCalled();
    });

    it('can sync with custom user data', async () => {
      mockApiClient.post.mockResolvedValue({ success: true });

      const { result } = renderHook(() => useClerkSync(false));

      const customUserData = {
        clerkId: 'custom_123',
        email: '<EMAIL>',
        firstName: 'Custom',
        lastName: 'User',
      };

      await act(async () => {
        // Test syncing with custom data
        await result.current.syncUser(customUserData);
      });

      expect(result.current.syncUser).toBeDefined();
      expect(mockApiClient.post).toHaveBeenCalledWith('/clerk/sync/', customUserData);
    });
  });

  describe('Sync status updates', () => {
    it('updates loading state during sync', async () => {
      let resolveSync: PromiseResolver<{ success: boolean }> = () => {};
      const syncPromise = new Promise((resolve) => {
        resolveSync = resolve;
      });
      mockApiClient.post.mockReturnValue(syncPromise);

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      } as MockUseUserReturn);

      const { result } = renderHook(() => useClerkSync(false));

      // Start manual sync
      act(() => {
        result.current.syncUser();
      });

      expect(result.current.syncStatus.isLoading).toBe(true);

      // Complete sync
      await act(async () => {
        resolveSync({ success: true });
      });

      expect(result.current.syncStatus.isLoading).toBe(false);
      expect(result.current.syncStatus.lastSyncAt).toBeInstanceOf(Date);
      expect(result.current.syncStatus.isInitialSync).toBe(false);
    });

    it('updates error state on sync failure', async () => {
      const error = new Error('Sync failed');
      mockApiClient.post.mockRejectedValue(error);

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      } as MockUseUserReturn);

      const { result } = renderHook(() => useClerkSync(false));

      await act(async () => {
        const success = await result.current.syncUser();
        expect(success).toBe(false);
      });

      expect(result.current.syncStatus.isLoading).toBe(false);
      expect(result.current.syncStatus.error).toBe('Sync failed');
    });

    it('handles non-Error objects in sync failure', async () => {
      const errorResponse = { status: 400, message: 'Bad request' };
      mockApiClient.post.mockRejectedValue(errorResponse);

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      } as MockUseUserReturn);

      const { result } = renderHook(() => useClerkSync(false));

      await act(async () => {
        await result.current.syncUser();
      });

      expect(result.current.syncStatus.error).toBe('Failed to sync user data');
    });
  });

  describe('needsSync detection', () => {
    it('returns true when no sync has occurred', () => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      } as MockUseUserReturn);

      const { result } = renderHook(() => useClerkSync(false));

      expect(result.current.needsSync).toBe(true);
    });

    it('returns false when user was updated before last sync', async () => {
      mockApiClient.post.mockResolvedValue({ success: true });

      // User updated before sync time
      const userWithOlderUpdate = {
        ...mockUser,
        updatedAt: new Date('2023-05-01').toISOString(), // Before sync
      };

      mockUseUser.mockReturnValue({
        user: userWithOlderUpdate,
        isLoaded: true,
      } as MockUseUserReturn);

      const { result } = renderHook(() => useClerkSync(false));

      // Perform sync
      await act(async () => {
        await result.current.syncUser();
      });

      expect(result.current.needsSync).toBe(false);
    });

    it('returns true when user was updated after last sync', async () => {
      mockApiClient.post.mockResolvedValue({ success: true });

      // Set initial user
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      } as MockUseUserReturn);

      const { result, rerender } = renderHook(() => useClerkSync(false));

      // Perform initial sync
      await act(async () => {
        await result.current.syncUser();
      });

      expect(result.current.needsSync).toBe(false);

      // Update user after sync
      const updatedUser = {
        ...mockUser,
        updatedAt: new Date().toISOString(), // After sync
      };

      mockUseUser.mockReturnValue({
        user: updatedUser,
        isLoaded: true,
      } as MockUseUserReturn);

      rerender();

      expect(result.current.needsSync).toBe(true);
    });

    it('uses createdAt when updatedAt is not available', () => {
      const userWithoutUpdatedAt = {
        ...mockUser,
        updatedAt: undefined,
        createdAt: new Date('2023-01-01').toISOString(),
      };

      mockUseUser.mockReturnValue({
        user: userWithoutUpdatedAt,
        isLoaded: true,
      } as MockUseUserReturn);

      const { result } = renderHook(() => useClerkSync(false));

      expect(result.current.needsSync).toBe(true);
    });
  });

  describe('Edge cases', () => {
    it('handles user with missing email address', async () => {
      const userWithoutEmail = {
        ...mockUser,
        emailAddresses: [],
      };

      mockApiClient.post.mockResolvedValue({ success: true });
      mockUseUser.mockReturnValue({
        user: userWithoutEmail,
        isLoaded: true,
      } as MockUseUserReturn);

      const { result } = renderHook(() => useClerkSync(false));

      await act(async () => {
        await result.current.syncUser();
      });

      expect(mockApiClient.post).toHaveBeenCalledWith('/clerk/sync/', {
        clerkId: 'user_123',
        email: '',
        firstName: 'John',
        lastName: 'Doe',
        imageUrl: 'https://example.com/avatar.jpg',
        phoneNumber: '+1234567890',
      });
    });

    it('handles user with missing phone number', async () => {
      const userWithoutPhone = {
        ...mockUser,
        phoneNumbers: [],
      };

      mockApiClient.post.mockResolvedValue({ success: true });
      mockUseUser.mockReturnValue({
        user: userWithoutPhone,
        isLoaded: true,
      } as MockUseUserReturn);

      const { result } = renderHook(() => useClerkSync(false));

      await act(async () => {
        await result.current.syncUser();
      });

      expect(mockApiClient.post).toHaveBeenCalledWith('/clerk/sync/', {
        clerkId: 'user_123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        imageUrl: 'https://example.com/avatar.jpg',
        phoneNumber: undefined,
      });
    });

    it('handles user with missing names', async () => {
      const userWithoutNames = {
        ...mockUser,
        firstName: null,
        lastName: null,
      };

      mockApiClient.post.mockResolvedValue({ success: true });
      mockUseUser.mockReturnValue({
        user: userWithoutNames,
        isLoaded: true,
      } as MockUseUserReturn);

      const { result } = renderHook(() => useClerkSync(false));

      await act(async () => {
        await result.current.syncUser();
      });

      expect(mockApiClient.post).toHaveBeenCalledWith('/clerk/sync/', {
        clerkId: 'user_123',
        email: '<EMAIL>',
        firstName: '',
        lastName: '',
        imageUrl: 'https://example.com/avatar.jpg',
        phoneNumber: '+1234567890',
      });
    });

    it('clears error on successful sync after previous error', async () => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      } as MockUseUserReturn);

      const { result } = renderHook(() => useClerkSync(false));

      // First sync fails
      mockApiClient.post.mockRejectedValueOnce(new Error('First error'));

      await act(async () => {
        await result.current.syncUser();
      });

      expect(result.current.syncStatus.error).toBe('First error');

      // Second sync succeeds
      mockApiClient.post.mockResolvedValueOnce({ success: true });

      await act(async () => {
        await result.current.syncUser();
      });

      expect(result.current.syncStatus.error).toBeNull();
    });
  });

  describe('Default parameters', () => {
    it('defaults autoSync to true', async () => {
      mockApiClient.post.mockResolvedValue({ success: true });

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      } as MockUseUserReturn);

      renderHook(() => useClerkSync()); // No autoSync parameter

      await waitFor(() => {
        expect(mockApiClient.post).toHaveBeenCalled();
      });
    });
  });
});
