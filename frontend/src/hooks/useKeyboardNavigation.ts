import { useEffect, useRef, type RefObject } from 'react'

export interface KeyboardNavigationOptions {
  /** Enable arrow key navigation */
  enableArrowKeys?: boolean
  /** Enable tab key navigation */
  enableTabKeys?: boolean
  /** Enable enter key activation */
  enableEnterKey?: boolean
  /** Enable escape key handling */
  enableEscapeKey?: boolean
  /** Callback for escape key */
  onEscape?: () => void
  /** Callback for enter key */
  onEnter?: () => void
  /** Auto-focus first item on mount */
  autoFocus?: boolean
}

export const useKeyboardNavigation = <T extends HTMLElement>(
  containerRef: RefObject<T>,
  options: KeyboardNavigationOptions = {}
) => {
  const {
    enableArrowKeys = true,
    enableTabKeys = true,
    enableEnterKey = true,
    enableEscapeKey = true,
    onEscape,
    onEnter,
    autoFocus = false
  } = options

  const focusableElements = useRef<HTMLElement[]>([])

  // Get all focusable elements within the container
  const getFocusableElements = (): HTMLElement[] => {
    if (!containerRef.current) {return []}

    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ')

    return Array.from(
      containerRef.current.querySelectorAll<HTMLElement>(focusableSelectors)
    ).filter(el => {
      // Filter out hidden elements
      const style = window.getComputedStyle(el)
      return style.display !== 'none' && style.visibility !== 'hidden'
    })
  }

  const focusElement = (element: HTMLElement) => {
    element.focus()
    // Scroll into view if needed
    element.scrollIntoView({
      block: 'nearest',
      inline: 'nearest',
      behavior: 'smooth'
    })
  }

  const handleKeyDown = (event: KeyboardEvent) => {
    if (!containerRef.current) {return}

    const currentElements = getFocusableElements()
    focusableElements.current = currentElements

    const currentIndex = currentElements.findIndex(el => el === document.activeElement)

    switch (event.key) {
      case 'ArrowDown':
        if (enableArrowKeys && currentElements.length > 0) {
          event.preventDefault()
          const nextIndex = currentIndex < currentElements.length - 1 ? currentIndex + 1 : 0
          focusElement(currentElements[nextIndex])
        }
        break

      case 'ArrowUp':
        if (enableArrowKeys && currentElements.length > 0) {
          event.preventDefault()
          const prevIndex = currentIndex > 0 ? currentIndex - 1 : currentElements.length - 1
          focusElement(currentElements[prevIndex])
        }
        break

      case 'Home':
        if (enableArrowKeys && currentElements.length > 0) {
          event.preventDefault()
          focusElement(currentElements[0])
        }
        break

      case 'End':
        if (enableArrowKeys && currentElements.length > 0) {
          event.preventDefault()
          focusElement(currentElements[currentElements.length - 1])
        }
        break

      case 'Tab':
        if (enableTabKeys) {
          // Let browser handle tab navigation, but track focus
          setTimeout(() => {
            focusableElements.current = getFocusableElements()
          }, 0)
        }
        break

      case 'Enter':
        if (enableEnterKey) {
          const activeElement = document.activeElement as HTMLElement
          if (activeElement && currentElements.includes(activeElement)) {
            // Allow form submission and button clicks to work naturally
            if (onEnter) {
              event.preventDefault()
              onEnter()
            }
          }
        }
        break

      case 'Escape':
        if (enableEscapeKey && onEscape) {
          event.preventDefault()
          onEscape()
        }
        break
    }
  }

  useEffect(() => {
    const container = containerRef.current
    if (!container) {return}

    // Set initial focusable elements
    focusableElements.current = getFocusableElements()

    // Auto-focus first element if requested
    if (autoFocus && focusableElements.current.length > 0) {
      focusElement(focusableElements.current[0])
    }

    // Add event listener
    container.addEventListener('keydown', handleKeyDown)

    return () => {
      container.removeEventListener('keydown', handleKeyDown)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    containerRef,
    enableArrowKeys,
    enableTabKeys,
    enableEnterKey,
    enableEscapeKey,
    autoFocus,
    onEscape,
    onEnter
  ])

  return {
    focusableElements: focusableElements.current,
    focusFirst: () => {
      const elements = getFocusableElements()
      if (elements.length > 0) {
        focusElement(elements[0])
      }
    },
    focusLast: () => {
      const elements = getFocusableElements()
      if (elements.length > 0) {
        focusElement(elements[elements.length - 1])
      }
    }
  }
}

export default useKeyboardNavigation
