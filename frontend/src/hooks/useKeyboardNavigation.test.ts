import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'

import { useKeyboardNavigation } from './useKeyboardNavigation'

// Mock scrollIntoView
const mockScrollIntoView = vi.fn()
Object.defineProperty(Element.prototype, 'scrollIntoView', {
  value: mockScrollIntoView,
  writable: true,
})

// Mock getComputedStyle
const mockGetComputedStyle = vi.fn()
Object.defineProperty(window, 'getComputedStyle', {
  value: mockGetComputedStyle,
  writable: true,
})

describe('useKeyboardNavigation', () => {
  let mockContainer: HTMLDivElement
  let mockButton1: HTMLButtonElement
  let mockButton2: HTMLButtonElement
  let mockInput: HTMLInputElement
  let mockLink: HTMLAnchorElement

  beforeEach(() => {
    vi.clearAllMocks()

    // Reset document.activeElement
    document.body.focus()

    // Create mock DOM elements
    mockContainer = document.createElement('div')
    mockButton1 = document.createElement('button')
    mockButton2 = document.createElement('button')
    mockInput = document.createElement('input')
    mockLink = document.createElement('a')

    mockButton1.textContent = 'Button 1'
    mockButton2.textContent = 'Button 2'
    mockInput.type = 'text'
    mockLink.href = '#'
    mockLink.textContent = 'Link'

    // Setup DOM structure
    mockContainer.appendChild(mockButton1)
    mockContainer.appendChild(mockButton2)
    mockContainer.appendChild(mockInput)
    mockContainer.appendChild(mockLink)
    document.body.appendChild(mockContainer)

    // Mock focus method for elements
    const mockFocus = vi.fn(function(this: HTMLElement) {
      // Simulate setting activeElement
      Object.defineProperty(document, 'activeElement', {
        value: this,
        configurable: true,
      })
    })

    mockButton1.focus = mockFocus.bind(mockButton1)
    mockButton2.focus = mockFocus.bind(mockButton2)
    mockInput.focus = mockFocus.bind(mockInput)
    mockLink.focus = mockFocus.bind(mockLink)

    // Mock getComputedStyle to return visible styles
    mockGetComputedStyle.mockReturnValue({
      display: 'block',
      visibility: 'visible',
    })
  })

  afterEach(() => {
    document.body.innerHTML = ''
  })

  describe('Initialization', () => {
    it('sets up keyboard navigation without errors', () => {
      const containerRef = { current: mockContainer }

      const { result } = renderHook(() =>
        useKeyboardNavigation(containerRef)
      )

      expect(result.current.focusableElements).toBeDefined()
      expect(Array.isArray(result.current.focusableElements)).toBe(true)
    })

    it('finds focusable elements correctly', () => {
      const containerRef = { current: mockContainer }

      const { result } = renderHook(() =>
        useKeyboardNavigation(containerRef)
      )

      expect(result.current.focusableElements).toHaveLength(4)
    })

    it('auto-focuses first element when autoFocus is true', () => {
      const containerRef = { current: mockContainer }

      renderHook(() =>
        useKeyboardNavigation(containerRef, { autoFocus: true })
      )

      expect(mockButton1.focus).toHaveBeenCalled()
      expect(mockScrollIntoView).toHaveBeenCalledWith({
        block: 'nearest',
        inline: 'nearest',
        behavior: 'smooth'
      })
    })

    it('does not auto-focus when autoFocus is false', () => {
      const containerRef = { current: mockContainer }

      renderHook(() =>
        useKeyboardNavigation(containerRef, { autoFocus: false })
      )

      expect(mockButton1.focus).not.toHaveBeenCalled()
    })

    it('handles null container ref gracefully', () => {
      const containerRef = { current: null }

      expect(() => {
        renderHook(() => useKeyboardNavigation(containerRef))
      }).not.toThrow()
    })
  })

  describe('Arrow key navigation', () => {
    it('navigates down with ArrowDown key', () => {
      const containerRef = { current: mockContainer }

      renderHook(() => useKeyboardNavigation(containerRef))

      // Set initial focus
      act(() => {
        mockButton1.focus()
      })

      // Simulate ArrowDown
      const event = new KeyboardEvent('keydown', { key: 'ArrowDown' })
      Object.defineProperty(event, 'preventDefault', { value: vi.fn() })

      act(() => {
        mockContainer.dispatchEvent(event)
      })

      expect(mockButton2.focus).toHaveBeenCalled()
    })

    it('navigates up with ArrowUp key', () => {
      const containerRef = { current: mockContainer }

      renderHook(() => useKeyboardNavigation(containerRef))

      // Set focus on second button
      act(() => {
        mockButton2.focus()
      })

      // Simulate ArrowUp
      const event = new KeyboardEvent('keydown', { key: 'ArrowUp' })
      Object.defineProperty(event, 'preventDefault', { value: vi.fn() })

      act(() => {
        mockContainer.dispatchEvent(event)
      })

      expect(mockButton1.focus).toHaveBeenCalled()
    })

    it('wraps to first element when navigating down from last', () => {
      const containerRef = { current: mockContainer }

      renderHook(() => useKeyboardNavigation(containerRef))

      // Set focus on last element
      act(() => {
        mockLink.focus()
      })

      // Simulate ArrowDown
      const event = new KeyboardEvent('keydown', { key: 'ArrowDown' })
      Object.defineProperty(event, 'preventDefault', { value: vi.fn() })

      act(() => {
        mockContainer.dispatchEvent(event)
      })

      expect(mockButton1.focus).toHaveBeenCalled()
    })

    it('wraps to last element when navigating up from first', () => {
      const containerRef = { current: mockContainer }

      renderHook(() => useKeyboardNavigation(containerRef))

      // Set focus on first element
      act(() => {
        mockButton1.focus()
      })

      // Simulate ArrowUp
      const event = new KeyboardEvent('keydown', { key: 'ArrowUp' })
      Object.defineProperty(event, 'preventDefault', { value: vi.fn() })

      act(() => {
        mockContainer.dispatchEvent(event)
      })

      expect(mockLink.focus).toHaveBeenCalled()
    })

    it('focuses first element with Home key', () => {
      const containerRef = { current: mockContainer }

      renderHook(() => useKeyboardNavigation(containerRef))

      // Set focus on middle element
      act(() => {
        mockInput.focus()
      })

      // Simulate Home
      const event = new KeyboardEvent('keydown', { key: 'Home' })
      Object.defineProperty(event, 'preventDefault', { value: vi.fn() })

      act(() => {
        mockContainer.dispatchEvent(event)
      })

      expect(mockButton1.focus).toHaveBeenCalled()
    })

    it('focuses last element with End key', () => {
      const containerRef = { current: mockContainer }

      renderHook(() => useKeyboardNavigation(containerRef))

      // Set focus on first element
      act(() => {
        mockButton1.focus()
      })

      // Simulate End
      const event = new KeyboardEvent('keydown', { key: 'End' })
      Object.defineProperty(event, 'preventDefault', { value: vi.fn() })

      act(() => {
        mockContainer.dispatchEvent(event)
      })

      expect(mockLink.focus).toHaveBeenCalled()
    })

    it('disables arrow key navigation when enableArrowKeys is false', () => {
      const containerRef = { current: mockContainer }

      renderHook(() =>
        useKeyboardNavigation(containerRef, { enableArrowKeys: false })
      )

      // Set initial focus
      act(() => {
        mockButton1.focus()
      })

      // Simulate ArrowDown
      const event = new KeyboardEvent('keydown', { key: 'ArrowDown' })
      const preventDefault = vi.fn()
      Object.defineProperty(event, 'preventDefault', { value: preventDefault })

      act(() => {
        mockContainer.dispatchEvent(event)
      })

      expect(preventDefault).not.toHaveBeenCalled()
      expect(mockButton2.focus).not.toHaveBeenCalled()
    })
  })

  describe('Tab key handling', () => {
    it('handles Tab key when enableTabKeys is true', () => {
      const containerRef = { current: mockContainer }

      renderHook(() => useKeyboardNavigation(containerRef))

      // Simulate Tab
      const event = new KeyboardEvent('keydown', { key: 'Tab' })

      act(() => {
        mockContainer.dispatchEvent(event)
      })

      // Should not prevent default for Tab
      expect(event.defaultPrevented).toBe(false)
    })

    it('ignores Tab key when enableTabKeys is false', () => {
      const containerRef = { current: mockContainer }

      renderHook(() =>
        useKeyboardNavigation(containerRef, { enableTabKeys: false })
      )

      // Tab handling should be disabled, but this doesn't change much in the test
      // since we don't prevent default anyway
      const event = new KeyboardEvent('keydown', { key: 'Tab' })

      expect(() => {
        act(() => {
          mockContainer.dispatchEvent(event)
        })
      }).not.toThrow()
    })
  })

  describe('Enter key handling', () => {
    it('calls onEnter callback when Enter is pressed', () => {
      const onEnter = vi.fn()
      const containerRef = { current: mockContainer }

      renderHook(() =>
        useKeyboardNavigation(containerRef, { onEnter })
      )

      // Set focus on a button
      act(() => {
        mockButton1.focus()
      })

      // Simulate Enter
      const event = new KeyboardEvent('keydown', { key: 'Enter' })
      Object.defineProperty(event, 'preventDefault', { value: vi.fn() })

      act(() => {
        mockContainer.dispatchEvent(event)
      })

      expect(onEnter).toHaveBeenCalled()
    })

    it('does not call onEnter when focused element is not in container', () => {
      const onEnter = vi.fn()
      const containerRef = { current: mockContainer }

      renderHook(() =>
        useKeyboardNavigation(containerRef, { onEnter })
      )

      // Focus external element
      const externalButton = document.createElement('button')
      document.body.appendChild(externalButton)
      externalButton.focus = vi.fn(() => {
        Object.defineProperty(document, 'activeElement', {
          value: externalButton,
          configurable: true,
        })
      })

      act(() => {
        externalButton.focus()
      })

      // Simulate Enter
      const event = new KeyboardEvent('keydown', { key: 'Enter' })

      act(() => {
        mockContainer.dispatchEvent(event)
      })

      expect(onEnter).not.toHaveBeenCalled()
    })

    it('ignores Enter when enableEnterKey is false', () => {
      const onEnter = vi.fn()
      const containerRef = { current: mockContainer }

      renderHook(() =>
        useKeyboardNavigation(containerRef, {
          enableEnterKey: false,
          onEnter
        })
      )

      // Set focus on a button
      act(() => {
        mockButton1.focus()
      })

      // Simulate Enter
      const event = new KeyboardEvent('keydown', { key: 'Enter' })

      act(() => {
        mockContainer.dispatchEvent(event)
      })

      expect(onEnter).not.toHaveBeenCalled()
    })
  })

  describe('Escape key handling', () => {
    it('calls onEscape callback when Escape is pressed', () => {
      const onEscape = vi.fn()
      const containerRef = { current: mockContainer }

      renderHook(() =>
        useKeyboardNavigation(containerRef, { onEscape })
      )

      // Simulate Escape
      const event = new KeyboardEvent('keydown', { key: 'Escape' })
      Object.defineProperty(event, 'preventDefault', { value: vi.fn() })

      act(() => {
        mockContainer.dispatchEvent(event)
      })

      expect(onEscape).toHaveBeenCalled()
    })

    it('does not call onEscape when enableEscapeKey is false', () => {
      const onEscape = vi.fn()
      const containerRef = { current: mockContainer }

      renderHook(() =>
        useKeyboardNavigation(containerRef, {
          enableEscapeKey: false,
          onEscape
        })
      )

      // Simulate Escape
      const event = new KeyboardEvent('keydown', { key: 'Escape' })

      act(() => {
        mockContainer.dispatchEvent(event)
      })

      expect(onEscape).not.toHaveBeenCalled()
    })

    it('does not call onEscape when no callback provided', () => {
      const containerRef = { current: mockContainer }

      expect(() => {
        renderHook(() => useKeyboardNavigation(containerRef))

        // Simulate Escape
        const event = new KeyboardEvent('keydown', { key: 'Escape' })

        act(() => {
          mockContainer.dispatchEvent(event)
        })
      }).not.toThrow()
    })
  })

  describe('Utility functions', () => {
    it('provides focusFirst function', () => {
      const containerRef = { current: mockContainer }

      const { result } = renderHook(() =>
        useKeyboardNavigation(containerRef)
      )

      act(() => {
        result.current.focusFirst()
      })

      expect(mockButton1.focus).toHaveBeenCalled()
    })

    it('provides focusLast function', () => {
      const containerRef = { current: mockContainer }

      const { result } = renderHook(() =>
        useKeyboardNavigation(containerRef)
      )

      act(() => {
        result.current.focusLast()
      })

      expect(mockLink.focus).toHaveBeenCalled()
    })

    it('handles empty container in utility functions', () => {
      const emptyContainer = document.createElement('div')
      const containerRef = { current: emptyContainer }

      const { result } = renderHook(() =>
        useKeyboardNavigation(containerRef)
      )

      expect(() => {
        act(() => {
          result.current.focusFirst()
          result.current.focusLast()
        })
      }).not.toThrow()
    })
  })

  describe('Focusable element detection', () => {
    it('includes various focusable element types', () => {
      const container = document.createElement('div')

      // Add different focusable elements
      const button = document.createElement('button')
      const input = document.createElement('input')
      const select = document.createElement('select')
      const textarea = document.createElement('textarea')
      const link = document.createElement('a')
      link.href = '#'
      const tabindexDiv = document.createElement('div')
      tabindexDiv.tabIndex = 0
      const contentEditable = document.createElement('div')
      contentEditable.contentEditable = 'true'

      container.appendChild(button)
      container.appendChild(input)
      container.appendChild(select)
      container.appendChild(textarea)
      container.appendChild(link)
      container.appendChild(tabindexDiv)
      container.appendChild(contentEditable)

      const containerRef = { current: container }

      const { result } = renderHook(() =>
        useKeyboardNavigation(containerRef)
      )

      expect(result.current.focusableElements).toHaveLength(7)
    })

    it('excludes disabled elements', () => {
      const container = document.createElement('div')

      const enabledButton = document.createElement('button')
      const disabledButton = document.createElement('button')
      disabledButton.disabled = true

      const enabledInput = document.createElement('input')
      const disabledInput = document.createElement('input')
      disabledInput.disabled = true

      container.appendChild(enabledButton)
      container.appendChild(disabledButton)
      container.appendChild(enabledInput)
      container.appendChild(disabledInput)

      const containerRef = { current: container }

      const { result } = renderHook(() =>
        useKeyboardNavigation(containerRef)
      )

      expect(result.current.focusableElements).toHaveLength(2)
    })

    it('excludes hidden elements', () => {
      const container = document.createElement('div')

      const visibleButton = document.createElement('button')
      const hiddenButton = document.createElement('button')
      const invisibleButton = document.createElement('button')

      container.appendChild(visibleButton)
      container.appendChild(hiddenButton)
      container.appendChild(invisibleButton)

      // Mock getComputedStyle for different visibility states
      mockGetComputedStyle.mockImplementation((element) => {
        if (element === hiddenButton) {
          return { display: 'none', visibility: 'visible' }
        }
        if (element === invisibleButton) {
          return { display: 'block', visibility: 'hidden' }
        }
        return { display: 'block', visibility: 'visible' }
      })

      const containerRef = { current: container }

      const { result } = renderHook(() =>
        useKeyboardNavigation(containerRef)
      )

      expect(result.current.focusableElements).toHaveLength(1)
    })

    it('excludes elements with tabindex="-1"', () => {
      const container = document.createElement('div')

      const normalDiv = document.createElement('div')
      normalDiv.tabIndex = 0

      const negativeTabindexDiv = document.createElement('div')
      negativeTabindexDiv.tabIndex = -1

      container.appendChild(normalDiv)
      container.appendChild(negativeTabindexDiv)

      const containerRef = { current: container }

      const { result } = renderHook(() =>
        useKeyboardNavigation(containerRef)
      )

      expect(result.current.focusableElements).toHaveLength(1)
    })
  })

  describe('Cleanup', () => {
    it('removes event listener on unmount', () => {
      const containerRef = { current: mockContainer }
      const removeEventListenerSpy = vi.spyOn(mockContainer, 'removeEventListener')

      const { unmount } = renderHook(() =>
        useKeyboardNavigation(containerRef)
      )

      unmount()

      expect(removeEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function))
    })

    it('handles null container during cleanup', () => {
      const containerRef = { current: mockContainer }

      const { unmount } = renderHook(() =>
        useKeyboardNavigation(containerRef)
      )

      // Set container to null before unmount
      containerRef.current = null

      expect(() => unmount()).not.toThrow()
    })
  })

  describe('Options changes', () => {
    it('re-initializes when options change', () => {
      const containerRef = { current: mockContainer }

      const { rerender } = renderHook(
        ({ autoFocus }) => useKeyboardNavigation(containerRef, { autoFocus }),
        { initialProps: { autoFocus: false } }
      )

      expect(mockButton1.focus).not.toHaveBeenCalled()

      rerender({ autoFocus: true })

      expect(mockButton1.focus).toHaveBeenCalled()
    })
  })
})
