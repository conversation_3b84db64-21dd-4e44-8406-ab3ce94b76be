import { useAuth } from '@clerk/clerk-react'
import { renderHook, waitFor, act } from '@testing-library/react'
import { describe, expect, it, vi, beforeEach, afterEach, Mock } from 'vitest'

import { useTokenStatus } from './useTokenStatus'

// Mock Clerk auth
vi.mock('@clerk/clerk-react', () => ({
  useAuth: vi.fn(),
}))

describe('useTokenStatus Hook', () => {
  const mockUseAuth = useAuth as Mock
  let mockGetToken: Mock

  beforeEach(() => {
    vi.useFakeTimers()
    mockGetToken = vi.fn()
    mockUseAuth.mockReturnValue({
      getToken: mockGetToken,
    })
  })

  afterEach(() => {
    vi.runOnlyPendingTimers()
    vi.useRealTimers()
    vi.clearAllMocks()
  })

  describe('Initial State', () => {
    it('should return initial token status state', () => {
      mockGetToken.mockResolvedValue(null)

      const { result } = renderHook(() => useTokenStatus())

      expect(result.current.isValid).toBe(false)
      expect(result.current.expiresAt).toBeNull()
      expect(result.current.timeUntilExpiration).toBeNull()
      expect(result.current.needsRefresh).toBe(false)
      expect(typeof result.current.refreshStatus).toBe('function')
    })
  })

  describe('Token Validation', () => {
    it('should return invalid status when no token is available', async () => {
      mockGetToken.mockResolvedValue(null)

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(false)
        expect(result.current.expiresAt).toBeNull()
        expect(result.current.timeUntilExpiration).toBeNull()
        expect(result.current.needsRefresh).toBe(false)
      })
    })

    it('should return valid status for a valid token', async () => {
      const futureTime = Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
      const mockToken = createMockJWT({ exp: futureTime })
      mockGetToken.mockResolvedValue(mockToken)

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(true)
        expect(result.current.expiresAt).toBe(futureTime * 1000)
        expect(result.current.timeUntilExpiration).toBeGreaterThan(0)
        expect(result.current.needsRefresh).toBe(false)
      })
    })

    it('should return invalid status for an expired token', async () => {
      const pastTime = Math.floor(Date.now() / 1000) - 3600 // 1 hour ago
      const mockToken = createMockJWT({ exp: pastTime })
      mockGetToken.mockResolvedValue(mockToken)

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(false)
        expect(result.current.expiresAt).toBe(pastTime * 1000)
        expect(result.current.timeUntilExpiration).toBeLessThan(0)
        expect(result.current.needsRefresh).toBe(false)
      })
    })

    it('should indicate needs refresh when token expires in less than 5 minutes', async () => {
      const nearExpiry = Math.floor(Date.now() / 1000) + 240 // 4 minutes from now
      const mockToken = createMockJWT({ exp: nearExpiry })
      mockGetToken.mockResolvedValue(mockToken)

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(true)
        expect(result.current.needsRefresh).toBe(true)
        expect(result.current.timeUntilExpiration).toBeLessThan(5 * 60 * 1000)
      })
    })

    it('should not indicate needs refresh when token has more than 5 minutes remaining', async () => {
      const futureTime = Math.floor(Date.now() / 1000) + 600 // 10 minutes from now
      const mockToken = createMockJWT({ exp: futureTime })
      mockGetToken.mockResolvedValue(mockToken)

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(true)
        expect(result.current.needsRefresh).toBe(false)
        expect(result.current.timeUntilExpiration).toBeGreaterThan(5 * 60 * 1000)
      })
    })
  })

  describe('Token Format Validation', () => {
    it('should handle malformed tokens gracefully', async () => {
      mockGetToken.mockResolvedValue('invalid-token')

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(false)
        expect(result.current.expiresAt).toBeNull()
      })
    })

    it('should handle tokens with missing parts', async () => {
      mockGetToken.mockResolvedValue('header.payload') // Missing signature

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(false)
        expect(result.current.expiresAt).toBeNull()
      })
    })

    it('should handle tokens with invalid payload encoding', async () => {
      const invalidToken = 'header.invalid-base64.signature'
      mockGetToken.mockResolvedValue(invalidToken)

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(false)
        expect(result.current.expiresAt).toBeNull()
      })
    })

    it('should handle tokens without expiration claim', async () => {
      const mockToken = createMockJWT({}) // No exp claim
      mockGetToken.mockResolvedValue(mockToken)

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(false)
        expect(result.current.expiresAt).toBeNull()
      })
    })
  })

  describe('Periodic Status Checking', () => {
    it('should check token status periodically', async () => {
      const futureTime = Math.floor(Date.now() / 1000) + 3600
      const mockToken = createMockJWT({ exp: futureTime })
      mockGetToken.mockResolvedValue(mockToken)

      renderHook(() => useTokenStatus())

      // Initial call
      expect(mockGetToken).toHaveBeenCalledTimes(1)

      // Advance timer by 1 minute
      act(() => {
        vi.advanceTimersByTime(60 * 1000)
      })

      await waitFor(() => {
        expect(mockGetToken).toHaveBeenCalledTimes(2)
      })

      // Advance timer by another minute
      act(() => {
        vi.advanceTimersByTime(60 * 1000)
      })

      await waitFor(() => {
        expect(mockGetToken).toHaveBeenCalledTimes(3)
      })
    })

    it('should stop checking when component unmounts', async () => {
      const futureTime = Math.floor(Date.now() / 1000) + 3600
      const mockToken = createMockJWT({ exp: futureTime })
      mockGetToken.mockResolvedValue(mockToken)

      const { unmount } = renderHook(() => useTokenStatus())

      // Initial call
      expect(mockGetToken).toHaveBeenCalledTimes(1)

      unmount()

      // Advance timer - should not trigger additional calls
      act(() => {
        vi.advanceTimersByTime(60 * 1000)
      })

      // Wait a bit to ensure no additional calls
      await new Promise(resolve => setTimeout(resolve, 100))

      expect(mockGetToken).toHaveBeenCalledTimes(1)
    })
  })

  describe('Manual Refresh', () => {
    it('should allow manual token status refresh', async () => {
      const futureTime = Math.floor(Date.now() / 1000) + 3600
      const mockToken = createMockJWT({ exp: futureTime })
      mockGetToken.mockResolvedValue(mockToken)

      const { result } = renderHook(() => useTokenStatus())

      // Wait for initial call
      await waitFor(() => {
        expect(mockGetToken).toHaveBeenCalledTimes(1)
      })

      // Call manual refresh
      await act(async () => {
        await result.current.refreshStatus()
      })

      expect(mockGetToken).toHaveBeenCalledTimes(2)
    })

    it('should update status after manual refresh', async () => {
      // Initially return valid token
      const futureTime = Math.floor(Date.now() / 1000) + 3600
      const validToken = createMockJWT({ exp: futureTime })
      mockGetToken.mockResolvedValue(validToken)

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(true)
      })

      // Change to return null token
      mockGetToken.mockResolvedValue(null)

      // Manual refresh
      await act(async () => {
        await result.current.refreshStatus()
      })

      expect(result.current.isValid).toBe(false)
      expect(result.current.expiresAt).toBeNull()
    })
  })

  describe('Error Handling', () => {
    it('should handle getToken errors gracefully', async () => {
      mockGetToken.mockRejectedValue(new Error('Token fetch failed'))

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(false)
        expect(result.current.expiresAt).toBeNull()
        expect(result.current.timeUntilExpiration).toBeNull()
        expect(result.current.needsRefresh).toBe(false)
      })
    })

    it('should continue periodic checks after errors', async () => {
      // First call fails
      mockGetToken.mockRejectedValueOnce(new Error('Network error'))

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(false)
      })

      // Second call succeeds
      const futureTime = Math.floor(Date.now() / 1000) + 3600
      const mockToken = createMockJWT({ exp: futureTime })
      mockGetToken.mockResolvedValue(mockToken)

      act(() => {
        vi.advanceTimersByTime(60 * 1000)
      })

      await waitFor(() => {
        expect(result.current.isValid).toBe(true)
      })
    })

    it('should handle JSON parsing errors in token payload', async () => {
      // Create token with invalid JSON in payload
      const invalidPayload = btoa('invalid-json{')
      const invalidToken = `header.${invalidPayload}.signature`
      mockGetToken.mockResolvedValue(invalidToken)

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(false)
        expect(result.current.expiresAt).toBeNull()
      })
    })
  })

  describe('Edge Cases', () => {
    it('should handle token expiration during hook lifecycle', async () => {
      // Token expires in 30 seconds
      const nearExpiry = Math.floor(Date.now() / 1000) + 30
      const mockToken = createMockJWT({ exp: nearExpiry })
      mockGetToken.mockResolvedValue(mockToken)

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(true)
        expect(result.current.needsRefresh).toBe(true)
      })

      // Advance time past expiration
      act(() => {
        vi.advanceTimersByTime(60 * 1000) // 1 minute
      })

      await waitFor(() => {
        expect(result.current.isValid).toBe(false)
      })
    })

    it('should handle very large expiration times', async () => {
      // Token expires in 100 years
      const farFuture = Math.floor(Date.now() / 1000) + (100 * 365 * 24 * 60 * 60)
      const mockToken = createMockJWT({ exp: farFuture })
      mockGetToken.mockResolvedValue(mockToken)

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(true)
        expect(result.current.needsRefresh).toBe(false)
        expect(result.current.timeUntilExpiration).toBeGreaterThan(0)
      })
    })

    it('should handle clock skew scenarios', async () => {
      // Token appears to expire in the past due to clock skew
      const slightlyPast = Math.floor(Date.now() / 1000) - 5
      const mockToken = createMockJWT({ exp: slightlyPast })
      mockGetToken.mockResolvedValue(mockToken)

      const { result } = renderHook(() => useTokenStatus())

      await waitFor(() => {
        expect(result.current.isValid).toBe(false)
        expect(result.current.timeUntilExpiration).toBeLessThan(0)
      })
    })
  })
})

// Helper function to create mock JWT tokens
function createMockJWT(payload: Record<string, unknown>): string {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }))
  const encodedPayload = btoa(JSON.stringify(payload))
  const signature = 'mock-signature'
  
  return `${header}.${encodedPayload}.${signature}`
}