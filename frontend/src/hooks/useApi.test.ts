import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook, waitFor } from '@testing-library/react';
import React, { ReactNode } from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// useApiClient is mocked below, not imported directly

import {
  useApiMutation,
  useApiQuery,
  useDelete,
  useGet,
  usePatch,
  usePost,
  usePut,
} from './useApi';

// Mock the API client
const mockApiClient = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn(),
};

vi.mock('@services/api-client', () => ({
  useApiClient: vi.fn(() => mockApiClient),
}));

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  });

  const Wrapper = ({ children }: { children: ReactNode }) =>
    React.createElement(QueryClientProvider, { client: queryClient }, children);

  Wrapper.displayName = 'TestWrapper';
  return Wrapper;
};

describe('useApi hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('useApiQuery', () => {
    it('makes GET request with correct parameters', async () => {
      const mockData = { id: 1, name: 'Test' };
      mockApiClient.get.mockResolvedValue(mockData);

      const { result } = renderHook(() => useApiQuery('test-key', '/api/test'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApiClient.get).toHaveBeenCalledWith('/api/test');
      expect(result.current.data).toEqual(mockData);
    });

    it('handles string query key', async () => {
      mockApiClient.get.mockResolvedValue({ data: 'test' });

      const { result } = renderHook(() => useApiQuery('test-key', '/api/test'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApiClient.get).toHaveBeenCalledWith('/api/test');
    });

    it('handles array query key', async () => {
      mockApiClient.get.mockResolvedValue({ data: 'test' });

      const { result } = renderHook(() => useApiQuery(['test', 'key'], '/api/test'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApiClient.get).toHaveBeenCalledWith('/api/test');
    });

    it('passes through query options', async () => {
      mockApiClient.get.mockResolvedValue({ data: 'test' });

      const { result } = renderHook(
        () => useApiQuery('test-key', '/api/test', { enabled: false }),
        { wrapper: createWrapper() },
      );

      expect(result.current.isLoading).toBe(false);
      expect(result.current.isFetching).toBe(false);
      expect(mockApiClient.get).not.toHaveBeenCalled();
    });

    it('handles API errors', async () => {
      const error = new Error('API Error');
      mockApiClient.get.mockRejectedValue(error);

      const { result } = renderHook(() => useApiQuery('test-key', '/api/test'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(error);
    });
  });

  describe('useApiMutation', () => {
    it('executes mutation function with correct parameters', async () => {
      const mockResponse = { success: true };
      const mutationFn = vi.fn().mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useApiMutation(mutationFn), { wrapper: createWrapper() });

      const testData = { name: 'test' };
      result.current.mutate(testData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mutationFn).toHaveBeenCalledWith(testData);
      expect(result.current.data).toEqual(mockResponse);
    });

    it('passes through mutation options', async () => {
      const onSuccess = vi.fn();
      const mutationFn = vi.fn().mockResolvedValue({ success: true });

      const { result } = renderHook(() => useApiMutation(mutationFn, { onSuccess }), {
        wrapper: createWrapper(),
      });

      result.current.mutate({ test: 'data' });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(onSuccess).toHaveBeenCalledWith({ success: true }, { test: 'data' }, undefined);
    });

    it('handles mutation errors', async () => {
      const error = new Error('Mutation Error');
      const mutationFn = vi.fn().mockRejectedValue(error);

      const { result } = renderHook(() => useApiMutation(mutationFn), { wrapper: createWrapper() });

      result.current.mutate({ test: 'data' });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(error);
    });
  });

  describe('useGet', () => {
    it('is an alias for useApiQuery', async () => {
      const mockData = { id: 1, name: 'Test' };
      mockApiClient.get.mockResolvedValue(mockData);

      const { result } = renderHook(() => useGet('test-key', '/api/test'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApiClient.get).toHaveBeenCalledWith('/api/test');
      expect(result.current.data).toEqual(mockData);
    });
  });

  describe('usePost', () => {
    it('creates POST mutation with correct endpoint', async () => {
      const mockResponse = { id: 1, created: true };
      mockApiClient.post.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => usePost('/api/users'), { wrapper: createWrapper() });

      const testData = { name: 'John', email: '<EMAIL>' };
      result.current.mutate(testData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApiClient.post).toHaveBeenCalledWith('/api/users', testData);
      expect(result.current.data).toEqual(mockResponse);
    });

    it('passes through mutation options', async () => {
      const onSuccess = vi.fn();
      mockApiClient.post.mockResolvedValue({ success: true });

      const { result } = renderHook(() => usePost('/api/users', { onSuccess }), {
        wrapper: createWrapper(),
      });

      result.current.mutate({ name: 'John' });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(onSuccess).toHaveBeenCalled();
    });
  });

  describe('usePut', () => {
    it('creates PUT mutation with correct endpoint', async () => {
      const mockResponse = { id: 1, updated: true };
      mockApiClient.put.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => usePut('/api/users/1'), { wrapper: createWrapper() });

      const testData = { name: 'John Updated' };
      result.current.mutate(testData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApiClient.put).toHaveBeenCalledWith('/api/users/1', testData);
      expect(result.current.data).toEqual(mockResponse);
    });
  });

  describe('usePatch', () => {
    it('creates PATCH mutation with correct endpoint', async () => {
      const mockResponse = { id: 1, patched: true };
      mockApiClient.patch.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => usePatch('/api/users/1'), { wrapper: createWrapper() });

      const testData = { email: '<EMAIL>' };
      result.current.mutate(testData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApiClient.patch).toHaveBeenCalledWith('/api/users/1', testData);
      expect(result.current.data).toEqual(mockResponse);
    });
  });

  describe('useDelete', () => {
    it('creates DELETE mutation with correct endpoint', async () => {
      const mockResponse = { deleted: true };
      mockApiClient.delete.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useDelete('/api/users'), { wrapper: createWrapper() });

      result.current.mutate('123');

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApiClient.delete).toHaveBeenCalledWith('/api/users/123');
      expect(result.current.data).toEqual(mockResponse);
    });

    it('handles delete with complex IDs', async () => {
      mockApiClient.delete.mockResolvedValue({ deleted: true });

      const { result } = renderHook(() => useDelete('/api/users'), { wrapper: createWrapper() });

      result.current.mutate('user_abc123');

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockApiClient.delete).toHaveBeenCalledWith('/api/users/user_abc123');
    });
  });

  describe('Type safety', () => {
    it('maintains type safety for response data', async () => {
      interface User {
        id: number;
        name: string;
        email: string;
      }

      const mockUser: User = { id: 1, name: 'John', email: '<EMAIL>' };
      mockApiClient.get.mockResolvedValue(mockUser);

      const { result } = renderHook(() => useGet<User>('user', '/api/users/1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      // Type should be inferred correctly
      expect(result.current.data?.id).toBe(1);
      expect(result.current.data?.name).toBe('John');
      expect(result.current.data?.email).toBe('<EMAIL>');
    });

    it('maintains type safety for mutation variables', async () => {
      interface CreateUserRequest {
        name: string;
        email: string;
      }

      interface CreateUserResponse {
        id: number;
        success: boolean;
      }

      const mockResponse: CreateUserResponse = { id: 1, success: true };
      mockApiClient.post.mockResolvedValue(mockResponse);

      const { result } = renderHook(
        () => usePost<CreateUserResponse, CreateUserRequest>('/api/users'),
        { wrapper: createWrapper() },
      );

      const userData: CreateUserRequest = { name: 'John', email: '<EMAIL>' };
      result.current.mutate(userData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data?.id).toBe(1);
      expect(result.current.data?.success).toBe(true);
    });
  });

  describe('Error handling', () => {
    it('handles network errors in queries', async () => {
      const networkError = new Error('Network Error');
      mockApiClient.get.mockRejectedValue(networkError);

      const { result } = renderHook(() => useGet('test', '/api/test'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(networkError);
    });

    it('handles API errors in mutations', async () => {
      const apiError = new Error('Validation Failed');
      mockApiClient.post.mockRejectedValue(apiError);

      const { result } = renderHook(() => usePost('/api/users'), { wrapper: createWrapper() });

      result.current.mutate({ name: 'Invalid' });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(apiError);
    });
  });

  describe('Integration scenarios', () => {
    it('handles query invalidation after mutation', async () => {
      const queryClient = new QueryClient({
        defaultOptions: { queries: { retry: false }, mutations: { retry: false } },
      });

      const wrapper = ({ children }: { children: ReactNode }) => {
        return React.createElement(QueryClientProvider, { client: queryClient }, children);
      };

      // Initial query
      mockApiClient.get.mockResolvedValue([{ id: 1, name: 'User 1' }]);

      const { result: queryResult } = renderHook(() => useGet('users', '/api/users'), { wrapper });

      await waitFor(() => {
        expect(queryResult.current.isSuccess).toBe(true);
      });

      // Mutation with invalidation
      mockApiClient.post.mockResolvedValue({ id: 2, name: 'User 2' });

      const { result: mutationResult } = renderHook(
        () =>
          usePost('/api/users', {
            onSuccess: () => {
              queryClient.invalidateQueries({ queryKey: ['users'] });
            },
          }),
        { wrapper },
      );

      mutationResult.current.mutate({ name: 'User 2' });

      await waitFor(() => {
        expect(mutationResult.current.isSuccess).toBe(true);
      });

      // Query should be invalidated
      expect(queryResult.current.isStale).toBe(true);
    });
  });
});
