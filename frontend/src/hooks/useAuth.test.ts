import { useAuth as useClerkAuth, useOrganization, useOrganizationList, useUser } from '@clerk/clerk-react'
import { renderHook } from '@testing-library/react'
import { beforeEach, describe, expect, it, vi, Mock } from 'vitest'

import { ROLE_PERMISSIONS } from '@/constants/permissions'

import { useAuth } from './useAuth'

// Mock Clerk hooks
vi.mock('@clerk/clerk-react', () => ({
  useAuth: vi.fn(),
  useUser: vi.fn(),
  useOrganization: vi.fn(),
  useOrganizationList: vi.fn(),
}))

// Mock API client
vi.mock('@/services/enhanced-api-client', () => ({
  useApiClient: vi.fn(() => ({
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    patch: vi.fn(),
  })),
}))

describe('useAuth Hook', () => {
  const mockClerkAuth = useClerkAuth as Mock
  const mockUseUser = useUser as Mock
  const mockUseOrganization = useOrganization as Mock
  const mockUseOrganizationList = useOrganizationList as Mock

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Authentication State', () => {
    it('should return loading state when auth is not loaded', () => {
      mockClerkAuth.mockReturnValue({
        isLoaded: false,
        isSignedIn: false,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: false,
        user: null,
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: false,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: false,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      expect(result.current.isLoaded).toBe(false)
      expect(result.current.isSignedIn).toBe(false)
      expect(result.current.userProfile).toBeNull()
    })

    it('should return signed out state when user is not authenticated', () => {
      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: false,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: null,
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      expect(result.current.isLoaded).toBe(true)
      expect(result.current.isSignedIn).toBe(false)
      expect(result.current.userProfile).toBeNull()
      expect(result.current.permissions).toEqual([])
      expect(result.current.organizationPermissions).toEqual([])
    })

    it('should return authenticated state with user profile', () => {
      const mockUser = {
        id: 'user_123',
        emailAddresses: [{ emailAddress: '<EMAIL>' }],
        firstName: 'John',
        lastName: 'Doe',
        imageUrl: 'https://example.com/avatar.jpg',
        publicMetadata: {
          memberType: 'MSR',
          role: 'MSR',
          permissions: ['view_dashboard', 'edit_profile'],
        },
      }

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: mockUser,
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      expect(result.current.isLoaded).toBe(true)
      expect(result.current.isSignedIn).toBe(true)
      expect(result.current.userProfile).toEqual({
        id: 'user_123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        imageUrl: 'https://example.com/avatar.jpg',
        memberType: 'MSR',
        role: 'MSR',
        permissions: ['view_dashboard', 'edit_profile'],
      })
    })
  })

  describe('Permission System', () => {
    it('should return permissions from user metadata', () => {
      const mockUser = {
        id: 'user_123',
        emailAddresses: [{ emailAddress: '<EMAIL>' }],
        publicMetadata: {
          memberType: 'MSR',
          permissions: ['view_dashboard', 'edit_profile', 'manage_members'],
        },
      }

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: mockUser,
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      expect(result.current.permissions).toEqual(['view_dashboard', 'edit_profile', 'manage_members'])
    })

    it('should fallback to role-based permissions when user permissions are not available', () => {
      const mockUser = {
        id: 'user_123',
        emailAddresses: [{ emailAddress: '<EMAIL>' }],
        publicMetadata: {
          memberType: 'MSR',
          role: 'MSR',
        },
      }

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: mockUser,
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      expect(result.current.permissions).toEqual(ROLE_PERMISSIONS.MSR)
    })

    it('should return empty permissions when role is not recognized', () => {
      const mockUser = {
        id: 'user_123',
        emailAddresses: [{ emailAddress: '<EMAIL>' }],
        publicMetadata: {
          memberType: 'UNKNOWN_ROLE',
          role: 'UNKNOWN_ROLE',
        },
      }

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: mockUser,
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      expect(result.current.permissions).toEqual([])
    })
  })

  describe('Organization Management', () => {
    it('should return organization data when user is in an organization', () => {
      const mockOrganization = {
        id: 'org_123',
        name: 'Test Organization',
        slug: 'test-org',
        imageUrl: 'https://example.com/org-logo.jpg',
      }

      const mockMembership = {
        id: 'mem_123',
        role: 'admin',
        publicMetadata: {
          permissions: ['org_admin', 'manage_billing'],
        },
      }

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: {
          id: 'user_123',
          emailAddresses: [{ emailAddress: '<EMAIL>' }],
          publicMetadata: { memberType: 'MSR' },
        },
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: mockOrganization,
        membership: mockMembership,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [
          {
            organization: mockOrganization,
            membership: mockMembership,
          },
        ],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      expect(result.current.organization).toEqual(mockOrganization)
      expect(result.current.activeOrganizationRole).toBe('admin')
      expect(result.current.organizationPermissions).toEqual(['org_admin', 'manage_billing'])
      expect(result.current.organizationMemberships).toHaveLength(1)
    })

    it('should handle organization switching', async () => {
      const mockSetActive = vi.fn().mockResolvedValue(undefined)

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: {
          id: 'user_123',
          emailAddresses: [{ emailAddress: '<EMAIL>' }],
          publicMetadata: { memberType: 'MSR' },
        },
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: mockSetActive,
      })

      const { result } = renderHook(() => useAuth())

      await result.current.switchOrganization('org_456')

      expect(mockSetActive).toHaveBeenCalledWith({ organization: 'org_456' })
    })

    it('should handle organization creation', async () => {
      const mockCreateOrganization = vi.fn().mockResolvedValue({
        id: 'org_new',
        name: 'New Organization',
      })

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: {
          id: 'user_123',
          emailAddresses: [{ emailAddress: '<EMAIL>' }],
          publicMetadata: { memberType: 'MSR' },
        },
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: mockCreateOrganization,
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      await result.current.createOrganization('New Organization')

      expect(mockCreateOrganization).toHaveBeenCalledWith({ name: 'New Organization' })
    })

    it('should handle leaving organization', async () => {
      const mockSetActive = vi.fn().mockResolvedValue(undefined)

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: {
          id: 'user_123',
          emailAddresses: [{ emailAddress: '<EMAIL>' }],
          publicMetadata: { memberType: 'MSR' },
        },
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: {
          id: 'org_123',
          name: 'Current Org',
        },
        membership: { role: 'member' },
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: mockSetActive,
      })

      const { result } = renderHook(() => useAuth())

      await result.current.leaveOrganization('org_123')

      expect(mockSetActive).toHaveBeenCalledWith({ organization: null })
    })
  })

  describe('Token Management', () => {
    it('should return token status based on Clerk auth state', () => {
      const mockGetToken = vi.fn().mockResolvedValue('mock-jwt-token')

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: mockGetToken,
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: {
          id: 'user_123',
          emailAddresses: [{ emailAddress: '<EMAIL>' }],
          publicMetadata: { memberType: 'MSR' },
        },
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      expect(result.current.tokenStatus).toBe('valid')
    })

    it('should return invalid token status when not signed in', () => {
      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: false,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: null,
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      expect(result.current.tokenStatus).toBe('invalid')
    })

    it('should handle token refresh', async () => {
      const mockGetToken = vi.fn().mockResolvedValue('refreshed-jwt-token')

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: mockGetToken,
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: {
          id: 'user_123',
          emailAddresses: [{ emailAddress: '<EMAIL>' }],
          publicMetadata: { memberType: 'MSR' },
        },
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      const token = await result.current.refreshToken()

      expect(mockGetToken).toHaveBeenCalledWith({ skipCache: true })
      expect(token).toBe('refreshed-jwt-token')
    })
  })

  describe('Profile Synchronization', () => {
    it('should handle profile refresh', async () => {
      const mockReload = vi.fn().mockResolvedValue(undefined)
      const mockUser = {
        id: 'user_123',
        emailAddresses: [{ emailAddress: '<EMAIL>' }],
        publicMetadata: { memberType: 'MSR' },
        reload: mockReload,
      }

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: mockUser,
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      await result.current.refreshProfile()

      expect(mockReload).toHaveBeenCalled()
    })

    it('should handle sign out', async () => {
      const mockSignOut = vi.fn().mockResolvedValue(undefined)

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: mockSignOut,
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: {
          id: 'user_123',
          emailAddresses: [{ emailAddress: '<EMAIL>' }],
          publicMetadata: { memberType: 'MSR' },
        },
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      await result.current.signOut()

      expect(mockSignOut).toHaveBeenCalled()
    })
  })

  describe('Error Handling', () => {
    it('should handle organization creation errors gracefully', async () => {
      const mockCreateOrganization = vi.fn().mockRejectedValue(new Error('Organization creation failed'))

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: {
          id: 'user_123',
          emailAddresses: [{ emailAddress: '<EMAIL>' }],
          publicMetadata: { memberType: 'MSR' },
        },
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: mockCreateOrganization,
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      await expect(result.current.createOrganization('New Organization')).rejects.toThrow(
        'Organization creation failed'
      )
    })

    it('should handle token refresh errors gracefully', async () => {
      const mockGetToken = vi.fn().mockRejectedValue(new Error('Token refresh failed'))

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: mockGetToken,
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: {
          id: 'user_123',
          emailAddresses: [{ emailAddress: '<EMAIL>' }],
          publicMetadata: { memberType: 'MSR' },
        },
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      const token = await result.current.refreshToken()

      expect(token).toBeNull()
    })
  })

  describe('Edge Cases', () => {
    it('should handle missing email addresses', () => {
      const mockUser = {
        id: 'user_123',
        emailAddresses: [],
        publicMetadata: { memberType: 'MSR' },
      }

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: mockUser,
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      expect(result.current.userProfile?.email).toBe('')
    })

    it('should handle malformed public metadata', () => {
      const mockUser = {
        id: 'user_123',
        emailAddresses: [{ emailAddress: '<EMAIL>' }],
        publicMetadata: null,
      }

      mockClerkAuth.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        signOut: vi.fn(),
        getToken: vi.fn(),
      })
      mockUseUser.mockReturnValue({
        isLoaded: true,
        user: mockUser,
      })
      mockUseOrganization.mockReturnValue({
        isLoaded: true,
        organization: null,
        membership: null,
      })
      mockUseOrganizationList.mockReturnValue({
        isLoaded: true,
        organizationList: [],
        createOrganization: vi.fn(),
        setActive: vi.fn(),
      })

      const { result } = renderHook(() => useAuth())

      expect(result.current.userProfile?.memberType).toBeUndefined()
      expect(result.current.userProfile?.role).toBeUndefined()
      expect(result.current.permissions).toEqual([])
    })
  })
})