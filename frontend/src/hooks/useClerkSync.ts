import { useUser } from '@clerk/clerk-react';
import { useCallback, useEffect, useState } from 'react';

import { useApiClient } from '@services/api-client';

interface SyncStatus {
  isLoading: boolean;
  error: string | null;
  lastSyncAt: Date | null;
  isInitialSync: boolean;
}

interface UserSyncData {
  clerkId: string;
  email: string;
  firstName: string;
  lastName: string;
  imageUrl?: string;
  phoneNumber?: string;
}

export function useClerkSync(autoSync: boolean = true) {
  const { user, isLoaded } = useUser();
  const apiClient = useApiClient();
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isLoading: false,
    error: null,
    lastSyncAt: null,
    isInitialSync: true,
  });

  const syncUserWithBackend = useCallback(
    async (userData?: UserSyncData) => {
      if (!user && !userData) {
        setSyncStatus((prev) => ({ ...prev, error: 'No user data to sync' }));
        return false;
      }

      const dataToSync = userData || {
        clerkId: user!.id,
        email: user!.emailAddresses[0]?.emailAddress || '',
        firstName: user!.firstName || '',
        lastName: user!.lastName || '',
        imageUrl: user!.imageUrl,
        phoneNumber: user!.phoneNumbers[0]?.phoneNumber,
      };

      setSyncStatus((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        await apiClient.post('/clerk/sync/', dataToSync);

        setSyncStatus((prev) => ({
          ...prev,
          isLoading: false,
          lastSyncAt: new Date(),
          isInitialSync: false,
        }));

        // User sync successful
        return true;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to sync user data';
        setSyncStatus((prev) => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));

        console.error('User sync failed:', error);
        return false;
      }
    },
    [user, apiClient],
  );

  // Auto-sync on user load/change
  useEffect(() => {
    if (autoSync && isLoaded && user && syncStatus.isInitialSync) {
      syncUserWithBackend();
    }
  }, [autoSync, isLoaded, user, syncStatus.isInitialSync, syncUserWithBackend]);

  // Manual sync function
  const manualSync = () => {
    if (user) {
      return syncUserWithBackend();
    }
    return Promise.resolve(false);
  };

  // Check if user needs to be synced (e.g., profile data changed)
  const needsSync = () => {
    if (!user || !syncStatus.lastSyncAt) {
      return true;
    }

    // Check if user was updated after last sync
    const userUpdatedAt = new Date(user.updatedAt || user.createdAt);
    return userUpdatedAt > syncStatus.lastSyncAt;
  };

  return {
    syncStatus,
    syncUser: manualSync,
    needsSync: needsSync(),
  };
}

export default useClerkSync;
