import { useAuth } from '@clerk/clerk-react'
import { useState, useEffect, useCallback } from 'react'

interface TokenStatus {
  isValid: boolean
  expiresAt: number | null
  timeUntilExpiration: number | null
  needsRefresh: boolean
}

export function useTokenStatus() {
  const { getToken } = useAuth()
  const [tokenStatus, setTokenStatus] = useState<TokenStatus>({
    isValid: false,
    expiresAt: null,
    timeUntilExpiration: null,
    needsRefresh: false,
  })

  const checkTokenStatus = useCallback(async () => {
    try {
      const token = await getToken()
      
      if (!token) {
        setTokenStatus({
          isValid: false,
          expiresAt: null,
          timeUntilExpiration: null,
          needsRefresh: false,
        })
        return
      }

      // Decode token to check expiration
      const parts = token.split('.')
      if (parts.length !== 3) {return}
      
      const payload = JSON.parse(atob(parts[1]))
      const expiresAt = payload.exp * 1000 // Convert to milliseconds
      const now = Date.now()
      const timeUntilExpiration = expiresAt - now
      
      // Consider token needing refresh if less than 5 minutes remaining
      const needsRefresh = timeUntilExpiration < 5 * 60 * 1000
      
      setTokenStatus({
        isValid: timeUntilExpiration > 0,
        expiresAt,
        timeUntilExpiration,
        needsRefresh,
      })
    } catch (error) {
      console.error('Failed to check token status:', error)
      setTokenStatus({
        isValid: false,
        expiresAt: null,
        timeUntilExpiration: null,
        needsRefresh: false,
      })
    }
  }, [getToken])

  // Check token status periodically
  useEffect(() => {
    checkTokenStatus()
    
    // Check every minute
    const interval = setInterval(checkTokenStatus, 60 * 1000)
    
    return () => clearInterval(interval)
  }, [checkTokenStatus])

  return {
    ...tokenStatus,
    refreshStatus: checkTokenStatus,
  }
}