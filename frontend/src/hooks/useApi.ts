import {
  useMutation,
  useQuery,
  type UseMutationOptions,
  type UseQueryOptions,
} from '@tanstack/react-query';

import { useApiClient } from '@services/api-client';

type UseApiQueryOptions<T> = Omit<UseQueryOptions<T>, 'queryKey' | 'queryFn'>;

type UseApiMutationOptions<T, V> = Omit<UseMutationOptions<T, unknown, V>, 'mutationFn'>;

export function useApiQuery<T = unknown>(
  key: string | string[],
  endpoint: string,
  options?: UseApiQueryOptions<T>,
) {
  const apiClient = useApiClient();

  return useQuery<T>({
    queryKey: Array.isArray(key) ? key : [key],
    queryFn: () => apiClient.get<T>(endpoint),
    ...options,
  });
}

export function useApiMutation<T = unknown, V = unknown>(
  mutationFn: (variables: V) => Promise<T>,
  options?: UseApiMutationOptions<T, V>,
) {
  return useMutation<T, unknown, V>({
    mutationFn,
    ...options,
  });
}

export function useGet<T = unknown>(
  key: string | string[],
  endpoint: string,
  options?: UseApiQueryOptions<T>,
) {
  return useApiQuery<T>(key, endpoint, options);
}

export function usePost<T = unknown, V = unknown>(
  endpoint: string,
  options?: UseApiMutationOptions<T, V>,
) {
  const apiClient = useApiClient();

  return useApiMutation<T, V>((data: V) => apiClient.post<T>(endpoint, data), options);
}

export function usePut<T = unknown, V = unknown>(
  endpoint: string,
  options?: UseApiMutationOptions<T, V>,
) {
  const apiClient = useApiClient();

  return useApiMutation<T, V>((data: V) => apiClient.put<T>(endpoint, data), options);
}

export function usePatch<T = unknown, V = unknown>(
  endpoint: string,
  options?: UseApiMutationOptions<T, V>,
) {
  const apiClient = useApiClient();

  return useApiMutation<T, V>((data: V) => apiClient.patch<T>(endpoint, data), options);
}

export function useDelete<T = unknown>(
  endpoint: string,
  options?: UseApiMutationOptions<T, string>,
) {
  const apiClient = useApiClient();

  return useApiMutation<T, string>(
    (id: string) => apiClient.delete<T>(`${endpoint}/${id}`),
    options,
  );
}
