import { 
  useUser, 
  useAuth as useClerkAuth, 
  useOrganization,
  useOrganizationList 
} from '@clerk/clerk-react'
import { useCallback, useEffect, useState } from 'react'

import { useApiClient } from '@/services/enhanced-api-client'
import { useTokenInterceptor } from '@/services/tokenInterceptor'
import { ExtendedAuthState, UserProfile, ClerkOrganization } from '@/types/auth'
import { useTokenRefresh } from '@/utils/tokenManagement'

import { useTokenStatus } from './useTokenStatus'

export function useAuth(): ExtendedAuthState {
  const { isLoaded, isSignedIn, user } = useUser()
  const { signOut: clerkSignOut } = useClerkAuth()
  const { 
    organization, 
    isLoaded: isOrganizationLoaded,
    membership 
  } = useOrganization()
  
  const { 
    setActive 
  } = useOrganizationList()
  
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [permissions, setPermissions] = useState<string[]>([])
  const [organizationPermissions, setOrganizationPermissions] = useState<string[]>([])
  const [isProfileLoaded, setIsProfileLoaded] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  
  const api = useApiClient()
  const tokenStatus = useTokenStatus()
  const { refreshToken, ensureValidToken } = useTokenRefresh()
  
  // Set up automatic token refresh
  useTokenInterceptor()

  // Get organization memberships
  const organizationMemberships = user?.organizationMemberships || []
  const activeOrganizationRole = membership?.role || null

  // Fetch user profile when user is loaded
  useEffect(() => {
    if (isLoaded && isSignedIn && user && !isProfileLoaded) {
      fetchUserProfile()
    }
  }, [isLoaded, isSignedIn, user, isProfileLoaded, fetchUserProfile])

  // Fetch organization-specific permissions
  useEffect(() => {
    if (organization && userProfile) {
      fetchOrganizationPermissions(organization.id)
    }
  }, [organization, userProfile, fetchOrganizationPermissions])

  const fetchUserProfile = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      // Fetch extended user profile from your backend
      const profile = await api.get<UserProfile>('/members/me')
      setUserProfile(profile)
      setPermissions(profile.permissions || [])
      setIsProfileLoaded(true)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load profile'
      setError(errorMessage)
      console.error('Failed to fetch user profile:', error)
      setIsProfileLoaded(true) // Still mark as loaded to avoid infinite loop
    } finally {
      setIsLoading(false)
    }
  }, [api])

  const fetchOrganizationPermissions = useCallback(async (orgId: string) => {
    try {
      const permissions = await api.get<string[]>(`/organizations/${orgId}/permissions`)
      setOrganizationPermissions(permissions)
    } catch (error) {
      console.error('Failed to fetch organization permissions:', error)
      setOrganizationPermissions([])
    }
  }, [api])

  const signOut = async () => {
    try {
      await clerkSignOut()
      // Clear application state
      setUserProfile(null)
      setPermissions([])
      setIsProfileLoaded(false)
      setError(null)
    } catch (error) {
      console.error('Sign out failed:', error)
    }
  }

  const switchOrganization = async (orgId: string) => {
    try {
      await setActive({ organization: orgId })
      // Refresh user profile for new organization context
      await fetchUserProfile()
      await fetchOrganizationPermissions(orgId)
    } catch (error) {
      console.error('Failed to switch organization:', error)
      throw error
    }
  }

  const createOrganization = async (name: string) => {
    try {
      // This would typically be handled by Clerk's organization creation
      // But you might want to sync with your backend
      const org = await api.post<ClerkOrganization>('/organizations', { name })
      return org
    } catch (error) {
      console.error('Failed to create organization:', error)
      throw error
    }
  }

  const leaveOrganization = async (orgId: string) => {
    try {
      await api.delete(`/organizations/${orgId}/leave`)
      // Refresh organization list
      window.location.reload() // or use a more elegant refresh
    } catch (error) {
      console.error('Failed to leave organization:', error)
      throw error
    }
  }

  const refreshProfile = async () => {
    setIsProfileLoaded(false)
    await fetchUserProfile()
  }

  return {
    // Clerk state (pass-through)
    isLoaded,
    isSignedIn: isSignedIn ?? false,
    user,
    
    // Enhanced organization state
    organization,
    isOrganizationLoaded,
    organizationMemberships,
    activeOrganizationRole,
    organizationPermissions,
    
    // Application state
    userProfile,
    permissions,
    isProfileLoaded,
    
    // Loading and error states
    error,
    isLoading,
    
    // Token management
    tokenStatus,
    refreshToken,
    ensureValidToken,
    
    // Organization actions
    switchOrganization,
    createOrganization,
    leaveOrganization,
    
    // Actions
    signOut,
    refreshProfile,
  }
}