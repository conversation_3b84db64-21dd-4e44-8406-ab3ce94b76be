import { Permission, Role, PERMISSIONS, ROLES } from '@/constants/permissions'

export interface RouteConfig {
  path: string
  title: string
  icon?: string
  
  // Access control
  requiredPermissions?: Permission[]
  allowedRoles?: Role[]
  requireAllPermissions?: boolean
  
  // Hierarchical access
  requireManagerAccess?: boolean
  
  // Organization requirements
  requireOrganization?: boolean
  
  // Navigation
  showInNavigation?: boolean
  navigationGroup?: string
  order?: number
  
  // Child routes
  children?: RouteConfig[]
}

// Explicit exports for better module resolution  
export type { RouteConfig }

export const ROUTE_CONFIGS: RouteConfig[] = [
  {
    path: '/dashboard',
    title: 'Dashboard',
    icon: 'dashboard',
    requiredPermissions: [PERMISSIONS.VIEW_DASHBOARD],
    showInNavigation: true,
    order: 1,
  },
  
  {
    path: '/members',
    title: 'Members',
    icon: 'users',
    requiredPermissions: [PERMISSIONS.VIEW_USERS],
    showInNavigation: true,
    navigationGroup: 'Management',
    order: 2,
    children: [
      {
        path: '/members/create',
        title: 'Add Member',
        requiredPermissions: [PERMISSIONS.CREATE_USERS],
        requireManagerAccess: true,
      },
      {
        path: '/members/:id/edit',
        title: 'Edit Member',
        requiredPermissions: [PERMISSIONS.EDIT_USERS],
        requireManagerAccess: true,
      },
    ],
  },
  
  {
    path: '/teams',
    title: 'Teams',
    icon: 'team',
    requiredPermissions: [PERMISSIONS.VIEW_TEAMS],
    showInNavigation: true,
    navigationGroup: 'Management',
    order: 3,
    children: [
      {
        path: '/teams/create',
        title: 'Create Team',
        requiredPermissions: [PERMISSIONS.CREATE_TEAMS],
        allowedRoles: [ROLES.MSM, ROLES.TSM, ROLES.NSM, ROLES.ADMIN],
      },
      {
        path: '/teams/:id/manage',
        title: 'Manage Team',
        requiredPermissions: [PERMISSIONS.MANAGE_TEAMS],
        requireManagerAccess: true,
      },
    ],
  },
  
  {
    path: '/reports',
    title: 'Reports',
    icon: 'chart',
    requiredPermissions: [PERMISSIONS.VIEW_REPORTS],
    showInNavigation: true,
    navigationGroup: 'Analytics',
    order: 4,
    children: [
      {
        path: '/reports/performance',
        title: 'Performance Reports',
        requiredPermissions: [PERMISSIONS.VIEW_PERFORMANCE_REPORTS],
        allowedRoles: [ROLES.MSM, ROLES.TSM, ROLES.NSM, ROLES.ADMIN],
      },
      {
        path: '/reports/financial',
        title: 'Financial Reports',
        requiredPermissions: [PERMISSIONS.VIEW_FINANCIAL_REPORTS],
        allowedRoles: [ROLES.TSM, ROLES.NSM, ROLES.ADMIN],
      },
    ],
  },
  
  {
    path: '/hierarchy',
    title: 'Organization Chart',
    icon: 'hierarchy',
    requiredPermissions: [PERMISSIONS.VIEW_HIERARCHY],
    showInNavigation: true,
    navigationGroup: 'Management',
    order: 5,
  },
  
  {
    path: '/admin',
    title: 'Administration',
    icon: 'settings',
    allowedRoles: [ROLES.ADMIN, ROLES.SUPER_ADMIN],
    showInNavigation: true,
    navigationGroup: 'Administration',
    order: 10,
    children: [
      {
        path: '/admin/users',
        title: 'User Management',
        requiredPermissions: [PERMISSIONS.MANAGE_USER_ROLES],
      },
      {
        path: '/admin/organizations',
        title: 'Organizations',
        requiredPermissions: [PERMISSIONS.MANAGE_ORGANIZATIONS],
        allowedRoles: [ROLES.SUPER_ADMIN],
      },
    ],
  },
]