import { renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import ApiClient, { useApiClient } from './api-client';

global.fetch = vi.fn();

vi.mock('@utils/env', () => ({
  getApiUrl: vi.fn(() => 'http://localhost:8000/api'),
}));

vi.mock('@clerk/clerk-react', () => ({
  useAuth: vi.fn(() => ({
    getToken: vi.fn(() => Promise.resolve('mock-token')),
  })),
}));

describe('ApiClient', () => {
  let apiClient: ApiClient;
  const mockFetch = fetch as vi.MockedFunction<typeof fetch>;

  beforeEach(() => {
    vi.clearAllMocks();
    apiClient = new ApiClient('http://localhost:8000/api');
  });

  describe('Basic HTTP methods', () => {
    it('makes GET request successfully', async () => {
      const mockData = { id: 1, name: 'Test' };
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(mockData),
      } as Response);

      const result = await apiClient.get('/users/1');

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/users/1',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'X-Request-ID': expect.stringMatching(/^req_/),
          }),
        }),
      );
      expect(result).toEqual(mockData);
    });

    it('makes POST request with data', async () => {
      const requestData = { name: 'New User', email: '<EMAIL>' };
      const responseData = { id: 1, ...requestData };

      mockFetch.mockResolvedValue({
        ok: true,
        status: 201,
        statusText: 'Created',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(responseData),
      } as Response);

      const result = await apiClient.post('/users', requestData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/users',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: JSON.stringify({ name: 'New User', email: '<EMAIL>' }),
        }),
      );
      expect(result).toEqual(responseData);
    });

    it('makes PUT request with data', async () => {
      const requestData = { name: 'Updated User' };
      const responseData = { id: 1, name: 'Updated User', email: '<EMAIL>' };

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(responseData),
      } as Response);

      const result = await apiClient.put('/users/1', requestData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/users/1',
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(requestData),
        }),
      );
      expect(result).toEqual(responseData);
    });

    it('makes PATCH request with data', async () => {
      const requestData = { name: 'Patched User' };
      const responseData = { id: 1, name: 'Patched User', email: '<EMAIL>' };

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(responseData),
      } as Response);

      const result = await apiClient.patch('/users/1', requestData);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/users/1',
        expect.objectContaining({
          method: 'PATCH',
          body: JSON.stringify(requestData),
        }),
      );
      expect(result).toEqual(responseData);
    });

    it('makes DELETE request', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 204,
        statusText: 'No Content',
        headers: new Headers(),
        text: () => Promise.resolve(''),
      } as Response);

      const result = await apiClient.delete('/users/1');

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/users/1',
        expect.objectContaining({
          method: 'DELETE',
        }),
      );
      expect(result).toBe('');
    });
  });

  describe('Error handling', () => {
    it('throws ApiError for HTTP errors', async () => {
      const errorData = { message: 'User not found' };
      mockFetch.mockResolvedValue({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(errorData),
      } as Response);

      await expect(apiClient.get('/users/999')).rejects.toMatchObject({
        name: 'ApiError',
        status: 404,
        statusText: 'Not Found',
        data: errorData,
        message: 'HTTP 404: Not Found',
      });
    });

    it('handles network errors', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      await expect(apiClient.get('/users')).rejects.toMatchObject({
        name: 'ApiError',
        status: 0,
        statusText: 'Network Error',
        message: 'Network error',
      });
    });

    it('handles response parsing errors', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.reject(new Error('Invalid JSON')),
      } as Response);

      await expect(apiClient.get('/users')).rejects.toMatchObject({
        name: 'ApiError',
        status: 200,
        statusText: 'Failed to parse response',
        message: 'Response parsing failed',
      });
    });
  });

  describe('URL building', () => {
    it('builds URL with query parameters', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve([]),
      } as Response);

      await apiClient.get('/users', { params: { page: '1', limit: '10' } });

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/users?page=1&limit=10',
        expect.any(Object),
      );
    });

    it('handles base URL properly', () => {
      const client = new ApiClient('https://api.example.com');
      expect(client).toBeInstanceOf(ApiClient);
    });
  });

  describe('Data transformations', () => {
    it('transforms camelCase to snake_case in requests', async () => {
      const requestData = { firstName: 'John', lastName: 'Doe', emailAddress: '<EMAIL>' };

      mockFetch.mockResolvedValue({
        ok: true,
        status: 201,
        statusText: 'Created',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ id: 1 }),
      } as Response);

      await apiClient.post('/users', requestData);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify({
            first_name: 'John',
            last_name: 'Doe',
            email_address: '<EMAIL>',
          }),
        }),
      );
    });

    it('transforms snake_case to camelCase in responses', async () => {
      const responseData = {
        id: 1,
        first_name: 'John',
        last_name: 'Doe',
        email_address: '<EMAIL>',
      };

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(responseData),
      } as Response);

      const result = await apiClient.get('/users/1');

      expect(result).toEqual({
        id: 1,
        firstName: 'John',
        lastName: 'Doe',
        emailAddress: '<EMAIL>',
      });
    });

    it('handles nested objects in transformations', async () => {
      const requestData = {
        userInfo: { firstName: 'John', lastName: 'Doe' },
        contactDetails: { emailAddress: '<EMAIL>' },
      };

      mockFetch.mockResolvedValue({
        ok: true,
        status: 201,
        statusText: 'Created',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ id: 1 }),
      } as Response);

      await apiClient.post('/users', requestData);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify({
            user_info: { first_name: 'John', last_name: 'Doe' },
            contact_details: { email_address: '<EMAIL>' },
          }),
        }),
      );
    });

    it('handles arrays in transformations', async () => {
      const requestData = [
        { firstName: 'John', lastName: 'Doe' },
        { firstName: 'Jane', lastName: 'Smith' },
      ];

      mockFetch.mockResolvedValue({
        ok: true,
        status: 201,
        statusText: 'Created',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ created: 2 }),
      } as Response);

      await apiClient.post('/users/bulk', requestData);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify([
            { first_name: 'John', last_name: 'Doe' },
            { first_name: 'Jane', last_name: 'Smith' },
          ]),
        }),
      );
    });

    it('can disable transformations', async () => {
      apiClient.enableDataTransformations(false);

      const requestData = { firstName: 'John', lastName: 'Doe' };

      mockFetch.mockResolvedValue({
        ok: true,
        status: 201,
        statusText: 'Created',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ first_name: 'John' }),
      } as Response);

      const result = await apiClient.post('/users', requestData);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: JSON.stringify({ firstName: 'John', lastName: 'Doe' }),
        }),
      );
      expect(result).toEqual({ first_name: 'John' });
    });
  });

  describe('Request interceptors', () => {
    it('applies request interceptors', async () => {
      const interceptor = vi.fn((config) => ({
        ...config,
        headers: { ...config.headers, 'X-Custom-Header': 'test' },
      }));

      const removeInterceptor = apiClient.addRequestInterceptor(interceptor);

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({}),
      } as Response);

      await apiClient.get('/users');

      expect(interceptor).toHaveBeenCalled();
      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'X-Custom-Header': 'test',
          }),
        }),
      );

      removeInterceptor();
    });

    it('removes request interceptors', async () => {
      const interceptor = vi.fn((config) => config);
      const removeInterceptor = apiClient.addRequestInterceptor(interceptor);

      removeInterceptor();

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({}),
      } as Response);

      await apiClient.get('/users');

      expect(interceptor).not.toHaveBeenCalled();
    });
  });

  describe('Response interceptors', () => {
    it('applies response interceptors', async () => {
      const interceptor = vi.fn((response) => ({
        ...response,
        data: { ...response.data, intercepted: true },
      }));

      const removeInterceptor = apiClient.addResponseInterceptor(interceptor);

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ id: 1 }),
      } as Response);

      const result = await apiClient.get('/users/1');

      expect(interceptor).toHaveBeenCalled();
      expect(result).toEqual({ id: 1, intercepted: true });

      removeInterceptor();
    });
  });

  describe('Configuration', () => {
    it('allows setting default timeout', () => {
      apiClient.setDefaultTimeout(5000);
      expect(() => apiClient.setDefaultTimeout(5000)).not.toThrow();
    });

    it('allows setting default retries', () => {
      apiClient.setDefaultRetries(3);
      expect(() => apiClient.setDefaultRetries(3)).not.toThrow();
    });
  });

  describe('Utility methods', () => {
    it('handles file upload', async () => {
      const file = new File(['content'], 'test.txt', { type: 'text/plain' });

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({ url: '/uploads/test.txt' }),
      } as Response);

      const result = await apiClient.upload('/upload', file);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/upload',
        expect.objectContaining({
          method: 'POST',
          body: expect.any(FormData),
        }),
      );
      expect(result).toEqual({ url: '/uploads/test.txt' });
    });

    it('handles file download', async () => {
      const blob = new Blob(['file content'], { type: 'application/octet-stream' });

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/octet-stream' }),
        blob: () => Promise.resolve(blob),
        text: () => Promise.resolve('file content'),
      } as Response);

      const result = await apiClient.downloadFile('/download/file.pdf');

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/download/file.pdf',
        expect.objectContaining({
          headers: expect.objectContaining({
            Accept: 'application/octet-stream',
          }),
        }),
      );
      expect(result).toEqual(blob);
    });
  });

  describe('Request metadata', () => {
    it('includes request ID in headers', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({}),
      } as Response);

      await apiClient.get('/users');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'X-Request-ID': expect.stringMatching(/^req_\d+_[a-z0-9]+$/),
          }),
        }),
      );
    });

    it('includes credentials for session management', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve({}),
      } as Response);

      await apiClient.get('/users');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          method: expect.any(String),
        }),
      );
    });
  });

  describe('Edge cases', () => {
    it('handles non-JSON responses', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'text/plain' }),
        text: () => Promise.resolve('Success'),
      } as Response);

      const result = await apiClient.get('/health');
      expect(result).toBe('Success');
    });

    it('handles empty responses', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 204,
        statusText: 'No Content',
        headers: new Headers(),
        text: () => Promise.resolve(''),
      } as Response);

      const result = await apiClient.delete('/users/1');
      expect(result).toBe('');
    });

    it('handles null and undefined data', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: () => Promise.resolve(null),
      } as Response);

      const result = await apiClient.get('/empty');
      expect(result).toBeNull();
    });
  });
});

describe('useApiClient', () => {
  it('returns apiClient instance', () => {
    const { result } = renderHook(() => useApiClient());
    expect(result.current).toBeDefined();
    expect(typeof result.current.get).toBe('function');
    expect(typeof result.current.post).toBe('function');
  });

  it('sets up Clerk token interceptor', async () => {
    const mockData = { id: 1, name: 'Test' };
    const mockFetch = fetch as vi.MockedFunction<typeof fetch>;

    mockFetch.mockResolvedValue({
      ok: true,
      status: 200,
      statusText: 'OK',
      headers: new Headers({ 'content-type': 'application/json' }),
      json: () => Promise.resolve(mockData),
    } as Response);

    const { result } = renderHook(() => useApiClient());

    await result.current.get('/test');

    // Should eventually include Authorization header with mock token
    // Note: The interceptor runs asynchronously, so exact timing may vary
    expect(mockFetch).toHaveBeenCalled();
  });
});
