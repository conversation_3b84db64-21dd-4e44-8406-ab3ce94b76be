import { useAuth } from '@clerk/clerk-react';
import { useEffect } from 'react';

interface RequestConfig extends RequestInit {
  params?: Record<string, string>;
  data?: unknown;
  skipAuth?: boolean;
  timeout?: number;
  retries?: number;
  transform?: {
    request?: (data: unknown) => unknown;
    response?: (data: unknown) => unknown;
  };
}

interface ApiResponse<T = unknown> {
  data: T;
  status: number;
  headers: Headers;
  meta?: {
    requestId?: string;
    timestamp: number;
    duration: number;
  };
}

interface ApiError extends Error {
  status: number;
  statusText: string;
  data?: unknown;
  requestId?: string;
}

type RequestInterceptor = (config: RequestConfig) => RequestConfig | Promise<RequestConfig>;
type ResponseInterceptor<T = unknown> = (
  response: ApiResponse<T>,
) => ApiResponse<T> | Promise<ApiResponse<T>>;

// Standard data transformations
const DEFAULT_TRANSFORMATIONS = {
  // Convert camelCase to snake_case for requests
  camelToSnake: (obj: unknown): unknown => {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(DEFAULT_TRANSFORMATIONS.camelToSnake);
    }

    const result: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(obj)) {
      const snakeKey = key.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
      result[snakeKey] = DEFAULT_TRANSFORMATIONS.camelToSnake(value);
    }
    return result;
  },

  // Convert snake_case to camelCase for responses
  snakeToCamel: (obj: unknown): unknown => {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(DEFAULT_TRANSFORMATIONS.snakeToCamel);
    }

    const result: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(obj)) {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
      result[camelKey] = DEFAULT_TRANSFORMATIONS.snakeToCamel(value);
    }
    return result;
  },
};

class ApiClient {
  private baseURL: string;
  private defaultHeaders: HeadersInit;
  private requestInterceptors: RequestInterceptor[] = [];
  private responseInterceptors: ResponseInterceptor[] = [];
  private defaultTimeout: number = 30000; // 30 seconds
  private defaultRetries: number = 1;
  private enableTransformations: boolean = true;

  constructor(baseURL?: string) {
    // Use provided baseURL or import from env utility (will be validated at startup)
    if (baseURL) {
      this.baseURL = baseURL;
    } else {
      // Dynamic import to avoid circular dependencies during env validation
      import('@utils/env').then(({ getApiUrl }) => {
        this.baseURL = getApiUrl();
      });
      // Fallback for immediate construction
      this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';
    }

    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Request timeout after ${timeoutMs}ms`)), timeoutMs);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  private async retryRequest<T>(
    fn: () => Promise<T>,
    retries: number,
    delay: number = 1000,
  ): Promise<T> {
    try {
      return await fn();
    } catch (error) {
      if (retries > 0 && this.isRetryableError(error)) {
        await new Promise((resolve) => setTimeout(resolve, delay));
        return this.retryRequest(fn, retries - 1, delay * 2);
      }
      throw error;
    }
  }

  private isRetryableError(error: unknown): boolean {
    // Retry on network errors or 5xx server errors
    if (error && typeof error === 'object') {
      const err = error as { status?: number; message?: string };
      return (
        !err.status ||
        err.status >= 500 ||
        (err.message?.includes('fetch') ?? false) ||
        (err.message?.includes('network') ?? false)
      );
    }
    return false;
  }

  private transformRequestData(data: unknown, config: RequestConfig): unknown {
    if (!this.enableTransformations) {
      return data;
    }

    const customTransform = config.transform?.request;
    if (customTransform) {
      return customTransform(data);
    }

    return DEFAULT_TRANSFORMATIONS.camelToSnake(data);
  }

  private transformResponseData(data: unknown, config: RequestConfig): unknown {
    if (!this.enableTransformations) {
      return data;
    }

    const customTransform = config.transform?.response;
    if (customTransform) {
      return customTransform(data);
    }

    return DEFAULT_TRANSFORMATIONS.snakeToCamel(data);
  }

  private buildURL(endpoint: string, params?: Record<string, string>): string {
    const url = new URL(`${this.baseURL}${endpoint}`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }
    return url.toString();
  }

  private async handleResponse<T>(
    response: Response,
    requestId: string,
    startTime: number,
    config: RequestConfig,
  ): Promise<ApiResponse<T>> {
    const contentType = response.headers.get('content-type');
    let data: unknown;

    try {
      if (contentType?.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }
    } catch {
      throw this.createApiError({
        status: response.status,
        statusText: 'Failed to parse response',
        data: null,
        requestId,
        message: 'Response parsing failed',
      });
    }

    // Transform response data
    const transformedData = this.transformResponseData(data, config) as T;

    if (!response.ok) {
      throw this.createApiError({
        status: response.status,
        statusText: response.statusText,
        data: transformedData,
        requestId,
        message: `HTTP ${response.status}: ${response.statusText}`,
      });
    }

    return {
      data: transformedData,
      status: response.status,
      headers: response.headers,
      meta: {
        requestId,
        timestamp: Date.now(),
        duration: Date.now() - startTime,
      },
    };
  }

  private createApiError(options: {
    status: number;
    statusText: string;
    data: unknown;
    requestId: string;
    message: string;
  }): ApiError {
    const error = new Error(options.message) as ApiError;
    error.name = 'ApiError';
    error.status = options.status;
    error.statusText = options.statusText;
    error.data = options.data;
    error.requestId = options.requestId;
    return error;
  }

  async request<T = unknown>(
    endpoint: string,
    config: RequestConfig = {},
  ): Promise<ApiResponse<T>> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    // Extract configuration with defaults
    const {
      params,
      data,
      headers: customHeaders,
      timeout = this.defaultTimeout,
      retries = this.defaultRetries,
      ...fetchConfig
    } = config;

    let requestConfig = { ...config };

    // Apply request interceptors
    for (const interceptor of this.requestInterceptors) {
      requestConfig = await interceptor(requestConfig);
    }

    // Transform request data
    const transformedData = data ? this.transformRequestData(data, requestConfig) : undefined;

    const makeRequest = async (): Promise<ApiResponse<T>> => {
      const url = this.buildURL(endpoint, params);

      const headers = {
        ...this.defaultHeaders,
        ...customHeaders,
        'X-Request-ID': requestId,
      };

      const finalConfig: RequestInit = {
        ...fetchConfig,
        headers,
        ...(transformedData ? { body: JSON.stringify(transformedData) } : {}),
      };

      const response = await fetch(url, finalConfig);
      let apiResponse = await this.handleResponse<T>(response, requestId, startTime, requestConfig);

      // Apply response interceptors
      for (const interceptor of this.responseInterceptors) {
        apiResponse = (await interceptor(apiResponse)) as ApiResponse<T>;
      }

      return apiResponse;
    };

    try {
      // Apply timeout and retry logic
      const requestPromise = retries > 0 ? this.retryRequest(makeRequest, retries) : makeRequest();

      return await this.withTimeout(requestPromise, timeout);
    } catch (error) {
      // Enhanced error handling
      if (error instanceof Error && !('status' in error)) {
        throw this.createApiError({
          status: 0,
          statusText: 'Network Error',
          data: null,
          requestId,
          message: error.message || 'Network request failed',
        });
      }
      throw error;
    }
  }

  async get<T = unknown>(endpoint: string, config?: RequestConfig): Promise<T> {
    const response = await this.request<T>(endpoint, {
      ...config,
      method: 'GET',
    });
    return response.data;
  }

  async post<T = unknown>(endpoint: string, data?: unknown, config?: RequestConfig): Promise<T> {
    const response = await this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      data,
    });
    return response.data;
  }

  async put<T = unknown>(endpoint: string, data?: unknown, config?: RequestConfig): Promise<T> {
    const response = await this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      data,
    });
    return response.data;
  }

  async patch<T = unknown>(endpoint: string, data?: unknown, config?: RequestConfig): Promise<T> {
    const response = await this.request<T>(endpoint, {
      ...config,
      method: 'PATCH',
      data,
    });
    return response.data;
  }

  async delete<T = unknown>(endpoint: string, config?: RequestConfig): Promise<T> {
    const response = await this.request<T>(endpoint, {
      ...config,
      method: 'DELETE',
    });
    return response.data;
  }

  addRequestInterceptor(interceptor: RequestInterceptor): () => void {
    this.requestInterceptors.push(interceptor);

    // Return a function to remove the interceptor
    return () => {
      const index = this.requestInterceptors.indexOf(interceptor);
      if (index !== -1) {
        this.requestInterceptors.splice(index, 1);
      }
    };
  }

  addResponseInterceptor<T = unknown>(interceptor: ResponseInterceptor<T>): () => void {
    this.responseInterceptors.push(interceptor as ResponseInterceptor);

    // Return a function to remove the interceptor
    return () => {
      const index = this.responseInterceptors.indexOf(interceptor as ResponseInterceptor);
      if (index !== -1) {
        this.responseInterceptors.splice(index, 1);
      }
    };
  }

  // Configuration methods
  setDefaultTimeout(timeout: number): void {
    this.defaultTimeout = timeout;
  }

  setDefaultRetries(retries: number): void {
    this.defaultRetries = retries;
  }

  enableDataTransformations(enable: boolean = true): void {
    this.enableTransformations = enable;
  }

  // Utility methods for common patterns
  async upload<T = unknown>(
    endpoint: string,
    file: File,
    config?: Omit<RequestConfig, 'data'>,
  ): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      headers: {
        // Let the browser set Content-Type for FormData
        ...config?.headers,
      },
      data: formData,
      transform: {
        request: (data) => data, // Skip transformation for FormData
        response: config?.transform?.response,
      },
    });
    return response.data;
  }

  async downloadFile(endpoint: string, config?: RequestConfig): Promise<Blob> {
    const response = await this.request<Blob>(endpoint, {
      ...config,
      headers: {
        ...config?.headers,
        Accept: 'application/octet-stream',
      },
      transform: {
        request: config?.transform?.request,
        response: (data) => data, // Skip transformation for binary data
      },
    });
    return response.data;
  }
}

export const apiClient = new ApiClient();

// Add automatic session validation on 401 errors
apiClient.addResponseInterceptor(async (response) => {
  if (response.status === 401) {
    // Session is invalid, redirect to sign in
    console.warn('Session expired, redirecting to sign in');
    window.location.href = '/sign-in';
    throw new Error('Authentication required');
  }

  return response;
});

// Add request/response logging in development
if (import.meta.env.DEV) {
  apiClient.addRequestInterceptor(async (config) => {
    console.warn(`🚀 API Request: ${config.method?.toUpperCase() || 'GET'}`);
    console.warn('Config:', config);
    return config;
  });

  apiClient.addResponseInterceptor(async (response) => {
    console.warn(`📨 API Response: ${response.status}`);
    console.warn('Response:', response);
    if (response.meta) {
      console.warn(`Duration: ${response.meta.duration}ms`);
      console.warn(`Request ID: ${response.meta.requestId}`);
    }
    return response;
  });
}
// React hook for Clerk-aware API client (imports moved to top)

export function useApiClient() {
  const { getToken } = useAuth();

  useEffect(() => {
    // Add request interceptor for Clerk authentication
    const removeInterceptor = apiClient.addRequestInterceptor(async (config) => {
      // Skip auth if explicitly requested
      if (config.skipAuth) {
        return config;
      }

      try {
        const token = await getToken();
        if (token) {
          config.headers = {
            ...config.headers,
            Authorization: `Bearer ${token}`,
          };
        }
      } catch (error) {
        console.warn('Failed to get Clerk token:', error);
        // Don't throw - continue with request without auth
      }

      return config;
    });

    // Cleanup interceptor on unmount
    return removeInterceptor;
  }, [getToken]);

  return apiClient;
}

export default ApiClient;
export type { ApiError, ApiResponse, RequestConfig, RequestInterceptor, ResponseInterceptor };
