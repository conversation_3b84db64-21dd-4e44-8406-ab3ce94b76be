import { renderHook, waitFor, act } from '@testing-library/react'
import { useAuth } from '@clerk/clerk-react'
import { describe, expect, it, vi, beforeEach, afterEach, Mock } from 'vitest'

import { useApiClient, ApiError } from './enhanced-api-client'

// Mock Clerk auth
vi.mock('@clerk/clerk-react', () => ({
  useAuth: vi.fn(),
}))

// Mock fetch
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('Enhanced API Client', () => {
  const mockUseAuth = useAuth as Mock
  let mockGetToken: Mock
  let mockSignOut: Mock

  beforeEach(() => {
    mockGetToken = vi.fn()
    mockSignOut = vi.fn()
    mockUseAuth.mockReturnValue({
      getToken: mockGetToken,
      signOut: mockSignOut,
    })
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('ApiError Class', () => {
    it('should create ApiError with correct properties', () => {
      const error = new ApiError(404, 'Not Found', { message: 'Resource not found' }, false)

      expect(error).toBeInstanceOf(Error)
      expect(error.status).toBe(404)
      expect(error.statusText).toBe('Not Found')
      expect(error.response).toEqual({ message: 'Resource not found' })
      expect(error.isAuthError).toBe(false)
      expect(error.message).toBe('API Error: 404 Not Found')
    })

    it('should create ApiError with auth error flag', () => {
      const error = new ApiError(401, 'Unauthorized', null, true)

      expect(error.isAuthError).toBe(true)
    })

    it('should default isAuthError to false', () => {
      const error = new ApiError(500, 'Internal Server Error')

      expect(error.isAuthError).toBe(false)
    })
  })

  describe('Token Management', () => {
    it('should get valid token for API calls', async () => {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.signature'
      mockGetToken.mockResolvedValue(mockToken)
      
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: async () => ({ data: 'test' }),
      })

      const { result } = renderHook(() => useApiClient())

      const response = await result.current.get('/test')

      expect(mockGetToken).toHaveBeenCalled()
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/test',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Authorization': `Bearer ${mockToken}`,
            'Content-Type': 'application/json',
          }),
        })
      )
      expect(response).toEqual({ data: 'test' })
    })

    it('should handle missing token', async () => {
      mockGetToken.mockResolvedValue(null)

      const { result } = renderHook(() => useApiClient())

      await expect(result.current.get('/test')).rejects.toThrow(ApiError)
      await expect(result.current.get('/test')).rejects.toThrow('No valid token available')
    })

    it('should validate JWT token format', async () => {
      mockGetToken.mockResolvedValue('invalid-token-format')

      const { result } = renderHook(() => useApiClient())

      await expect(result.current.get('/test')).rejects.toThrow(ApiError)
      await expect(result.current.get('/test')).rejects.toThrow('No valid token available')
    })

    it('should retry token retrieval with exponential backoff', async () => {
      // First two calls fail, third succeeds
      mockGetToken
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValue('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.signature')

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: async () => ({ data: 'test' }),
      })

      const { result } = renderHook(() => useApiClient())

      const startTime = Date.now()
      await result.current.get('/test')
      const endTime = Date.now()

      expect(mockGetToken).toHaveBeenCalledTimes(3)
      // Should have waited at least 1s + 2s = 3s for retries
      expect(endTime - startTime).toBeGreaterThanOrEqual(3000)
    })

    it('should fail after max retry attempts', async () => {
      mockGetToken.mockRejectedValue(new Error('Persistent network error'))

      const { result } = renderHook(() => useApiClient())

      await expect(result.current.get('/test')).rejects.toThrow(ApiError)
      expect(mockGetToken).toHaveBeenCalledTimes(4) // Initial + 3 retries
    })
  })

  describe('HTTP Methods', () => {
    beforeEach(() => {
      mockGetToken.mockResolvedValue('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.signature')
    })

    it('should handle GET requests', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: async () => ({ data: 'get-response' }),
      })

      const { result } = renderHook(() => useApiClient())
      const response = await result.current.get('/users')

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/users',
        expect.objectContaining({
          method: 'GET',
        })
      )
      expect(response).toEqual({ data: 'get-response' })
    })

    it('should handle POST requests with data', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 201,
        headers: new Map([['content-type', 'application/json']]),
        json: async () => ({ id: 1, name: 'Created' }),
      })

      const { result } = renderHook(() => useApiClient())
      const postData = { name: 'New User', email: '<EMAIL>' }
      const response = await result.current.post('/users', postData)

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/users',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(postData),
        })
      )
      expect(response).toEqual({ id: 1, name: 'Created' })
    })

    it('should handle PUT requests with data', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: async () => ({ id: 1, name: 'Updated' }),
      })

      const { result } = renderHook(() => useApiClient())
      const updateData = { name: 'Updated User' }
      const response = await result.current.put('/users/1', updateData)

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/users/1',
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(updateData),
        })
      )
      expect(response).toEqual({ id: 1, name: 'Updated' })
    })

    it('should handle PATCH requests with data', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: async () => ({ id: 1, name: 'Patched' }),
      })

      const { result } = renderHook(() => useApiClient())
      const patchData = { name: 'Patched User' }
      const response = await result.current.patch('/users/1', patchData)

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/users/1',
        expect.objectContaining({
          method: 'PATCH',
          body: JSON.stringify(patchData),
        })
      )
      expect(response).toEqual({ id: 1, name: 'Patched' })
    })

    it('should handle DELETE requests', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 204,
        headers: new Map(),
        text: async () => '',
      })

      const { result } = renderHook(() => useApiClient())
      const response = await result.current.delete('/users/1')

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/users/1',
        expect.objectContaining({
          method: 'DELETE',
        })
      )
      expect(response).toBe('')
    })

    it('should handle requests without data', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: async () => ({ success: true }),
      })

      const { result } = renderHook(() => useApiClient())
      const response = await result.current.post('/action')

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/action',
        expect.objectContaining({
          method: 'POST',
          body: undefined,
        })
      )
      expect(response).toEqual({ success: true })
    })
  })

  describe('Authentication Error Handling', () => {
    beforeEach(() => {
      mockGetToken.mockResolvedValue('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.signature')
    })

    it('should retry on 401 errors with fresh token', async () => {
      // First call returns 401, second call succeeds
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 401,
          statusText: 'Unauthorized',
          text: async () => 'Token expired',
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: async () => ({ data: 'success' }),
        })

      const { result } = renderHook(() => useApiClient())
      const response = await result.current.get('/protected')

      expect(mockFetch).toHaveBeenCalledTimes(2)
      expect(response).toEqual({ data: 'success' })
    })

    it('should throw ApiError on persistent 401 errors', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        text: async () => 'Invalid token',
      })

      const { result } = renderHook(() => useApiClient())

      await expect(result.current.get('/protected')).rejects.toThrow(ApiError)
      expect(mockFetch).toHaveBeenCalledTimes(2) // Initial + 1 retry
    })

    it('should handle 403 errors as authorization failures', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        status: 403,
        statusText: 'Forbidden',
        text: async () => 'Access denied',
      })

      const { result } = renderHook(() => useApiClient())

      await expect(result.current.get('/admin')).rejects.toThrow(ApiError)
      
      try {
        await result.current.get('/admin')
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError)
        expect((error as ApiError).status).toBe(403)
        expect((error as ApiError).isAuthError).toBe(false)
      }
    })
  })

  describe('Response Handling', () => {
    beforeEach(() => {
      mockGetToken.mockResolvedValue('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.signature')
    })

    it('should parse JSON responses', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: async () => ({ name: 'John', age: 30 }),
      })

      const { result } = renderHook(() => useApiClient())
      const response = await result.current.get('/user')

      expect(response).toEqual({ name: 'John', age: 30 })
    })

    it('should handle text responses', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'text/plain']]),
        text: async () => 'Success message',
      })

      const { result } = renderHook(() => useApiClient())
      const response = await result.current.get('/status')

      expect(response).toBe('Success message')
    })

    it('should handle empty responses', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 204,
        headers: new Map(),
        text: async () => '',
      })

      const { result } = renderHook(() => useApiClient())
      const response = await result.current.delete('/item/1')

      expect(response).toBe('')
    })

    it('should handle malformed JSON gracefully', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: async () => {
          throw new Error('Invalid JSON')
        },
      })

      const { result } = renderHook(() => useApiClient())

      await expect(result.current.get('/malformed')).rejects.toThrow()
    })
  })

  describe('Error Handling', () => {
    beforeEach(() => {
      mockGetToken.mockResolvedValue('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.signature')
    })

    it('should handle 400 errors', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: async () => 'Invalid input',
      })

      const { result } = renderHook(() => useApiClient())

      try {
        await result.current.post('/users', { invalid: 'data' })
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError)
        expect((error as ApiError).status).toBe(400)
        expect((error as ApiError).response).toBe('Invalid input')
      }
    })

    it('should handle 500 errors', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        text: async () => 'Server error occurred',
      })

      const { result } = renderHook(() => useApiClient())

      try {
        await result.current.get('/users')
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError)
        expect((error as ApiError).status).toBe(500)
        expect((error as ApiError).isAuthError).toBe(false)
      }
    })

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      const { result } = renderHook(() => useApiClient())

      try {
        await result.current.get('/users')
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError)
        expect((error as ApiError).status).toBe(0)
        expect((error as ApiError).statusText).toBe('Network error')
      }
    })

    it('should handle fetch timeout errors', async () => {
      mockFetch.mockRejectedValue(new Error('The operation was aborted'))

      const { result } = renderHook(() => useApiClient())

      try {
        await result.current.get('/slow-endpoint')
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError)
        expect((error as ApiError).status).toBe(0)
      }
    })
  })

  describe('Retry Logic', () => {
    beforeEach(() => {
      mockGetToken.mockResolvedValue('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.signature')
    })

    it('should track retry counts per request', async () => {
      // Set up different responses for different endpoints
      mockFetch.mockImplementation((url) => {
        if (url.includes('/endpoint1')) {
          return Promise.resolve({
            ok: false,
            status: 401,
            statusText: 'Unauthorized',
            text: async () => 'Auth error',
          })
        }
        return Promise.resolve({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: async () => ({ success: true }),
        })
      })

      const { result } = renderHook(() => useApiClient())

      // First endpoint should fail after retries
      await expect(result.current.get('/endpoint1')).rejects.toThrow(ApiError)

      // Second endpoint should succeed immediately
      const response = await result.current.get('/endpoint2')
      expect(response).toEqual({ success: true })
    })

    it('should clear retry count on successful requests', async () => {
      // First call fails, subsequent calls succeed
      let callCount = 0
      mockFetch.mockImplementation(() => {
        callCount++
        if (callCount === 1) {
          return Promise.resolve({
            ok: false,
            status: 401,
            statusText: 'Unauthorized',
            text: async () => 'Auth error',
          })
        }
        return Promise.resolve({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: async () => ({ success: true }),
        })
      })

      const { result } = renderHook(() => useApiClient())

      // First request should succeed after retry
      const response1 = await result.current.get('/test')
      expect(response1).toEqual({ success: true })

      // Reset call count
      callCount = 0

      // Second request should succeed immediately
      const response2 = await result.current.get('/test')
      expect(response2).toEqual({ success: true })
      expect(callCount).toBe(1) // No retries needed
    })
  })

  describe('Edge Cases', () => {
    it('should handle undefined response data', async () => {
      mockGetToken.mockResolvedValue('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.signature')
      
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: async () => undefined,
      })

      const { result } = renderHook(() => useApiClient())
      const response = await result.current.get('/empty')

      expect(response).toBeUndefined()
    })

    it('should handle null response data', async () => {
      mockGetToken.mockResolvedValue('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.signature')
      
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: async () => null,
      })

      const { result } = renderHook(() => useApiClient())
      const response = await result.current.get('/null')

      expect(response).toBeNull()
    })

    it('should handle missing content-type header', async () => {
      mockGetToken.mockResolvedValue('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.signature')
      
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map(),
        text: async () => 'Plain text response',
      })

      const { result } = renderHook(() => useApiClient())
      const response = await result.current.get('/no-content-type')

      expect(response).toBe('Plain text response')
    })

    it('should handle very large request data', async () => {
      mockGetToken.mockResolvedValue('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.signature')
      
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: async () => ({ id: 1 }),
      })

      const largeData = {
        content: 'x'.repeat(10000), // 10KB string
        metadata: Array.from({ length: 1000 }, (_, i) => ({ id: i, value: `item${i}` })),
      }

      const { result } = renderHook(() => useApiClient())
      const response = await result.current.post('/large-data', largeData)

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/large-data',
        expect.objectContaining({
          body: JSON.stringify(largeData),
        })
      )
      expect(response).toEqual({ id: 1 })
    })
  })
})