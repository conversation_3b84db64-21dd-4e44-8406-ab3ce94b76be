import { useAuth } from '@clerk/clerk-react'
import { useCallback, useRef } from 'react'

// Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'
const MAX_RETRY_ATTEMPTS = 3
const TOKEN_RETRY_DELAY = 1000 // 1 second

export class ApiError extends Error {
  constructor(
    public status: number,
    public statusText: string,
    public response?: unknown,
    public isAuthError: boolean = false
  ) {
    super(`API Error: ${status} ${statusText}`)
  }
}

export function useApiClient() {
  const { getToken } = useAuth()
  
  // Track retry attempts to prevent infinite loops
  const retryCount = useRef<Map<string, number>>(new Map())

  const getValidToken = useCallback(async (attempt: number = 0): Promise<string | null> => {
    try {
      // Get fresh token from Clerk
      const token = await getToken()
      
      if (!token) {
        console.warn('No token available from <PERSON>')
        return null
      }

      // Validate token structure (basic check)
      if (!isValidJWT(token)) {
        console.error('Invalid token format received from Clerk')
        return null
      }

      return token
    } catch (error) {
      console.error(`Failed to get token (attempt ${attempt + 1}):`, error)
      
      // Retry with exponential backoff
      if (attempt < MAX_RETRY_ATTEMPTS) {
        await new Promise(resolve => 
          setTimeout(resolve, TOKEN_RETRY_DELAY * Math.pow(2, attempt))
        )
        return getValidToken(attempt + 1)
      }
      
      return null
    }
  }, [getToken])

  const apiCall = useCallback(async <T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> => {
    const requestId = `${options.method || 'GET'}-${endpoint}`
    const currentRetries = retryCount.current.get(requestId) || 0

    try {
      // Get valid token
      const token = await getValidToken()
      
      if (!token) {
        throw new ApiError(401, 'No valid token available', null, true)
      }

      // Make API request
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          ...options.headers,
        },
      })

      // Handle authentication errors
      if (response.status === 401 || response.status === 403) {
        // Clear retry count on auth errors
        retryCount.current.delete(requestId)
        
        const errorData = await response.text()
        const isAuthError = response.status === 401
        
        // On 401, try to get a fresh token and retry once
        if (isAuthError && currentRetries < 1) {
          retryCount.current.set(requestId, currentRetries + 1)
          
          // Wait a moment and retry with fresh token
          await new Promise(resolve => setTimeout(resolve, 500))
          return apiCall<T>(endpoint, options)
        }
        
        // If still failing, it's a real auth error
        if (isAuthError) {
          console.warn('Authentication failed, user may need to sign in again')
          // Optionally trigger sign out on persistent auth failures
          // await signOut()
        }
        
        throw new ApiError(response.status, response.statusText, errorData, isAuthError)
      }

      // Handle other HTTP errors
      if (!response.ok) {
        const errorData = await response.text()
        throw new ApiError(response.status, response.statusText, errorData)
      }

      // Clear retry count on success
      retryCount.current.delete(requestId)

      // Parse response
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        return await response.json()
      } else {
        return await response.text() as unknown as T
      }

    } catch (error) {
      // Clear retry count on final failure
      if (currentRetries >= MAX_RETRY_ATTEMPTS) {
        retryCount.current.delete(requestId)
      }
      
      // Re-throw known API errors
      if (error instanceof ApiError) {
        throw error
      }
      
      // Wrap unknown errors
      throw new ApiError(0, 'Network error', error)
    }
  }, [getValidToken])

  return {
    get: <T>(endpoint: string) => apiCall<T>(endpoint, { method: 'GET' }),
    post: <T>(endpoint: string, data?: unknown) =>
      apiCall<T>(endpoint, {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
      }),
    put: <T>(endpoint: string, data?: unknown) =>
      apiCall<T>(endpoint, {
        method: 'PUT',
        body: data ? JSON.stringify(data) : undefined,
      }),
    delete: <T>(endpoint: string) =>
      apiCall<T>(endpoint, { method: 'DELETE' }),
    patch: <T>(endpoint: string, data?: unknown) =>
      apiCall<T>(endpoint, {
        method: 'PATCH',
        body: data ? JSON.stringify(data) : undefined,
      }),
  }
}

// Utility function to validate JWT format
function isValidJWT(token: string): boolean {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {return false}
    
    // Try to decode the payload (without verification)
    const payload = JSON.parse(atob(parts[1]))
    
    // Check for required JWT fields
    return payload.exp && payload.iat && payload.sub
  } catch {
    return false
  }
}