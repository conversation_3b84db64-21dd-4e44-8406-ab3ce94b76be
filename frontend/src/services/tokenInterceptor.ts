import { useAuth } from '@clerk/clerk-react'
import { useEffect, useRef } from 'react'

export function useTokenInterceptor() {
  const { getToken, isSignedIn } = useAuth()
  const refreshTimer = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (!isSignedIn) {
      if (refreshTimer.current) {
        clearInterval(refreshTimer.current)
        refreshTimer.current = null
      }
      return
    }

    // Set up automatic token refresh
    const setupTokenRefresh = async () => {
      try {
        const token = await getToken()
        if (!token) {return}

        // Parse token to get expiration
        const payload = JSON.parse(atob(token.split('.')[1]))
        const expiresAt = payload.exp * 1000
        const now = Date.now()
        const timeUntilExpiration = expiresAt - now

        // Refresh token 5 minutes before expiration
        const refreshTime = Math.max(0, timeUntilExpiration - 5 * 60 * 1000)

        if (refreshTimer.current) {
          clearTimeout(refreshTimer.current)
        }

        refreshTimer.current = setTimeout(async () => {
          try {
            await getToken() // This will automatically refresh if needed
            setupTokenRefresh() // Schedule next refresh
          } catch (error) {
            console.error('Automatic token refresh failed:', error)
          }
        }, refreshTime)

      } catch (error) {
        console.error('Failed to setup token refresh:', error)
      }
    }

    setupTokenRefresh()

    return () => {
      if (refreshTimer.current) {
        clearTimeout(refreshTimer.current)
        refreshTimer.current = null
      }
    }
  }, [getToken, isSignedIn])
}