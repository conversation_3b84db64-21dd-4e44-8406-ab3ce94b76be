import { useUser } from '@clerk/clerk-react';
import type { MockComponentProps, MockUseUserReturn } from '@test/types';
import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { useClerkSync } from '@hooks/useClerkSync';

vi.mock('@clerk/clerk-react', () => ({
  useUser: vi.fn(),
}));

vi.mock('@components/AuthGuard', () => ({
  AuthGuard: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="auth-guard">{children}</div>
  ),
}));

vi.mock('@components/UserProfile', () => ({
  UserProfile: ({ showFullProfile }: { showFullProfile?: boolean }) => (
    <div data-testid="user-profile" data-show-full-profile={showFullProfile}>
      User Profile Component
    </div>
  ),
}));

vi.mock('@components/ui/Card', () => ({
  Card: ({ children, ...props }: MockComponentProps) => (
    <div data-testid="card" {...props}>
      {children}
    </div>
  ),
  CardContent: ({ children, ...props }: MockComponentProps) => (
    <div data-testid="card-content" {...props}>
      {children}
    </div>
  ),
  CardDescription: ({ children, ...props }: MockComponentProps) => (
    <p data-testid="card-description" {...props}>
      {children}
    </p>
  ),
  CardHeader: ({ children, ...props }: MockComponentProps) => (
    <div data-testid="card-header" {...props}>
      {children}
    </div>
  ),
  CardTitle: ({ children, ...props }: MockComponentProps) => (
    <h3 data-testid="card-title" {...props}>
      {children}
    </h3>
  ),
}));

// Mock useClerkSync
vi.mock('@hooks/useClerkSync', () => ({
  useClerkSync: vi.fn(() => ({})),
}));

// Mock TanStack Router
vi.mock('@tanstack/react-router', () => ({
  createFileRoute: () => ({
    component: null,
  }),
}));

const mockUseUser = useUser as vi.MockedFunction<typeof useUser>;

// We'll create a test component that simulates the Dashboard function from the route
const Dashboard = () => {
  const { user } = useUser();
  const {} = useClerkSync();

  return (
    <div data-testid="dashboard-component">
      <div className="px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="mb-2 text-3xl font-bold text-gray-900">
            Welcome back, {user?.firstName}!
          </h1>
          <p className="text-gray-600">Here&apos;s what&apos;s happening with your team today.</p>
        </div>

        <div className="grid grid-cols-1 gap-6 mb-8 md:grid-cols-2 lg:grid-cols-4">
          <div data-testid="card">
            <div
              data-testid="card-header"
              className="flex flex-row justify-between items-center pb-2 space-y-0"
            >
              <h3 data-testid="card-title" className="text-sm font-medium">
                Total Members
              </h3>
            </div>
            <div data-testid="card-content">
              <div className="text-2xl font-bold">1,234</div>
              <p className="text-xs text-gray-600">+20.1% from last month</p>
            </div>
          </div>

          <div data-testid="card">
            <div
              data-testid="card-header"
              className="flex flex-row justify-between items-center pb-2 space-y-0"
            >
              <h3 data-testid="card-title" className="text-sm font-medium">
                Active Teams
              </h3>
            </div>
            <div data-testid="card-content">
              <div className="text-2xl font-bold">45</div>
              <p className="text-xs text-gray-600">+5.2% from last month</p>
            </div>
          </div>

          <div data-testid="card">
            <div
              data-testid="card-header"
              className="flex flex-row justify-between items-center pb-2 space-y-0"
            >
              <h3 data-testid="card-title" className="text-sm font-medium">
                Regions
              </h3>
            </div>
            <div data-testid="card-content">
              <div className="text-2xl font-bold">8</div>
              <p className="text-xs text-gray-600">No change</p>
            </div>
          </div>

          <div data-testid="card">
            <div
              data-testid="card-header"
              className="flex flex-row justify-between items-center pb-2 space-y-0"
            >
              <h3 data-testid="card-title" className="text-sm font-medium">
                Member Types
              </h3>
            </div>
            <div data-testid="card-content">
              <div className="text-2xl font-bold">12</div>
              <p className="text-xs text-gray-600">+1 new type</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2">
            <div data-testid="card">
              <div data-testid="card-header">
                <h3 data-testid="card-title">Recent Activity</h3>
                <p data-testid="card-description">Latest member management activities</p>
              </div>
              <div data-testid="card-content">
                <div className="space-y-4">
                  <div className="flex items-center">
                    <div className="mr-3 w-2 h-2 rounded-full bg-primary-600"></div>
                    <div>
                      <p className="text-sm">New member registered</p>
                      <p className="text-xs text-gray-600">2 minutes ago</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <div data-testid="user-profile" data-show-full-profile="true">
              User Profile Component
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

describe('Dashboard Route', () => {
  const mockUser = {
    id: 'user_123',
    firstName: 'John',
    lastName: 'Doe',
    emailAddresses: [{ emailAddress: '<EMAIL>' }],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseUser.mockReturnValue({
      user: mockUser,
      isLoaded: true,
    } as MockUseUserReturn);
  });

  describe('Basic rendering', () => {
    it('renders dashboard component', () => {
      render(<Dashboard />);

      expect(screen.getByTestId('dashboard-component')).toBeInTheDocument();
    });

    it('displays personalized welcome message', () => {
      render(<Dashboard />);

      expect(screen.getByText('Welcome back, John!')).toBeInTheDocument();
      expect(screen.getByText("Here's what's happening with your team today.")).toBeInTheDocument();
    });

    it('handles user without first name', () => {
      mockUseUser.mockReturnValue({
        user: { ...mockUser, firstName: null },
        isLoaded: true,
      } as MockUseUserReturn);

      render(<Dashboard />);

      expect(screen.getByText('Welcome back, !')).toBeInTheDocument();
    });

    it('handles no user', () => {
      mockUseUser.mockReturnValue({
        user: null,
        isLoaded: true,
      } as MockUseUserReturn);

      render(<Dashboard />);

      expect(screen.getByText('Welcome back, !')).toBeInTheDocument();
    });
  });

  describe('Metrics cards', () => {
    it('displays all four metric cards', () => {
      render(<Dashboard />);

      expect(screen.getByText('Total Members')).toBeInTheDocument();
      expect(screen.getByText('Active Teams')).toBeInTheDocument();
      expect(screen.getByText('Regions')).toBeInTheDocument();
      expect(screen.getByText('Member Types')).toBeInTheDocument();
    });

    it('shows correct metric values', () => {
      render(<Dashboard />);

      expect(screen.getByText('1,234')).toBeInTheDocument();
      expect(screen.getByText('45')).toBeInTheDocument();
      expect(screen.getByText('8')).toBeInTheDocument();
      expect(screen.getByText('12')).toBeInTheDocument();
    });

    it('displays metric changes', () => {
      render(<Dashboard />);

      expect(screen.getByText('+20.1% from last month')).toBeInTheDocument();
      expect(screen.getByText('+5.2% from last month')).toBeInTheDocument();
      expect(screen.getByText('No change')).toBeInTheDocument();
      expect(screen.getByText('+1 new type')).toBeInTheDocument();
    });
  });

  describe('Activity section', () => {
    it('displays recent activity card', () => {
      render(<Dashboard />);

      expect(screen.getByText('Recent Activity')).toBeInTheDocument();
      expect(screen.getByText('Latest member management activities')).toBeInTheDocument();
    });

    it('shows activity items', () => {
      render(<Dashboard />);

      expect(screen.getByText('New member registered')).toBeInTheDocument();
      expect(screen.getByText('2 minutes ago')).toBeInTheDocument();
    });
  });

  describe('User profile section', () => {
    it('includes full user profile', () => {
      render(<Dashboard />);

      const userProfile = screen.getByTestId('user-profile');
      expect(userProfile).toBeInTheDocument();
      expect(userProfile).toHaveAttribute('data-show-full-profile', 'true');
    });
  });

  describe('Layout and styling', () => {
    it('applies correct container classes', () => {
      render(<Dashboard />);

      const container = screen.getByTestId('dashboard-component').firstChild;
      expect(container).toHaveClass('max-w-7xl', 'mx-auto', 'px-4', 'sm:px-6', 'lg:px-8', 'py-8');
    });

    it('uses responsive grid layout for metrics', () => {
      render(<Dashboard />);

      const metricsGrid = screen.getByText('Total Members').closest('.grid');
      expect(metricsGrid).toHaveClass('grid', 'grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-4');
    });

    it('uses responsive layout for bottom section', () => {
      render(<Dashboard />);

      const bottomGrid = screen.getByText('Recent Activity').closest('.grid');
      expect(bottomGrid).toHaveClass('grid', 'grid-cols-1', 'lg:grid-cols-3');
    });
  });

  describe('Content structure', () => {
    it('has proper heading hierarchy', () => {
      render(<Dashboard />);

      const mainHeading = screen.getByRole('heading', { level: 1 });
      expect(mainHeading).toHaveTextContent('Welcome back, John!');
    });

    it('displays cards with proper structure', () => {
      render(<Dashboard />);

      const cards = screen.getAllByTestId('card');
      expect(cards.length).toBeGreaterThan(0);

      // Each metrics card should have header and content
      const metricsCards = cards.slice(0, 4);
      metricsCards.forEach((card) => {
        expect(card.querySelector('[data-testid="card-header"]')).toBeInTheDocument();
        expect(card.querySelector('[data-testid="card-content"]')).toBeInTheDocument();
      });
    });
  });

  describe('Integration with hooks', () => {
    it('calls useUser hook', () => {
      render(<Dashboard />);

      expect(mockUseUser).toHaveBeenCalled();
    });

    it('calls useClerkSync hook', () => {
      const mockUseClerkSync = useClerkSync as vi.MockedFunction<typeof useClerkSync>;

      render(<Dashboard />);

      expect(mockUseClerkSync).toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('provides semantic structure', () => {
      render(<Dashboard />);

      // Main heading
      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();

      // Card titles should be headings
      const cardTitles = screen.getAllByTestId('card-title');
      expect(cardTitles.length).toBeGreaterThan(0);
    });

    it('includes descriptive text for metrics', () => {
      render(<Dashboard />);

      // Each metric should have a description or change indicator
      expect(screen.getByText('+20.1% from last month')).toBeInTheDocument();
      expect(screen.getByText('+5.2% from last month')).toBeInTheDocument();
      expect(screen.getByText('No change')).toBeInTheDocument();
      expect(screen.getByText('+1 new type')).toBeInTheDocument();
    });
  });
});
