export interface UserProfile {
  id: string
  email: string
  firstName: string
  lastName: string
  memberType: string
  permissions: string[]
  organizationId?: string
  createdAt: string
  updatedAt: string
}

export interface AuthPermission {
  resource: string
  action: string
  conditions?: Record<string, unknown>
}

export interface OrganizationContext {
  id: string
  name: string
  role: string
  permissions: string[]
}

export interface TokenStatus {
  isValid: boolean
  expiresAt: number | null
  timeUntilExpiration: number | null
  needsRefresh: boolean
  refreshStatus: () => Promise<void>
}

// Clerk-specific type definitions
export interface ClerkUser {
  id: string
  emailAddresses: Array<{ emailAddress: string }>
  firstName: string | null
  lastName: string | null
  organizationMemberships: ClerkOrganizationMembership[]
}

export interface ClerkOrganization {
  id: string
  name: string
  slug: string | null
  imageUrl: string
  createdAt: Date
}

export interface ClerkOrganizationMembership {
  id: string
  role: string
  organization: ClerkOrganization
  permissions: string[]
}

// Explicit exports for better module resolution
export type { UserProfile, AuthPermission, OrganizationContext, TokenStatus, ExtendedAuthState }

export interface ExtendedAuthState {
  // Clerk state (pass-through)
  isLoaded: boolean
  isSignedIn: boolean
  user: ClerkUser | null
  
  // Enhanced organization state
  organization: ClerkOrganization | null
  isOrganizationLoaded: boolean
  organizationMemberships: ClerkOrganizationMembership[]
  activeOrganizationRole: string | null
  organizationPermissions: string[]
  
  // Application-specific state
  userProfile: UserProfile | null
  permissions: string[]
  isProfileLoaded: boolean
  
  // Loading and error states
  error: string | null
  isLoading: boolean
  
  // Token management
  tokenStatus: TokenStatus
  refreshToken: () => Promise<boolean>
  ensureValidToken: () => Promise<string | null>
  
  // Organization actions
  switchOrganization: (orgId: string) => Promise<void>
  createOrganization: (name: string) => Promise<ClerkOrganization>
  leaveOrganization: (orgId: string) => Promise<void>
  
  // Actions
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
}