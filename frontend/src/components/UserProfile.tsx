import { UserButton, useUser, useClerk } from '@clerk/clerk-react'

import { Button } from '@components/ui/Button'

interface UserProfileProps {
  showFullProfile?: boolean
  className?: string
}

export function UserProfile({ showFullProfile = false, className = '' }: UserProfileProps) {
  const { user, isLoaded } = useUser()
  const { signOut } = useClerk()

  if (!isLoaded) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-8 w-8 bg-gray-300 rounded-full"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  if (!showFullProfile) {
    return (
      <div className={`flex items-center gap-3 ${className}`}>
        <span className="text-sm text-gray-700 hidden sm:inline">
          {user.firstName} {user.lastName}
        </span>
        <UserButton
          afterSignOutUrl="/sign-in"
          appearance={{
            elements: {
              avatarBox: 'h-8 w-8 rounded-full border-2 border-gray-200 hover:border-gray-300 transition-colors'
            }
          }}
        />
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <div className="flex items-center gap-4 mb-4">
        <img
          src={user.imageUrl}
          alt={`${user.firstName} ${user.lastName}`}
          className="h-16 w-16 rounded-full border-2 border-gray-200"
        />
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            {user.firstName} {user.lastName}
          </h2>
          <p className="text-gray-600">{user.emailAddresses[0]?.emailAddress}</p>
        </div>
      </div>

      <div className="space-y-3">
        <div>
          <dt className="text-sm font-medium text-gray-500">Member Since</dt>
          <dd className="text-sm text-gray-900">
            {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
          </dd>
        </div>

        {user.phoneNumbers && user.phoneNumbers.length > 0 && (
          <div>
            <dt className="text-sm font-medium text-gray-500">Phone</dt>
            <dd className="text-sm text-gray-900">{user.phoneNumbers[0].phoneNumber}</dd>
          </div>
        )}

        <div>
          <dt className="text-sm font-medium text-gray-500">Account Status</dt>
          <dd className="text-sm">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Active
            </span>
          </dd>
        </div>
      </div>

      <div className="mt-6 flex gap-3">
        <UserButton
          afterSignOutUrl="/sign-in"
          appearance={{
            elements: {
              userButtonAvatarBox: 'hidden',
              userButtonTrigger: 'w-full justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors'
            }
          }}
        >
          Manage Account
        </UserButton>

        <Button
          variant="outline"
          onClick={() => signOut()}
          className="flex-1"
        >
          Sign Out
        </Button>
      </div>
    </div>
  )
}

export default UserProfile
