import { useClerk, useUser } from '@clerk/clerk-react';

/**
 * Debug component to check Clerk configuration and status
 * Helps diagnose authentication issues during development
 */
export function ClerkDebug() {
  const clerk = useClerk();
  const { isLoaded, isSignedIn, user } = useUser();

  return (
    <div className="fixed bottom-4 right-4 bg-gray-900 text-white p-4 rounded-lg shadow-lg max-w-md text-xs">
      <h3 className="font-bold mb-2">Clerk Debug Info</h3>
      <div className="space-y-1">
        <p>
          <strong>Loaded:</strong> {isLoaded ? '✅' : '❌'}
        </p>
        <p>
          <strong>Signed In:</strong> {isSignedIn ? '✅' : '❌'}
        </p>
        <p>
          <strong>User ID:</strong> {user?.id || 'N/A'}
        </p>
        <p>
          <strong>Email:</strong> {user?.primaryEmailAddress?.emailAddress || 'N/A'}
        </p>
        <p>
          <strong>Frontend API:</strong> {clerk.frontendApi || 'N/A'}
        </p>
        <p>
          <strong>Publishable Key:</strong>{' '}
          {import.meta.env.VITE_CLERK_PUBLISHABLE_KEY?.slice(0, 20)}...
        </p>
        <p>
          <strong>Environment:</strong> {import.meta.env.MODE}
        </p>
        <p>
          <strong>API URL:</strong> {import.meta.env.VITE_API_URL}
        </p>
      </div>
    </div>
  );
}
