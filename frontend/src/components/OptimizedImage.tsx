import { useEffect, useRef, useState, type ImgHTMLAttributes } from 'react'

import { cn } from '@utils/cn'

interface OptimizedImageProps extends ImgHTMLAttributes<HTMLImageElement> {
  src: string
  alt: string
  fallbackSrc?: string
  blurDataURL?: string
  priority?: boolean
  onLoadingComplete?: () => void
}

export function OptimizedImage({
  src,
  alt,
  fallbackSrc = '/images/placeholder.svg',
  blurDataURL,
  priority = false,
  className,
  onLoadingComplete,
  ...props
}: OptimizedImageProps) {
  const [imageSrc, setImageSrc] = useState(blurDataURL || fallbackSrc)
  const [isLoading, setIsLoading] = useState(true)
  const [isInView, setIsInView] = useState(priority)
  const imgRef = useRef<HTMLImageElement>(null)

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || !imgRef.current) {return}

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true)
            observer.disconnect()
          }
        })
      },
      {
        rootMargin: '50px', // Start loading 50px before entering viewport
      }
    )

    observer.observe(imgRef.current)

    return () => observer.disconnect()
  }, [priority])

  // Load the actual image when in view
  useEffect(() => {
    if (!isInView) {return}

    const img = new Image()
    img.src = src

    img.onload = () => {
      setImageSrc(src)
      setIsLoading(false)
      onLoadingComplete?.()
    }

    img.onerror = () => {
      setImageSrc(fallbackSrc)
      setIsLoading(false)
    }
  }, [isInView, src, fallbackSrc, onLoadingComplete])

  return (
    <img
      ref={imgRef}
      src={imageSrc}
      alt={alt}
      className={cn(
        'transition-opacity duration-300',
        isLoading ? 'opacity-0' : 'opacity-100',
        className
      )}
      loading={priority ? 'eager' : 'lazy'}
      decoding="async"
      {...props}
    />
  )
}

// Picture component for responsive images
interface PictureProps {
  sources: Array<{
    srcSet: string
    media?: string
    type?: string
  }>
  alt: string
  className?: string
  priority?: boolean
}

export function Picture({ sources, alt, className, priority = false }: PictureProps) {
  return (
    <picture>
      {sources.map((source, index) => (
        <source
          key={index}
          srcSet={source.srcSet}
          media={source.media}
          type={source.type}
        />
      ))}
      <OptimizedImage
        src={sources[sources.length - 1].srcSet}
        alt={alt}
        className={className}
        priority={priority}
      />
    </picture>
  )
}
