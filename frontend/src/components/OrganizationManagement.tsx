import { useState } from 'react'

import { useAuth } from '@/hooks/useAuth'

export function OrganizationManagement() {
  const { 
    organization, 
    organizationMemberships,
    createOrganization,
    leaveOrganization,
    activeOrganizationRole 
  } = useAuth()
  
  const [newOrgName, setNewOrgName] = useState('')
  const [isCreating, setIsCreating] = useState(false)

  const handleCreateOrganization = async () => {
    if (!newOrgName.trim()) {return}
    
    setIsCreating(true)
    try {
      await createOrganization(newOrgName)
      setNewOrgName('')
      // Show success notification
    } catch (error) {
      console.error('Failed to create organization:', error)
      // Show error notification
    } finally {
      setIsCreating(false)
    }
  }

  const handleLeaveOrganization = async (orgId: string) => {
    if (window.confirm('Are you sure you want to leave this organization?')) {
      try {
        await leaveOrganization(orgId)
        // Show success notification
      } catch (error) {
        console.error('Failed to leave organization:', error)
        // Show error notification
      }
    }
  }

  return (
    <div className="space-y-6">
      {/* Current Organization */}
      {organization && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Current Organization</h3>
          <div className="flex justify-between items-center">
            <div>
              <h4 className="font-semibold">{organization.name}</h4>
              <p className="text-sm text-gray-500 capitalize">
                Your role: {activeOrganizationRole}
              </p>
            </div>
            {activeOrganizationRole !== 'admin' && (
              <button
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                onClick={() => handleLeaveOrganization(organization.id)}
              >
                Leave Organization
              </button>
            )}
          </div>
        </div>
      )}

      {/* Organization List */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Your Organizations</h3>
        <div className="space-y-2">
          {organizationMemberships.map((membership) => (
            <div
              key={membership.organization.id}
              className="flex justify-between items-center p-3 border rounded"
            >
              <div>
                <h4 className="font-medium">{membership.organization.name}</h4>
                <p className="text-sm text-gray-500 capitalize">
                  Role: {membership.role}
                </p>
              </div>
              <div className="flex space-x-2">
                {membership.organization.id === organization?.id && (
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                    Active
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Create New Organization */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Create New Organization</h3>
        <div className="flex space-x-2">
          <input
            type="text"
            placeholder="Organization name"
            value={newOrgName}
            onChange={(e) => setNewOrgName(e.target.value)}
            disabled={isCreating}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <button
            onClick={handleCreateOrganization}
            disabled={isCreating || !newOrgName.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isCreating ? 'Creating...' : 'Create'}
          </button>
        </div>
      </div>
    </div>
  )
}