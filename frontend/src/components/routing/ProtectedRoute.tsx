import { ReactNode } from 'react'

import { RoleBasedGuard } from '@/components/guards/RoleBasedGuard'
import { ROUTE_CONFIGS, RouteConfig } from '@/config/routes'
import { Permission, Role } from '@/constants/permissions'

interface ProtectedRouteProps {
  path: string
  children: ReactNode
  
  // Override route config if needed
  requiredPermissions?: Permission[]
  allowedRoles?: Role[]
  requireAllPermissions?: boolean
  requireManagerAccess?: boolean
  requireOrganization?: boolean
}

export function ProtectedRoute({
  path,
  children,
  requiredPermissions,
  allowedRoles,
  requireAllPermissions,
  requireManagerAccess,
  requireOrganization,
}: ProtectedRouteProps) {
  // Find route configuration
  const routeConfig = findRouteConfig(path, ROUTE_CONFIGS)
  
  // Use props if provided, otherwise use route config
  const finalPermissions = requiredPermissions || routeConfig?.requiredPermissions
  const finalRoles = allowedRoles || routeConfig?.allowedRoles
  const finalRequireAll = requireAllPermissions ?? routeConfig?.requireAllPermissions ?? true
  const finalRequireManager = requireManagerAccess ?? routeConfig?.requireManagerAccess ?? false
  const finalRequireOrg = requireOrganization ?? routeConfig?.requireOrganization ?? false

  return (
    <RoleBasedGuard
      requiredPermissions={finalPermissions}
      allowedRoles={finalRoles}
      requireAllPermissions={finalRequireAll}
      requireManagerAccess={finalRequireManager}
      requireOrganization={finalRequireOrg}
    >
      {children}
    </RoleBasedGuard>
  )
}

// Helper function to find route config
function findRouteConfig(path: string, configs: RouteConfig[]): RouteConfig | null {
  for (const config of configs) {
    if (config.path === path) {
      return config
    }
    if (config.children) {
      const childConfig = findRouteConfig(path, config.children)
      if (childConfig) {return childConfig}
    }
  }
  return null
}