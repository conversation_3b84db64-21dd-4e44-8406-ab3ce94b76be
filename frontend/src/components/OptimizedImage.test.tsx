import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'


import { OptimizedImage, Picture } from './OptimizedImage'

// Mock IntersectionObserver
const mockIntersectionObserver = vi.fn()
mockIntersectionObserver.mockReturnValue({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
})
window.IntersectionObserver = mockIntersectionObserver

// Mock Image constructor
const mockImage = vi.fn(() => ({
  onload: null,
  onerror: null,
  src: '',
}))
global.Image = mockImage as typeof Image

describe('OptimizedImage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockIntersectionObserver.mockReturnValue({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    })
  })

  describe('Basic rendering', () => {
    it('renders image with basic props', () => {
      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
        />
      )

      const img = screen.getByRole('img', { name: 'Test image' })
      expect(img).toBeInTheDocument()
      expect(img).toHaveAttribute('alt', 'Test image')
    })

    it('applies custom className', () => {
      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
          className="custom-image"
        />
      )

      const img = screen.getByRole('img')
      expect(img).toHaveClass('custom-image')
    })

    it('forwards other HTML attributes', () => {
      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
          width={100}
          height={100}
          data-testid="test-image"
        />
      )

      const img = screen.getByTestId('test-image')
      expect(img).toHaveAttribute('width', '100')
      expect(img).toHaveAttribute('height', '100')
    })
  })

  describe('Loading behavior', () => {
    it('starts with opacity-0 (loading state)', () => {
      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
        />
      )

      const img = screen.getByRole('img')
      expect(img).toHaveClass('opacity-0')
    })

    it('uses fallback src initially when no blurDataURL', () => {
      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
          fallbackSrc="/placeholder.jpg"
        />
      )

      const img = screen.getByRole('img')
      expect(img).toHaveAttribute('src', '/placeholder.jpg')
    })

    it('uses blurDataURL initially when provided', () => {
      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
          blurDataURL="data:image/jpeg;base64,blur"
        />
      )

      const img = screen.getByRole('img')
      expect(img).toHaveAttribute('src', 'data:image/jpeg;base64,blur')
    })

    it('uses default fallback when none provided', () => {
      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
        />
      )

      const img = screen.getByRole('img')
      expect(img).toHaveAttribute('src', '/images/placeholder.svg')
    })
  })

  describe('Priority loading', () => {
    it('sets loading="eager" when priority is true', () => {
      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
          priority={true}
        />
      )

      const img = screen.getByRole('img')
      expect(img).toHaveAttribute('loading', 'eager')
    })

    it('sets loading="lazy" when priority is false', () => {
      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
          priority={false}
        />
      )

      const img = screen.getByRole('img')
      expect(img).toHaveAttribute('loading', 'lazy')
    })

    it('skips intersection observer when priority is true', () => {
      const mockObserve = vi.fn()
      mockIntersectionObserver.mockReturnValue({
        observe: mockObserve,
        unobserve: vi.fn(),
        disconnect: vi.fn(),
      })

      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
          priority={true}
        />
      )

      expect(mockObserve).not.toHaveBeenCalled()
    })
  })

  describe('Lazy loading with Intersection Observer', () => {
    it('sets up intersection observer when priority is false', () => {
      const mockObserve = vi.fn()
      mockIntersectionObserver.mockReturnValue({
        observe: mockObserve,
        unobserve: vi.fn(),
        disconnect: vi.fn(),
      })

      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
          priority={false}
        />
      )

      expect(mockIntersectionObserver).toHaveBeenCalledWith(
        expect.any(Function),
        { rootMargin: '50px' }
      )
      expect(mockObserve).toHaveBeenCalled()
    })

    it('loads image when intersection observer triggers', async () => {
      let intersectionCallback: (entries: IntersectionObserverEntry[]) => void = () => {}

      mockIntersectionObserver.mockImplementation((callback) => {
        intersectionCallback = callback
        return {
          observe: vi.fn(),
          unobserve: vi.fn(),
          disconnect: vi.fn(),
        }
      })

      let imageOnLoad: (() => void) | null = null
      mockImage.mockImplementation(() => {
        const img = {
          onload: null as (() => void) | null,
          onerror: null,
          src: '',
        }

        Object.defineProperty(img, 'onload', {
          set(fn: (() => void) | null) {
            imageOnLoad = fn
          },
          get() {
            return imageOnLoad
          }
        })

        return img
      })

      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
          priority={false}
        />
      )

      // Simulate intersection
      intersectionCallback([{ isIntersecting: true } as IntersectionObserverEntry])

      // Simulate image load
      if (imageOnLoad) {
        imageOnLoad()
      }

      await waitFor(() => {
        const img = screen.getByRole('img')
        expect(img).toHaveAttribute('src', 'https://example.com/image.jpg')
        expect(img).toHaveClass('opacity-100')
      })
    })

    it('handles image load error by using fallback', async () => {
      let intersectionCallback: (entries: IntersectionObserverEntry[]) => void = () => {}

      mockIntersectionObserver.mockImplementation((callback) => {
        intersectionCallback = callback
        return {
          observe: vi.fn(),
          unobserve: vi.fn(),
          disconnect: vi.fn(),
        }
      })

      let imageOnError: (() => void) | null = null
      mockImage.mockImplementation(() => {
        const img = {
          onload: null,
          onerror: null as (() => void) | null,
          src: '',
        }

        Object.defineProperty(img, 'onerror', {
          set(fn: (() => void) | null) {
            imageOnError = fn
          },
          get() {
            return imageOnError
          }
        })

        return img
      })

      render(
        <OptimizedImage
          src="https://example.com/broken-image.jpg"
          alt="Test image"
          fallbackSrc="/error-placeholder.jpg"
          priority={false}
        />
      )

      // Simulate intersection
      intersectionCallback([{ isIntersecting: true } as IntersectionObserverEntry])

      // Simulate image error
      if (imageOnError) {
        imageOnError()
      }

      await waitFor(() => {
        const img = screen.getByRole('img')
        expect(img).toHaveAttribute('src', '/error-placeholder.jpg')
        expect(img).toHaveClass('opacity-100')
      })
    })
  })

  describe('Callbacks', () => {
    it('calls onLoadingComplete when image loads', async () => {
      const onLoadingComplete = vi.fn()

      let imageOnLoad: (() => void) | null = null
      mockImage.mockImplementation(() => {
        const img = {
          onload: null as (() => void) | null,
          onerror: null,
          src: '',
        }

        Object.defineProperty(img, 'onload', {
          set(fn: (() => void) | null) {
            imageOnLoad = fn
          }
        })

        return img
      })

      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
          priority={true}
          onLoadingComplete={onLoadingComplete}
        />
      )

      // Simulate image load
      if (imageOnLoad) {
        imageOnLoad()
      }

      await waitFor(() => {
        expect(onLoadingComplete).toHaveBeenCalledTimes(1)
      })
    })
  })

  describe('Image attributes', () => {
    it('sets decoding="async"', () => {
      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
        />
      )

      const img = screen.getByRole('img')
      expect(img).toHaveAttribute('decoding', 'async')
    })

    it('maintains transition classes', () => {
      render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
        />
      )

      const img = screen.getByRole('img')
      expect(img).toHaveClass('transition-opacity', 'duration-300')
    })
  })

  describe('Cleanup', () => {
    it('disconnects intersection observer on unmount', () => {
      const mockDisconnect = vi.fn()
      mockIntersectionObserver.mockReturnValue({
        observe: vi.fn(),
        unobserve: vi.fn(),
        disconnect: mockDisconnect,
      })

      const { unmount } = render(
        <OptimizedImage
          src="https://example.com/image.jpg"
          alt="Test image"
          priority={false}
        />
      )

      unmount()
      expect(mockDisconnect).toHaveBeenCalled()
    })
  })
})

describe('Picture', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic rendering', () => {
    it('renders picture element with sources', () => {
      const sources = [
        { srcSet: 'image-small.webp', media: '(max-width: 640px)', type: 'image/webp' },
        { srcSet: 'image-large.webp', media: '(min-width: 641px)', type: 'image/webp' },
        { srcSet: 'image-fallback.jpg' }
      ]

      render(
        <Picture
          sources={sources}
          alt="Test picture"
        />
      )

      const picture = document.querySelector('picture')
      expect(picture).toBeInTheDocument()

      const sources_elements = document.querySelectorAll('source')
      expect(sources_elements).toHaveLength(3)
    })

    it('sets correct attributes on source elements', () => {
      const sources = [
        { srcSet: 'image-small.webp', media: '(max-width: 640px)', type: 'image/webp' },
        { srcSet: 'image-large.webp', media: '(min-width: 641px)', type: 'image/webp' }
      ]

      render(
        <Picture
          sources={sources}
          alt="Test picture"
        />
      )

      const sourceElements = document.querySelectorAll('source')

      expect(sourceElements[0]).toHaveAttribute('srcset', 'image-small.webp')
      expect(sourceElements[0]).toHaveAttribute('media', '(max-width: 640px)')
      expect(sourceElements[0]).toHaveAttribute('type', 'image/webp')

      expect(sourceElements[1]).toHaveAttribute('srcset', 'image-large.webp')
      expect(sourceElements[1]).toHaveAttribute('media', '(min-width: 641px)')
      expect(sourceElements[1]).toHaveAttribute('type', 'image/webp')
    })

    it('uses last source as fallback for OptimizedImage', () => {
      const sources = [
        { srcSet: 'image-small.webp', media: '(max-width: 640px)' },
        { srcSet: 'image-fallback.jpg' }
      ]

      render(
        <Picture
          sources={sources}
          alt="Test picture"
        />
      )

      const img = screen.getByRole('img')
      expect(img).toHaveAttribute('src', 'image-fallback.jpg')
    })

    it('passes props to OptimizedImage', () => {
      const sources = [
        { srcSet: 'image.jpg' }
      ]

      render(
        <Picture
          sources={sources}
          alt="Test picture"
          className="custom-picture"
          priority={true}
        />
      )

      const img = screen.getByRole('img')
      expect(img).toHaveClass('custom-picture')
      expect(img).toHaveAttribute('loading', 'eager')
    })

    it('handles sources without media or type attributes', () => {
      const sources = [
        { srcSet: 'image.jpg' }
      ]

      render(
        <Picture
          sources={sources}
          alt="Test picture"
        />
      )

      const sourceElement = document.querySelector('source')
      expect(sourceElement).toHaveAttribute('srcset', 'image.jpg')
      expect(sourceElement).not.toHaveAttribute('media')
      expect(sourceElement).not.toHaveAttribute('type')
    })
  })

  describe('Multiple sources', () => {
    it('maintains source order', () => {
      const sources = [
        { srcSet: 'first.webp', type: 'image/webp' },
        { srcSet: 'second.avif', type: 'image/avif' },
        { srcSet: 'third.jpg', type: 'image/jpeg' }
      ]

      render(
        <Picture
          sources={sources}
          alt="Test picture"
        />
      )

      const sourceElements = document.querySelectorAll('source')
      expect(sourceElements[0]).toHaveAttribute('srcset', 'first.webp')
      expect(sourceElements[1]).toHaveAttribute('srcset', 'second.avif')
      expect(sourceElements[2]).toHaveAttribute('srcset', 'third.jpg')
    })

    it('handles empty sources array', () => {
      render(
        <Picture
          sources={[]}
          alt="Test picture"
        />
      )

      const picture = document.querySelector('picture')
      expect(picture).toBeInTheDocument()

      const sourceElements = document.querySelectorAll('source')
      expect(sourceElements).toHaveLength(0)

      // Should still render img element
      const img = screen.getByRole('img')
      expect(img).toBeInTheDocument()
    })
  })

  describe('Responsive images', () => {
    it('creates responsive image with different formats', () => {
      const sources = [
        { srcSet: 'image-480.webp 480w, image-800.webp 800w', type: 'image/webp' },
        { srcSet: 'image-480.jpg 480w, image-800.jpg 800w', type: 'image/jpeg' }
      ]

      render(
        <Picture
          sources={sources}
          alt="Responsive image"
        />
      )

      const sourceElements = document.querySelectorAll('source')
      expect(sourceElements[0]).toHaveAttribute('srcset', 'image-480.webp 480w, image-800.webp 800w')
      expect(sourceElements[0]).toHaveAttribute('type', 'image/webp')

      expect(sourceElements[1]).toHaveAttribute('srcset', 'image-480.jpg 480w, image-800.jpg 800w')
      expect(sourceElements[1]).toHaveAttribute('type', 'image/jpeg')
    })
  })
})
