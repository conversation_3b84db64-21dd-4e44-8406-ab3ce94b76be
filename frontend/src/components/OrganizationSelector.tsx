import { useState } from 'react'

import { useAuth } from '@/hooks/useAuth'

export function OrganizationSelector() {
  const { 
    organization,
    organizationMemberships,
    switchOrganization,
    isOrganizationLoaded 
  } = useAuth()
  
  const [isLoading, setIsLoading] = useState(false)

  const handleOrganizationSwitch = async (orgId: string) => {
    if (orgId === organization?.id) {return}
    
    setIsLoading(true)
    try {
      await switchOrganization(orgId)
    } catch (error) {
      console.error('Organization switch failed:', error)
      // Show error notification
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOrganizationLoaded) {
    return (
      <div className="w-48 h-10 bg-gray-200 animate-pulse rounded" />
    )
  }

  if (organizationMemberships.length === 0) {
    return (
      <div className="text-sm text-gray-500">
        No organizations
      </div>
    )
  }

  return (
    <select
      value={organization?.id || ''}
      onChange={(e) => handleOrganizationSwitch(e.target.value)}
      disabled={isLoading}
      className="w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
    >
      <option value="">Select organization...</option>
      {organizationMemberships.map((membership) => (
        <option 
          key={membership.organization.id} 
          value={membership.organization.id}
        >
          {membership.organization.name} ({membership.role})
        </option>
      ))}
    </select>
  )
}