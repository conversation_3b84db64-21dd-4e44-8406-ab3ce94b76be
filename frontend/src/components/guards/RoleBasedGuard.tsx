import { Navigate } from '@tanstack/react-router'
import { ReactNode } from 'react'

import { Permission, Role } from '@/constants/permissions'
import { useAuth } from '@/hooks/useAuth'
import { hasPermission, hasAnyPermission, hasAllPermissions } from '@/utils/permissions'

interface RoleBasedGuardProps {
  children: ReactNode
  
  // Permission-based access
  requiredPermissions?: Permission[]
  requireAllPermissions?: boolean // true = all permissions required, false = any permission required
  
  // Role-based access
  allowedRoles?: Role[]
  blockedRoles?: Role[]
  
  // Hierarchical access
  requireManagerAccess?: boolean
  requireSubordinateAccess?: boolean
  subordinateId?: string
  
  // Organization context
  requireOrganization?: boolean
  organizationId?: string
  
  // Redirect options
  unauthorizedRedirect?: string
  noOrganizationRedirect?: string
}

export function RoleBasedGuard({
  children,
  requiredPermissions = [],
  requireAllPermissions = true,
  allowedRoles = [],
  blockedRoles = [],
  requireManagerAccess = false,
  requireSubordinateAccess = false,
  subordinateId,
  requireOrganization = false,
  organizationId,
  unauthorizedRedirect = '/unauthorized',
  noOrganizationRedirect = '/select-organization',
}: RoleBasedGuardProps) {
  const {
    isLoaded,
    isSignedIn,
    userProfile,
    permissions,
    organizationPermissions,
    organization,
    isOrganizationLoaded,
  } = useAuth()

  // Show loading while authentication loads
  if (!isLoaded || !userProfile || (requireOrganization && !isOrganizationLoaded)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // Check if user is signed in
  if (!isSignedIn) {
    return <Navigate to="/sign-in" />
  }

  // Check organization requirements
  if (requireOrganization && !organization) {
    return <Navigate to={noOrganizationRedirect} />
  }

  if (organizationId && organization?.id !== organizationId) {
    return <Navigate to={unauthorizedRedirect} />
  }

  // Check role-based access
  const userRole = userProfile?.memberType || userProfile?.role
  
  if (allowedRoles.length > 0 && !allowedRoles.includes(userRole)) {
    return <Navigate to={unauthorizedRedirect} />
  }

  if (blockedRoles.length > 0 && blockedRoles.includes(userRole)) {
    return <Navigate to={unauthorizedRedirect} />
  }

  // Check permission-based access
  const effectivePermissions = [
    ...permissions,
    ...organizationPermissions,
  ]

  if (requiredPermissions.length > 0) {
    const hasAccess = requireAllPermissions
      ? hasAllPermissions(effectivePermissions, requiredPermissions)
      : hasAnyPermission(effectivePermissions, requiredPermissions)
    
    if (!hasAccess) {
      return <Navigate to={unauthorizedRedirect} />
    }
  }

  // Check hierarchical access (manager/subordinate relationships)
  if (requireManagerAccess) {
    const canManageUsers = hasPermission(effectivePermissions, 'manage_subordinates')
    if (!canManageUsers) {
      return <Navigate to={unauthorizedRedirect} />
    }
  }

  if (requireSubordinateAccess && subordinateId) {
    // Check if user can access specific subordinate
    // This would require backend API call to verify relationship
    // For now, we'll use permission-based check
    const canViewSubordinates = hasPermission(effectivePermissions, 'view_subordinates')
    if (!canViewSubordinates) {
      return <Navigate to={unauthorizedRedirect} />
    }
  }

  return <>{children}</>
}