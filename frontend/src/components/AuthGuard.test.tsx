import { useUser } from '@clerk/clerk-react';
import type { MockUseUserReturn } from '@test/types';
import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';


import { AuthGuard } from './AuthGuard';

vi.mock('@clerk/clerk-react', () => ({
  useUser: vi.fn(),
}));

// Mock TanStack Router Navigate component
vi.mock('@tanstack/react-router', () => ({
  Navigate: ({ to }: { to: string }) => (
    <div data-testid="navigate" data-to={to}>
      Redirecting...
    </div>
  ),
}));

const mockUseUser = useUser as vi.MockedFunction<typeof useUser>;

describe('AuthGuard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Loading states', () => {
    it('shows loading fallback when Clerk is not loaded', () => {
      mockUseUser.mockReturnValue({
        isLoaded: false,
        isSignedIn: false,
        user: null,
      } as MockUseUserReturn);

      render(
        <AuthGuard>
          <div>Protected content</div>
        </AuthGuard>,
      );

      // Should show default loading component (spinner SVG)
      expect(document.querySelector('svg')).toBeInTheDocument();
      expect(screen.queryByText('Protected content')).not.toBeInTheDocument();
    });

    it('shows custom fallback when provided and not loaded', () => {
      mockUseUser.mockReturnValue({
        isLoaded: false,
        isSignedIn: false,
        user: null,
      } as MockUseUserReturn);

      render(
        <AuthGuard fallback={<div>Custom loading...</div>}>
          <div>Protected content</div>
        </AuthGuard>,
      );

      expect(screen.getByText('Custom loading...')).toBeInTheDocument();
      expect(screen.queryByText('Protected content')).not.toBeInTheDocument();
    });
  });

  describe('Authentication states', () => {
    it('redirects to sign-in when not authenticated', () => {
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: false,
        user: null,
      } as MockUseUserReturn);

      render(
        <AuthGuard>
          <div>Protected content</div>
        </AuthGuard>,
      );

      const navigate = screen.getByTestId('navigate');
      expect(navigate).toBeInTheDocument();
      expect(navigate).toHaveAttribute('data-to', '/sign-in');
      expect(screen.queryByText('Protected content')).not.toBeInTheDocument();
    });

    it('redirects to custom path when redirectTo is provided', () => {
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: false,
        user: null,
      } as MockUseUserReturn);

      render(
        <AuthGuard redirectTo="/custom-login">
          <div>Protected content</div>
        </AuthGuard>,
      );

      const navigate = screen.getByTestId('navigate');
      expect(navigate).toHaveAttribute('data-to', '/custom-login');
    });

    it('renders children when authenticated', () => {
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        user: {
          id: 'user_123',
          firstName: 'John',
          lastName: 'Doe',
          emailAddresses: [{ emailAddress: '<EMAIL>' }],
        },
      } as MockUseUserReturn);

      render(
        <AuthGuard>
          <div>Protected content</div>
        </AuthGuard>,
      );

      expect(screen.getByText('Protected content')).toBeInTheDocument();
      expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
    });

    it('renders multiple children when authenticated', () => {
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        user: { id: 'user_123', emailAddresses: [] },
      } as MockUseUserReturn);

      render(
        <AuthGuard>
          <div>Content 1</div>
          <div>Content 2</div>
        </AuthGuard>,
      );

      expect(screen.getByText('Content 1')).toBeInTheDocument();
      expect(screen.getByText('Content 2')).toBeInTheDocument();
    });
  });

  describe('Organization requirements', () => {
    it('redirects to select-organization when requireOrganization is true and user has no org memberships', () => {
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        user: {
          id: 'user_123',
          organizationMemberships: [],
        },
      } as MockUseUserReturn);

      render(
        <AuthGuard requireOrganization={true}>
          <div>Protected content</div>
        </AuthGuard>,
      );

      const navigate = screen.getByTestId('navigate');
      expect(navigate).toHaveAttribute('data-to', '/select-organization');
      expect(screen.queryByText('Protected content')).not.toBeInTheDocument();
    });

    it('renders children when requireOrganization is true and user has org memberships', () => {
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        user: {
          id: 'user_123',
          organizationMemberships: [{ organization: { id: 'org_123' } }],
        },
      } as MockUseUserReturn);

      render(
        <AuthGuard requireOrganization={true}>
          <div>Protected content</div>
        </AuthGuard>,
      );

      expect(screen.getByText('Protected content')).toBeInTheDocument();
      expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
    });

    it('renders children when requireOrganization is false regardless of org memberships', () => {
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        user: {
          id: 'user_123',
          organizationMemberships: [],
        },
      } as MockUseUserReturn);

      render(
        <AuthGuard requireOrganization={false}>
          <div>Protected content</div>
        </AuthGuard>,
      );

      expect(screen.getByText('Protected content')).toBeInTheDocument();
      expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
    });
  });

  describe('Permission requirements', () => {
    it('renders children when no permissions are required', () => {
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        user: { id: 'user_123' },
      } as MockUseUserReturn);

      render(
        <AuthGuard requiredPermissions={[]}>
          <div>Protected content</div>
        </AuthGuard>,
      );

      expect(screen.getByText('Protected content')).toBeInTheDocument();
    });

    it('renders children when permissions are provided (TODO: implement permission checking)', () => {
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        user: { id: 'user_123' },
      } as MockUseUserReturn);

      render(
        <AuthGuard requiredPermissions={['read:users', 'write:users']}>
          <div>Protected content</div>
        </AuthGuard>,
      );

      // For now, this should still render since permission checking is not implemented
      expect(screen.getByText('Protected content')).toBeInTheDocument();
    });
  });

  describe('Component rendering', () => {
    it('renders complex child components', () => {
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        user: { id: 'user_123', emailAddresses: [] },
      } as MockUseUserReturn);

      const ComplexComponent = () => (
        <div>
          <h1>Dashboard</h1>
          <button>Action</button>
          <p>Some content</p>
        </div>
      );

      render(
        <AuthGuard>
          <ComplexComponent />
        </AuthGuard>,
      );

      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Action')).toBeInTheDocument();
      expect(screen.getByText('Some content')).toBeInTheDocument();
    });

    it('preserves event handlers in children', () => {
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        user: { id: 'user_123', emailAddresses: [] },
      } as MockUseUserReturn);

      const handleClick = vi.fn();

      render(
        <AuthGuard>
          <button onClick={handleClick}>Click me</button>
        </AuthGuard>,
      );

      const button = screen.getByText('Click me');
      button.click();
      expect(handleClick).toHaveBeenCalledTimes(1);
    });
  });

  describe('Edge cases', () => {
    it('handles undefined user', () => {
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: false,
        user: undefined,
      } as MockUseUserReturn);

      render(
        <AuthGuard>
          <div>Protected content</div>
        </AuthGuard>,
      );

      expect(screen.getByTestId('navigate')).toBeInTheDocument();
      expect(screen.queryByText('Protected content')).not.toBeInTheDocument();
    });

    it('handles null user', () => {
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: false,
        user: null,
      } as MockUseUserReturn);

      render(
        <AuthGuard>
          <div>Protected content</div>
        </AuthGuard>,
      );

      expect(screen.getByTestId('navigate')).toBeInTheDocument();
    });
  });

  describe('Default props', () => {
    it('uses default redirectTo when not provided', () => {
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: false,
        user: null,
      } as MockUseUserReturn);

      render(
        <AuthGuard>
          <div>Protected content</div>
        </AuthGuard>,
      );

      const navigate = screen.getByTestId('navigate');
      expect(navigate).toHaveAttribute('data-to', '/sign-in');
    });
  });

  describe('State transitions', () => {
    it('handles transition from loading to authenticated', () => {
      const { rerender } = render(
        <AuthGuard>
          <div>Protected content</div>
        </AuthGuard>,
      );

      // Initial loading state
      mockUseUser.mockReturnValue({
        isLoaded: false,
        isSignedIn: false,
        user: null,
      } as MockUseUserReturn);

      rerender(
        <AuthGuard>
          <div>Protected content</div>
        </AuthGuard>,
      );

      expect(document.querySelector('svg')).toBeInTheDocument();

      // Transition to authenticated
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: true,
        user: { id: 'user_123', emailAddresses: [] },
      } as MockUseUserReturn);

      rerender(
        <AuthGuard>
          <div>Protected content</div>
        </AuthGuard>,
      );

      expect(screen.getByText('Protected content')).toBeInTheDocument();
      expect(document.querySelector('svg')).not.toBeInTheDocument();
    });

    it('handles transition from loading to unauthenticated', () => {
      const { rerender } = render(
        <AuthGuard>
          <div>Protected content</div>
        </AuthGuard>,
      );

      // Initial loading state
      mockUseUser.mockReturnValue({
        isLoaded: false,
        isSignedIn: false,
        user: null,
      } as MockUseUserReturn);

      rerender(
        <AuthGuard>
          <div>Protected content</div>
        </AuthGuard>,
      );

      expect(document.querySelector('svg')).toBeInTheDocument();

      // Transition to unauthenticated
      mockUseUser.mockReturnValue({
        isLoaded: true,
        isSignedIn: false,
        user: null,
      } as MockUseUserReturn);

      rerender(
        <AuthGuard>
          <div>Protected content</div>
        </AuthGuard>,
      );

      expect(screen.getByTestId('navigate')).toBeInTheDocument();
      expect(document.querySelector('svg')).not.toBeInTheDocument();
      expect(screen.queryByText('Protected content')).not.toBeInTheDocument();
    });
  });
});
