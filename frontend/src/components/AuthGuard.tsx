import { Navigate } from '@tanstack/react-router';
import { ReactNode } from 'react';

import { Loading } from '@/components/ui/Loading';
import { useAuth } from '@/hooks/useAuth';
import { hasAllPermissions } from '@/utils/permissions';

interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
  redirectTo?: string;
  requireOrganization?: boolean;
  requiredPermissions?: string[];
  requiredOrganizationRole?: string[];
  organizationId?: string;
}

/**
 * AuthGuard component that protects routes with Clerk authentication
 *
 * Features:
 * - Automatic session validation with Clerk
 * - Loading state during auth checks
 * - Customizable fallback UI
 * - Optional redirect on auth failure
 * - Organization membership validation
 * - Permission-based access control
 */
export function AuthGuard({
  children,
  fallback = <Loading />,
  redirectTo = '/sign-in',
  requireOrganization = false,
  requiredPermissions = [],
  requiredOrganizationRole = [],
  organizationId,
}: AuthGuardProps) {
  const {
    isLoaded,
    isSignedIn,
    organization,
    isOrganizationLoaded,
    organizationPermissions,
    activeOrganizationRole,
    permissions
  } = useAuth();

  // Show loading state while Clerk loads
  if (!isLoaded || (requireOrganization && !isOrganizationLoaded)) {
    return <>{fallback}</>;
  }

  // Redirect to sign-in if not authenticated
  if (!isSignedIn) {
    return <Navigate to={redirectTo} />;
  }

  // Check organization requirement
  if (requireOrganization && !organization) {
    return <Navigate to="/select-organization" />;
  }

  // Check specific organization requirement
  if (organizationId && organization?.id !== organizationId) {
    return <Navigate to="/unauthorized" />;
  }

  // Check organization role requirements
  if (requiredOrganizationRole.length > 0 && activeOrganizationRole) {
    if (!requiredOrganizationRole.includes(activeOrganizationRole)) {
      return <Navigate to="/unauthorized" />;
    }
  }

  // Check permissions (organization-scoped and user permissions)
  if (requiredPermissions.length > 0) {
    const effectivePermissions = [
      ...permissions,
      ...organizationPermissions,
    ];
    
    if (!hasAllPermissions(effectivePermissions, requiredPermissions)) {
      return <Navigate to="/unauthorized" />;
    }
  }

  // Render children if authenticated and authorized
  return <>{children}</>;
}

export default AuthGuard;
