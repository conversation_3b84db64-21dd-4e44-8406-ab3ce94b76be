import { Link, useLocation } from '@tanstack/react-router'
import { useMemo } from 'react'

import { ROUTE_CONFIGS, RouteConfig } from '@/config/routes'
import { useAuth } from '@/hooks/useAuth'
// Import removed - utility functions not used in current implementation

interface NavigationItem extends RouteConfig {
  isActive: boolean
  hasAccess: boolean
}

export function DynamicNavigation() {
  const { permissions, organizationPermissions, userProfile } = useAuth()
  const location = useLocation()
  
  const userRole = userProfile?.memberType

  // Filter routes based on user permissions and roles
  const accessibleRoutes = useMemo(() => {
    const effectivePermissions = [
      ...permissions,
      ...organizationPermissions,
    ]
    
    const checkRouteAccess = (route: RouteConfig): boolean => {
      // Check role-based access
      if (route.allowedRoles && route.allowedRoles.length > 0) {
        if (!route.allowedRoles.includes(userRole)) {
          return false
        }
      }
      
      // Check permission-based access
      if (route.requiredPermissions && route.requiredPermissions.length > 0) {
        const hasAccess = route.requireAllPermissions
          ? route.requiredPermissions.every(p => effectivePermissions.includes(p))
          : route.requiredPermissions.some(p => effectivePermissions.includes(p))
        
        if (!hasAccess) {
          return false
        }
      }
      
      return true
    }

    return ROUTE_CONFIGS
      .filter(route => route.showInNavigation && checkRouteAccess(route))
      .map(route => ({
        ...route,
        isActive: location.pathname.startsWith(route.path),
        hasAccess: checkRouteAccess(route),
        children: route.children?.filter(checkRouteAccess),
      }))
      .sort((a, b) => (a.order || 999) - (b.order || 999))
  }, [permissions, organizationPermissions, userRole, location.pathname])

  // Group routes by navigation group
  const groupedRoutes = useMemo(() => {
    const groups: Record<string, NavigationItem[]> = {}
    
    accessibleRoutes.forEach(route => {
      const group = route.navigationGroup || 'Main'
      if (!groups[group]) {
        groups[group] = []
      }
      groups[group].push(route)
    })
    
    return groups
  }, [accessibleRoutes])

  return (
    <nav className="space-y-6">
      {Object.entries(groupedRoutes).map(([groupName, routes]) => (
        <div key={groupName}>
          <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wide">
            {groupName}
          </h3>
          <div className="mt-2 space-y-1">
            {routes.map((route) => (
              <Link
                key={route.path}
                to={route.path}
                className={`group flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                  route.isActive
                    ? 'bg-blue-100 text-blue-900'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                {route.icon && (
                  <span className="flex-shrink-0 mr-3 h-5 w-5">
                    {/* Icon component based on route.icon */}
                    <span className="text-current">📊</span>
                  </span>
                )}
                {route.title}
              </Link>
            ))}
          </div>
        </div>
      ))}
    </nav>
  )
}