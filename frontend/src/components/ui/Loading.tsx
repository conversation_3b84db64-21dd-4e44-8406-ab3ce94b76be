import * as React from 'react'

import { cn } from '@utils/cn'

export interface LoadingProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'default' | 'lg'
  variant?: 'spinner' | 'dots' | 'pulse'
}

const Loading = React.forwardRef<HTMLDivElement, LoadingProps>(
  ({ className, size = 'default', variant = 'spinner', ...props }, ref) => {
    const sizeClasses = {
      sm: 'h-4 w-4',
      default: 'h-8 w-8',
      lg: 'h-12 w-12',
    }

    if (variant === 'spinner') {
      return (
        <div
          ref={ref}
          className={cn('inline-block', className)}
          {...props}
        >
          <svg
            className={cn(
              'animate-spin text-primary-600',
              sizeClasses[size]
            )}
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        </div>
      )
    }

    if (variant === 'dots') {
      return (
        <div
          ref={ref}
          className={cn('flex space-x-2', className)}
          {...props}
        >
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={cn(
                'rounded-full bg-primary-600',
                size === 'sm' && 'h-1.5 w-1.5',
                size === 'default' && 'h-2.5 w-2.5',
                size === 'lg' && 'h-3.5 w-3.5'
              )}
              style={{
                animation: 'bounce 1.4s ease-in-out infinite',
                animationDelay: `${i * 0.16}s`,
              }}
            />
          ))}
        </div>
      )
    }

    if (variant === 'pulse') {
      return (
        <div
          ref={ref}
          className={cn(
            'animate-pulse rounded-md bg-gray-200',
            sizeClasses[size],
            className
          )}
          {...props}
        />
      )
    }

    return null
  }
)
Loading.displayName = 'Loading'

export { Loading }
