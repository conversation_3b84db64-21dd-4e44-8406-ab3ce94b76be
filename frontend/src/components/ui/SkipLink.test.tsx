import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'

import { SkipLink, SkipLinks } from './SkipLink'

describe('SkipLink', () => {
  it('renders with correct href and text', () => {
    render(
      <SkipLink href="#main-content">
        Skip to main content
      </SkipLink>
    )

    const link = screen.getByRole('link', { name: 'Skip to main content' })
    expect(link).toBeInTheDocument()
    expect(link).toHaveAttribute('href', '#main-content')
  })

  it('has proper accessibility classes by default', () => {
    render(
      <SkipLink href="#main-content">
        Skip to main content
      </SkipLink>
    )

    const link = screen.getByRole('link')

    // Should be positioned off-screen by default
    expect(link).toHaveClass('absolute', 'left-[-10000px]', 'overflow-hidden')

    // Should have focus styles
    expect(link).toHaveClass(
      'focus:fixed',
      'focus:top-4',
      'focus:left-4',
      'focus:w-auto',
      'focus:h-auto',
      'focus:overflow-visible'
    )
  })

  it('has proper visual styling for focused state', () => {
    render(
      <SkipLink href="#main-content">
        Skip to main content
      </SkipLink>
    )

    const link = screen.getByRole('link')

    // Visual appearance when focused
    expect(link).toHaveClass(
      'focus:z-50',
      'focus:px-4',
      'focus:py-2',
      'focus:bg-gray-900',
      'focus:text-white',
      'focus:rounded-md',
      'focus:shadow-lg'
    )

    // Typography
    expect(link).toHaveClass(
      'focus:text-sm',
      'focus:font-medium',
      'focus:no-underline'
    )
  })

  it('has proper focus ring styles', () => {
    render(
      <SkipLink href="#main-content">
        Skip to main content
      </SkipLink>
    )

    const link = screen.getByRole('link')

    expect(link).toHaveClass(
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-white',
      'focus:ring-offset-2',
      'focus:ring-offset-gray-900'
    )
  })

  it('applies custom className', () => {
    render(
      <SkipLink href="#main-content" className="custom-skip-link">
        Skip to main content
      </SkipLink>
    )

    const link = screen.getByRole('link')
    expect(link).toHaveClass('custom-skip-link')
  })

  it('forwards other props', () => {
    render(
      <SkipLink
        href="#main-content"
        data-testid="skip-link"
        tabIndex={0}
      >
        Skip to main content
      </SkipLink>
    )

    const link = screen.getByTestId('skip-link')
    expect(link).toHaveAttribute('tabIndex', '0')
  })

  it('supports ref forwarding', () => {
    let ref: HTMLAnchorElement | null = null

    render(
      <SkipLink href="#main-content" ref={(el) => { ref = el }}>
        Skip to main content
      </SkipLink>
    )

    expect(ref).toBeInstanceOf(HTMLAnchorElement)
  })

  it('accepts different href patterns', () => {
    const { rerender } = render(
      <SkipLink href="#main-content">Skip to main</SkipLink>
    )

    expect(screen.getByRole('link')).toHaveAttribute('href', '#main-content')

    rerender(
      <SkipLink href="#navigation">Skip to navigation</SkipLink>
    )

    expect(screen.getByRole('link')).toHaveAttribute('href', '#navigation')
  })
})

describe('SkipLinks', () => {
  const defaultLinks = [
    { href: '#main-content', label: 'Skip to main content' },
    { href: '#navigation', label: 'Skip to navigation' },
    { href: '#footer', label: 'Skip to footer' }
  ]

  it('renders multiple skip links', () => {
    render(<SkipLinks links={defaultLinks} />)

    expect(screen.getByRole('link', { name: 'Skip to main content' })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: 'Skip to navigation' })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: 'Skip to footer' })).toBeInTheDocument()
  })

  it('has proper navigation structure', () => {
    render(<SkipLinks links={defaultLinks} />)

    const nav = screen.getByRole('navigation', { name: 'Skip navigation' })
    expect(nav).toBeInTheDocument()

    // All links should be within the navigation
    const links = screen.getAllByRole('link')
    links.forEach(link => {
      expect(nav).toContainElement(link)
    })
  })

  it('has screen reader friendly structure', () => {
    render(<SkipLinks links={defaultLinks} data-testid="skip-links" />)

    const container = screen.getByTestId('skip-links')
    expect(container).toHaveClass('sr-only', 'focus-within:not-sr-only')
  })

  it('applies custom className', () => {
    render(
      <SkipLinks
        links={defaultLinks}
        className="custom-skip-links"
        data-testid="skip-links"
      />
    )

    const container = screen.getByTestId('skip-links')
    expect(container).toHaveClass('custom-skip-links')
  })

  it('forwards other props', () => {
    render(
      <SkipLinks
        links={defaultLinks}
        data-testid="skip-links"
        role="banner"
      />
    )

    const container = screen.getByTestId('skip-links')
    expect(container).toHaveAttribute('role', 'banner')
  })

  it('supports ref forwarding', () => {
    let ref: HTMLDivElement | null = null

    render(
      <SkipLinks
        links={defaultLinks}
        ref={(el) => { ref = el }}
      />
    )

    expect(ref).toBeInstanceOf(HTMLDivElement)
  })

  it('handles empty links array', () => {
    render(<SkipLinks links={[]} data-testid="skip-links" />)

    const container = screen.getByTestId('skip-links')
    expect(container).toBeInTheDocument()

    const nav = screen.getByRole('navigation')
    expect(nav).toBeInTheDocument()

    const links = screen.queryAllByRole('link')
    expect(links).toHaveLength(0)
  })

  it('creates unique keys for each link', () => {
    const linksWithDuplicateLabels = [
      { href: '#section1', label: 'Skip to content' },
      { href: '#section2', label: 'Skip to content' }
    ]

    render(<SkipLinks links={linksWithDuplicateLabels} />)

    const links = screen.getAllByRole('link', { name: 'Skip to content' })
    expect(links).toHaveLength(2)
    expect(links[0]).toHaveAttribute('href', '#section1')
    expect(links[1]).toHaveAttribute('href', '#section2')
  })

  it('maintains link order', () => {
    render(<SkipLinks links={defaultLinks} />)

    const links = screen.getAllByRole('link')
    expect(links[0]).toHaveTextContent('Skip to main content')
    expect(links[1]).toHaveTextContent('Skip to navigation')
    expect(links[2]).toHaveTextContent('Skip to footer')
  })
})

describe('SkipLink Accessibility', () => {
  it('is keyboard accessible', () => {
    render(
      <SkipLink href="#main-content">
        Skip to main content
      </SkipLink>
    )

    const link = screen.getByRole('link')

    // Should be focusable
    link.focus()
    expect(document.activeElement).toBe(link)
  })

  it('provides meaningful link text', () => {
    render(
      <SkipLink href="#main-content">
        Skip to main content
      </SkipLink>
    )

    // Link should have descriptive text for screen readers
    expect(screen.getByRole('link', { name: 'Skip to main content' })).toBeInTheDocument()
  })

  it('uses semantic navigation structure in SkipLinks', () => {
    const links = [
      { href: '#main-content', label: 'Skip to main content' }
    ]

    render(<SkipLinks links={links} />)

    // Should use nav element with proper labeling
    const nav = screen.getByRole('navigation', { name: 'Skip navigation' })
    expect(nav).toBeInTheDocument()
  })
})
