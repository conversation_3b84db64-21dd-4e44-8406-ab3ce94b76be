import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite'

import { <PERSON><PERSON> } from './Button'
import { Form, FormField, FormSection } from './Form'
import { Input } from './Input'

const meta = {
  title: 'Components/Form',
  component: Form,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Accessible form components with proper ARIA labels, error states, and keyboard navigation.',
      },
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Form>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    onSubmit: (e) => console.log('Form submitted', e),
  },
  render: (args) => (
    <div className="w-96 p-6">
      <Form {...args}>
        <FormField
          label="Email Address"
          id="email"
          required
          helpText="We'll never share your email with anyone else."
        >
          <Input type="email" placeholder="Enter your email" />
        </FormField>

        <FormField
          label="Password"
          id="password"
          required
        >
          <Input type="password" placeholder="Enter your password" />
        </FormField>

        <Button type="submit" className="w-full">
          Sign In
        </Button>
      </Form>
    </div>
  ),
}

export const WithErrors: Story = {
  args: {
    onSubmit: (e) => console.log('Form submitted', e),
  },
  render: (args) => (
    <div className="w-96 p-6">
      <Form {...args}>
        <FormField
          label="Email Address"
          id="email-error"
          required
          error="Please enter a valid email address."
          helpText="We'll never share your email with anyone else."
        >
          <Input type="email" placeholder="Enter your email" />
        </FormField>

        <FormField
          label="Password"
          id="password-error"
          required
          error="Password must be at least 8 characters long."
        >
          <Input type="password" placeholder="Enter your password" />
        </FormField>

        <Button type="submit" className="w-full">
          Sign In
        </Button>
      </Form>
    </div>
  ),
}

export const WithSections: Story = {
  args: {
    onSubmit: (e) => console.log('Form submitted', e),
  },
  render: (args) => (
    <div className="w-96 p-6">
      <Form {...args}>
        <FormSection
          title="Personal Information"
          description="Please provide your personal details."
        >
          <FormField
            label="First Name"
            id="firstName"
            required
          >
            <Input type="text" placeholder="Enter your first name" />
          </FormField>

          <FormField
            label="Last Name"
            id="lastName"
            required
          >
            <Input type="text" placeholder="Enter your last name" />
          </FormField>
        </FormSection>

        <FormSection
          title="Account Details"
          description="Set up your account credentials."
        >
          <FormField
            label="Email Address"
            id="email-section"
            required
            helpText="We'll use this for account notifications."
          >
            <Input type="email" placeholder="Enter your email" />
          </FormField>

          <FormField
            label="Password"
            id="password-section"
            required
            helpText="Must be at least 8 characters long."
          >
            <Input type="password" placeholder="Create a password" />
          </FormField>
        </FormSection>

        <Button type="submit" className="w-full">
          Create Account
        </Button>
      </Form>
    </div>
  ),
}

export const KeyboardNavigation: Story = {
  args: {
    onSubmit: (e) => console.log('Form submitted', e),
  },
  render: (args) => (
    <div className="w-96 p-6">
      <div className="mb-4 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-900 mb-2">Keyboard Navigation Test</h3>
        <p className="text-sm text-blue-800">
          Use Tab to navigate between fields, Enter to submit, and screen readers will announce labels and errors.
        </p>
      </div>

      <Form {...args}>
        <FormField
          label="Field 1"
          id="field1"
          required
          helpText="This field has help text."
        >
          <Input type="text" placeholder="First field" />
        </FormField>

        <FormField
          label="Field 2"
          id="field2"
          error="This field has an error message."
        >
          <Input type="text" placeholder="Second field" />
        </FormField>

        <FormField
          label="Field 3"
          id="field3"
        >
          <Input type="text" placeholder="Third field" />
        </FormField>

        <div className="flex gap-2">
          <Button type="button" variant="outline">
            Cancel
          </Button>
          <Button type="submit">
            Submit Form
          </Button>
        </div>
      </Form>
    </div>
  ),
}
