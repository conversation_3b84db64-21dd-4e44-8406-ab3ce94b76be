import * as React from 'react'

import { cn } from '../../utils/cn'

export interface FormFieldProps {
  label: string
  id: string
  required?: boolean
  error?: string
  helpText?: string
  children: React.ReactElement
}

export const FormField = React.forwardRef<HTMLDivElement, FormFieldProps>(
  ({ label, id, required, error, helpText, children, ...props }, ref) => {
    const errorId = error ? `${id}-error` : undefined
    const helpId = helpText ? `${id}-help` : undefined
    const describedBy = [errorId, helpId].filter(Boolean).join(' ') || undefined

    // Clone the child element to add accessibility attributes
    const childWithProps = React.cloneElement(children, {
      id,
      'aria-required': required,
      'aria-invalid': !!error,
      'aria-describedby': describedBy,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any)

    return (
      <div ref={ref} className="space-y-2" {...props}>
        <label
          htmlFor={id}
          className={cn(
            "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
            required && "after:content-['*'] after:ml-1 after:text-red-500"
          )}
        >
          {label}
        </label>
        {childWithProps}
        {helpText && (
          <p id={helpId} className="text-xs text-gray-600" role="note">
            {helpText}
          </p>
        )}
        {error && (
          <p
            id={errorId}
            className="text-xs text-red-600"
            role="alert"
            aria-live="polite"
          >
            {error}
          </p>
        )}
      </div>
    )
  }
)
FormField.displayName = 'FormField'

export interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void
}

export const Form = React.forwardRef<HTMLFormElement, FormProps>(
  ({ className, children, onSubmit, ...props }, ref) => {
    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault()
      onSubmit(e)
    }

    return (
      <form
        ref={ref}
        className={cn("space-y-6", className)}
        onSubmit={handleSubmit}
        noValidate
        {...props}
      >
        {children}
      </form>
    )
  }
)
Form.displayName = 'Form'

export interface FormSectionProps extends React.HTMLAttributes<HTMLFieldSetElement> {
  title?: string
  description?: string
}

export const FormSection = React.forwardRef<HTMLFieldSetElement, FormSectionProps>(
  ({ className, title, description, children, ...props }, ref) => {
    return (
      <fieldset
        ref={ref}
        className={cn("space-y-4", className)}
        {...props}
      >
        {title && (
          <legend className="text-lg font-semibold leading-none tracking-tight">
            {title}
          </legend>
        )}
        {description && (
          <p className="text-sm text-gray-600">
            {description}
          </p>
        )}
        {children}
      </fieldset>
    )
  }
)
FormSection.displayName = 'FormSection'
