import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'

import { Loading } from './Loading'

describe('Loading', () => {
  describe('Spinner variant', () => {
    it('renders spinner by default', () => {
      render(<Loading data-testid="loading" />)

      const loading = screen.getByTestId('loading')
      expect(loading).toBeInTheDocument()
      expect(loading.querySelector('svg')).toBeInTheDocument()
    })

    it('applies correct size classes for spinner', () => {
      const { rerender } = render(<Loading size="sm" data-testid="loading" />)
      let svg = screen.getByTestId('loading').querySelector('svg')
      expect(svg).toHaveClass('h-4', 'w-4')

      rerender(<Loading size="default" data-testid="loading" />)
      svg = screen.getByTestId('loading').querySelector('svg')
      expect(svg).toHaveClass('h-8', 'w-8')

      rerender(<Loading size="lg" data-testid="loading" />)
      svg = screen.getByTestId('loading').querySelector('svg')
      expect(svg).toHaveClass('h-12', 'w-12')
    })

    it('has animation classes for spinner', () => {
      render(<Loading variant="spinner" data-testid="loading" />)

      const svg = screen.getByTestId('loading').querySelector('svg')
      expect(svg).toHaveClass('animate-spin', 'text-primary-600')
    })
  })

  describe('Dots variant', () => {
    it('renders three dots', () => {
      render(<Loading variant="dots" data-testid="loading" />)

      const loading = screen.getByTestId('loading')
      const dots = loading.querySelectorAll('div')
      expect(dots).toHaveLength(3)
    })

    it('applies correct size classes for dots', () => {
      const { rerender } = render(<Loading variant="dots" size="sm" data-testid="loading" />)
      let dots = screen.getByTestId('loading').querySelectorAll('div')
      dots.forEach(dot => {
        expect(dot).toHaveClass('h-1.5', 'w-1.5')
      })

      rerender(<Loading variant="dots" size="default" data-testid="loading" />)
      dots = screen.getByTestId('loading').querySelectorAll('div')
      dots.forEach(dot => {
        expect(dot).toHaveClass('h-2.5', 'w-2.5')
      })

      rerender(<Loading variant="dots" size="lg" data-testid="loading" />)
      dots = screen.getByTestId('loading').querySelectorAll('div')
      dots.forEach(dot => {
        expect(dot).toHaveClass('h-3.5', 'w-3.5')
      })
    })

    it('has proper styling for dots', () => {
      render(<Loading variant="dots" data-testid="loading" />)

      const loading = screen.getByTestId('loading')
      expect(loading).toHaveClass('flex', 'space-x-2')

      const dots = loading.querySelectorAll('div')
      dots.forEach(dot => {
        expect(dot).toHaveClass('rounded-full', 'bg-primary-600')
      })
    })

    it('has staggered animation delays for dots', () => {
      render(<Loading variant="dots" data-testid="loading" />)

      const dots = screen.getByTestId('loading').querySelectorAll('div')
      expect(dots[0]).toHaveStyle('animation-delay: 0s')
      expect(dots[1]).toHaveStyle('animation-delay: 0.16s')
      expect(dots[2]).toHaveStyle('animation-delay: 0.32s')
    })
  })

  describe('Pulse variant', () => {
    it('renders pulse variant', () => {
      render(<Loading variant="pulse" data-testid="loading" />)

      const loading = screen.getByTestId('loading')
      expect(loading).toHaveClass('animate-pulse', 'rounded-md', 'bg-gray-200')
    })

    it('applies correct size classes for pulse', () => {
      const { rerender } = render(<Loading variant="pulse" size="sm" data-testid="loading" />)
      let loading = screen.getByTestId('loading')
      expect(loading).toHaveClass('h-4', 'w-4')

      rerender(<Loading variant="pulse" size="default" data-testid="loading" />)
      loading = screen.getByTestId('loading')
      expect(loading).toHaveClass('h-8', 'w-8')

      rerender(<Loading variant="pulse" size="lg" data-testid="loading" />)
      loading = screen.getByTestId('loading')
      expect(loading).toHaveClass('h-12', 'w-12')
    })
  })

  describe('Custom props', () => {
    it('applies custom className', () => {
      render(<Loading className="custom-loading" data-testid="loading" />)

      const loading = screen.getByTestId('loading')
      expect(loading).toHaveClass('custom-loading')
    })

    it('forwards other props', () => {
      render(<Loading aria-label="Loading content" data-testid="loading" />)

      const loading = screen.getByTestId('loading')
      expect(loading).toHaveAttribute('aria-label', 'Loading content')
    })

    it('supports ref forwarding', () => {
      let ref: HTMLDivElement | null = null

      render(<Loading ref={(el) => { ref = el }} data-testid="loading" />)

      expect(ref).toBeInstanceOf(HTMLDivElement)
    })
  })

  describe('Default values', () => {
    it('uses default size when not specified', () => {
      render(<Loading data-testid="loading" />)

      const svg = screen.getByTestId('loading').querySelector('svg')
      expect(svg).toHaveClass('h-8', 'w-8')
    })

    it('uses spinner variant when not specified', () => {
      render(<Loading data-testid="loading" />)

      const loading = screen.getByTestId('loading')
      expect(loading.querySelector('svg')).toBeInTheDocument()
    })
  })

  describe('Edge cases', () => {
    it('returns null for invalid variant', () => {
      // This test might need to be adjusted based on TypeScript constraints
      const { container } = render(<Loading variant={'invalid' as 'spinner'} />)
      expect(container.firstChild).toBeNull()
    })
  })

  describe('Accessibility', () => {
    it('can accept aria-label for screen readers', () => {
      render(
        <Loading
          aria-label="Loading data"
          data-testid="loading"
        />
      )

      const loading = screen.getByTestId('loading')
      expect(loading).toHaveAttribute('aria-label', 'Loading data')
    })

    it('can accept role attribute', () => {
      render(
        <Loading
          role="status"
          aria-live="polite"
          data-testid="loading"
        />
      )

      const loading = screen.getByTestId('loading')
      expect(loading).toHaveAttribute('role', 'status')
      expect(loading).toHaveAttribute('aria-live', 'polite')
    })
  })
})
