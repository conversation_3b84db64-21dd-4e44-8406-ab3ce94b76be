import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'

import { Form, FormField, FormSection } from './Form'
import { Input } from './Input'

describe('Form', () => {
  it('renders form with children', () => {
    render(
      <Form onSubmit={vi.fn()}>
        <div>Test content</div>
      </Form>
    )
    expect(screen.getByText('Test content')).toBeInTheDocument()
  })

  it('calls onSubmit when form is submitted', () => {
    const handleSubmit = vi.fn()
    render(
      <Form onSubmit={handleSubmit}>
        <button type="submit">Submit</button>
      </Form>
    )

    fireEvent.click(screen.getByText('Submit'))
    expect(handleSubmit).toHaveBeenCalledTimes(1)
  })

  it('prevents default form submission', () => {
    const handleSubmit = vi.fn()
    const preventDefault = vi.fn()

    render(
      <Form onSubmit={handleSubmit}>
        <button type="submit">Submit</button>
      </Form>
    )

    const form = screen.getByRole('form')
    fireEvent.submit(form, { preventDefault } as React.FormEvent<HTMLFormElement>)

    expect(preventDefault).toHaveBeenCalled()
  })

  it('applies custom className', () => {
    render(
      <Form onSubmit={vi.fn()} className="custom-class">
        <div>Content</div>
      </Form>
    )

    const form = screen.getByRole('form')
    expect(form).toHaveClass('custom-class')
  })

  it('has noValidate attribute', () => {
    render(
      <Form onSubmit={vi.fn()}>
        <div>Content</div>
      </Form>
    )

    const form = screen.getByRole('form')
    expect(form).toHaveAttribute('noValidate')
  })
})

describe('FormField', () => {
  it('renders label and input correctly', () => {
    render(
      <FormField label="Email" id="email">
        <Input type="email" />
      </FormField>
    )

    expect(screen.getByLabelText('Email')).toBeInTheDocument()
    expect(screen.getByRole('textbox')).toBeInTheDocument()
  })

  it('shows required indicator when required', () => {
    render(
      <FormField label="Email" id="email" required>
        <Input type="email" />
      </FormField>
    )

    const label = screen.getByText('Email')
    expect(label).toHaveClass("after:content-['*']")
  })

  it('displays error message when error is provided', () => {
    render(
      <FormField label="Email" id="email" error="Invalid email">
        <Input type="email" />
      </FormField>
    )

    expect(screen.getByText('Invalid email')).toBeInTheDocument()
    expect(screen.getByRole('alert')).toBeInTheDocument()
  })

  it('displays help text when provided', () => {
    render(
      <FormField label="Email" id="email" helpText="Enter your email address">
        <Input type="email" />
      </FormField>
    )

    expect(screen.getByText('Enter your email address')).toBeInTheDocument()
  })

  it('sets proper accessibility attributes on input', () => {
    render(
      <FormField label="Email" id="email" required error="Invalid email" helpText="Help text">
        <Input type="email" />
      </FormField>
    )

    const input = screen.getByRole('textbox')
    expect(input).toHaveAttribute('id', 'email')
    expect(input).toHaveAttribute('aria-required', 'true')
    expect(input).toHaveAttribute('aria-invalid', 'true')
    expect(input).toHaveAttribute('aria-describedby')
  })

  it('associates label with input using htmlFor', () => {
    render(
      <FormField label="Email" id="email">
        <Input type="email" />
      </FormField>
    )

    const label = screen.getByText('Email')
    expect(label).toHaveAttribute('for', 'email')
  })

  it('error message has proper ARIA attributes', () => {
    render(
      <FormField label="Email" id="email" error="Invalid email">
        <Input type="email" />
      </FormField>
    )

    const errorMessage = screen.getByText('Invalid email')
    expect(errorMessage).toHaveAttribute('role', 'alert')
    expect(errorMessage).toHaveAttribute('aria-live', 'polite')
    expect(errorMessage).toHaveAttribute('id', 'email-error')
  })

  it('help text has proper attributes', () => {
    render(
      <FormField label="Email" id="email" helpText="Help text">
        <Input type="email" />
      </FormField>
    )

    const helpText = screen.getByText('Help text')
    expect(helpText).toHaveAttribute('role', 'note')
    expect(helpText).toHaveAttribute('id', 'email-help')
  })
})

describe('FormSection', () => {
  it('renders with title and children', () => {
    render(
      <FormSection title="Personal Information">
        <div>Form fields</div>
      </FormSection>
    )

    expect(screen.getByText('Personal Information')).toBeInTheDocument()
    expect(screen.getByText('Form fields')).toBeInTheDocument()
  })

  it('renders with description', () => {
    render(
      <FormSection title="Personal Information" description="Enter your details">
        <div>Form fields</div>
      </FormSection>
    )

    expect(screen.getByText('Enter your details')).toBeInTheDocument()
  })

  it('renders without title', () => {
    render(
      <FormSection>
        <div>Form fields</div>
      </FormSection>
    )

    expect(screen.getByText('Form fields')).toBeInTheDocument()
    expect(screen.queryByRole('group')).toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(
      <FormSection className="custom-section">
        <div>Content</div>
      </FormSection>
    )

    const fieldset = screen.getByRole('group')
    expect(fieldset).toHaveClass('custom-section')
  })

  it('uses fieldset and legend for semantic structure', () => {
    render(
      <FormSection title="Personal Information">
        <div>Form fields</div>
      </FormSection>
    )

    expect(screen.getByRole('group')).toBeInTheDocument()
    const legend = screen.getByText('Personal Information')
    expect(legend.tagName).toBe('LEGEND')
  })
})
