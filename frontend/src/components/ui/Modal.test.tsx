import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'

import {
  Modal,
  ModalTrigger,
  ModalContent,
  ModalHeader,
  ModalTitle,
  ModalDescription,
  ModalFooter,
  ModalClose,
} from './Modal'

describe('Modal Components', () => {
  describe('Modal', () => {
    it('renders modal content when open', () => {
      render(
        <Modal open>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Test Modal</ModalTitle>
              <ModalDescription>This is a test modal</ModalDescription>
            </ModalHeader>
          </ModalContent>
        </Modal>
      )

      expect(screen.getByText('Test Modal')).toBeInTheDocument()
      expect(screen.getByText('This is a test modal')).toBeInTheDocument()
    })

    it('does not render modal content when closed', () => {
      render(
        <Modal open={false}>
          <ModalContent>
            <ModalTitle>Test Modal</ModalTitle>
          </ModalContent>
        </Modal>
      )

      expect(screen.queryByText('Test Modal')).not.toBeInTheDocument()
    })

    it('calls onOpenChange when modal state changes', () => {
      const handleOpenChange = vi.fn()

      render(
        <Modal open onOpenChange={handleOpenChange}>
          <ModalContent>
            <ModalTitle>Test Modal</ModalTitle>
          </ModalContent>
        </Modal>
      )

      // Click the close button
      const closeButton = screen.getByRole('button', { name: /close/i })
      fireEvent.click(closeButton)

      expect(handleOpenChange).toHaveBeenCalledWith(false)
    })
  })

  describe('ModalTrigger', () => {
    it('renders trigger button', () => {
      render(
        <Modal>
          <ModalTrigger asChild>
            <button>Open Modal</button>
          </ModalTrigger>
          <ModalContent>
            <ModalTitle>Test Modal</ModalTitle>
          </ModalContent>
        </Modal>
      )

      expect(screen.getByText('Open Modal')).toBeInTheDocument()
    })

    it('opens modal when trigger is clicked', () => {
      render(
        <Modal>
          <ModalTrigger asChild>
            <button>Open Modal</button>
          </ModalTrigger>
          <ModalContent>
            <ModalTitle>Test Modal</ModalTitle>
          </ModalContent>
        </Modal>
      )

      fireEvent.click(screen.getByText('Open Modal'))
      expect(screen.getByText('Test Modal')).toBeInTheDocument()
    })
  })

  describe('ModalContent', () => {
    it('renders with default classes', () => {
      render(
        <Modal open>
          <ModalContent data-testid="modal-content">
            <ModalTitle>Test Modal</ModalTitle>
          </ModalContent>
        </Modal>
      )

      const content = screen.getByTestId('modal-content')
      expect(content).toHaveClass('fixed', 'left-[50%]', 'top-[50%]')
    })

    it('applies custom className', () => {
      render(
        <Modal open>
          <ModalContent className="custom-modal" data-testid="modal-content">
            <ModalTitle>Test Modal</ModalTitle>
          </ModalContent>
        </Modal>
      )

      const content = screen.getByTestId('modal-content')
      expect(content).toHaveClass('custom-modal')
    })

    it('includes close button with screen reader text', () => {
      render(
        <Modal open>
          <ModalContent>
            <ModalTitle>Test Modal</ModalTitle>
          </ModalContent>
        </Modal>
      )

      const closeButton = screen.getByRole('button', { name: /close/i })
      expect(closeButton).toBeInTheDocument()
      expect(screen.getByText('Close')).toHaveClass('sr-only')
    })

    it('closes modal when close button is clicked', () => {
      const handleOpenChange = vi.fn()

      render(
        <Modal open onOpenChange={handleOpenChange}>
          <ModalContent>
            <ModalTitle>Test Modal</ModalTitle>
          </ModalContent>
        </Modal>
      )

      const closeButton = screen.getByRole('button', { name: /close/i })
      fireEvent.click(closeButton)

      expect(handleOpenChange).toHaveBeenCalledWith(false)
    })
  })

  describe('ModalHeader', () => {
    it('renders with proper layout classes', () => {
      render(
        <Modal open>
          <ModalContent>
            <ModalHeader data-testid="modal-header">
              <ModalTitle>Test Modal</ModalTitle>
            </ModalHeader>
          </ModalContent>
        </Modal>
      )

      const header = screen.getByTestId('modal-header')
      expect(header).toHaveClass('flex', 'flex-col', 'space-y-1.5')
    })

    it('applies custom className', () => {
      render(
        <Modal open>
          <ModalContent>
            <ModalHeader className="custom-header" data-testid="modal-header">
              <ModalTitle>Test Modal</ModalTitle>
            </ModalHeader>
          </ModalContent>
        </Modal>
      )

      const header = screen.getByTestId('modal-header')
      expect(header).toHaveClass('custom-header')
    })
  })

  describe('ModalTitle', () => {
    it('renders title with proper styling', () => {
      render(
        <Modal open>
          <ModalContent>
            <ModalTitle>Test Modal Title</ModalTitle>
          </ModalContent>
        </Modal>
      )

      const title = screen.getByText('Test Modal Title')
      expect(title).toHaveClass('text-lg', 'font-semibold')
    })

    it('applies custom className', () => {
      render(
        <Modal open>
          <ModalContent>
            <ModalTitle className="custom-title">Test Modal Title</ModalTitle>
          </ModalContent>
        </Modal>
      )

      const title = screen.getByText('Test Modal Title')
      expect(title).toHaveClass('custom-title')
    })
  })

  describe('ModalDescription', () => {
    it('renders description with proper styling', () => {
      render(
        <Modal open>
          <ModalContent>
            <ModalDescription>This is a modal description</ModalDescription>
          </ModalContent>
        </Modal>
      )

      const description = screen.getByText('This is a modal description')
      expect(description).toHaveClass('text-sm', 'text-gray-500')
    })

    it('applies custom className', () => {
      render(
        <Modal open>
          <ModalContent>
            <ModalDescription className="custom-description">
              This is a modal description
            </ModalDescription>
          </ModalContent>
        </Modal>
      )

      const description = screen.getByText('This is a modal description')
      expect(description).toHaveClass('custom-description')
    })
  })

  describe('ModalFooter', () => {
    it('renders with proper layout classes', () => {
      render(
        <Modal open>
          <ModalContent>
            <ModalFooter data-testid="modal-footer">
              <button>Cancel</button>
              <button>Save</button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )

      const footer = screen.getByTestId('modal-footer')
      expect(footer).toHaveClass('flex', 'flex-col-reverse', 'sm:flex-row')
    })

    it('applies custom className', () => {
      render(
        <Modal open>
          <ModalContent>
            <ModalFooter className="custom-footer" data-testid="modal-footer">
              <button>Cancel</button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )

      const footer = screen.getByTestId('modal-footer')
      expect(footer).toHaveClass('custom-footer')
    })
  })

  describe('Complete Modal Example', () => {
    it('renders complete modal with all components', () => {
      render(
        <Modal open>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Delete Account</ModalTitle>
              <ModalDescription>
                This action cannot be undone. This will permanently delete your account.
              </ModalDescription>
            </ModalHeader>
            <ModalFooter>
              <ModalClose asChild>
                <button>Cancel</button>
              </ModalClose>
              <button>Delete Account</button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )

      expect(screen.getByText('Delete Account')).toBeInTheDocument()
      expect(screen.getByText('This action cannot be undone. This will permanently delete your account.')).toBeInTheDocument()
      expect(screen.getByText('Cancel')).toBeInTheDocument()
    })

    it('closes modal when ModalClose is clicked', () => {
      const handleOpenChange = vi.fn()

      render(
        <Modal open onOpenChange={handleOpenChange}>
          <ModalContent>
            <ModalTitle>Test Modal</ModalTitle>
            <ModalFooter>
              <ModalClose asChild>
                <button>Cancel</button>
              </ModalClose>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )

      fireEvent.click(screen.getByText('Cancel'))
      expect(handleOpenChange).toHaveBeenCalledWith(false)
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(
        <Modal open>
          <ModalContent>
            <ModalTitle>Test Modal</ModalTitle>
            <ModalDescription>Test description</ModalDescription>
          </ModalContent>
        </Modal>
      )

      // Modal should have role="dialog"
      expect(screen.getByRole('dialog')).toBeInTheDocument()
    })

    it('supports keyboard navigation', () => {
      const handleOpenChange = vi.fn()

      render(
        <Modal open onOpenChange={handleOpenChange}>
          <ModalContent>
            <ModalTitle>Test Modal</ModalTitle>
          </ModalContent>
        </Modal>
      )

      // Escape key should close modal
      fireEvent.keyDown(document, { key: 'Escape' })
      expect(handleOpenChange).toHaveBeenCalledWith(false)
    })
  })
})
