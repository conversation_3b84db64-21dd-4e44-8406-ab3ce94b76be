import * as React from 'react'

import { cn } from '../../utils/cn'

export interface SkipLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  href: string
  children: React.ReactNode
}

export const SkipLink = React.forwardRef<HTMLAnchorElement, SkipLinkProps>(
  ({ className, href, children, ...props }, ref) => {
    return (
      <a
        ref={ref}
        href={href}
        className={cn(
          // Position skip link off-screen by default
          'absolute left-[-10000px] top-auto w-1 h-1 overflow-hidden',
          // Show skip link when focused
          'focus:fixed focus:top-4 focus:left-4 focus:w-auto focus:h-auto focus:overflow-visible',
          // Style the visible skip link
          'focus:z-50 focus:px-4 focus:py-2 focus:bg-gray-900 focus:text-white focus:rounded-md focus:shadow-lg',
          // Ensure proper contrast and typography
          'focus:text-sm focus:font-medium focus:no-underline',
          // Focus states
          'focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-900',
          className
        )}
        {...props}
      >
        {children}
      </a>
    )
  }
)
SkipLink.displayName = 'SkipLink'

export interface SkipLinksProps extends React.HTMLAttributes<HTMLDivElement> {
  links: Array<{
    href: string
    label: string
  }>
}

export const SkipLinks = React.forwardRef<HTMLDivElement, SkipLinksProps>(
  ({ className, links, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("sr-only focus-within:not-sr-only", className)}
        {...props}
      >
        <nav aria-label="Skip navigation">
          {links.map((link, index) => (
            <SkipLink key={index} href={link.href}>
              {link.label}
            </SkipLink>
          ))}
        </nav>
      </div>
    )
  }
)
SkipLinks.displayName = 'SkipLinks'
