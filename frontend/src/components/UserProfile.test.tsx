import { useUser, useClerk } from '@clerk/clerk-react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'

import { UserProfile } from './UserProfile'

// Type definitions for test mocks
interface MockButtonProps {
  children: React.ReactNode
  onClick?: () => void
  variant?: string
  className?: string
}

// Mock Clerk hooks and components
vi.mock('@clerk/clerk-react', () => ({
  useUser: vi.fn(),
  useClerk: vi.fn(),
  UserButton: vi.fn(({ children, afterSignOutUrl }) => (
    <div data-testid="user-button" data-after-sign-out-url={afterSignOutUrl}>
      {children || 'User Button'}
    </div>
  )),
}))

// Mock Button component
vi.mock('@components/ui/Button', () => ({
  Button: ({ children, onClick, variant, className }: MockButtonProps) => (
    <button
      onClick={onClick}
      data-variant={variant}
      className={className}
      data-testid="button"
    >
      {children}
    </button>
  ),
}))

const mockUseUser = useUser as vi.MockedFunction<typeof useUser>
const mockUseClerk = useClerk as vi.MockedFunction<typeof useClerk>

const mockUser = {
  id: 'user_123',
  firstName: 'John',
  lastName: 'Doe',
  imageUrl: 'https://example.com/avatar.jpg',
  emailAddresses: [{ emailAddress: '<EMAIL>' }],
  phoneNumbers: [{ phoneNumber: '+**********' }],
  createdAt: new Date('2023-01-01').toISOString(),
}

describe('UserProfile', () => {
  const mockSignOut = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    mockUseClerk.mockReturnValue({
      signOut: mockSignOut,
    })
  })

  describe('Loading states', () => {
    it('shows loading skeleton when user is not loaded', () => {
      mockUseUser.mockReturnValue({
        user: null,
        isLoaded: false,
      })

      render(<UserProfile />)

      const loadingElement = document.querySelector('.animate-pulse')
      expect(loadingElement).toBeInTheDocument()
      expect(screen.queryByText('John')).not.toBeInTheDocument()
    })

    it('applies custom className to loading skeleton', () => {
      mockUseUser.mockReturnValue({
        user: null,
        isLoaded: false,
      })

      render(<UserProfile className="custom-class" />)

      const loadingElement = document.querySelector('.animate-pulse')
      expect(loadingElement).toHaveClass('custom-class')
    })
  })

  describe('No user state', () => {
    it('returns null when user is loaded but no user exists', () => {
      mockUseUser.mockReturnValue({
        user: null,
        isLoaded: true,
      })

      const { container } = render(<UserProfile />)
      expect(container.firstChild).toBeNull()
    })
  })

  describe('Compact profile (default)', () => {
    beforeEach(() => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      })
    })

    it('renders compact profile by default', () => {
      render(<UserProfile />)

      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByTestId('user-button')).toBeInTheDocument()
    })

    it('hides name on small screens', () => {
      render(<UserProfile />)

      const nameElement = screen.getByText('John Doe')
      expect(nameElement).toHaveClass('hidden', 'sm:inline')
    })

    it('applies custom className to compact profile', () => {
      render(<UserProfile className="custom-profile" />)

      const profileContainer = screen.getByText('John Doe').closest('div')
      expect(profileContainer).toHaveClass('custom-profile')
    })

    it('configures UserButton with correct props', () => {
      render(<UserProfile />)

      const userButton = screen.getByTestId('user-button')
      expect(userButton).toHaveAttribute('data-after-sign-out-url', '/sign-in')
    })
  })

  describe('Full profile', () => {
    beforeEach(() => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      })
    })

    it('renders full profile when showFullProfile is true', () => {
      render(<UserProfile showFullProfile={true} />)

      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      expect(screen.getByText('Member Since')).toBeInTheDocument()
      expect(screen.getByText('1/1/2023')).toBeInTheDocument()
    })

    it('displays user avatar in full profile', () => {
      render(<UserProfile showFullProfile={true} />)

      const avatar = screen.getByAltText('John Doe')
      expect(avatar).toBeInTheDocument()
      expect(avatar).toHaveAttribute('src', 'https://example.com/avatar.jpg')
    })

    it('displays phone number when available', () => {
      render(<UserProfile showFullProfile={true} />)

      expect(screen.getByText('Phone')).toBeInTheDocument()
      expect(screen.getByText('+**********')).toBeInTheDocument()
    })

    it('does not display phone section when no phone numbers', () => {
      const userWithoutPhone = {
        ...mockUser,
        phoneNumbers: [],
      }

      mockUseUser.mockReturnValue({
        user: userWithoutPhone,
        isLoaded: true,
      })

      render(<UserProfile showFullProfile={true} />)

      expect(screen.queryByText('Phone')).not.toBeInTheDocument()
    })

    it('displays account status as active', () => {
      render(<UserProfile showFullProfile={true} />)

      expect(screen.getByText('Account Status')).toBeInTheDocument()
      expect(screen.getByText('Active')).toBeInTheDocument()

      const statusBadge = screen.getByText('Active')
      expect(statusBadge).toHaveClass('bg-green-100', 'text-green-800')
    })

    it('shows manage account button and sign out button', () => {
      render(<UserProfile showFullProfile={true} />)

      expect(screen.getByTestId('user-button')).toBeInTheDocument()
      expect(screen.getByText('Sign Out')).toBeInTheDocument()
    })

    it('calls signOut when sign out button is clicked', () => {
      render(<UserProfile showFullProfile={true} />)

      const signOutButton = screen.getByText('Sign Out')
      fireEvent.click(signOutButton)

      expect(mockSignOut).toHaveBeenCalledTimes(1)
    })

    it('applies custom className to full profile', () => {
      render(<UserProfile showFullProfile={true} className="custom-full-profile" />)

      const profileContainer = screen.getByText('John Doe').closest('.bg-white')
      expect(profileContainer).toHaveClass('custom-full-profile')
    })
  })

  describe('Edge cases', () => {
    it('handles missing email address', () => {
      const userWithoutEmail = {
        ...mockUser,
        emailAddresses: [],
      }

      mockUseUser.mockReturnValue({
        user: userWithoutEmail,
        isLoaded: true,
      })

      render(<UserProfile showFullProfile={true} />)

      expect(screen.getByText('John Doe')).toBeInTheDocument()
      // Should still render the profile even without email
    })

    it('handles missing creation date', () => {
      const userWithoutCreatedAt = {
        ...mockUser,
        createdAt: null,
      }

      mockUseUser.mockReturnValue({
        user: userWithoutCreatedAt,
        isLoaded: true,
      })

      render(<UserProfile showFullProfile={true} />)

      expect(screen.getByText('N/A')).toBeInTheDocument()
    })

    it('handles missing first or last name', () => {
      const userWithoutLastName = {
        ...mockUser,
        lastName: null,
      }

      mockUseUser.mockReturnValue({
        user: userWithoutLastName,
        isLoaded: true,
      })

      render(<UserProfile />)

      expect(screen.getByText('John ')).toBeInTheDocument()
    })

    it('handles completely missing names', () => {
      const userWithoutNames = {
        ...mockUser,
        firstName: null,
        lastName: null,
      }

      mockUseUser.mockReturnValue({
        user: userWithoutNames,
        isLoaded: true,
      })

      render(<UserProfile />)

      // Should still render UserButton even without names
      expect(screen.getByTestId('user-button')).toBeInTheDocument()
    })
  })

  describe('Profile variants', () => {
    beforeEach(() => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      })
    })

    it('switches between compact and full profile', () => {
      const { rerender } = render(<UserProfile showFullProfile={false} />)

      // Compact profile should not show email or member since
      expect(screen.queryByText('<EMAIL>')).not.toBeInTheDocument()
      expect(screen.queryByText('Member Since')).not.toBeInTheDocument()

      rerender(<UserProfile showFullProfile={true} />)

      // Full profile should show email and member since
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      expect(screen.getByText('Member Since')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    beforeEach(() => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      })
    })

    it('has proper alt text for avatar', () => {
      render(<UserProfile showFullProfile={true} />)

      const avatar = screen.getByAltText('John Doe')
      expect(avatar).toBeInTheDocument()
    })

    it('uses semantic HTML structure', () => {
      render(<UserProfile showFullProfile={true} />)

      // Should use proper heading structure
      const heading = screen.getByRole('heading', { level: 2 })
      expect(heading).toHaveTextContent('John Doe')
    })

    it('uses description list for user details', () => {
      render(<UserProfile showFullProfile={true} />)

      const memberSinceLabel = screen.getByText('Member Since')
      const memberSinceValue = screen.getByText('1/1/2023')

      expect(memberSinceLabel.tagName).toBe('DT')
      expect(memberSinceValue.tagName).toBe('DD')
    })

    it('has accessible button for sign out', () => {
      render(<UserProfile showFullProfile={true} />)

      const signOutButton = screen.getByRole('button', { name: 'Sign Out' })
      expect(signOutButton).toBeInTheDocument()
    })
  })
})
