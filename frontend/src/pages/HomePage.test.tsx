import type { MockComponentProps } from '@test/types'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'


import { HomePage } from './HomePage'

// Mock UI components
vi.mock('@components/ui/Button', () => ({
  Button: ({ children, className, variant, ...props }: MockComponentProps & { variant?: string }) => (
    <button
      className={className}
      data-variant={variant}
      {...props}
    >
      {children}
    </button>
  ),
}))

vi.mock('@components/ui/Card', () => ({
  Card: ({ children, ...props }: MockComponentProps) => (
    <div data-testid="card" {...props}>{children}</div>
  ),
  CardContent: ({ children, ...props }: MockComponentProps) => (
    <div data-testid="card-content" {...props}>{children}</div>
  ),
  CardDescription: ({ children, ...props }: MockComponentProps) => (
    <p data-testid="card-description" {...props}>{children}</p>
  ),
  CardHeader: ({ children, ...props }: MockComponentProps) => (
    <div data-testid="card-header" {...props}>{children}</div>
  ),
  CardTitle: ({ children, ...props }: MockComponentProps) => (
    <h3 data-testid="card-title" {...props}>{children}</h3>
  ),
}))

describe('HomePage', () => {
  describe('Basic rendering', () => {
    it('renders without errors', () => {
      expect(() => render(<HomePage />)).not.toThrow()
    })

    it('displays the main heading', () => {
      render(<HomePage />)

      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Welcome to XD Incentives')
    })

    it('has proper page structure with container and grid', () => {
      render(<HomePage />)

      const container = screen.getByText('Welcome to XD Incentives').closest('div')
      expect(container).toHaveClass('container', 'mx-auto', 'px-4', 'py-8')
    })
  })

  describe('Card components', () => {
    it('renders three main cards', () => {
      render(<HomePage />)

      const cards = screen.getAllByTestId('card')
      expect(cards).toHaveLength(3)
    })

    it('displays Dashboard card with correct content', () => {
      render(<HomePage />)

      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      expect(screen.getByText('View your performance metrics')).toBeInTheDocument()
      expect(screen.getByText('Go to Dashboard')).toBeInTheDocument()
    })

    it('displays Teams card with correct content', () => {
      render(<HomePage />)

      expect(screen.getByText('Teams')).toBeInTheDocument()
      expect(screen.getByText('Manage your team members')).toBeInTheDocument()
      expect(screen.getByText('View Teams')).toBeInTheDocument()
    })

    it('displays Reports card with correct content', () => {
      render(<HomePage />)

      expect(screen.getByText('Reports')).toBeInTheDocument()
      expect(screen.getByText('Generate and view reports')).toBeInTheDocument()
      expect(screen.getByText('View Reports')).toBeInTheDocument()
    })

    it('renders card headers and content correctly', () => {
      render(<HomePage />)

      // Each card should have a header and content
      const headers = screen.getAllByTestId('card-header')
      const contents = screen.getAllByTestId('card-content')

      expect(headers).toHaveLength(3)
      expect(contents).toHaveLength(3)
    })

    it('renders card titles and descriptions', () => {
      render(<HomePage />)

      const titles = screen.getAllByTestId('card-title')
      const descriptions = screen.getAllByTestId('card-description')

      expect(titles).toHaveLength(3)
      expect(descriptions).toHaveLength(3)
    })
  })

  describe('Button variants', () => {
    it('uses default variant for Dashboard button', () => {
      render(<HomePage />)

      const dashboardButton = screen.getByText('Go to Dashboard')
      expect(dashboardButton).not.toHaveAttribute('data-variant')
      expect(dashboardButton).toHaveClass('w-full')
    })

    it('uses outline variant for Teams button', () => {
      render(<HomePage />)

      const teamsButton = screen.getByText('View Teams')
      expect(teamsButton).toHaveAttribute('data-variant', 'outline')
      expect(teamsButton).toHaveClass('w-full')
    })

    it('uses secondary variant for Reports button', () => {
      render(<HomePage />)

      const reportsButton = screen.getByText('View Reports')
      expect(reportsButton).toHaveAttribute('data-variant', 'secondary')
      expect(reportsButton).toHaveClass('w-full')
    })
  })

  describe('Layout and styling', () => {
    it('applies responsive grid classes', () => {
      render(<HomePage />)

      const gridContainer = screen.getByText('Dashboard').closest('.grid')
      expect(gridContainer).toHaveClass(
        'grid',
        'gap-6',
        'md:grid-cols-2',
        'lg:grid-cols-3'
      )
    })

    it('has proper spacing for main heading', () => {
      render(<HomePage />)

      const heading = screen.getByRole('heading', { level: 1 })
      expect(heading).toHaveClass('text-4xl', 'font-bold', 'mb-8')
    })

    it('applies container styling correctly', () => {
      render(<HomePage />)

      const container = screen.getByText('Welcome to XD Incentives').parentElement
      expect(container).toHaveClass('container', 'mx-auto', 'px-4', 'py-8')
    })
  })

  describe('Interactive elements', () => {
    it('renders clickable buttons', () => {
      render(<HomePage />)

      const dashboardButton = screen.getByText('Go to Dashboard')
      const teamsButton = screen.getByText('View Teams')
      const reportsButton = screen.getByText('View Reports')

      expect(dashboardButton.tagName).toBe('BUTTON')
      expect(teamsButton.tagName).toBe('BUTTON')
      expect(reportsButton.tagName).toBe('BUTTON')
    })

    it('handles button clicks without errors', () => {
      render(<HomePage />)

      const dashboardButton = screen.getByText('Go to Dashboard')

      expect(() => {
        fireEvent.click(dashboardButton)
      }).not.toThrow()
    })

    it('all buttons are interactive', () => {
      render(<HomePage />)

      const buttons = screen.getAllByRole('button')
      expect(buttons).toHaveLength(3)

      buttons.forEach(button => {
        expect(button).toBeInTheDocument()
        expect(button).not.toBeDisabled()
      })
    })
  })

  describe('Content structure', () => {
    it('maintains consistent card structure', () => {
      render(<HomePage />)

      const cardTitles = ['Dashboard', 'Teams', 'Reports']
      const cardDescriptions = [
        'View your performance metrics',
        'Manage your team members',
        'Generate and view reports'
      ]
      const buttonTexts = [
        'Go to Dashboard',
        'View Teams',
        'View Reports'
      ]

      cardTitles.forEach(title => {
        expect(screen.getByText(title)).toBeInTheDocument()
      })

      cardDescriptions.forEach(description => {
        expect(screen.getByText(description)).toBeInTheDocument()
      })

      buttonTexts.forEach(buttonText => {
        expect(screen.getByText(buttonText)).toBeInTheDocument()
      })
    })

    it('uses semantic HTML structure', () => {
      render(<HomePage />)

      // Main heading should be h1
      const mainHeading = screen.getByRole('heading', { level: 1 })
      expect(mainHeading).toHaveTextContent('Welcome to XD Incentives')

      // Card titles should be headings (h3 in our mock)
      const cardTitles = screen.getAllByTestId('card-title')
      cardTitles.forEach(title => {
        expect(title.tagName).toBe('H3')
      })

      // Descriptions should be paragraphs
      const descriptions = screen.getAllByTestId('card-description')
      descriptions.forEach(description => {
        expect(description.tagName).toBe('P')
      })
    })
  })

  describe('Accessibility', () => {
    it('has proper heading hierarchy', () => {
      render(<HomePage />)

      // Should have one h1
      const h1Elements = screen.getAllByRole('heading', { level: 1 })
      expect(h1Elements).toHaveLength(1)

      // Card titles are h3 (appropriate for this context)
      const cardTitles = screen.getAllByTestId('card-title')
      expect(cardTitles).toHaveLength(3)
    })

    it('provides meaningful button text', () => {
      render(<HomePage />)

      const buttons = screen.getAllByRole('button')

      buttons.forEach(button => {
        expect(button.textContent).toBeTruthy()
        expect(button.textContent?.trim()).not.toBe('')
      })
    })

    it('has descriptive content for each section', () => {
      render(<HomePage />)

      // Each card should have both title and description
      const cards = [
        { title: 'Dashboard', description: 'View your performance metrics' },
        { title: 'Teams', description: 'Manage your team members' },
        { title: 'Reports', description: 'Generate and view reports' }
      ]

      cards.forEach(card => {
        expect(screen.getByText(card.title)).toBeInTheDocument()
        expect(screen.getByText(card.description)).toBeInTheDocument()
      })
    })
  })

  describe('Component integration', () => {
    it('integrates Card components correctly', () => {
      render(<HomePage />)

      // Each card should have the expected child components
      const cards = screen.getAllByTestId('card')

      cards.forEach(card => {
        // Each card should contain a header and content
        expect(card.querySelector('[data-testid="card-header"]')).toBeInTheDocument()
        expect(card.querySelector('[data-testid="card-content"]')).toBeInTheDocument()
      })
    })

    it('integrates Button components with proper props', () => {
      render(<HomePage />)

      const dashboardButton = screen.getByText('Go to Dashboard')
      const teamsButton = screen.getByText('View Teams')
      const reportsButton = screen.getByText('View Reports')

      // Verify button classes are applied
      expect(dashboardButton).toHaveClass('w-full')
      expect(teamsButton).toHaveClass('w-full')
      expect(reportsButton).toHaveClass('w-full')

      // Verify variants are set correctly
      expect(teamsButton).toHaveAttribute('data-variant', 'outline')
      expect(reportsButton).toHaveAttribute('data-variant', 'secondary')
    })
  })

  describe('Error handling', () => {
    it('renders gracefully even if components throw', () => {
      // This test ensures the page structure is resilient
      expect(() => {
        render(<HomePage />)
      }).not.toThrow()
    })
  })
})
