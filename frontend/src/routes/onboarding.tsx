import { useUser } from '@clerk/clerk-react'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useEffect } from 'react'

import { Button } from '@components/ui/Button'

export const Route = createFileRoute('/onboarding')({
  component: OnboardingPage,
})

function OnboardingPage() {
  const { user, isLoaded } = useUser()
  const navigate = useNavigate()

  useEffect(() => {
    if (isLoaded && !user) {
      navigate({ to: '/sign-in' })
    }
  }, [isLoaded, user, navigate])

  const handleContinue = () => {
    navigate({ to: '/dashboard' })
  }

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Welcome to XD Incentives!
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Let&apos;s get you set up with your account
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Account Information</h3>
              <dl className="mt-2 border-t border-gray-200 pt-4">
                <div className="py-2">
                  <dt className="text-sm font-medium text-gray-500">Email</dt>
                  <dd className="text-sm text-gray-900">{user?.emailAddresses[0]?.emailAddress}</dd>
                </div>
                <div className="py-2">
                  <dt className="text-sm font-medium text-gray-500">Name</dt>
                  <dd className="text-sm text-gray-900">
                    {user?.firstName} {user?.lastName}
                  </dd>
                </div>
              </dl>
            </div>

            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <h4 className="text-sm font-medium text-blue-900">Next Steps</h4>
                <ul className="mt-2 text-sm text-blue-700 list-disc list-inside space-y-1">
                  <li>Complete your profile information</li>
                  <li>Set up your team preferences</li>
                  <li>Explore the dashboard features</li>
                </ul>
              </div>

              <Button
                onClick={handleContinue}
                className="w-full"
                size="lg"
              >
                Continue to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
