import { UserProfile } from '@clerk/clerk-react';
import { createFileRoute } from '@tanstack/react-router';

import { AuthGuard } from '@components/AuthGuard';

export const Route = createFileRoute('/profile')({
  component: Profile,
});

function Profile() {
  return (
    <AuthGuard>
      <div className="container mx-auto py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Profile Settings</h1>
            <p className="text-gray-600">Manage your account settings and profile information</p>
          </div>

          <div className="bg-white rounded-lg shadow">
            <UserProfile
              routing="path"
              path="/profile"
              appearance={{
                elements: {
                  card: 'shadow-none border-0',
                  navbar: 'hidden',
                  navbarMobileMenuButton: 'hidden',
                },
              }}
            />
          </div>
        </div>
      </div>
    </AuthGuard>
  );
}
