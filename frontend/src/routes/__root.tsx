import { useUser } from '@clerk/clerk-react';
import { createRootRoute, Link, Outlet } from '@tanstack/react-router';
import { TanStackRouterDevtools } from '@tanstack/router-devtools';

import { ClerkDebug } from '@components/ClerkDebug';
import { UserProfile } from '@components/UserProfile';

function RootLayout() {
  const { isLoaded, isSignedIn } = useUser();

  // Show loading spinner while Clerk loads
  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-gray-50">
        <nav className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex">
                <div className="flex-shrink-0 flex items-center">
                  <Link to="/" className="text-xl font-bold text-gray-900">
                    XD Incentives
                  </Link>
                </div>
                <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                  <Link
                    to="/"
                    className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm [&.active]:border-blue-500 [&.active]:text-gray-900"
                  >
                    Home
                  </Link>
                  {isSignedIn && (
                    <Link
                      to="/dashboard"
                      className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm [&.active]:border-blue-500 [&.active]:text-gray-900"
                    >
                      Dashboard
                    </Link>
                  )}
                  <Link
                    to="/about"
                    className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm [&.active]:border-blue-500 [&.active]:text-gray-900"
                  >
                    About
                  </Link>
                </div>
              </div>
              <div className="flex items-center">
                {isSignedIn ? (
                  <UserProfile />
                ) : (
                  <div className="flex space-x-4">
                    <Link
                      to="/sign-in"
                      className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                    >
                      Sign In
                    </Link>
                    <Link
                      to="/sign-up"
                      className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium"
                    >
                      Sign Up
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </nav>
        <main>
          <Outlet />
        </main>
        {import.meta.env.MODE === 'development' && <ClerkDebug />}
      </div>
      <TanStackRouterDevtools />
    </>
  );
}

export const Route = createRootRoute({
  component: RootLayout,
});
