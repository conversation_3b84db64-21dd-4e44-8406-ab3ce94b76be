import { OrganizationProfile } from '@clerk/clerk-react';
import { createFileRoute } from '@tanstack/react-router';

import { AuthGuard } from '@components/AuthGuard';

export const Route = createFileRoute('/organization')({
  component: Organization,
});

function Organization() {
  return (
    <AuthGuard requireOrganization={true}>
      <div className="container mx-auto py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Organization Settings</h1>
            <p className="text-gray-600">
              Manage your organization settings, members, and permissions
            </p>
          </div>

          <div className="bg-white rounded-lg shadow">
            <OrganizationProfile
              routing="path"
              path="/organization"
              appearance={{
                elements: {
                  card: 'shadow-none border-0',
                  navbar: 'hidden',
                  navbarMobileMenuButton: 'hidden',
                },
              }}
            />
          </div>
        </div>
      </div>
    </AuthGuard>
  );
}
