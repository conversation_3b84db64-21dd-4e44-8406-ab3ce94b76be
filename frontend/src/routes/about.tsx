import { createFileRoute } from '@tanstack/react-router'

export const Route = createFileRoute('/about')({
  component: About,
})

function About() {
  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">About XD Incentives</h1>

      <div className="prose max-w-none">
        <p className="text-lg text-gray-700 mb-4">
          XD Incentives is a Django-based member management system with complex organizational
          hierarchies, team management, and permission-based access control.
        </p>

        <h2 className="text-2xl font-semibold text-gray-800 mt-8 mb-4">Key Features</h2>
        <ul className="list-disc list-inside space-y-2 text-gray-700">
          <li>Clerk authentication integration</li>
          <li>Redis caching for performance</li>
          <li>Celery background task processing</li>
          <li>MySQL database with complex relationships</li>
          <li>Modern React frontend with TypeScript</li>
          <li>Tailwind CSS for styling</li>
        </ul>

        <h2 className="text-2xl font-semibold text-gray-800 mt-8 mb-4">Architecture</h2>
        <p className="text-gray-700 mb-4">
          The application follows a modular Django structure with separate apps for member
          management, API endpoints, customer management, and theme customization.
        </p>
      </div>
    </div>
  )
}
