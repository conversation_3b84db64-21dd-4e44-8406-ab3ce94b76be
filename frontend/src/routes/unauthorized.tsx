import { useUser } from '@clerk/clerk-react';
import { createFileRout<PERSON>, <PERSON> } from '@tanstack/react-router';

import { Button } from '@components/ui/Button';

export const Route = createFileRoute('/unauthorized')({
  component: Unauthorized,
});

function Unauthorized() {
  const { user } = useUser();

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <svg
              className="h-8 w-8 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 className="text-3xl font-extrabold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-sm text-gray-600 mb-8">
            You don&apos;t have permission to access this page or resource.
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="space-y-6">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <h3 className="text-sm font-medium text-red-900 mb-2">Insufficient Permissions</h3>
              <div className="text-sm text-red-700">
                <p className="mb-2">
                  Your current account doesn&apos;t have the required permissions to access this
                  resource.
                </p>
                {user && (
                  <div className="mt-3 p-3 bg-red-100 rounded">
                    <p className="font-medium">Signed in as:</p>
                    <p>{user.emailAddresses[0]?.emailAddress}</p>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <p className="text-sm text-gray-600">If you believe this is an error, please:</p>
              <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
                <li>Contact your administrator to request access</li>
                <li>Verify you&apos;re signed into the correct account</li>
                <li>Check if you need to join an organization</li>
              </ul>
            </div>

            <div className="flex space-x-4">
              <Button variant="outline" className="flex-1" onClick={() => window.history.back()}>
                Go Back
              </Button>
              <Link to="/dashboard" className="flex-1">
                <Button className="w-full">Dashboard</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
