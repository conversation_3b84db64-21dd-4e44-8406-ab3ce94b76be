import { useUser, useOrganizationList } from '@clerk/clerk-react';
import { createFileRoute, Navigate } from '@tanstack/react-router';

import { AuthGuard } from '@components/AuthGuard';
import { Button } from '@components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@components/ui/Card';

export const Route = createFileRoute('/select-organization')({
  component: SelectOrganization,
});

function SelectOrganization() {
  return (
    <AuthGuard>
      <SelectOrganizationContent />
    </AuthGuard>
  );
}

function SelectOrganizationContent() {
  const { user } = useUser();
  const { organizationList, isLoaded, setActive } = useOrganizationList();

  // If user already has an active organization, redirect to dashboard
  if (isLoaded && user?.organizationMemberships?.length) {
    return <Navigate to="/dashboard" />;
  }

  const handleCreateOrganization = () => {
    // This would typically open Clerk's organization creation flow
    // For now, redirect to organization page which will handle creation
    window.location.href = '/organization';
  };

  const handleJoinOrganization = async (organizationId: string) => {
    if (setActive) {
      await setActive({ organization: organizationId });
      window.location.href = '/dashboard';
    }
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-gray-900 mb-2">Select Organization</h2>
          <p className="text-sm text-gray-600 mb-8">
            Choose an organization to continue or create a new one
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-2xl">
        <div className="space-y-6">
          {/* Available Organizations */}
          {organizationList && organizationList.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Available Organizations</h3>
              <div className="grid gap-4">
                {organizationList.map((org) => (
                  <Card key={org.organization.id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-lg">{org.organization.name}</CardTitle>
                          <CardDescription>Role: {org.membership.role}</CardDescription>
                        </div>
                        <Button
                          onClick={() => handleJoinOrganization(org.organization.id)}
                          size="sm"
                        >
                          Select
                        </Button>
                      </div>
                    </CardHeader>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Create New Organization */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {organizationList && organizationList.length > 0 ? 'Or Create New' : 'Get Started'}
            </h3>
            <Card>
              <CardHeader>
                <CardTitle>Create New Organization</CardTitle>
                <CardDescription>
                  Set up a new organization and invite your team members
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button onClick={handleCreateOrganization} className="w-full" variant="outline">
                  Create Organization
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Help Section */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Need Help?</h4>
            <div className="text-sm text-blue-700">
              <p className="mb-2">If you can&apos;t find your organization:</p>
              <ul className="list-disc list-inside space-y-1">
                <li>Check with your administrator for an invitation</li>
                <li>Verify you&apos;re using the correct email address</li>
                <li>Contact support if you need assistance</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
