import { createFileRoute } from '@tanstack/react-router'

import { But<PERSON> } from '@components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@components/ui/Card'

export const Route = createFileRoute('/')({
  component: Index,
})

function Index() {
  return (
    <div className="p-8 max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          XD Incentives Platform
        </h1>
        <p className="text-lg text-gray-600">
          Modern member management system with organizational hierarchies
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Member Management</CardTitle>
            <CardDescription>
              Manage organizational members with complex hierarchies and permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button>View Members</Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Team Organization</CardTitle>
            <CardDescription>
              Create and manage teams with role-based access control
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline">Manage Teams</Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Dashboard</CardTitle>
            <CardDescription>
              View analytics and insights about your organization
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="secondary">Go to Dashboard</Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
