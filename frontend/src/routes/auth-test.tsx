import { useClerk, useUser } from '@clerk/clerk-react';
import { createFileRoute } from '@tanstack/react-router';
import { useState } from 'react';

export const Route = createFileRoute('/auth-test')({
  component: AuthTestPage,
});

function AuthTestPage() {
  const clerk = useClerk();
  const { isLoaded, isSignedIn, user } = useUser();
  const [testResult, setTestResult] = useState<string>('');

  const runDiagnostics = async () => {
    let result = '=== Clerk Diagnostics ===\n';

    try {
      result += `Clerk Loaded: ${isLoaded}\n`;
      result += `User Signed In: ${isSignedIn}\n`;
      result += `Frontend API: ${clerk.frontendApi || 'N/A'}\n`;
      result += `Environment: ${import.meta.env.MODE}\n`;
      result += `Publishable Key: ${import.meta.env.VITE_CLERK_PUBLISHABLE_KEY?.slice(0, 20)}...\n`;
      result += `Current URL: ${window.location.href}\n`;
      result += `Hostname: ${window.location.hostname}\n`;

      if (user) {
        result += `User ID: ${user.id}\n`;
        result += `Email: ${user.primaryEmailAddress?.emailAddress || 'N/A'}\n`;
        result += `Created At: ${user.createdAt}\n`;
      }

      // Test Clerk session
      if (clerk.session) {
        result += `Session ID: ${clerk.session.id}\n`;
        result += `Session Status: ${clerk.session.status}\n`;
      } else {
        result += `Session: Not available\n`;
      }

      // Check for errors in console
      result += '\n=== Browser Console Errors ===\n';
      result += 'Check browser console for specific Clerk errors\n';
    } catch (error) {
      result += `\nERROR: ${error}\n`;
    }

    setTestResult(result);
  };

  const testSignIn = async () => {
    try {
      await clerk.openSignIn();
      setTestResult('Sign-in modal opened successfully');
    } catch (error) {
      setTestResult(`Sign-in error: ${error}`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Clerk Authentication Test</h1>

        <div className="bg-white shadow rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Quick Status</h2>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <span className="font-medium">Clerk Loaded:</span>
              <span
                className={`px-2 py-1 rounded text-sm ${isLoaded ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
              >
                {isLoaded ? 'Yes' : 'No'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="font-medium">Signed In:</span>
              <span
                className={`px-2 py-1 rounded text-sm ${isSignedIn ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}
              >
                {isSignedIn ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Actions</h2>
          <div className="space-x-4">
            <button
              onClick={runDiagnostics}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
            >
              Run Diagnostics
            </button>
            <button
              onClick={testSignIn}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded"
            >
              Test Sign In
            </button>
            {isSignedIn && (
              <button
                onClick={() => clerk.signOut()}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded"
              >
                Sign Out
              </button>
            )}
          </div>
        </div>

        {testResult && (
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto whitespace-pre-wrap">
              {testResult}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
