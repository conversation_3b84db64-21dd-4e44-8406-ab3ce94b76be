import { useAuth } from '@clerk/clerk-react'
import { useCallback } from 'react'

export function useTokenRefresh() {
  const { getToken } = useAuth()

  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      // Force token refresh by requesting new token
      const token = await getToken({ template: 'default' })
      return !!token
    } catch (error) {
      console.error('Token refresh failed:', error)
      return false
    }
  }, [getToken])

  const ensureValidToken = useCallback(async (): Promise<string | null> => {
    try {
      // Get token with automatic refresh
      const token = await getToken()
      
      if (!token) {
        console.warn('No token available after refresh attempt')
        return null
      }

      return token
    } catch (error) {
      console.error('Failed to ensure valid token:', error)
      return null
    }
  }, [getToken])

  return {
    refreshToken,
    ensureValidToken,
  }
}