import { onCLS, onFCP, onFID, onINP, onLCP, onTTFB, type Metric } from 'web-vitals'

// Thresholds for Core Web Vitals (in milliseconds)
const THRESHOLDS = {
  LCP: { good: 2500, needsImprovement: 4000 }, // Largest Contentful Paint
  FID: { good: 100, needsImprovement: 300 },   // First Input Delay
  CLS: { good: 0.1, needsImprovement: 0.25 },  // Cumulative Layout Shift
  FCP: { good: 1800, needsImprovement: 3000 }, // First Contentful Paint
  INP: { good: 200, needsImprovement: 500 },   // Interaction to Next Paint
  TTFB: { good: 800, needsImprovement: 1800 }, // Time to First Byte
}

// Get rating for a metric value
function getRating(metricName: string, value: number): 'good' | 'needs-improvement' | 'poor' {
  const threshold = THRESHOLDS[metricName as keyof typeof THRESHOLDS]
  if (!threshold) {
    return 'good'
  }

  if (metricName === 'CLS') {
    // CLS is not in milliseconds
    if (value <= threshold.good) {
      return 'good'
    }
    if (value <= threshold.needsImprovement) {
      return 'needs-improvement'
    }
    return 'poor'
  }

  if (value <= threshold.good) {
    return 'good'
  }
  if (value <= threshold.needsImprovement) {
    return 'needs-improvement'
  }
  return 'poor'
}

// Log metric to console with color coding
function logMetric(metric: Metric) {
  const rating = getRating(metric.name, metric.value)
  const color = rating === 'good' ? 'green' : rating === 'needs-improvement' ? 'orange' : 'red'

  console.warn(
    `%c[Web Vitals] ${metric.name}: ${metric.value.toFixed(2)}${metric.name === 'CLS' ? '' : 'ms'} (${rating})`,
    `color: ${color}; font-weight: bold;`
  )

  // Log additional details in development
  if (import.meta.env.DEV) {
    console.warn(`  ID: ${metric.id}`)
    console.warn(`  Navigation Type: ${metric.navigationType}`)
    if (metric.entries?.length) {
      console.warn(`  Entries:`, metric.entries)
    }
  }
}

// Send metrics to analytics service
async function sendToAnalytics(metric: Metric) {
  const rating = getRating(metric.name, metric.value)

  // Replace with your analytics endpoint
  const analyticsEndpoint = import.meta.env.VITE_ANALYTICS_ENDPOINT

  if (!analyticsEndpoint) {
    return // Skip if no endpoint configured
  }

  const payload = {
    metric: metric.name,
    value: metric.value,
    rating,
    id: metric.id,
    navigationType: metric.navigationType,
    timestamp: Date.now(),
    url: window.location.href,
    userAgent: navigator.userAgent,
  }

  try {
    // Use sendBeacon for reliability
    if (navigator.sendBeacon) {
      navigator.sendBeacon(analyticsEndpoint, JSON.stringify(payload))
    } else {
      // Fallback to fetch
      await fetch(analyticsEndpoint, {
        method: 'POST',
        body: JSON.stringify(payload),
        headers: { 'Content-Type': 'application/json' },
        keepalive: true,
      })
    }
  } catch (error) {
    console.error('Failed to send analytics:', error)
  }
}

// Report handler
function reportWebVital(metric: Metric) {
  logMetric(metric)

  // Only send to analytics in production
  if (import.meta.env.PROD) {
    sendToAnalytics(metric)
  }
}

// Initialize Web Vitals monitoring
export function initWebVitals() {
  onCLS(reportWebVital)
  onFCP(reportWebVital)
  onFID(reportWebVital)
  onINP(reportWebVital)
  onLCP(reportWebVital)
  onTTFB(reportWebVital)
}

// Get current Web Vitals (for testing)
export async function getCurrentWebVitals() {
  const vitals: Record<string, number> = {}

  return new Promise<typeof vitals>((resolve) => {
    let count = 0
    const checkComplete = () => {
      count++
      if (count === 6) {
        resolve(vitals)
      }
    }

    onCLS((metric) => { vitals.CLS = metric.value; checkComplete() })
    onFCP((metric) => { vitals.FCP = metric.value; checkComplete() })
    onFID((metric) => { vitals.FID = metric.value; checkComplete() })
    onINP((metric) => { vitals.INP = metric.value; checkComplete() })
    onLCP((metric) => { vitals.LCP = metric.value; checkComplete() })
    onTTFB((metric) => { vitals.TTFB = metric.value; checkComplete() })

    // Timeout after 5 seconds
    setTimeout(() => resolve(vitals), 5000)
  })
}
