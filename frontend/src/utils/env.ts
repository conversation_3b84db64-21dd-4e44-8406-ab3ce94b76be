/**
 * Environment variable validation and configuration
 * Validates required environment variables at application startup
 */

interface EnvConfig {
  API_URL: string
  NODE_ENV: 'development' | 'production' | 'test'
  VITE_APP_NAME?: string
  VITE_API_URL?: string
  VITE_ENABLE_ANALYTICS?: string
  VITE_ENABLE_ERROR_REPORTING?: string
  VITE_CLERK_PUBLISHABLE_KEY?: string
  VITE_CLERK_SIGN_IN_URL?: string
  VITE_CLERK_SIGN_UP_URL?: string
  VITE_CLERK_AFTER_SIGN_IN_URL?: string
  VITE_CLERK_AFTER_SIGN_UP_URL?: string
}

interface ValidationError {
  variable: string
  message: string
  severity: 'error' | 'warning'
}

// Required environment variables
const REQUIRED_VARS = [
  'VITE_API_URL',
  'VITE_CLERK_PUBLISHABLE_KEY',
] as const

// Optional environment variables with defaults
const OPTIONAL_VARS = {
  VITE_APP_NAME: 'XD Incentives',
  VITE_ENABLE_ANALYTICS: 'false',
  VITE_ENABLE_ERROR_REPORTING: 'false',
  VITE_CLERK_SIGN_IN_URL: '/sign-in',
  VITE_CLERK_SIGN_UP_URL: '/sign-up',
  VITE_CLERK_AFTER_SIGN_IN_URL: '/dashboard',
  VITE_CLERK_AFTER_SIGN_UP_URL: '/onboarding',
} as const

// Environment variable type validation
function validateEnvType(key: string, value: string | undefined): ValidationError | null {
  if (!value) {return null}

  switch (key) {
    case 'VITE_API_URL':
      try {
        new URL(value)
        return null
      } catch {
        return {
          variable: key,
          message: 'Must be a valid URL',
          severity: 'error'
        }
      }

    case 'VITE_ENABLE_ANALYTICS':
    case 'VITE_ENABLE_ERROR_REPORTING':
      if (!['true', 'false'].includes(value.toLowerCase())) {
        return {
          variable: key,
          message: 'Must be "true" or "false"',
          severity: 'warning'
        }
      }
      return null

    default:
      return null
  }
}

// Validate all environment variables
function validateEnvironment(): ValidationError[] {
  const errors: ValidationError[] = []

  // Check required variables
  for (const variable of REQUIRED_VARS) {
    const value = import.meta.env[variable]

    if (!value) {
      errors.push({
        variable,
        message: 'Required environment variable is missing',
        severity: 'error'
      })
      continue
    }

    // Type validation
    const typeError = validateEnvType(variable, value)
    if (typeError) {
      errors.push(typeError)
    }
  }

  // Check optional variables for type validation
  for (const [variable] of Object.entries(OPTIONAL_VARS)) {
    const value = import.meta.env[variable]
    if (value) {
      const typeError = validateEnvType(variable, value)
      if (typeError) {
        errors.push(typeError)
      }
    }
  }

  return errors
}

// Get validated environment configuration
export function getEnvConfig(): EnvConfig {
  const errors = validateEnvironment()

  // Handle errors
  if (errors.length > 0) {
    const criticalErrors = errors.filter(e => e.severity === 'error')
    const warnings = errors.filter(e => e.severity === 'warning')

    // Log warnings
    if (warnings.length > 0) {
      console.warn('Environment Configuration Warnings:')
      warnings.forEach(warning => {
        console.warn(`  ${warning.variable}: ${warning.message}`)
      })
    }

    // Throw on critical errors
    if (criticalErrors.length > 0) {
      console.error('Environment Configuration Errors:')
      criticalErrors.forEach(error => {
        console.error(`  ${error.variable}: ${error.message}`)
      })

      throw new Error(
        `Environment validation failed. Missing or invalid required variables: ${
          criticalErrors.map(e => e.variable).join(', ')
        }`
      )
    }
  }

  // Build configuration with defaults
  const config: EnvConfig = {
    API_URL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api',
    NODE_ENV: (import.meta.env.MODE || 'development') as EnvConfig['NODE_ENV'],
    VITE_APP_NAME: import.meta.env.VITE_APP_NAME || OPTIONAL_VARS.VITE_APP_NAME,
    VITE_API_URL: import.meta.env.VITE_API_URL,
    VITE_ENABLE_ANALYTICS: import.meta.env.VITE_ENABLE_ANALYTICS || OPTIONAL_VARS.VITE_ENABLE_ANALYTICS,
    VITE_ENABLE_ERROR_REPORTING: import.meta.env.VITE_ENABLE_ERROR_REPORTING || OPTIONAL_VARS.VITE_ENABLE_ERROR_REPORTING,
  }

  return config
}

// Helper functions for specific environment values
export function isProduction(): boolean {
  return getEnvConfig().NODE_ENV === 'production'
}

export function isDevelopment(): boolean {
  return getEnvConfig().NODE_ENV === 'development'
}

export function isTest(): boolean {
  return getEnvConfig().NODE_ENV === 'test'
}

export function isAnalyticsEnabled(): boolean {
  return getEnvConfig().VITE_ENABLE_ANALYTICS === 'true'
}

export function isErrorReportingEnabled(): boolean {
  return getEnvConfig().VITE_ENABLE_ERROR_REPORTING === 'true'
}

export function getApiUrl(): string {
  return getEnvConfig().VITE_API_URL
}

export function getAppName(): string {
  return getEnvConfig().VITE_APP_NAME || 'XD Incentives'
}

export function getClerkPublishableKey(): string {
  return import.meta.env.VITE_CLERK_PUBLISHABLE_KEY
}

export function getClerkSignInUrl(): string {
  return import.meta.env.VITE_CLERK_SIGN_IN_URL || OPTIONAL_VARS.VITE_CLERK_SIGN_IN_URL
}

export function getClerkSignUpUrl(): string {
  return import.meta.env.VITE_CLERK_SIGN_UP_URL || OPTIONAL_VARS.VITE_CLERK_SIGN_UP_URL
}

export function getClerkAfterSignInUrl(): string {
  return import.meta.env.VITE_CLERK_AFTER_SIGN_IN_URL || OPTIONAL_VARS.VITE_CLERK_AFTER_SIGN_IN_URL
}

export function getClerkAfterSignUpUrl(): string {
  return import.meta.env.VITE_CLERK_AFTER_SIGN_UP_URL || OPTIONAL_VARS.VITE_CLERK_AFTER_SIGN_UP_URL
}

// Initialize and validate environment at module load
let envConfig: EnvConfig

try {
  envConfig = getEnvConfig()

  if (isDevelopment()) {
    console.warn('Environment Configuration:', {
      NODE_ENV: envConfig.NODE_ENV,
      API_URL: envConfig.API_URL,
      APP_NAME: envConfig.VITE_APP_NAME,
      ANALYTICS_ENABLED: isAnalyticsEnabled(),
      ERROR_REPORTING_ENABLED: isErrorReportingEnabled(),
    })
  }
} catch (error) {
  console.error('Failed to initialize environment configuration:', error)
  throw error
}

export default envConfig
