import { describe, it, expect, vi, beforeEach } from 'vitest'

// We need to mock import.meta.env before importing the module
const mockEnv = {
  MODE: 'test',
  VITE_API_URL: 'http://localhost:8000/api',
  VITE_CLERK_PUBLISHABLE_KEY: 'pk_test_12345',
}

vi.stubGlobal('import', {
  meta: {
    env: mockEnv
  }
})

// Now import the module after mocking
import {
  getEnvConfig,
  isProduction,
  isDevelopment,
  isTest,
  isAnalyticsEnabled,
  isErrorReportingEnabled,
  getApiUrl,
  getAppName,
  getClerkPublishableKey,
  getClerkSignInUrl,
  getClerkSignUpUrl,
  getClerkAfterSignInUrl,
  getClerkAfterSignUpUrl,
} from './env'

describe('env utility', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset to basic valid configuration
    Object.assign(mockEnv, {
      MODE: 'test',
      VITE_API_URL: 'http://localhost:8000/api',
      VITE_CLERK_PUBLISHABLE_KEY: 'pk_test_12345',
    })
  })

  describe('getEnvConfig', () => {
    it('returns valid configuration with required variables', () => {
      const config = getEnvConfig()

      expect(config.NODE_ENV).toBe('test')
      expect(config.API_URL).toBe('http://localhost:8000/api')
      expect(config.VITE_API_URL).toBe('http://localhost:8000/api')
      expect(config.VITE_APP_NAME).toBe('XD Incentives')
    })

    it('applies default values for optional variables', () => {
      const config = getEnvConfig()

      expect(config.VITE_APP_NAME).toBe('XD Incentives')
      expect(config.VITE_ENABLE_ANALYTICS).toBe('false')
      expect(config.VITE_ENABLE_ERROR_REPORTING).toBe('false')
    })

    it('uses provided values over defaults', () => {
      Object.assign(mockEnv, {
        VITE_APP_NAME: 'Custom App',
        VITE_ENABLE_ANALYTICS: 'true',
        VITE_ENABLE_ERROR_REPORTING: 'true',
      })

      const config = getEnvConfig()

      expect(config.VITE_APP_NAME).toBe('Custom App')
      expect(config.VITE_ENABLE_ANALYTICS).toBe('true')
      expect(config.VITE_ENABLE_ERROR_REPORTING).toBe('true')
    })

    it('throws error when required variables are missing', () => {
      delete mockEnv.VITE_API_URL

      expect(() => getEnvConfig()).toThrow('Environment validation failed')
    })

    it('throws error when VITE_CLERK_PUBLISHABLE_KEY is missing', () => {
      delete mockEnv.VITE_CLERK_PUBLISHABLE_KEY

      expect(() => getEnvConfig()).toThrow('Environment validation failed')
    })
  })

  describe('URL validation', () => {
    it('validates API_URL format', () => {
      mockEnv.VITE_API_URL = 'invalid-url'

      expect(() => getEnvConfig()).toThrow('Environment validation failed')
    })

    it('accepts valid URLs', () => {
      mockEnv.VITE_API_URL = 'https://api.example.com/v1'

      expect(() => getEnvConfig()).not.toThrow()

      const config = getEnvConfig()
      expect(config.VITE_API_URL).toBe('https://api.example.com/v1')
    })

    it('accepts localhost URLs', () => {
      mockEnv.VITE_API_URL = 'http://localhost:3000/api'

      expect(() => getEnvConfig()).not.toThrow()
    })

    it('accepts URLs with ports', () => {
      mockEnv.VITE_API_URL = 'http://api.localhost:8080/v1'

      expect(() => getEnvConfig()).not.toThrow()
    })
  })

  describe('Boolean validation', () => {
    it('warns about invalid boolean values but does not throw', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      mockEnv.VITE_ENABLE_ANALYTICS = 'maybe'

      expect(() => getEnvConfig()).not.toThrow()
      expect(consoleSpy).toHaveBeenCalledWith('Environment Configuration Warnings:')

      consoleSpy.mockRestore()
    })

    it('accepts valid boolean strings', () => {
      Object.assign(mockEnv, {
        VITE_ENABLE_ANALYTICS: 'true',
        VITE_ENABLE_ERROR_REPORTING: 'false',
      })

      expect(() => getEnvConfig()).not.toThrow()
    })

    it('is case-insensitive for boolean values', () => {
      Object.assign(mockEnv, {
        VITE_ENABLE_ANALYTICS: 'TRUE',
        VITE_ENABLE_ERROR_REPORTING: 'FALSE',
      })

      expect(() => getEnvConfig()).not.toThrow()
    })
  })

  describe('Environment type helpers', () => {
    it('detects production environment', () => {
      mockEnv.MODE = 'production'

      expect(isProduction()).toBe(true)
      expect(isDevelopment()).toBe(false)
      expect(isTest()).toBe(false)
    })

    it('detects development environment', () => {
      mockEnv.MODE = 'development'

      expect(isDevelopment()).toBe(true)
      expect(isProduction()).toBe(false)
      expect(isTest()).toBe(false)
    })

    it('detects test environment', () => {
      mockEnv.MODE = 'test'

      expect(isTest()).toBe(true)
      expect(isProduction()).toBe(false)
      expect(isDevelopment()).toBe(false)
    })
  })

  describe('Feature flag helpers', () => {
    it('detects analytics enabled', () => {
      mockEnv.VITE_ENABLE_ANALYTICS = 'true'

      expect(isAnalyticsEnabled()).toBe(true)
    })

    it('detects analytics disabled', () => {
      mockEnv.VITE_ENABLE_ANALYTICS = 'false'

      expect(isAnalyticsEnabled()).toBe(false)
    })

    it('defaults analytics to disabled', () => {
      delete mockEnv.VITE_ENABLE_ANALYTICS

      expect(isAnalyticsEnabled()).toBe(false)
    })

    it('detects error reporting enabled', () => {
      mockEnv.VITE_ENABLE_ERROR_REPORTING = 'true'

      expect(isErrorReportingEnabled()).toBe(true)
    })

    it('detects error reporting disabled', () => {
      mockEnv.VITE_ENABLE_ERROR_REPORTING = 'false'

      expect(isErrorReportingEnabled()).toBe(false)
    })

    it('defaults error reporting to disabled', () => {
      delete mockEnv.VITE_ENABLE_ERROR_REPORTING

      expect(isErrorReportingEnabled()).toBe(false)
    })
  })

  describe('Configuration getters', () => {
    it('returns API URL', () => {
      expect(getApiUrl()).toBe('http://localhost:8000/api')
    })

    it('returns app name', () => {
      expect(getAppName()).toBe('XD Incentives')
    })

    it('returns custom app name when provided', () => {
      mockEnv.VITE_APP_NAME = 'Custom Application'

      expect(getAppName()).toBe('Custom Application')
    })

    it('returns Clerk publishable key', () => {
      expect(getClerkPublishableKey()).toBe('pk_test_12345')
    })
  })

  describe('Clerk URL helpers', () => {
    it('returns default sign-in URL', () => {
      expect(getClerkSignInUrl()).toBe('/sign-in')
    })

    it('returns custom sign-in URL when provided', () => {
      mockEnv.VITE_CLERK_SIGN_IN_URL = '/custom-sign-in'

      expect(getClerkSignInUrl()).toBe('/custom-sign-in')
    })

    it('returns default sign-up URL', () => {
      expect(getClerkSignUpUrl()).toBe('/sign-up')
    })

    it('returns custom sign-up URL when provided', () => {
      mockEnv.VITE_CLERK_SIGN_UP_URL = '/custom-sign-up'

      expect(getClerkSignUpUrl()).toBe('/custom-sign-up')
    })

    it('returns default after sign-in URL', () => {
      expect(getClerkAfterSignInUrl()).toBe('/dashboard')
    })

    it('returns custom after sign-in URL when provided', () => {
      mockEnv.VITE_CLERK_AFTER_SIGN_IN_URL = '/welcome'

      expect(getClerkAfterSignInUrl()).toBe('/welcome')
    })

    it('returns default after sign-up URL', () => {
      expect(getClerkAfterSignUpUrl()).toBe('/onboarding')
    })

    it('returns custom after sign-up URL when provided', () => {
      mockEnv.VITE_CLERK_AFTER_SIGN_UP_URL = '/setup'

      expect(getClerkAfterSignUpUrl()).toBe('/setup')
    })
  })

  describe('Error handling', () => {
    it('logs warnings for non-critical validation errors', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      mockEnv.VITE_ENABLE_ANALYTICS = 'invalid'

      getEnvConfig()

      expect(consoleSpy).toHaveBeenCalledWith('Environment Configuration Warnings:')
      expect(consoleSpy).toHaveBeenCalledWith('  VITE_ENABLE_ANALYTICS: Must be "true" or "false"')

      consoleSpy.mockRestore()
    })

    it('logs errors for critical validation failures', () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      delete mockEnv.VITE_API_URL

      expect(() => getEnvConfig()).toThrow()
      expect(consoleErrorSpy).toHaveBeenCalledWith('Environment Configuration Errors:')

      consoleErrorSpy.mockRestore()
    })

    it('includes all missing required variables in error message', () => {
      delete mockEnv.VITE_API_URL
      delete mockEnv.VITE_CLERK_PUBLISHABLE_KEY

      expect(() => getEnvConfig()).toThrow('VITE_API_URL, VITE_CLERK_PUBLISHABLE_KEY')
    })
  })

  describe('Edge cases', () => {
    it('handles empty string values as missing', () => {
      mockEnv.VITE_API_URL = ''

      expect(() => getEnvConfig()).toThrow('Environment validation failed')
    })

    it('handles undefined NODE_ENV', () => {
      delete mockEnv.MODE

      const config = getEnvConfig()
      expect(config.NODE_ENV).toBe('development')
    })

    it('handles whitespace in environment variables', () => {
      mockEnv.VITE_API_URL = '  http://localhost:8000/api  '

      // Should work but might not trim automatically
      expect(() => getEnvConfig()).not.toThrow()
    })

    it('validates URLs with different protocols', () => {
      mockEnv.VITE_API_URL = 'https://secure-api.example.com'

      expect(() => getEnvConfig()).not.toThrow()

      const config = getEnvConfig()
      expect(config.VITE_API_URL).toBe('https://secure-api.example.com')
    })
  })

  describe('Development logging', () => {
    it('logs configuration in development mode', () => {
      const consoleInfoSpy = vi.spyOn(console, 'info').mockImplementation(() => {})

      mockEnv.MODE = 'development'

      getEnvConfig()

      expect(consoleInfoSpy).toHaveBeenCalledWith(
        'Environment Configuration:',
        expect.objectContaining({
          NODE_ENV: 'development',
          API_URL: 'http://localhost:8000/api',
        })
      )

      consoleInfoSpy.mockRestore()
    })

    it('does not log configuration in production mode', () => {
      const consoleInfoSpy = vi.spyOn(console, 'info').mockImplementation(() => {})

      mockEnv.MODE = 'production'

      getEnvConfig()

      expect(consoleInfoSpy).not.toHaveBeenCalledWith(
        'Environment Configuration:',
        expect.any(Object)
      )

      consoleInfoSpy.mockRestore()
    })
  })
})
