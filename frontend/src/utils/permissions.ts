export function hasPermission(
  userPermissions: string[], 
  requiredPermission: string
): boolean {
  return userPermissions.includes(requiredPermission)
}

export function hasAnyPermission(
  userPermissions: string[], 
  requiredPermissions: string[]
): boolean {
  return requiredPermissions.some(permission => 
    userPermissions.includes(permission)
  )
}

export function hasAllPermissions(
  userPermissions: string[], 
  requiredPermissions: string[]
): boolean {
  return requiredPermissions.every(permission => 
    userPermissions.includes(permission)
  )
}

// Permission constants
export const PERMISSIONS = {
  VIEW_DASHBOARD: 'view_dashboard',
  MANAGE_USERS: 'manage_users',
  VIEW_REPORTS: 'view_reports',
  ADMIN_ACCESS: 'admin_access',
  VIEW_TEAMS: 'view_teams',
  MANAGE_TEAMS: 'manage_teams',
  VIEW_HIERARCHY: 'view_hierarchy',
  MANAGE_HIERARCHY: 'manage_hierarchy',
  VIEW_SUBORDINATES: 'view_subordinates',
  MANAGE_SUBORDINATES: 'manage_subordinates',
  CREATE_USERS: 'create_users',
  EDIT_USERS: 'edit_users',
  DELETE_USERS: 'delete_users',
  VIEW_PERFORMANCE_REPORTS: 'view_performance_reports',
  VIEW_FINANCIAL_REPORTS: 'view_financial_reports',
  EXPORT_REPORTS: 'export_reports',
} as const

export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS]