import { describe, it, expect } from 'vitest'

import { cn } from './cn'

describe('cn utility', () => {
  describe('Basic functionality', () => {
    it('combines classes correctly', () => {
      const result = cn('class1', 'class2')
      expect(result).toBe('class1 class2')
    })

    it('handles empty input', () => {
      const result = cn()
      expect(result).toBe('')
    })

    it('handles single class', () => {
      const result = cn('single-class')
      expect(result).toBe('single-class')
    })

    it('handles undefined and null values', () => {
      const result = cn('class1', undefined, 'class2', null, 'class3')
      expect(result).toBe('class1 class2 class3')
    })

    it('handles boolean conditions', () => {
      const result = cn('always', true && 'conditional', false && 'never')
      expect(result).toBe('always conditional')
    })
  })

  describe('clsx functionality', () => {
    it('handles object notation', () => {
      const result = cn({
        'class1': true,
        'class2': false,
        'class3': true,
      })
      expect(result).toBe('class1 class3')
    })

    it('handles arrays', () => {
      const result = cn(['class1', 'class2'], 'class3')
      expect(result).toBe('class1 class2 class3')
    })

    it('handles mixed types', () => {
      const result = cn(
        'base',
        ['array1', 'array2'],
        { conditional: true, hidden: false },
        undefined,
        'final'
      )
      expect(result).toBe('base array1 array2 conditional final')
    })

    it('handles nested arrays', () => {
      const result = cn(['class1', ['nested1', 'nested2']], 'class3')
      expect(result).toBe('class1 nested1 nested2 class3')
    })
  })

  describe('Tailwind merge functionality', () => {
    it('merges conflicting Tailwind classes correctly', () => {
      const result = cn('px-2 py-1 px-3')
      expect(result).toBe('py-1 px-3')
    })

    it('handles responsive variants', () => {
      const result = cn('text-sm md:text-base lg:text-lg text-xl')
      expect(result).toBe('md:text-base lg:text-lg text-xl')
    })

    it('merges color variants', () => {
      const result = cn('bg-red-500 bg-blue-500')
      expect(result).toBe('bg-blue-500')
    })

    it('merges sizing classes', () => {
      const result = cn('w-4 h-4 w-8 h-8')
      expect(result).toBe('w-8 h-8')
    })

    it('keeps non-conflicting classes', () => {
      const result = cn('flex items-center justify-center p-4 bg-blue-500 text-white')
      expect(result).toBe('flex items-center justify-center p-4 bg-blue-500 text-white')
    })

    it('handles modifiers correctly', () => {
      const result = cn('hover:bg-red-500 focus:bg-red-500 hover:bg-blue-500')
      expect(result).toBe('focus:bg-red-500 hover:bg-blue-500')
    })
  })

  describe('Complex scenarios', () => {
    it('handles component variant patterns', () => {
      const variant = 'primary'
      const size = 'large'
      const disabled = false

      const result = cn(
        'button',
        {
          'bg-blue-500 text-white': variant === 'primary',
          'bg-gray-200 text-gray-800': variant === 'secondary',
        },
        {
          'px-4 py-2 text-sm': size === 'small',
          'px-6 py-3 text-base': size === 'medium',
          'px-8 py-4 text-lg': size === 'large',
        },
        {
          'opacity-50 cursor-not-allowed': disabled,
        }
      )

      expect(result).toBe('button bg-blue-500 text-white px-8 py-4 text-lg')
    })

    it('handles conditional styling with overrides', () => {
      const isActive = true
      const customPadding = 'px-10'

      const result = cn(
        'base-class px-4 py-2',
        isActive && 'bg-blue-500 text-white',
        customPadding // Should override px-4
      )

      expect(result).toBe('base-class py-2 bg-blue-500 text-white px-10')
    })

    it('handles state combinations', () => {
      const result = cn(
        'transition-colors',
        'hover:bg-blue-100 focus:bg-blue-100',
        'hover:bg-red-100', // Should override hover:bg-blue-100
        'active:bg-blue-200'
      )

      expect(result).toBe('transition-colors focus:bg-blue-100 hover:bg-red-100 active:bg-blue-200')
    })
  })

  describe('Real-world usage patterns', () => {
    it('handles button component classes', () => {
      const result = cn(
        'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
        'disabled:opacity-50 disabled:pointer-events-none',
        'bg-primary text-primary-foreground hover:bg-primary/90',
        'h-10 px-4 py-2'
      )

      expect(result).toContain('inline-flex')
      expect(result).toContain('items-center')
      expect(result).toContain('justify-center')
      expect(result).toContain('bg-primary')
    })

    it('handles input component classes', () => {
      const error = true
      const result = cn(
        'flex h-10 w-full rounded-md border px-3 py-2 text-sm',
        'file:border-0 file:bg-transparent file:text-sm file:font-medium',
        'placeholder:text-muted-foreground',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
        'disabled:cursor-not-allowed disabled:opacity-50',
        error ? 'border-red-500 text-red-900' : 'border-input'
      )

      expect(result).toContain('border-red-500')
      expect(result).toContain('text-red-900')
      expect(result).not.toContain('border-input')
    })

    it('handles card component classes', () => {
      const result = cn(
        'rounded-lg border bg-card text-card-foreground shadow-sm',
        'hover:shadow-md transition-shadow'
      )

      expect(result).toBe('rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow')
    })
  })

  describe('Edge cases', () => {
    it('handles empty strings', () => {
      const result = cn('', 'class1', '', 'class2', '')
      expect(result).toBe('class1 class2')
    })

    it('handles whitespace', () => {
      const result = cn('  class1  ', '  class2  ')
      expect(result).toBe('class1 class2')
    })

    it('handles duplicate classes', () => {
      const result = cn('class1', 'class2', 'class1', 'class3')
      expect(result).toBe('class2 class1 class3')
    })

    it('handles very long class lists', () => {
      const manyClasses = Array.from({ length: 100 }, (_, i) => `class-${i}`)
      const result = cn(...manyClasses)

      expect(result).toContain('class-0')
      expect(result).toContain('class-99')
      expect(result.split(' ')).toHaveLength(100)
    })

    it('handles special characters in class names', () => {
      const result = cn('class-1', 'class_2', 'class:3', 'class[4]')
      expect(result).toBe('class-1 class_2 class:3 class[4]')
    })
  })

  describe('Performance', () => {
    it('handles repeated calls efficiently', () => {
      const classes = ['class1', 'class2', 'class3']

      // Multiple calls should not throw or cause issues
      for (let i = 0; i < 100; i++) {
        const result = cn(...classes)
        expect(result).toBe('class1 class2 class3')
      }
    })

    it('handles large input efficiently', () => {
      const largeInput = {
        'class-a': true,
        'class-b': false,
        'class-c': true,
        // Add many more classes
        ...Object.fromEntries(
          Array.from({ length: 50 }, (_, i) => [`dynamic-class-${i}`, i % 2 === 0])
        )
      }

      const result = cn(largeInput)
      expect(result).toContain('class-a')
      expect(result).toContain('class-c')
      expect(result).not.toContain('class-b')
    })
  })
})
