import { describe, expect, it } from 'vitest'

import { hasPermission, hasAnyPermission, hasAllPermissions } from './permissions'

describe('Permission Utilities', () => {
  describe('hasPermission', () => {
    it('should return true when user has the required permission', () => {
      const userPermissions = ['view_dashboard', 'edit_profile', 'manage_team']
      const requiredPermission = 'view_dashboard'
      
      expect(hasPermission(userPermissions, requiredPermission)).toBe(true)
    })

    it('should return false when user does not have the required permission', () => {
      const userPermissions = ['view_dashboard', 'edit_profile']
      const requiredPermission = 'manage_team'
      
      expect(hasPermission(userPermissions, requiredPermission)).toBe(false)
    })

    it('should return false when userPermissions is empty', () => {
      const userPermissions: string[] = []
      const requiredPermission = 'view_dashboard'
      
      expect(hasPermission(userPermissions, requiredPermission)).toBe(false)
    })

    it('should handle case-sensitive permission matching', () => {
      const userPermissions = ['view_dashboard']
      const requiredPermission = 'VIEW_DASHBOARD'
      
      expect(hasPermission(userPermissions, requiredPermission)).toBe(false)
    })

    it('should handle special characters in permissions', () => {
      const userPermissions = ['manage:team', 'view@dashboard', 'edit_profile-v2']
      
      expect(hasPermission(userPermissions, 'manage:team')).toBe(true)
      expect(hasPermission(userPermissions, 'view@dashboard')).toBe(true)
      expect(hasPermission(userPermissions, 'edit_profile-v2')).toBe(true)
    })
  })

  describe('hasAnyPermission', () => {
    it('should return true when user has at least one required permission', () => {
      const userPermissions = ['view_dashboard', 'edit_profile']
      const requiredPermissions = ['manage_team', 'view_dashboard', 'admin_access']
      
      expect(hasAnyPermission(userPermissions, requiredPermissions)).toBe(true)
    })

    it('should return false when user has none of the required permissions', () => {
      const userPermissions = ['view_dashboard', 'edit_profile']
      const requiredPermissions = ['manage_team', 'admin_access', 'super_admin']
      
      expect(hasAnyPermission(userPermissions, requiredPermissions)).toBe(false)
    })

    it('should return true when user has multiple required permissions', () => {
      const userPermissions = ['view_dashboard', 'edit_profile', 'manage_team']
      const requiredPermissions = ['edit_profile', 'manage_team']
      
      expect(hasAnyPermission(userPermissions, requiredPermissions)).toBe(true)
    })

    it('should return false when both arrays are empty', () => {
      const userPermissions: string[] = []
      const requiredPermissions: string[] = []
      
      expect(hasAnyPermission(userPermissions, requiredPermissions)).toBe(false)
    })

    it('should return false when userPermissions is empty', () => {
      const userPermissions: string[] = []
      const requiredPermissions = ['view_dashboard', 'edit_profile']
      
      expect(hasAnyPermission(userPermissions, requiredPermissions)).toBe(false)
    })

    it('should return false when requiredPermissions is empty', () => {
      const userPermissions = ['view_dashboard', 'edit_profile']
      const requiredPermissions: string[] = []
      
      expect(hasAnyPermission(userPermissions, requiredPermissions)).toBe(false)
    })

    it('should handle duplicate permissions in user array', () => {
      const userPermissions = ['view_dashboard', 'view_dashboard', 'edit_profile']
      const requiredPermissions = ['view_dashboard']
      
      expect(hasAnyPermission(userPermissions, requiredPermissions)).toBe(true)
    })

    it('should handle duplicate permissions in required array', () => {
      const userPermissions = ['view_dashboard']
      const requiredPermissions = ['view_dashboard', 'view_dashboard', 'edit_profile']
      
      expect(hasAnyPermission(userPermissions, requiredPermissions)).toBe(true)
    })
  })

  describe('hasAllPermissions', () => {
    it('should return true when user has all required permissions', () => {
      const userPermissions = ['view_dashboard', 'edit_profile', 'manage_team', 'admin_access']
      const requiredPermissions = ['view_dashboard', 'edit_profile', 'manage_team']
      
      expect(hasAllPermissions(userPermissions, requiredPermissions)).toBe(true)
    })

    it('should return false when user is missing one or more required permissions', () => {
      const userPermissions = ['view_dashboard', 'edit_profile']
      const requiredPermissions = ['view_dashboard', 'edit_profile', 'manage_team']
      
      expect(hasAllPermissions(userPermissions, requiredPermissions)).toBe(false)
    })

    it('should return true when required permissions is empty', () => {
      const userPermissions = ['view_dashboard', 'edit_profile']
      const requiredPermissions: string[] = []
      
      expect(hasAllPermissions(userPermissions, requiredPermissions)).toBe(true)
    })

    it('should return false when userPermissions is empty but required permissions exist', () => {
      const userPermissions: string[] = []
      const requiredPermissions = ['view_dashboard', 'edit_profile']
      
      expect(hasAllPermissions(userPermissions, requiredPermissions)).toBe(false)
    })

    it('should return true when both arrays are empty', () => {
      const userPermissions: string[] = []
      const requiredPermissions: string[] = []
      
      expect(hasAllPermissions(userPermissions, requiredPermissions)).toBe(true)
    })

    it('should handle exact match of permissions arrays', () => {
      const userPermissions = ['view_dashboard', 'edit_profile', 'manage_team']
      const requiredPermissions = ['view_dashboard', 'edit_profile', 'manage_team']
      
      expect(hasAllPermissions(userPermissions, requiredPermissions)).toBe(true)
    })

    it('should handle order independence', () => {
      const userPermissions = ['manage_team', 'view_dashboard', 'edit_profile']
      const requiredPermissions = ['view_dashboard', 'edit_profile', 'manage_team']
      
      expect(hasAllPermissions(userPermissions, requiredPermissions)).toBe(true)
    })

    it('should handle duplicate permissions in required array', () => {
      const userPermissions = ['view_dashboard', 'edit_profile']
      const requiredPermissions = ['view_dashboard', 'view_dashboard', 'edit_profile']
      
      expect(hasAllPermissions(userPermissions, requiredPermissions)).toBe(true)
    })

    it('should handle single permission requirement', () => {
      const userPermissions = ['view_dashboard', 'edit_profile', 'manage_team']
      const requiredPermissions = ['edit_profile']
      
      expect(hasAllPermissions(userPermissions, requiredPermissions)).toBe(true)
    })
  })

  describe('Edge Cases', () => {
    it('should handle empty strings in permissions', () => {
      const userPermissions = ['', 'view_dashboard', '']
      const requiredPermissions = ['']
      
      expect(hasPermission(userPermissions, '')).toBe(true)
      expect(hasAnyPermission(userPermissions, requiredPermissions)).toBe(true)
      expect(hasAllPermissions(userPermissions, requiredPermissions)).toBe(true)
    })

    it('should handle whitespace-only permissions', () => {
      const userPermissions = ['   ', 'view_dashboard']
      const requiredPermissions = ['   ']
      
      expect(hasPermission(userPermissions, '   ')).toBe(true)
      expect(hasAnyPermission(userPermissions, requiredPermissions)).toBe(true)
      expect(hasAllPermissions(userPermissions, requiredPermissions)).toBe(true)
    })

    it('should handle very long permission strings', () => {
      const longPermission = 'a'.repeat(1000)
      const userPermissions = [longPermission, 'view_dashboard']
      
      expect(hasPermission(userPermissions, longPermission)).toBe(true)
    })

    it('should handle unicode characters in permissions', () => {
      const userPermissions = ['查看仪表板', 'éditer_profil', '管理团队']
      
      expect(hasPermission(userPermissions, '查看仪表板')).toBe(true)
      expect(hasPermission(userPermissions, 'éditer_profil')).toBe(true)
      expect(hasAnyPermission(userPermissions, ['查看仪表板', '未知权限'])).toBe(true)
      expect(hasAllPermissions(userPermissions, ['查看仪表板', 'éditer_profil'])).toBe(true)
    })
  })

  describe('Performance Tests', () => {
    it('should handle large permission arrays efficiently', () => {
      const userPermissions = Array.from({ length: 1000 }, (_, i) => `permission_${i}`)
      const requiredPermissions = ['permission_500', 'permission_750']
      
      const start = performance.now()
      const result = hasAllPermissions(userPermissions, requiredPermissions)
      const duration = performance.now() - start
      
      expect(result).toBe(true)
      expect(duration).toBeLessThan(10) // Should complete in less than 10ms
    })

    it('should handle many required permissions efficiently', () => {
      const userPermissions = Array.from({ length: 100 }, (_, i) => `permission_${i}`)
      const requiredPermissions = Array.from({ length: 50 }, (_, i) => `permission_${i}`)
      
      const start = performance.now()
      const result = hasAllPermissions(userPermissions, requiredPermissions)
      const duration = performance.now() - start
      
      expect(result).toBe(true)
      expect(duration).toBeLessThan(5) // Should complete in less than 5ms
    })
  })
})