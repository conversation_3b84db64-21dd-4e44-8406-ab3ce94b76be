import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

import { initWebVitals, getCurrentWebVitals } from './webVitals'

// Mock web-vitals module
vi.mock('web-vitals', () => ({
  onCLS: vi.fn((cb) => cb({ name: 'CLS', value: 0.05, id: '1', navigationType: 'navigate' })),
  onFCP: vi.fn((cb) => cb({ name: 'FCP', value: 1500, id: '2', navigationType: 'navigate' })),
  onFID: vi.fn((cb) => cb({ name: 'FID', value: 50, id: '3', navigationType: 'navigate' })),
  onINP: vi.fn((cb) => cb({ name: 'INP', value: 150, id: '4', navigationType: 'navigate' })),
  onLCP: vi.fn((cb) => cb({ name: 'LCP', value: 2000, id: '5', navigationType: 'navigate' })),
  onTTFB: vi.fn((cb) => cb({ name: 'TTFB', value: 500, id: '6', navigationType: 'navigate' })),
}))

describe('Web Vitals', () => {
  let consoleWarnSpy: ReturnType<typeof vi.spyOn>

  beforeEach(() => {
    consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    consoleWarnSpy.mockRestore()
  })

  describe('initWebVitals', () => {
    it('should initialize web vitals monitoring', () => {
      initWebVitals()

      // Check that main metrics are logged (6 metrics × 3 calls each in dev mode = 18)
      expect(consoleWarnSpy).toHaveBeenCalledTimes(18)
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining('[Web Vitals] CLS'),
        expect.any(String)
      )
    })

    it('should log metrics with appropriate ratings', () => {
      initWebVitals()

      // CLS should be "good" (< 0.1)
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining('CLS: 0.05 (good)'),
        expect.stringContaining('color: green')
      )

      // LCP should be "good" (< 2500ms)
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining('LCP: 2000.00ms (good)'),
        expect.stringContaining('color: green')
      )
    })
  })

  describe('getCurrentWebVitals', () => {
    it('should return current web vitals values', async () => {
      const vitals = await getCurrentWebVitals()

      expect(vitals).toEqual({
        CLS: 0.05,
        FCP: 1500,
        FID: 50,
        INP: 150,
        LCP: 2000,
        TTFB: 500,
      })
    })
  })

  describe('Performance Thresholds', () => {
    it('should correctly identify good metrics', () => {
      const goodMetrics = {
        LCP: 2000,    // < 2500ms
        FID: 50,      // < 100ms
        CLS: 0.05,    // < 0.1
        FCP: 1500,    // < 1800ms
        INP: 150,     // < 200ms
        TTFB: 500,    // < 800ms
      }

      initWebVitals()

      // All metrics should be logged as "good"
      Object.keys(goodMetrics).forEach(metric => {
        expect(consoleWarnSpy).toHaveBeenCalledWith(
          expect.stringContaining(`${metric}:`),
          expect.stringContaining('color: green')
        )
      })
    })
  })
})
