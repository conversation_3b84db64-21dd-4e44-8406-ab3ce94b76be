export const PERMISSIONS = {
  // Dashboard access
  VIEW_DASHBOARD: 'view_dashboard',
  VIEW_ADMIN_DASHBOARD: 'view_admin_dashboard',
  VIEW_MANAGER_DASHBOARD: 'view_manager_dashboard',
  
  // User management
  VIEW_USERS: 'view_users',
  CREATE_USERS: 'create_users',
  EDIT_USERS: 'edit_users',
  DELETE_USERS: 'delete_users',
  MANAGE_USER_ROLES: 'manage_user_roles',
  
  // Team management
  VIEW_TEAMS: 'view_teams',
  CREATE_TEAMS: 'create_teams',
  MANAGE_TEAMS: 'manage_teams',
  VIEW_TEAM_PERFORMANCE: 'view_team_performance',
  
  // Reports and analytics
  VIEW_REPORTS: 'view_reports',
  VIEW_FINANCIAL_REPORTS: 'view_financial_reports',
  VIEW_PERFORMANCE_REPORTS: 'view_performance_reports',
  EXPORT_REPORTS: 'export_reports',
  
  // Hierarchy management
  VIEW_HIERARCHY: 'view_hierarchy',
  <PERSON><PERSON><PERSON>_HIERARCHY: 'manage_hierarchy',
  VIEW_SUBORDINATES: 'view_subordinates',
  MANA<PERSON>_SUBORDINATES: 'manage_subordinates',
  
  // System administration
  SYSTEM_ADMIN: 'system_admin',
  MANAGE_ORGANIZATIONS: 'manage_organizations',
  MANAGE_SYSTEM_SETTINGS: 'manage_system_settings',
} as const

export const ROLES = {
  // Member hierarchy roles
  MSR: 'msr', // Member Sales Representative
  MSM: 'msm', // Member Sales Manager
  TSM: 'tsm', // Territory Sales Manager
  NSM: 'nsm', // National Sales Manager
  
  // Administrative roles
  ADMIN: 'admin',
  SUPER_ADMIN: 'super_admin',
  
  // Organizational roles
  TEAM_LEAD: 'team_lead',
  REGIONAL_MANAGER: 'regional_manager',
} as const

export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS]
export type Role = typeof ROLES[keyof typeof ROLES]

// Role-based permission mappings
export const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  [ROLES.MSR]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_USERS,
    PERMISSIONS.VIEW_TEAMS,
    PERMISSIONS.VIEW_REPORTS,
  ],
  [ROLES.MSM]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_MANAGER_DASHBOARD,
    PERMISSIONS.VIEW_USERS,
    PERMISSIONS.CREATE_USERS,
    PERMISSIONS.EDIT_USERS,
    PERMISSIONS.VIEW_TEAMS,
    PERMISSIONS.CREATE_TEAMS,
    PERMISSIONS.MANAGE_TEAMS,
    PERMISSIONS.VIEW_REPORTS,
    PERMISSIONS.VIEW_PERFORMANCE_REPORTS,
    PERMISSIONS.VIEW_SUBORDINATES,
    PERMISSIONS.MANAGE_SUBORDINATES,
  ],
  [ROLES.TSM]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_MANAGER_DASHBOARD,
    PERMISSIONS.VIEW_USERS,
    PERMISSIONS.CREATE_USERS,
    PERMISSIONS.EDIT_USERS,
    PERMISSIONS.VIEW_TEAMS,
    PERMISSIONS.CREATE_TEAMS,
    PERMISSIONS.MANAGE_TEAMS,
    PERMISSIONS.VIEW_REPORTS,
    PERMISSIONS.VIEW_PERFORMANCE_REPORTS,
    PERMISSIONS.VIEW_FINANCIAL_REPORTS,
    PERMISSIONS.EXPORT_REPORTS,
    PERMISSIONS.VIEW_HIERARCHY,
    PERMISSIONS.VIEW_SUBORDINATES,
    PERMISSIONS.MANAGE_SUBORDINATES,
  ],
  [ROLES.NSM]: [
    // NSM has access to most features
    ...Object.values(PERMISSIONS).filter(p => 
      !p.includes('SYSTEM') && p !== PERMISSIONS.MANAGE_ORGANIZATIONS
    ),
  ],
  [ROLES.ADMIN]: [
    ...Object.values(PERMISSIONS),
  ],
  [ROLES.SUPER_ADMIN]: [
    ...Object.values(PERMISSIONS),
  ],
  [ROLES.TEAM_LEAD]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_USERS,
    PERMISSIONS.VIEW_TEAMS,
    PERMISSIONS.MANAGE_TEAMS,
    PERMISSIONS.VIEW_REPORTS,
    PERMISSIONS.VIEW_TEAM_PERFORMANCE,
  ],
  [ROLES.REGIONAL_MANAGER]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_MANAGER_DASHBOARD,
    PERMISSIONS.VIEW_USERS,
    PERMISSIONS.CREATE_USERS,
    PERMISSIONS.EDIT_USERS,
    PERMISSIONS.VIEW_TEAMS,
    PERMISSIONS.CREATE_TEAMS,
    PERMISSIONS.MANAGE_TEAMS,
    PERMISSIONS.VIEW_REPORTS,
    PERMISSIONS.VIEW_PERFORMANCE_REPORTS,
    PERMISSIONS.VIEW_FINANCIAL_REPORTS,
    PERMISSIONS.EXPORT_REPORTS,
    PERMISSIONS.VIEW_HIERARCHY,
    PERMISSIONS.VIEW_SUBORDINATES,
    PERMISSIONS.MANAGE_SUBORDINATES,
  ],
}