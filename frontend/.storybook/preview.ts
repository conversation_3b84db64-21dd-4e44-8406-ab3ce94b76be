import type { Preview } from '@storybook/react-vite'
import React from 'react'
import '../src/index.css';

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
       color: /(background|color)$/i,
       date: /Date$/i,
      },
    },
    a11y: {
      // WCAG 2.1 AA compliance configuration
      config: {
        rules: [
          // Color contrast checks
          {
            id: 'color-contrast',
            enabled: true,
            options: {
              // AA level requires 4.5:1 for normal text, 3:1 for large text
              contrastRatio: {
                normal: 4.5,
                large: 3.0,
              },
            },
          },
          // Keyboard navigation
          {
            id: 'keyboard',
            enabled: true,
          },
          // Focus management
          {
            id: 'focus-order-semantics',
            enabled: true,
          },
          // ARIA labels and roles
          {
            id: 'aria-valid-attr-value',
            enabled: true,
          },
          {
            id: 'aria-required-children',
            enabled: true,
          },
          {
            id: 'aria-required-parent',
            enabled: true,
          },
          // Form accessibility
          {
            id: 'label',
            enabled: true,
          },
          {
            id: 'form-field-multiple-labels',
            enabled: true,
          },
          // Image accessibility
          {
            id: 'image-alt',
            enabled: true,
          },
          // Link accessibility
          {
            id: 'link-name',
            enabled: true,
          },
          // Button accessibility
          {
            id: 'button-name',
            enabled: true,
          },
          // Heading structure
          {
            id: 'heading-order',
            enabled: true,
          },
          // Skip links
          {
            id: 'skip-link',
            enabled: true,
          },
          // Landmark regions
          {
            id: 'region',
            enabled: true,
          },
        ],
      },
      // Manual checks that require human verification
      manual: true,
      // Element selector to ignore in a11y checks
      element: '#storybook-root',
      // Disable specific rules for certain stories if needed
      disable: [],
      // Additional options
      options: {
        runOnly: {
          type: 'tag',
          values: ['wcag2a', 'wcag2aa', 'wcag21aa']
        },
        restoreScroll: true,
      },
    },
  },
  // Global decorators for accessibility testing
  decorators: [
    (Story) => {
      return React.createElement(
        'div',
        { role: 'main', 'aria-label': 'Storybook Story' },
        React.createElement(Story)
      )
    },
  ],
};

export default preview;
