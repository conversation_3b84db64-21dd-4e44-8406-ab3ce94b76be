{"ci": {"collect": {"url": ["http://localhost:3000/"], "numberOfRuns": 3, "settings": {"preset": "desktop", "throttling": {"cpuSlowdownMultiplier": 1}}}, "assert": {"assertions": {"categories:performance": ["error", {"minScore": 0.9}], "categories:accessibility": ["error", {"minScore": 0.95}], "categories:best-practices": ["error", {"minScore": 0.95}], "categories:seo": ["error", {"minScore": 0.95}], "categories:pwa": ["warn", {"minScore": 0.9}], "first-contentful-paint": ["error", {"maxNumericValue": 1800}], "largest-contentful-paint": ["error", {"maxNumericValue": 2500}], "cumulative-layout-shift": ["error", {"maxNumericValue": 0.1}], "total-blocking-time": ["error", {"maxNumericValue": 300}], "speed-index": ["error", {"maxNumericValue": 3400}], "resource-summary:script:size": ["error", {"maxNumericValue": 200000}], "resource-summary:stylesheet:size": ["error", {"maxNumericValue": 100000}], "resource-summary:image:size": ["error", {"maxNumericValue": 500000}], "resource-summary:total:size": ["error", {"maxNumericValue": 1000000}], "uses-responsive-images": "error", "uses-optimized-images": "error", "uses-text-compression": "error", "uses-rel-preconnect": "warn", "font-display": "warn"}}, "upload": {"target": "temporary-public-storage"}}}