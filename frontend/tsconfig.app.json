{"compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "ES2022", "useDefineForClassFields": true, "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "types": ["vite/client", "vitest/globals", "jest", "@testing-library/jest-dom"], "moduleResolution": "bundler", "allowImportingTsExtensions": true, "verbatimModuleSyntax": true, "moduleDetection": "force", "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "erasableSyntaxOnly": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@pages/*": ["src/pages/*"], "@hooks/*": ["src/hooks/*"], "@services/*": ["src/services/*"], "@utils/*": ["src/utils/*"], "@types/*": ["src/types/*"], "@assets/*": ["src/assets/*"], "@test/*": ["src/test/*"]}}, "include": ["src"], "exclude": ["node_modules", "dist", "build", "src/*.gen.ts"]}