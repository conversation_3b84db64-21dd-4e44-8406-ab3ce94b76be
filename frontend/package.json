{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit && vite build", "build:analyze": "ANALYZE=true npm run build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "lighthouse": "lighthouse http://localhost:3000 --output html --output-path ./lighthouse-report.html", "routes:generate": "tsr generate"}, "dependencies": {"@clerk/clerk-react": "^5.42.1", "@clerk/themes": "^2.4.10", "@radix-ui/react-dialog": "^1.1.14", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.84.2", "@tanstack/react-router": "^1.131.2", "react": "^19.1.1", "react-dom": "^19.1.1", "web-vitals": "^4.2.1", "zustand": "^5.0.7"}, "devDependencies": {"@axe-core/react": "^4.10.2", "@chromatic-com/storybook": "^4.1.0", "@eslint/js": "^9.33.0", "@radix-ui/react-slot": "^1.2.3", "@rollup/plugin-terser": "^0.4.4", "@storybook/addon-a11y": "^9.1.1", "@storybook/addon-docs": "^9.1.1", "@storybook/addon-onboarding": "^9.1.1", "@storybook/addon-vitest": "^9.1.1", "@storybook/react-vite": "^9.1.1", "@tanstack/react-query-devtools": "^5.84.2", "@tanstack/router-cli": "^1.131.7", "@tanstack/router-devtools": "^1.131.2", "@tanstack/router-vite-plugin": "^1.131.2", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.13", "@types/jest-axe": "^3.5.9", "@types/node": "^24.2.1", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9.33.0", "eslint-import-resolver-typescript": "^3.7.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-storybook": "^9.1.1", "globals": "^16.3.0", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "lighthouse": "^12.0.0", "postcss": "^8.5.6", "rollup": "^4.27.0", "rollup-plugin-visualizer": "^5.12.0", "storybook": "^9.1.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "terser": "^5.31.3", "typescript": "~5.9.2", "typescript-eslint": "^8.39.0", "vite": "5.4.11", "vitest": "^3.2.4"}}