# Security & Deployment Best Practices

## Table of Contents
1. [Security Guidelines](#security-guidelines)
2. [Authentication & Authorization](#authentication--authorization)
3. [Data Protection](#data-protection)
4. [Infrastructure Security](#infrastructure-security)
5. [Deployment Best Practices](#deployment-best-practices)
6. [Monitoring & Logging](#monitoring--logging)
7. [Incident Response](#incident-response)
8. [Compliance & Auditing](#compliance--auditing)

---

## Security Guidelines

### Core Security Principles

#### Defense in Depth
- Multiple layers of security controls
- No single point of failure in security architecture
- Fail-safe defaults and graceful degradation

#### Least Privilege Access
- Grant minimum necessary permissions
- Regular access reviews and cleanup
- Role-based access control (RBAC)

#### Security by Design
- Security considerations in all development phases
- Threat modeling for new features
- Secure coding practices and peer review

### Code Security Standards

#### Input Validation
```python
# Always validate and sanitize input
from django.core.validators import validate_email
from django.utils.html import escape

def safe_input_handler(user_input):
    # Validate format
    if not isinstance(user_input, str) or len(user_input) > 255:
        raise ValidationError("Invalid input format")
    
    # Sanitize HTML
    return escape(user_input.strip())
```

#### SQL Injection Prevention
```python
# Use Django ORM or parameterized queries
# GOOD
Member.objects.filter(username=username)

# BAD - Never do this
cursor.execute(f"SELECT * FROM member WHERE username = '{username}'")
```

#### XSS Prevention
```python
# Template auto-escaping enabled by default
# For dynamic content, use |safe sparingly and validate input
from django.utils.safestring import mark_safe
from django.utils.html import format_html

def safe_dynamic_content(content):
    # Validate and sanitize before marking safe
    validated_content = validate_html_content(content)
    return mark_safe(validated_content)
```

---

## Authentication & Authorization

### Multi-Factor Authentication (MFA)

#### Clerk Integration
```python
# Clerk configuration for production
CLERK_SETTINGS = {
    'CLERK_PUBLISHABLE_KEY': os.getenv('CLERK_PUBLISHABLE_KEY'),
    'CLERK_SECRET_KEY': os.getenv('CLERK_SECRET_KEY'),
    'REQUIRE_2FA': True,
    'SESSION_DURATION': '1h',
    'MAX_SESSIONS': 3
}
```

#### JWT Security
```python
# JWT configuration
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'AUTH_HEADER_TYPES': ('Bearer',),
}
```

#### Password Requirements
```python
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {'min_length': 12}
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]
```

### Permission System Security

#### Dynamic Permissions Validation
```python
def validate_member_permissions(member, required_permission):
    """Validate member permissions securely"""
    if not member.is_active:
        return False
    
    # Check member type permissions
    member_permissions = member.member_type.permissions.get('permissions', [])
    
    # Validate permission format
    if not isinstance(required_permission, str):
        return False
    
    return required_permission in member_permissions
```

#### Role-Based Access Control
```python
class PermissionMixin:
    """Secure permission checking mixin"""
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        required_perms = getattr(view, 'required_permissions', [])
        if not required_perms:
            return True
        
        return all(
            validate_member_permissions(request.user, perm) 
            for perm in required_perms
        )
```

---

## Data Protection

### Encryption Standards

#### Data at Rest
```yaml
# Database encryption
DB_ENCRYPTION:
  algorithm: "AES-256-GCM"
  key_rotation: "90 days"
  backup_encryption: true

# File system encryption
FILESYSTEM:
  encryption: "LUKS/dm-crypt"
  key_management: "HashiCorp Vault"
```

#### Data in Transit
```nginx
# NGINX SSL configuration
server {
    listen 443 ssl http2;
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # Modern SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
    ssl_prefer_server_ciphers off;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
}
```

### Personal Data Handling

#### PII Protection
```python
class PIIField(models.CharField):
    """Encrypted field for PII data"""
    
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('max_length', 255)
        super().__init__(*args, **kwargs)
    
    def from_db_value(self, value, expression, connection):
        if value is None:
            return value
        return decrypt_pii(value)
    
    def to_python(self, value):
        if isinstance(value, str):
            return value
        return decrypt_pii(value) if value else None
    
    def get_prep_value(self, value):
        return encrypt_pii(value) if value else None
```

#### Data Retention Policies
```python
# Automated data cleanup
class DataRetentionManager:
    RETENTION_PERIODS = {
        'audit_logs': timedelta(days=2555),  # 7 years
        'user_sessions': timedelta(days=30),
        'password_resets': timedelta(days=1),
        'communications': timedelta(days=1095),  # 3 years
    }
    
    def cleanup_expired_data(self):
        for model_name, retention_period in self.RETENTION_PERIODS.items():
            cutoff_date = timezone.now() - retention_period
            self.delete_expired_records(model_name, cutoff_date)
```

---

## Infrastructure Security

### Container Security

#### Docker Security
```dockerfile
# Multi-stage build for security
FROM python:3.12-slim as builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --user --no-cache-dir -r requirements.txt

FROM python:3.12-slim
# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser
WORKDIR /app

# Copy only necessary files
COPY --from=builder /root/.local /home/<USER>/.local
COPY --chown=appuser:appuser . .

# Security configurations
USER appuser
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/status/ || exit 1
```

#### Container Scanning
```yaml
# .github/workflows/security.yml
name: Security Scan
on: [push, pull_request]

jobs:
  container-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build image
        run: docker build -t xd-incentives:test .
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'xd-incentives:test'
          format: 'sarif'
          output: 'trivy-results.sarif'
```

### Network Security

#### Firewall Rules
```bash
# UFW firewall configuration
ufw default deny incoming
ufw default allow outgoing

# Allow necessary services
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS

# Database access (internal only)
ufw allow from 10.0.0.0/24 to any port 3306

ufw enable
```

#### Network Isolation
```yaml
# Docker Compose network security
version: '3.8'
services:
  web:
    networks:
      - frontend
      - backend
  
  db:
    networks:
      - backend
    # No frontend network access

networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true  # No external access
```

---

## Deployment Best Practices

### Environment Configuration

#### Production Settings
```python
# config/settings/production.py
import os
from .base import *

# Security settings
DEBUG = False
ALLOWED_HOSTS = [os.getenv('ALLOWED_HOSTS', '').split(',')]

# HTTPS settings
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Cookie security
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# Content Security Policy
CSP_DEFAULT_SRC = ("'self'",)
CSP_SCRIPT_SRC = ("'self'", "'unsafe-inline'")
CSP_STYLE_SRC = ("'self'", "'unsafe-inline'")
```

#### Environment Variables
```bash
# .env.production
SECRET_KEY=your-super-secret-key-here
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# Database
DB_NAME=xd_incentives_prod
DB_USER=app_user
DB_PASSWORD=complex-password-here
DB_HOST=db.internal
DB_PORT=3306

# External services
CLERK_PUBLISHABLE_KEY=pk_live_...
CLERK_SECRET_KEY=sk_live_...
REDIS_URL=redis://redis.internal:6379/0

# Email
EMAIL_HOST=smtp.yourdomain.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=email-password-here
```

### Blue-Green Deployment

#### Deployment Script
```bash
#!/bin/bash
# deploy.sh - Blue-green deployment script

set -e

BLUE_CONTAINER="xd-backend-blue"
GREEN_CONTAINER="xd-backend-green"
NGINX_CONFIG="/etc/nginx/sites-available/xd-incentives"

# Determine current active container
CURRENT=$(docker ps --format "table {{.Names}}" | grep -E "(blue|green)" | head -1)

if [[ $CURRENT == *"blue"* ]]; then
    DEPLOY_TO="green"
    CURRENT_CONTAINER=$BLUE_CONTAINER
    NEW_CONTAINER=$GREEN_CONTAINER
else
    DEPLOY_TO="blue"
    CURRENT_CONTAINER=$GREEN_CONTAINER
    NEW_CONTAINER=$BLUE_CONTAINER
fi

echo "Deploying to $DEPLOY_TO environment"

# Build and start new container
docker build -t xd-incentives:$DEPLOY_TO .
docker run -d --name $NEW_CONTAINER \
    --env-file .env.production \
    -p 800${DEPLOY_TO:0:1}:8000 \
    xd-incentives:$DEPLOY_TO

# Health check
echo "Performing health check..."
for i in {1..30}; do
    if curl -f http://localhost:800${DEPLOY_TO:0:1}/status/; then
        echo "Health check passed"
        break
    fi
    sleep 2
done

# Update NGINX configuration
sed -i "s/800[0-9]/800${DEPLOY_TO:0:1}/" $NGINX_CONFIG
nginx -s reload

# Stop old container
docker stop $CURRENT_CONTAINER
docker rm $CURRENT_CONTAINER

echo "Deployment to $DEPLOY_TO completed successfully"
```

### Database Migration Security

#### Safe Migration Process
```python
# management/commands/safe_migrate.py
from django.core.management.base import BaseCommand
from django.db import transaction

class Command(BaseCommand):
    def handle(self, *args, **options):
        # Create database backup before migration
        self.create_backup()
        
        try:
            with transaction.atomic():
                # Run migrations
                call_command('migrate')
                
                # Verify data integrity
                self.verify_data_integrity()
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Migration failed: {e}')
            )
            # Restore from backup if needed
            self.restore_backup()
            raise
    
    def create_backup(self):
        """Create database backup before migration"""
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"backup_before_migration_{timestamp}.sql"
        # Implementation depends on database type
```

---

## Monitoring & Logging

### Security Logging

#### Audit Trail Configuration
```python
# config/logging_settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'security': {
            'format': '{asctime} {levelname} {name} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'security_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/security.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 10,
            'formatter': 'security',
        },
    },
    'loggers': {
        'security': {
            'handlers': ['security_file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

#### Security Event Tracking
```python
import logging

security_logger = logging.getLogger('security')

class SecurityMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Log authentication attempts
        if request.path.startswith('/api/token/'):
            self.log_auth_attempt(request)
        
        # Log admin access
        if request.path.startswith('/admin/'):
            self.log_admin_access(request)
        
        response = self.get_response(request)
        
        # Log suspicious activity
        if response.status_code == 403:
            self.log_access_denied(request)
        
        return response
    
    def log_auth_attempt(self, request):
        security_logger.info(f"Auth attempt from {request.META.get('REMOTE_ADDR')}")
    
    def log_admin_access(self, request):
        if request.user.is_authenticated:
            security_logger.info(f"Admin access by {request.user} from {request.META.get('REMOTE_ADDR')}")
```

### Performance Monitoring

#### Application Performance Monitoring (APM)
```python
# APM integration
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.celery import CeleryIntegration

sentry_sdk.init(
    dsn=os.getenv('SENTRY_DSN'),
    integrations=[
        DjangoIntegration(
            transaction_style='url',
            middleware_spans=True,
            signals_spans=True,
        ),
        CeleryIntegration(monitor_beat_tasks=True),
    ],
    traces_sample_rate=0.1,
    send_default_pii=False,  # GDPR compliance
    environment=os.getenv('ENVIRONMENT', 'production'),
)
```

---

## Incident Response

### Security Incident Response Plan

#### Detection and Assessment
1. **Automated Detection**
   - Log analysis alerts
   - Anomaly detection
   - Performance monitoring

2. **Manual Detection**
   - User reports
   - Security audits
   - Penetration testing

#### Response Procedures
```python
# Emergency response script
class IncidentResponse:
    def __init__(self):
        self.severity_levels = {
            'low': {'response_time': '24h', 'escalation': False},
            'medium': {'response_time': '4h', 'escalation': True},
            'high': {'response_time': '1h', 'escalation': True},
            'critical': {'response_time': '15m', 'escalation': True}
        }
    
    def handle_security_incident(self, incident_type, severity):
        # Immediate containment
        if severity == 'critical':
            self.emergency_lockdown()
        
        # Notification
        self.notify_security_team(incident_type, severity)
        
        # Evidence preservation
        self.preserve_logs()
        
        # Recovery planning
        self.initiate_recovery_plan(incident_type)
    
    def emergency_lockdown(self):
        """Emergency system lockdown procedures"""
        # Disable external access
        # Backup current state
        # Alert management
        pass
```

### Business Continuity

#### Backup Strategy
```bash
#!/bin/bash
# backup_system.sh - Comprehensive backup script

# Database backup
mysqldump --single-transaction --routines --triggers \
    $DB_NAME | gzip > "backups/db_$(date +%Y%m%d_%H%M%S).sql.gz"

# Application files backup
tar -czf "backups/app_$(date +%Y%m%d_%H%M%S).tar.gz" \
    --exclude='logs' --exclude='backups' /app

# Upload to S3
aws s3 sync backups/ s3://your-backup-bucket/xd-incentives/

# Cleanup old backups (keep 30 days)
find backups/ -name "*.gz" -mtime +30 -delete
```

#### Disaster Recovery
```yaml
# Disaster recovery playbook
recovery_procedures:
  rto: "4 hours"  # Recovery Time Objective
  rpo: "1 hour"   # Recovery Point Objective
  
  steps:
    1: "Assess damage and determine recovery strategy"
    2: "Restore from most recent backup"
    3: "Verify data integrity and system functionality"
    4: "Update DNS and routing to recovery environment"
    5: "Monitor system performance and user access"
    6: "Plan return to primary environment"
```

---

## Compliance & Auditing

### GDPR Compliance

#### Data Subject Rights
```python
class GDPRMixin:
    """GDPR compliance utilities"""
    
    def export_user_data(self, user):
        """Export all user data for GDPR requests"""
        data = {
            'profile': self.get_user_profile(user),
            'communications': self.get_user_communications(user),
            'audit_logs': self.get_user_audit_logs(user),
        }
        return data
    
    def anonymize_user_data(self, user):
        """Anonymize user data for deletion requests"""
        user.first_name = f"Deleted_{user.id}"
        user.last_name = "User"
        user.email = f"deleted_{user.id}@example.com"
        user.is_active = False
        user.save()
        
        # Anonymize related data
        self.anonymize_communications(user)
        self.anonymize_audit_logs(user)
```

### Security Auditing

#### Regular Security Assessments
```python
# Security audit command
class Command(BaseCommand):
    def handle(self, *args, **options):
        audit_results = []
        
        # Check password policies
        audit_results.append(self.audit_password_policies())
        
        # Check permission configurations
        audit_results.append(self.audit_permissions())
        
        # Check encryption settings
        audit_results.append(self.audit_encryption())
        
        # Generate report
        self.generate_audit_report(audit_results)
    
    def audit_password_policies(self):
        """Audit password policy compliance"""
        weak_passwords = []
        for user in Member.objects.all():
            if not self.check_password_strength(user):
                weak_passwords.append(user.username)
        
        return {
            'check': 'Password Policies',
            'status': 'PASS' if not weak_passwords else 'FAIL',
            'details': weak_passwords
        }
```

#### Compliance Reporting
```python
class ComplianceReporter:
    def generate_monthly_report(self):
        """Generate monthly compliance report"""
        report = {
            'period': self.get_report_period(),
            'security_incidents': self.get_security_incidents(),
            'access_reviews': self.get_access_reviews(),
            'data_breaches': self.get_data_breaches(),
            'training_completion': self.get_training_completion(),
        }
        
        # Generate PDF report
        self.create_pdf_report(report)
        
        # Send to compliance team
        self.send_compliance_report(report)
```

---

## Security Checklist

### Pre-Deployment Security Checklist

#### Application Security
- [ ] Input validation implemented
- [ ] SQL injection protection verified
- [ ] XSS protection enabled
- [ ] CSRF protection configured
- [ ] Authentication system tested
- [ ] Authorization rules verified
- [ ] Password policies enforced
- [ ] Session management secured

#### Infrastructure Security
- [ ] HTTPS configured with valid certificates
- [ ] Firewall rules configured
- [ ] Database access restricted
- [ ] Container security scanned
- [ ] Network segmentation implemented
- [ ] Backup systems tested
- [ ] Monitoring and alerting configured

#### Operational Security
- [ ] Security logging enabled
- [ ] Incident response plan documented
- [ ] Access reviews completed
- [ ] Security training provided
- [ ] Compliance requirements met
- [ ] Disaster recovery tested

### Post-Deployment Security Monitoring

#### Daily Checks
- [ ] Security alerts reviewed
- [ ] Failed login attempts monitored
- [ ] System performance monitored
- [ ] Backup completion verified

#### Weekly Checks
- [ ] Security logs analyzed
- [ ] Access permissions reviewed
- [ ] Vulnerability scans performed
- [ ] Security patches applied

#### Monthly Checks
- [ ] Comprehensive security audit
- [ ] Compliance report generated
- [ ] Incident response plan updated
- [ ] Security training reviewed

---

## Contact Information

For security-related issues or questions:

- **Security Team**: <EMAIL>
- **Incident Reporting**: Use internal incident management system in monday.com

Remember: Security is everyone's responsibility. Report suspicious activities immediately.