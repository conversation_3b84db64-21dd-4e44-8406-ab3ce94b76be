# Development Best Practices Guide

## Table of Contents
1. [Code Organization](#code-organization)
2. [Django Best Practices](#django-best-practices)
3. [API Development](#api-development)
4. [Database Management](#database-management)
5. [Testing Strategy](#testing-strategy)
6. [Performance Optimization](#performance-optimization)
7. [Code Quality](#code-quality)
8. [Version Control](#version-control)
9. [Documentation](#documentation)
10. [Troubleshooting](#troubleshooting)

---

## Code Organization

### Project Structure Standards

#### App Organization
```
apps/
├── member/                 # Core member management
│   ├── models.py          # Data models
│   ├── views.py           # Business logic
│   ├── serializers.py     # API serialization
│   ├── admin.py           # Admin interface
│   ├── forms.py           # Form definitions
│   ├── urls.py            # URL routing
│   ├── signals.py         # Django signals
│   ├── tasks.py           # Celery tasks
│   ├── utils.py           # Utility functions
│   ├── managers.py        # Custom managers
│   ├── permissions.py     # Permission classes
│   ├── decorators.py      # Custom decorators
│   ├── templatetags/      # Template tags
│   ├── templates/         # App templates
│   ├── static/           # App static files
│   ├── migrations/       # Database migrations
│   ├── management/       # Management commands
│   └── tests/            # Test files
│       ├── test_models.py
│       ├── test_views.py
│       ├── test_api.py
│       └── factories.py
```

#### Configuration Structure
```
config/
├── __init__.py
├── settings/              # Environment-based settings
│   ├── __init__.py
│   ├── base.py           # Base settings
│   ├── development.py    # Development settings
│   ├── production.py     # Production settings
│   └── testing.py        # Testing settings
├── urls.py               # Root URL configuration
├── wsgi.py               # WSGI configuration
├── asgi.py               # ASGI configuration
├── celery.py             # Celery configuration
└── logging_settings.py   # Logging configuration
```

### Naming Conventions

#### Python Code
```python
# Classes: PascalCase
class MemberManager:
    pass

class MemberHierarchy:
    pass

# Functions and variables: snake_case
def get_member_details():
    pass

def calculate_member_statistics():
    pass

member_count = 0
hierarchical_structure = {}

# Constants: UPPER_SNAKE_CASE
MAX_SUBORDINATES = 50
DEFAULT_MEMBER_TYPE = 'regular'
```

#### Database
```python
# Model names: PascalCase (singular)
class Member(models.Model):
    pass

class MemberType(models.Model):
    pass

# Field names: snake_case
class Member(models.Model):
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    date_joined = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

# Table names: lowercase with underscores (Django auto-generates)
# member, member_type, member_hierarchy
```

#### URLs
```python
# URL patterns: kebab-case
urlpatterns = [
    path('members/', MemberListView.as_view(), name='member-list'),
    path('member-types/', MemberTypeListView.as_view(), name='member-type-list'),
    path('organization-chart/', OrganizationChartView.as_view(), name='organization-chart'),
]
```

---

## Django Best Practices

### Model Design

#### Model Structure
```python
from django.db import models
from django.core.validators import RegexValidator
from django.utils.translation import gettext_lazy as _

class Member(models.Model):
    """
    Core member model extending AbstractUser
    
    This model represents system users with extended profile information
    and organizational hierarchy relationships.
    """
    
    # Use choices for constrained fields
    class Status(models.TextChoices):
        ACTIVE = 'active', _('Active')
        INACTIVE = 'inactive', _('Inactive')
        PENDING = 'pending', _('Pending Approval')
        SUSPENDED = 'suspended', _('Suspended')
    
    # Field definitions with proper validation
    first_name = models.CharField(
        max_length=100,
        validators=[RegexValidator(r'^[a-zA-Z\s]+$', 'Only letters and spaces allowed')]
    )
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    
    # Use related_name for reverse relationships
    member_type = models.ForeignKey(
        'MemberType',
        on_delete=models.PROTECT,
        related_name='members'
    )
    
    # Custom managers
    objects = MemberManager()
    active = ActiveMemberManager()
    
    class Meta:
        db_table = 'member'
        verbose_name = _('Member')
        verbose_name_plural = _('Members')
        ordering = ['last_name', 'first_name']
        indexes = [
            models.Index(fields=['status', 'is_active']),
            models.Index(fields=['member_type', 'region']),
        ]
    
    def __str__(self):
        return f"{self.get_full_name()} ({self.username})"
    
    def get_full_name(self):
        """Return full name with proper formatting"""
        return f"{self.first_name} {self.last_name}".strip()
    
    def get_permissions(self):
        """Get all permissions for this member"""
        if not self.member_type:
            return []
        return self.member_type.permissions.get('permissions', [])
    
    def has_permission(self, permission):
        """Check if member has specific permission"""
        return permission in self.get_permissions()
```

#### Custom Managers and QuerySets
```python
class MemberQuerySet(models.QuerySet):
    """Custom QuerySet for Member model"""
    
    def active(self):
        """Return only active members"""
        return self.filter(is_active=True, status=Member.Status.ACTIVE)
    
    def by_region(self, region):
        """Filter members by region"""
        return self.filter(region=region)
    
    def with_permissions(self, permission):
        """Filter members with specific permission"""
        return self.filter(
            member_type__permissions__permissions__contains=[permission]
        )
    
    def prefetch_related_data(self):
        """Optimize queries with common related data"""
        return self.select_related(
            'member_type', 'region'
        ).prefetch_related(
            'teams', 'managers', 'subordinates'
        )

class MemberManager(models.Manager):
    """Custom manager for Member model"""
    
    def get_queryset(self):
        return MemberQuerySet(self.model, using=self._db)
    
    def active(self):
        return self.get_queryset().active()
    
    def by_region(self, region):
        return self.get_queryset().by_region(region)
    
    def create_member(self, **kwargs):
        """Create member with default values"""
        kwargs.setdefault('status', Member.Status.PENDING)
        kwargs.setdefault('is_active', True)
        return self.create(**kwargs)
```

### View Patterns

#### Class-Based Views
```python
from django.views.generic import ListView, DetailView, CreateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q, Prefetch

class MemberListView(LoginRequiredMixin, ListView):
    """List all members with filtering and pagination"""
    
    model = Member
    template_name = 'member/member_list.html'
    context_object_name = 'members'
    paginate_by = 20
    
    def get_queryset(self):
        """Optimize queryset with filtering"""
        queryset = Member.objects.prefetch_related_data()
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(email__icontains=search)
            )
        
        # Filter by member type
        member_type = self.request.GET.get('member_type')
        if member_type:
            queryset = queryset.filter(member_type__id=member_type)
        
        return queryset.distinct()
    
    def get_context_data(self, **kwargs):
        """Add additional context"""
        context = super().get_context_data(**kwargs)
        context['member_types'] = MemberType.objects.filter(is_active=True)
        context['search_query'] = self.request.GET.get('search', '')
        return context

class MemberDetailView(LoginRequiredMixin, DetailView):
    """Display detailed member information"""
    
    model = Member
    template_name = 'member/member_detail.html'
    context_object_name = 'member'
    
    def get_queryset(self):
        """Optimize with related data"""
        return Member.objects.prefetch_related_data()
    
    def get_context_data(self, **kwargs):
        """Add hierarchy and team information"""
        context = super().get_context_data(**kwargs)
        member = self.get_object()
        
        context['hierarchy'] = {
            'managers': member.get_managers(),
            'subordinates': member.get_subordinates(),
        }
        context['teams'] = member.get_active_teams()
        
        return context
```

#### Permission Mixins
```python
from django.contrib.auth.mixins import UserPassesTestMixin
from django.core.exceptions import PermissionDenied

class PermissionRequiredMixin(UserPassesTestMixin):
    """Mixin to check specific permissions"""
    
    permission_required = None
    
    def test_func(self):
        """Test if user has required permission"""
        if not self.permission_required:
            return True
        
        return self.request.user.has_permission(self.permission_required)
    
    def handle_no_permission(self):
        """Custom handling for permission denied"""
        if self.request.user.is_authenticated:
            raise PermissionDenied("You don't have permission to access this resource.")
        return super().handle_no_permission()

class AdminRequiredMixin(UserPassesTestMixin):
    """Mixin requiring admin permissions"""
    
    def test_func(self):
        return (
            self.request.user.is_authenticated and
            (self.request.user.is_superuser or 
             self.request.user.has_permission('admin.access'))
        )
```

---

## API Development

### REST API Design Principles

#### Consistent URL Structure
```python
# Good URL patterns
/api/v1/members/                    # List/Create members
/api/v1/members/{id}/               # Get/Update/Delete specific member
/api/v1/members/{id}/teams/         # Member's teams
/api/v1/teams/{id}/members/         # Team's members
/api/v1/organization-chart/         # Hierarchical data

# Avoid
/api/v1/get-members/               # Don't use verbs in URLs
/api/v1/member-list/               # Don't use action names
/api/v1/members/get/{id}/          # HTTP method defines action
```

#### HTTP Status Codes
```python
from rest_framework import status
from rest_framework.response import Response

class MemberAPIView(APIView):
    def get(self, request, pk=None):
        if pk:
            try:
                member = Member.objects.get(pk=pk)
                serializer = MemberSerializer(member)
                return Response(serializer.data)  # 200 OK
            except Member.DoesNotExist:
                return Response(
                    {'error': 'Member not found'}, 
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            members = Member.objects.all()
            serializer = MemberSerializer(members, many=True)
            return Response(serializer.data)  # 200 OK
    
    def post(self, request):
        serializer = MemberSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                serializer.data, 
                status=status.HTTP_201_CREATED
            )
        return Response(
            serializer.errors, 
            status=status.HTTP_400_BAD_REQUEST
        )
```

### Serializer Patterns

#### Base Serializer Structure
```python
from rest_framework import serializers
from django.contrib.auth import get_user_model

class BaseSerializer(serializers.ModelSerializer):
    """Base serializer with common functionality"""
    
    def to_representation(self, instance):
        """Custom representation with null field removal"""
        data = super().to_representation(instance)
        # Remove null fields from response
        return {key: value for key, value in data.items() if value is not None}

class MemberSerializer(BaseSerializer):
    """Member serializer with nested relationships"""
    
    full_name = serializers.SerializerMethodField()
    member_type = MemberTypeSerializer(read_only=True)
    region = RegionSerializer(read_only=True)
    teams = serializers.SerializerMethodField()
    
    class Meta:
        model = Member
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'full_name', 'status', 'member_type', 'region',
            'teams', 'is_active', 'date_joined'
        ]
        read_only_fields = ['id', 'date_joined']
    
    def get_full_name(self, obj):
        """Get formatted full name"""
        return obj.get_full_name()
    
    def get_teams(self, obj):
        """Get member's active teams"""
        teams = obj.teams.filter(memberteam__end_date__isnull=True)
        return TeamBasicSerializer(teams, many=True).data
    
    def validate_email(self, value):
        """Custom email validation"""
        if Member.objects.filter(email=value).exclude(pk=self.instance.pk if self.instance else None).exists():
            raise serializers.ValidationError("Email already exists")
        return value
    
    def create(self, validated_data):
        """Custom create with additional logic"""
        # Set default member type if not provided
        if 'member_type' not in validated_data:
            default_type = MemberType.objects.get(slug='regular')
            validated_data['member_type'] = default_type
        
        return super().create(validated_data)
```

#### Nested Serialization
```python
class HierarchySerializer(serializers.ModelSerializer):
    """Serializer for hierarchy relationships"""
    
    member = serializers.SerializerMethodField()
    manager = serializers.SerializerMethodField()
    
    class Meta:
        model = MemberHierarchy
        fields = [
            'id', 'member', 'manager', 'relationship_type',
            'is_primary', 'start_date', 'end_date'
        ]
    
    def get_member(self, obj):
        return {
            'id': obj.member.id,
            'username': obj.member.username,
            'full_name': obj.member.get_full_name()
        }
    
    def get_manager(self, obj):
        return {
            'id': obj.manager.id,
            'username': obj.manager.username,
            'full_name': obj.manager.get_full_name()
        }

class OrganizationChartSerializer(serializers.Serializer):
    """Serializer for hierarchical organization chart"""
    
    def to_representation(self, queryset):
        """Build hierarchical structure"""
        def build_hierarchy(member, visited=None):
            if visited is None:
                visited = set()
            
            if member.id in visited:
                return None  # Prevent circular references
            
            visited.add(member.id)
            
            subordinates = member.get_subordinates().exclude(id__in=visited)
            
            return {
                'member': {
                    'id': member.id,
                    'username': member.username,
                    'full_name': member.get_full_name(),
                    'member_type': member.member_type.name if member.member_type else None
                },
                'subordinates': [
                    build_hierarchy(sub, visited.copy()) 
                    for sub in subordinates
                    if build_hierarchy(sub, visited.copy()) is not None
                ]
            }
        
        # Get top-level members (no managers)
        top_level = queryset.filter(managers__isnull=True)
        
        return {
            'hierarchy': [build_hierarchy(member) for member in top_level],
            'statistics': {
                'total_members': queryset.count(),
                'top_level_count': top_level.count()
            }
        }
```

### API Versioning

#### URL-based Versioning
```python
# urls.py
from django.urls import path, include

urlpatterns = [
    path('api/v1/', include('apps.api.v1.urls')),
    path('api/v2/', include('apps.api.v2.urls')),  # Future version
]

# apps/api/v1/urls.py
urlpatterns = [
    path('members/', MemberListCreateAPIView.as_view(), name='member-list'),
    path('members/<int:pk>/', MemberRetrieveUpdateDestroyAPIView.as_view(), name='member-detail'),
]
```

#### Backward Compatibility
```python
class MemberSerializerV1(serializers.ModelSerializer):
    """Version 1 of Member serializer"""
    
    class Meta:
        model = Member
        fields = ['id', 'username', 'email', 'first_name', 'last_name']

class MemberSerializerV2(MemberSerializerV1):
    """Version 2 with additional fields"""
    
    member_type = MemberTypeSerializer(read_only=True)
    teams = serializers.SerializerMethodField()
    
    class Meta(MemberSerializerV1.Meta):
        fields = MemberSerializerV1.Meta.fields + ['member_type', 'teams', 'status']
```

---

## Database Management

### Migration Best Practices

#### Safe Migration Patterns
```python
# Example: Adding a non-null field safely
from django.db import migrations, models

class Migration(migrations.Migration):
    """
    Safe migration: Adding non-null field with default value
    """
    
    dependencies = [
        ('member', '0010_previous_migration'),
    ]
    
    operations = [
        # Step 1: Add field with default value
        migrations.AddField(
            model_name='member',
            name='tier',
            field=models.IntegerField(default=1),
        ),
        # Step 2: Run data migration to set proper values
        migrations.RunPython(
            code=update_member_tiers,
            reverse_code=migrations.RunPython.noop,
        ),
        # Step 3: Remove default (in next migration if needed)
    ]

def update_member_tiers(apps, schema_editor):
    """Data migration to set proper tier values"""
    Member = apps.get_model('member', 'Member')
    
    # Set tiers based on member type
    Member.objects.filter(member_type__slug='premium').update(tier=3)
    Member.objects.filter(member_type__slug='standard').update(tier=2)
    # default=1 already set for others
```

#### Index Management
```python
class Migration(migrations.Migration):
    """Adding database indexes for performance"""
    
    operations = [
        migrations.RunSQL(
            # Add index
            "CREATE INDEX CONCURRENTLY member_status_active_idx ON member (status, is_active);",
            # Remove index
            "DROP INDEX member_status_active_idx;"
        ),
    ]
```

### Query Optimization

#### Efficient QuerySets
```python
class MemberViewSet(viewsets.ModelViewSet):
    """Optimized Member ViewSet"""
    
    def get_queryset(self):
        """Optimize queries based on action"""
        queryset = Member.objects.all()
        
        if self.action == 'list':
            # List view: minimal data with pagination
            queryset = queryset.select_related(
                'member_type', 'region'
            ).only(
                'id', 'username', 'first_name', 'last_name',
                'email', 'status', 'member_type__name', 'region__title'
            )
        
        elif self.action == 'retrieve':
            # Detail view: full data with relationships
            queryset = queryset.select_related(
                'member_type', 'region'
            ).prefetch_related(
                Prefetch(
                    'memberteam_set',
                    queryset=MemberTeam.objects.select_related('team').filter(
                        end_date__isnull=True
                    )
                ),
                'managers__manager',
                'subordinates__member'
            )
        
        return queryset
```

#### Raw SQL When Needed
```python
from django.db import connection

def get_member_statistics():
    """Complex statistics query using raw SQL"""
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT 
                mt.name as member_type,
                COUNT(*) as total_members,
                COUNT(CASE WHEN m.is_active = true THEN 1 END) as active_members,
                AVG(EXTRACT(days FROM NOW() - m.date_joined)) as avg_days_since_joined
            FROM member m
            JOIN member_type mt ON m.member_type_id = mt.id
            WHERE m.created >= %s
            GROUP BY mt.id, mt.name
            ORDER BY total_members DESC
        """, [timezone.now() - timedelta(days=30)])
        
        columns = [col[0] for col in cursor.description]
        return [dict(zip(columns, row)) for row in cursor.fetchall()]
```

### Database Connection Management
```python
# settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
        },
        'CONN_MAX_AGE': 3600,  # Connection pooling
        'TIME_ZONE': 'UTC',
    }
}

# Connection monitoring
import logging
from django.db import connections
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    def handle(self, *args, **options):
        """Monitor database connections"""
        for alias in connections:
            connection = connections[alias]
            logging.info(f"Database {alias}: {connection.queries_log}")
```

---

## Testing Strategy

### Test Structure

#### Test Organization
```python
# tests/test_models.py
from django.test import TestCase
from django.core.exceptions import ValidationError
from ..models import Member, MemberType
from .factories import MemberFactory, MemberTypeFactory

class MemberModelTest(TestCase):
    """Test Member model functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.member_type = MemberTypeFactory()
        self.member = MemberFactory(member_type=self.member_type)
    
    def test_member_creation(self):
        """Test basic member creation"""
        self.assertTrue(isinstance(self.member, Member))
        self.assertEqual(self.member.member_type, self.member_type)
    
    def test_full_name_method(self):
        """Test get_full_name method"""
        self.member.first_name = "John"
        self.member.last_name = "Doe"
        self.assertEqual(self.member.get_full_name(), "John Doe")
    
    def test_permission_checking(self):
        """Test permission system"""
        # Test with permission
        self.member_type.permissions = {'permissions': ['member.view']}
        self.member_type.save()
        self.assertTrue(self.member.has_permission('member.view'))
        
        # Test without permission  
        self.assertFalse(self.member.has_permission('admin.access'))
    
    def test_string_representation(self):
        """Test __str__ method"""
        expected = f"{self.member.get_full_name()} ({self.member.username})"
        self.assertEqual(str(self.member), expected)

    def test_unique_constraints(self):
        """Test unique field constraints"""
        with self.assertRaises(ValidationError):
            duplicate_member = MemberFactory(email=self.member.email)
            duplicate_member.full_clean()
```

#### Test Factories
```python
# tests/factories.py
import factory
from factory.django import DjangoModelFactory
from django.contrib.auth import get_user_model
from ..models import Member, MemberType, Team

class MemberTypeFactory(DjangoModelFactory):
    """Factory for MemberType model"""
    
    class Meta:
        model = MemberType
    
    name = factory.Sequence(lambda n: f"Member Type {n}")
    slug = factory.LazyAttribute(lambda obj: obj.name.lower().replace(' ', '_'))
    description = factory.Faker('text', max_nb_chars=200)
    is_active = True
    permissions = factory.LazyFunction(
        lambda: {'permissions': ['member.view', 'communication.send']}
    )

class MemberFactory(DjangoModelFactory):
    """Factory for Member model"""
    
    class Meta:
        model = Member
    
    username = factory.Sequence(lambda n: f"user{n}")
    email = factory.LazyAttribute(lambda obj: f"{obj.username}@example.com")
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    member_type = factory.SubFactory(MemberTypeFactory)
    is_active = True
    status = Member.Status.ACTIVE

class TeamFactory(DjangoModelFactory):
    """Factory for Team model"""
    
    class Meta:
        model = Team
    
    name = factory.Sequence(lambda n: f"Team {n}")
    team_type = 'project'
    description = factory.Faker('text', max_nb_chars=200)
    team_lead = factory.SubFactory(MemberFactory)
    is_active = True
```

### API Testing

#### Comprehensive API Tests
```python
# tests/test_api.py
from rest_framework.test import APITestCase
from rest_framework import status
from django.urls import reverse
from django.contrib.auth import get_user_model
from .factories import MemberFactory, MemberTypeFactory

class MemberAPITest(APITestCase):
    """Test Member API endpoints"""
    
    def setUp(self):
        """Set up test data and authentication"""
        self.admin_type = MemberTypeFactory(
            permissions={'permissions': ['admin.access', 'member.manage']}
        )
        self.regular_type = MemberTypeFactory(
            permissions={'permissions': ['member.view']}
        )
        
        self.admin_user = MemberFactory(member_type=self.admin_type)
        self.regular_user = MemberFactory(member_type=self.regular_type)
        
        # Get JWT token for authentication
        self.admin_token = self.get_jwt_token(self.admin_user)
        self.regular_token = self.get_jwt_token(self.regular_user)
    
    def get_jwt_token(self, user):
        """Get JWT token for user"""
        from rest_framework_simplejwt.tokens import RefreshToken
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def test_member_list_authenticated(self):
        """Test member list endpoint with authentication"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        url = reverse('api:member-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('members', response.data)
        self.assertIsInstance(response.data['members'], list)
    
    def test_member_list_unauthenticated(self):
        """Test member list endpoint without authentication"""
        url = reverse('api:member-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_member_detail_permissions(self):
        """Test member detail with different permission levels"""
        member = MemberFactory()
        url = reverse('api:member-detail', kwargs={'pk': member.pk})
        
        # Admin can access
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Regular user cannot access others
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.regular_token}')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_member_creation(self):
        """Test member creation via API"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        url = reverse('api:member-list')
        
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'first_name': 'New',
            'last_name': 'User',
            'member_type': self.regular_type.id
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['username'], 'newuser')
    
    def test_member_update(self):
        """Test member update via API"""
        member = MemberFactory()
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        url = reverse('api:member-detail', kwargs={'pk': member.pk})
        
        data = {
            'first_name': 'Updated',
            'last_name': 'Name'
        }
        
        response = self.client.patch(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['first_name'], 'Updated')
    
    def test_api_error_handling(self):
        """Test API error responses"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # Test 404
        url = reverse('api:member-detail', kwargs={'pk': 99999})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
        # Test validation error
        url = reverse('api:member-list')
        data = {'username': '', 'email': 'invalid-email'}
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('username', response.data)
```

### Performance Testing

#### Load Testing
```python
# tests/test_performance.py
from django.test import TransactionTestCase
from django.test.utils import override_settings
import time
from .factories import MemberFactory

@override_settings(DEBUG=False)
class PerformanceTest(TransactionTestCase):
    """Performance tests for critical paths"""
    
    def setUp(self):
        """Create test data"""
        self.members = MemberFactory.create_batch(1000)
    
    def test_member_list_performance(self):
        """Test member list query performance"""
        from django.db import connection
        from django.test import Client
        
        client = Client()
        
        # Reset queries
        connection.queries_log.clear()
        
        start_time = time.time()
        response = client.get('/api/v1/members/')
        end_time = time.time()
        
        # Assertions
        self.assertEqual(response.status_code, 200)
        self.assertLess(end_time - start_time, 1.0)  # Should complete in < 1 second
        self.assertLess(len(connection.queries), 10)  # Should use < 10 queries
    
    def test_hierarchy_query_performance(self):
        """Test organization chart performance"""
        # Create hierarchy
        for i in range(100):
            if i > 0:
                manager = self.members[i // 5]  # Create 5:1 hierarchy
                member = self.members[i]
                MemberHierarchy.objects.create(
                    member=member,
                    manager=manager,
                    relationship_type='direct_manager'
                )
        
        start_time = time.time()
        from apps.member.views import OrganizationChartView
        view = OrganizationChartView()
        data = view.get_organization_data()
        end_time = time.time()
        
        self.assertLess(end_time - start_time, 2.0)  # Should complete in < 2 seconds
        self.assertIsInstance(data, dict)
```

---

## Performance Optimization

### Database Optimization

#### Query Optimization Strategies
```python
# Efficient pagination
from django.core.paginator import Paginator
from django.db.models import Prefetch

class OptimizedMemberListView(ListView):
    """Optimized member list with efficient pagination"""
    
    def get_queryset(self):
        # Use select_related for foreign keys
        queryset = Member.objects.select_related(
            'member_type', 'region'
        )
        
        # Use prefetch_related for many-to-many/reverse foreign keys
        queryset = queryset.prefetch_related(
            Prefetch(
                'teams',
                queryset=Team.objects.select_related('team_lead')
            )
        )
        
        # Use only() to limit fields
        if self.request.GET.get('minimal'):
            queryset = queryset.only(
                'id', 'username', 'first_name', 'last_name',
                'member_type__name'
            )
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Efficient counting for pagination
        context['total_count'] = self.get_queryset().count()
        
        return context
```

#### Caching Strategies
```python
from django.core.cache import cache
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator

class CachedMemberStatsView(View):
    """Member statistics with caching"""
    
    @method_decorator(cache_page(60 * 15))  # Cache for 15 minutes
    def get(self, request):
        stats = self.get_member_statistics()
        return JsonResponse(stats)
    
    def get_member_statistics(self):
        """Get member statistics with caching"""
        cache_key = 'member_statistics'
        stats = cache.get(cache_key)
        
        if stats is None:
            stats = {
                'total_members': Member.objects.count(),
                'active_members': Member.objects.filter(is_active=True).count(),
                'by_type': self.get_members_by_type(),
                'by_region': self.get_members_by_region(),
            }
            cache.set(cache_key, stats, 60 * 15)  # Cache for 15 minutes
        
        return stats
    
    def get_members_by_type(self):
        """Cached member type statistics"""
        cache_key = 'member_stats_by_type'
        stats = cache.get(cache_key)
        
        if stats is None:
            stats = list(
                Member.objects.values('member_type__name')
                .annotate(count=Count('id'))
                .order_by('-count')
            )
            cache.set(cache_key, stats, 60 * 30)  # Cache for 30 minutes
        
        return stats

# Cache invalidation
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

@receiver([post_save, post_delete], sender=Member)
def invalidate_member_cache(sender, **kwargs):
    """Invalidate member-related caches on model changes"""
    cache_keys = [
        'member_statistics',
        'member_stats_by_type',
        'member_stats_by_region',
    ]
    cache.delete_many(cache_keys)
```

### Frontend Optimization

#### Template Optimization
```html
<!-- member/member_list.html -->
{% load cache %}
{% load member_tags %}

<!-- Cache expensive computations -->
{% cache 900 member_list_header %}
<div class="stats-header">
    <h2>Members ({{ total_count }})</h2>
    <div class="quick-stats">
        {% get_member_statistics as stats %}
        <span>Active: {{ stats.active_count }}</span>
        <span>Types: {{ stats.type_count }}</span>
    </div>
</div>
{% endcache %}

<!-- Efficient template loops -->
<div class="member-list">
    {% for member in members %}
        <div class="member-card" data-member-id="{{ member.id }}">
            <h3>{{ member.get_full_name }}</h3>
            <p>{{ member.member_type.name }}</p>
            
            <!-- Lazy load team information -->
            <div class="teams" data-url="{% url 'api:member-teams' member.id %}">
                Loading teams...
            </div>
        </div>
    {% empty %}
        <p>No members found.</p>
    {% endfor %}
</div>

<!-- Efficient pagination -->
{% if is_paginated %}
<nav class="pagination">
    {% if page_obj.has_previous %}
        <a href="?page=1">First</a>
        <a href="?page={{ page_obj.previous_page_number }}">Previous</a>
    {% endif %}
    
    <span>Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
    
    {% if page_obj.has_next %}
        <a href="?page={{ page_obj.next_page_number }}">Next</a>
        <a href="?page={{ page_obj.paginator.num_pages }}">Last</a>
    {% endif %}
</nav>
{% endif %}
```

#### JavaScript Optimization
```javascript
// Static files optimization
class MemberManager {
    constructor() {
        this.cache = new Map();
        this.debounceTimer = null;
    }
    
    // Debounced search
    search(query) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
            this.performSearch(query);
        }, 300);
    }
    
    // Cached API calls
    async getMemberDetails(memberId) {
        const cacheKey = `member_${memberId}`;
        
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }
        
        const response = await fetch(`/api/v1/members/${memberId}/`);
        const data = await response.json();
        
        this.cache.set(cacheKey, data);
        return data;
    }
    
    // Lazy loading
    initializeLazyLoading() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadMemberTeams(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        });
        
        document.querySelectorAll('.teams[data-url]').forEach(el => {
            observer.observe(el);
        });
    }
    
    async loadMemberTeams(element) {
        const url = element.dataset.url;
        try {
            const response = await fetch(url);
            const data = await response.json();
            element.innerHTML = this.renderTeams(data);
        } catch (error) {
            element.innerHTML = '<span class="error">Failed to load teams</span>';
        }
    }
}
```

---

## Code Quality

### Code Standards

#### PEP 8 Compliance
```python
# Good: Proper spacing and naming
class MemberAnalytics:
    """Analytics for member data with proper formatting"""
    
    MAX_QUERY_SIZE = 1000
    DEFAULT_TIME_RANGE = 30  # days
    
    def __init__(self, time_range=None):
        self.time_range = time_range or self.DEFAULT_TIME_RANGE
        self.cache_timeout = 3600  # 1 hour
    
    def get_member_growth_rate(self, start_date, end_date):
        """
        Calculate member growth rate for given period.
        
        Args:
            start_date (datetime): Start of period
            end_date (datetime): End of period
            
        Returns:
            float: Growth rate as percentage
        """
        initial_count = self._get_member_count_at_date(start_date)
        final_count = self._get_member_count_at_date(end_date)
        
        if initial_count == 0:
            return 0.0
        
        growth_rate = ((final_count - initial_count) / initial_count) * 100
        return round(growth_rate, 2)
    
    def _get_member_count_at_date(self, target_date):
        """Private helper method for member count"""
        return Member.objects.filter(
            date_joined__lte=target_date,
            is_active=True
        ).count()
```

#### Type Hints
```python
from typing import Dict, List, Optional, Union
from django.db.models import QuerySet
from django.http import HttpRequest, HttpResponse

class MemberService:
    """Service class for member operations with type hints"""
    
    def get_member_hierarchy(
        self, 
        member_id: int, 
        depth: int = 3
    ) -> Dict[str, Union[Dict, List]]:
        """
        Get member hierarchy with specified depth.
        
        Args:
            member_id: ID of the member
            depth: Maximum depth to traverse
            
        Returns:
            Dictionary containing hierarchy data
        """
        try:
            member = Member.objects.get(id=member_id)
        except Member.DoesNotExist:
            return {'error': 'Member not found'}
        
        return {
            'member': self._serialize_member(member),
            'managers': self._get_managers(member),
            'subordinates': self._get_subordinates(member, depth)
        }
    
    def _serialize_member(self, member: Member) -> Dict[str, Union[str, int]]:
        """Serialize member data"""
        return {
            'id': member.id,
            'username': member.username,
            'full_name': member.get_full_name(),
            'email': member.email
        }
    
    def bulk_update_members(
        self, 
        member_data: List[Dict[str, Union[str, int]]]
    ) -> Dict[str, int]:
        """
        Bulk update multiple members.
        
        Args:
            member_data: List of member data dictionaries
            
        Returns:
            Dictionary with update statistics
        """
        updated_count = 0
        error_count = 0
        
        for data in member_data:
            try:
                member_id = data.get('id')
                if member_id:
                    Member.objects.filter(id=member_id).update(**data)
                    updated_count += 1
            except Exception:
                error_count += 1
        
        return {
            'updated': updated_count,
            'errors': error_count,
            'total': len(member_data)
        }
```

### Documentation Standards

#### Docstring Format
```python
class MemberHierarchyManager:
    """
    Manager for handling complex member hierarchy operations.
    
    This manager provides methods for creating, updating, and querying
    hierarchical relationships between members in the organization.
    
    Attributes:
        max_hierarchy_depth (int): Maximum allowed hierarchy depth
        circular_reference_check (bool): Whether to check for circular references
    """
    
    def __init__(self, max_depth=10, check_circular=True):
        """
        Initialize the hierarchy manager.
        
        Args:
            max_depth (int, optional): Maximum hierarchy depth. Defaults to 10.
            check_circular (bool, optional): Check for circular refs. Defaults to True.
        """
        self.max_hierarchy_depth = max_depth
        self.circular_reference_check = check_circular
    
    def create_hierarchy_relationship(
        self, 
        member: Member, 
        manager: Member, 
        relationship_type: str = 'direct_manager'
    ) -> Optional[MemberHierarchy]:
        """
        Create a new hierarchy relationship between two members.
        
        Args:
            member (Member): The subordinate member
            manager (Member): The manager member
            relationship_type (str): Type of relationship (default: 'direct_manager')
        
        Returns:
            Optional[MemberHierarchy]: Created relationship or None if failed
        
        Raises:
            ValueError: If relationship would create circular reference
            ValidationError: If relationship violates business rules
        
        Example:
            >>> manager = Member.objects.get(username='john_manager')
            >>> subordinate = Member.objects.get(username='jane_employee')
            >>> hierarchy_mgr = MemberHierarchyManager()
            >>> relationship = hierarchy_mgr.create_hierarchy_relationship(
            ...     subordinate, manager, 'direct_manager'
            ... )
            >>> print(relationship.relationship_type)
            'direct_manager'
        """
        # Validate inputs
        if not isinstance(member, Member) or not isinstance(manager, Member):
            raise ValueError("Both member and manager must be Member instances")
        
        if member == manager:
            raise ValueError("Member cannot be their own manager")
        
        # Check for circular references
        if self.circular_reference_check:
            if self._would_create_circular_reference(member, manager):
                raise ValueError("Relationship would create circular reference")
        
        # Create relationship
        try:
            relationship = MemberHierarchy.objects.create(
                member=member,
                manager=manager,
                relationship_type=relationship_type,
                is_primary=not member.managers.exists()
            )
            return relationship
        except IntegrityError as e:
            raise ValidationError(f"Failed to create relationship: {e}")
    
    def _would_create_circular_reference(
        self, 
        member: Member, 
        potential_manager: Member
    ) -> bool:
        """
        Check if adding a manager would create a circular reference.
        
        This method traverses up the hierarchy from the potential manager
        to see if the member is already a manager higher up in the chain.
        
        Args:
            member (Member): The member who would get a new manager
            potential_manager (Member): The potential new manager
        
        Returns:
            bool: True if circular reference would be created
        """
        visited = set()
        current = potential_manager
        
        while current and current.id not in visited:
            visited.add(current.id)
            
            if current == member:
                return True
            
            # Get primary manager
            primary_manager = current.managers.filter(
                memberhierarchy__is_primary=True
            ).first()
            
            current = primary_manager
        
        return False
```

---

## Version Control

### Git Workflow

#### Branch Strategy
```bash
# Main branches
main                    # Production-ready code
develop                 # Integration branch

# Feature branches
feature/member-search   # New features
feature/api-v2         # API improvements

# Release branches  
release/v1.2.0         # Prepare for release

# Hotfix branches
hotfix/security-fix    # Critical fixes
```

#### Commit Message Format
```
type(scope): brief description

More detailed explanation if needed.

- List of changes
- Can be multiple lines
- Use bullet points

Fixes #123
Closes #456
```

Examples:
```bash
feat(member): add advanced search functionality

- Implement full-text search across member fields
- Add filters for member type and region
- Include pagination for large result sets

Closes #234

fix(api): resolve JWT token expiration handling

The token refresh mechanism was not properly handling
expired tokens, causing 401 errors for long-running sessions.

- Update token refresh logic in auth middleware
- Add proper error handling for expired tokens
- Include tests for token expiration scenarios

Fixes #456

docs(readme): update installation instructions

- Add Docker requirements
- Update environment variable examples
- Include troubleshooting section

chore(deps): update Django to 5.2.4

Security update for Django framework.

Fixes #789
```

#### Pre-commit Hooks
```bash
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
  
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.12
  
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503]
  
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black]
```

---

## Documentation

### Code Documentation

#### Module Documentation
```python
"""
Member Management Module

This module provides comprehensive member management functionality including:
- Member CRUD operations
- Hierarchical organization management
- Team membership handling
- Permission-based access control

The module is structured around the following main components:
- Models: Core data structures (Member, MemberType, MemberHierarchy)
- Views: Web interface and API endpoints
- Services: Business logic and complex operations
- Utils: Helper functions and utilities

Example:
    Basic member operations:
    
    >>> from apps.member.services import MemberService
    >>> service = MemberService()
    >>> member = service.create_member(
    ...     username='john_doe',
    ...     email='<EMAIL>',
    ...     member_type='regular'
    ... )
    >>> print(member.get_full_name())
    'John Doe'

Authors: <AUTHORS>

Version:
    1.0.0

License:
    Proprietary - Integrity XD
"""
```

#### API Documentation
```python
class MemberViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Member resources.
    
    Provides CRUD operations for Member objects with proper authentication
    and permission checking. Supports filtering, searching, and pagination.
    
    Authentication:
        Requires JWT token authentication for all operations.
    
    Permissions:
        - List/Retrieve: Any authenticated user
        - Create/Update/Delete: Admin users only
    
    Filtering:
        - member_type: Filter by member type ID
        - region: Filter by region ID  
        - status: Filter by member status
        - is_active: Filter by active status (true/false)
    
    Search:
        Searches across username, email, first_name, and last_name fields.
    
    Ordering:
        Default: last_name, first_name
        Available: username, email, date_joined, last_login
    
    Example Usage:
        List members:
        GET /api/v1/members/
        
        Create member:
        POST /api/v1/members/
        {
            "username": "john_doe",
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe",
            "member_type": 2
        }
        
        Search members:
        GET /api/v1/members/?search=john&member_type=2
    """
    
    queryset = Member.objects.all()
    serializer_class = MemberSerializer
    permission_classes = [IsAuthenticated, MemberPermissions]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['member_type', 'region', 'status', 'is_active']
    search_fields = ['username', 'email', 'first_name', 'last_name']
    ordering_fields = ['username', 'email', 'date_joined', 'last_login']
    ordering = ['last_name', 'first_name']
    pagination_class = StandardResultsSetPagination
```

---

## Troubleshooting

### Common Issues and Solutions

#### Database Issues
```python
# Issue: Database connection errors
# Solution: Check connection settings and pool configuration

# settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.getenv('DB_NAME'),
        'USER': os.getenv('DB_USER'),
        'PASSWORD': os.getenv('DB_PASSWORD'),
        'HOST': os.getenv('DB_HOST', 'localhost'),
        'PORT': os.getenv('DB_PORT', '3306'),
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
        'CONN_MAX_AGE': 3600,  # Connection pooling
        'CONN_HEALTH_CHECKS': True,  # Health checks
    }
}

# Debug database queries
if DEBUG:
    LOGGING['loggers']['django.db.backends'] = {
        'level': 'DEBUG',
        'handlers': ['console'],
    }
```

#### Performance Issues
```python
# Issue: Slow queries
# Solution: Use Django Debug Toolbar and query analysis

# Install django-debug-toolbar for development
INSTALLED_APPS += ['debug_toolbar']
MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware']

# Query optimization helper
from django.db import connection
from django.conf import settings

def analyze_queries(func):
    """Decorator to analyze database queries"""
    def wrapper(*args, **kwargs):
        if settings.DEBUG:
            initial_queries = len(connection.queries)
            start_time = time.time()
            
            result = func(*args, **kwargs)
            
            end_time = time.time()
            final_queries = len(connection.queries)
            
            print(f"Function: {func.__name__}")
            print(f"Queries: {final_queries - initial_queries}")
            print(f"Time: {end_time - start_time:.4f}s")
            
            return result
        return func(*args, **kwargs)
    return wrapper

# Usage
@analyze_queries
def get_member_hierarchy(member_id):
    return Member.objects.get(id=member_id).get_hierarchy()
```

#### Memory Issues
```python
# Issue: High memory usage with large querysets
# Solution: Use iterator() and pagination

def process_all_members():
    """Process all members efficiently"""
    # Instead of: members = Member.objects.all()
    # Use iterator to avoid loading all objects into memory
    
    for member in Member.objects.iterator(chunk_size=1000):
        process_member(member)

def bulk_update_members(updates):
    """Efficient bulk updates"""
    # Use bulk_update for better performance
    members_to_update = []
    
    for update_data in updates:
        member = Member(id=update_data['id'])
        member.status = update_data['status']
        members_to_update.append(member)
    
    Member.objects.bulk_update(
        members_to_update, 
        ['status'], 
        batch_size=1000
    )
```

#### Authentication Issues
```python
# Issue: JWT token problems
# Solution: Proper token handling and debugging

from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.exceptions import TokenError

class AuthenticationService:
    """Service for handling authentication issues"""
    
    @staticmethod
    def debug_token(token_string):
        """Debug JWT token issues"""
        try:
            # Decode token without verification for debugging
            import jwt
            decoded = jwt.decode(
                token_string, 
                options={"verify_signature": False}
            )
            print(f"Token payload: {decoded}")
            
            # Check expiration
            import datetime
            exp = datetime.datetime.fromtimestamp(decoded['exp'])
            now = datetime.datetime.now()
            print(f"Token expires: {exp}")
            print(f"Current time: {now}")
            print(f"Expired: {exp < now}")
            
        except Exception as e:
            print(f"Token decode error: {e}")
    
    @staticmethod
    def refresh_token_safely(refresh_token):
        """Safely refresh JWT token"""
        try:
            token = RefreshToken(refresh_token)
            return {
                'access': str(token.access_token),
                'refresh': str(token)
            }
        except TokenError as e:
            return {'error': f'Token refresh failed: {e}'}
```

### Debug Utilities

#### Development Helpers
```python
# apps/core/debug.py
from django.conf import settings
from django.db import connection
import functools
import time

def debug_only(func):
    """Only run in debug mode"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        if settings.DEBUG:
            return func(*args, **kwargs)
        return None
    return wrapper

@debug_only
def print_queries():
    """Print all database queries"""
    for query in connection.queries:
        print(f"Query: {query['sql']}")
        print(f"Time: {query['time']}")
        print("-" * 50)

class QueryDebugger:
    """Context manager for debugging queries"""
    
    def __enter__(self):
        self.initial_queries = len(connection.queries)
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        self.final_queries = len(connection.queries)
        
        print(f"Queries executed: {self.final_queries - self.initial_queries}")
        print(f"Execution time: {self.end_time - self.start_time:.4f}s")

# Usage
with QueryDebugger():
    members = Member.objects.prefetch_related('teams').all()
    for member in members:
        print(member.get_full_name())
```

This comprehensive guide provides the foundation for maintaining high code quality, performance, and reliability in the XD Incentives project. Regular review and updates of these practices ensure the codebase remains maintainable and scalable.