# SCRUM 101: Your Agile Development Guide at Integrity

Welcome to the team! This guide will teach you everything you need to know about SCRUM - the framework we use to build amazing software for our clients. Don't worry if this is all new to you. We'll start simple and build up your knowledge step by step.

## Table of Contents
1. [What is SCRUM and Why We Use It](#what-is-scrum)
2. [The Three Key Players](#core-roles)
3. [The SCRUM Meetings You'll Attend](#scrum-events)
4. [The Three Things We Track](#scrum-artifacts)
5. [Using Monday.com for SCRUM](#monday-integration)
6. [Your Daily Developer Workflow](#daily-workflow)
7. [Mistakes to Avoid (We've All Made Them!)](#common-pitfalls)
8. [The Integrity Way](#integrity-practices)
9. [Writing Like a Professional](#communication-standards)
10. [Quick Reference Guide](#quick-reference)

---

## 1. What is SCRUM and Why We Use It {#what-is-scrum}

### The Simple Version
SCRUM is like building a house one room at a time instead of trying to build the entire house at once. Every two weeks (called a "sprint"), we complete a small, working piece of software that the client can see and use.

### Why This Works for Integrity
- **Clients see progress regularly** - No more waiting months to see if we're on track
- **We can adapt quickly** - Client needs change? No problem, we adjust in the next sprint
- **Everyone knows what's happening** - Clear roles, clear meetings, clear goals
- **Quality stays high** - We deliver tested, working code every two weeks

### Think of it Like This
Traditional development is like ordering a custom car and waiting 6 months to see if you like it. SCRUM is like getting to test drive your car every two weeks as it's being built, making adjustments along the way.

---

## 2. The Three Key Players (Core Roles) {#core-roles}

### Product Owner (The Client's Voice - Marshall)
- **Who:** Usually someone from the client's team or our project lead
- **What they do:** Decides WHAT we build and in what order
- **How you work with them:** They'll clarify requirements and accept your completed work

### Scrum Master (The Process Guardian - Drew & Alex)
- **Who:** Could be a project lead or senior developer
- **What they do:** Makes sure the SCRUM process runs smoothly
- **How you work with them:** They'll help remove blockers and run our meetings

### Development Team (That's You!)
- **Who:** Developers, designers, and anyone building the product
- **What you do:** Turn user stories into working software
- **Your superpower:** Self-organization - you decide HOW to build things

**Remember:** At Integrity, we're all adults who manage ourselves. These roles help organize work, not create hierarchy.

---

## 3. The SCRUM Meetings You'll Attend {#scrum-events}

### Weekly Team Meeting (1 hour, every Wednesday)
**Purpose:** Quick sync with the team to catch up on progress, address any blockers, and plan for the week
**Your part:** Answer three questions:
1. What did I complete last week?
2. What will I work on this week?
3. Am I blocked by anything?

**Pro tip:** Prepare your answers before the meeting. Keep it brief - details can be discussed after.

### Sprint Planning (2-4 hours, start of each sprint)
**Purpose:** Decide what we'll build in the next two weeks
**Your part:**
- Estimate how long tasks will take
- Commit to what you can realistically complete
- Ask questions about unclear requirements

**In Monday.com:** You'll see new items added to the Sprint board with estimates.

### Sprint Review (1 hour, end of each sprint)
**Purpose:** Show the client (and/or entire project team) what we built
**Your part:**
- Demo your completed features
- Be ready to explain technical decisions
- Take notes on client (and/or entire project team) feedback

### Sprint Retrospective (30 Minutes - 1 hour, after Sprint Review)
**Purpose:** Improve how we work together
**Your part:** Share honestly about:
- What went well?
- What could be better?
- What will we try differently?

**Culture note:** This aligns with our value of continuous learning and growing those around you.

---

## 4. The Three Things We Track (SCRUM Artifacts) {#scrum-artifacts}

### Product Backlog (The Master List)
- **What:** All features, bugs, and improvements we might build
- **Where:** Monday.com main board
- **Your interaction:** Pull items from here during sprint planning

### Sprint Backlog (Your Two-Week Focus)
- **What:** The specific items you're building this sprint
- **Where:** Monday.com sprint board
- **Your responsibility:** Update status daily

### Increment (What You Deliver)
- **What:** Working, tested, deployable code
- **Standard:** If it's not documented and tested, it's not done
- **Remember:** "If it wasn't documented, it never happened"

---

## 5. Using Monday.com for SCRUM {#monday-integration}

### Your Daily Monday.com Workflow

#### Morning Check-in:
1. Open your Sprint board
2. Review your assigned items
3. Update yesterday's task status based on your progress. (See Status Column Values below)
4. Move today's work to "In Progress"

#### Creating/Updating Items:

```
Status Column Values:
- BLOCKED: Currently blocked by a blocker
- To Do: Not started yet
- In Progress: Currently working on it
- Code Review: Ready for peer review
- Testing: Being tested
- Done: Complete and accepted

Always Include:
- Clear title: "Fix login validation for email field"
- estimates: Your time estimate
- Description: Technical approach and acceptance criteria
- Updates: Daily progress notes
```

### Pro Tips for Monday.com:
- Subscribe to items you're working on for notifications
- Use @mentions to get someone's attention
- Link related items using the "Link" column
- Add time tracking to show actual vs. estimated

---

## 6. Your Daily Developer Workflow {#daily-workflow}

### Start of Sprint (Day 1):
1. Attend Sprint Planning
2. Review your assigned stories in Monday.com
3. Break down large stories into tasks
4. Set up your development environment
5. Kick some butt

### Working on a Story:
1. **Start:** Move item to "In Progress" in Monday.com
2. **Code:** Create feature branch from develop (or test first if you prefer TDD)
3. **Test:** Write/run unit tests
4. **Document:** Update code comments, project documentation, and README (if necessary)
5. **Review:** Create pull request, tag reviewer
6. **Complete:** Merge to develop, move to "Done" in Monday.com

### End of Sprint (Day 10):
1. Ensure all your items are tested and documented
2. Prepare demo for Sprint Review
3. Think about retrospective feedback
4. Help teammates complete their items

---

## 7. Mistakes to Avoid (We've All Made Them!) {#common-pitfalls}

### The "Silent Struggler"
**Mistake:** Working on a blocker for days without asking for help
**Fix:** Raise blockers immediately in check ins or Slack. We're a team!

### The "It Works on My Machine"
**Mistake:** Not testing in the staging environment (once established)
**Fix:** Always deploy and test in staging before marking as done

### The "Sprint Spillover"
**Mistake:** Taking on too much work and not finishing
**Fix:** Under-promise, over-deliver. Better to add work mid-sprint than carry over

### The "Documentation/Testing Later"
**Mistake:** Planning to document or test "after everything works"
**Fix:** Test and document as you go. Your future self (and team) will thank you

### The "Lone Wolf"
**Mistake:** Making major technical decisions alone
**Fix:** Discuss architecture changes with the team first

### The "Meeting Skipper"
**Mistake:** Missing check ins because "I have nothing new"
**Fix:** Attendance is about team sync, not individual updates

### The "Estimation Optimist"
**Mistake:** "This'll only take an hour" (takes two days)
**Fix:** Double (or triple) your initial estimate, then add buffer time

### The "Context Switcher"
**Mistake:** Working on multiple stories simultaneously
**Fix:** Focus on one story at a time to completion

---

## 8. The Integrity Way {#integrity-practices}

### How SCRUM Fits Our Culture

#### "Team of Adults"
- You own your sprint commitments
- You manage your time and priorities
- You communicate professionally and proactively

#### "Deliver Excellence"
- Every sprint increment is production-ready
- Code is tested, documented, and reviewed
- We deliver on time, to spec, and in budget

#### "Own Bigger Projects"
- Start with small stories, prove reliability
- Graduate to epic ownership
- Lead sprint planning for your features

#### "Grow Those Around You"
- Share knowledge in retrospectives
- Provide helpful code reviews
- Document clearly for the next developer

### Our Flexible SCRUM Approach
While we follow SCRUM principles, we adapt based on:
- **Client/Team availability:** Sometimes reviews happen async
- **Team size:** Smaller teams might combine roles
- **Project phase:** Early sprints might focus on research
- **Technical needs:** Some sprints prioritize debt cleanup

---

## 9. Writing Like a Professional {#communication-standards}

### Every Message Matters
Remember: "Spelling, grammar and punctuation are a big deal." Your written communication represents Integrity to our clients.

### Git Commit Messages:

```
✅ Good: "Fix user authentication to handle expired JWT tokens"
❌ Bad: "fixed auth bug"
```

### Monday.com Updates:

```
✅ Good: "Completed API endpoint for user profile updates. All tests passing. Ready for review."
❌ Bad: "done with api stuff"
```

### Pull Request Descriptions:

```markdown
# Fill out the template that is auto generated for you!
```

### Code Comments:

```python
# Good: Explains why, not what
def calculate_discount(user, cart_total):
    """
    Apply tiered discount based on user loyalty status.
    Business rule: Premium users get 20% off orders over $100.
    """
    
# Bad: Restates the obvious
def calculate_discount(user, cart_total):
    """Calculate the discount"""  # This adds no value
```

---

## 10. Quick Reference Guide {#quick-reference}

### Daily Checklist
- [ ] Check Monday.com for overnight updates
- [ ] Update story status in Monday.com
- [ ] Push code with clear commit messages
- [ ] Document any decisions or changes
- [ ] Review pull requests from teammates

### Sprint Rhythm

```
Week 1, Monday: Sprint Planning (2-4 hours)
Week 1, Tuesday-Friday: Development
Week 1, Wednesday: Weekly Team Check-in (1 hour)
Week 2, Monday-Thursday: Development & Testing
Week 2, Wednesday: Weekly Team Check-in (1 hour)
Week 2, Friday AM: Sprint Review (1 hour)
Week 2, Friday PM: Sprint Retrospective (0.5 - 1 hour)
```

### Story Point Guide
- **1 point:** 2-4 hours (simple bug fix)
- **2 points:** 1 day (small feature)
- **3 points:** 1.5 days (medium feature)
- **5 points:** 2-3 days (complex feature)
- **8 points:** 4-5 days (should probably be split)

### Definition of Done
- [ ] Code complete and working
- [ ] Unit tests written and passing
- [ ] Code reviewed by peer
- [ ] Deployed to staging
- [ ] Documentation updated
- [ ] Monday.com item updated
- [ ] Product Owner accepted

---

## Your First Sprint Survival Guide

### Week 1: Learning
- Shadow sprint planning
- Take on 1-2 small stories (2-3 points total)
- Ask lots of questions
- Focus on understanding the codebase

### Week 2: Contributing
- Lead your story demos
- Provide input in retrospective
- Start reviewing others' code
- Document what you learned

### By Sprint 3: Flying Solo
- Estimate your own stories
- Take on 6-8 points
- Help onboard the next new person
- Own a small feature end-to-end

---

## Remember

You're joining a team that values:
- **Ownership:** Your sprint commitments are your promises
- **Excellence:** Every line of code represents Integrity
- **Growth:** Each sprint is a chance to level up
- **Communication:** Clear, professional, documented

Welcome to SCRUM at Integrity. We're excited to see what you'll build!

---

*Questions? Your Scrum Master(s) and team are here to help. Don't hesitate to ask!*