# Sprint Planning Meeting Agenda

## 1) Sprint Goal Setting

_Define what we'll accomplish in this first sprint, focusing on foundational elements and early wins._

### Activities
- Review project vision and immediate client priorities
- Define Sprint 1 success criteria
- Establish measurable sprint goal
- Confirm sprint dates and key milestones

> Sprint Goal Statement: "By end of Sprint 1, we will have [specific deliverable] that enables [business value]"

---

## 2) Product Backlog Review

_Understand the prioritized work items available for this sprint._

### Activities
- Review acceptance criteria for each item
- Q&A on business requirements and user needs
- Identify any dependencies or prerequisites
- Flag items needing compliance considerations (SOC 2, PCI, GDPR, A11y)

### Key questions to address
- Who are the end users?
- What's the business impact?
- Are there any external dependencies?
- What does "done" look like for each item?

---

## 3) Technical Discussion & Approach

_Align on technical approach and identify potential challenges._

### Activities
- Discuss architecture decisions needed
- Review development environment setup status
- Identify technical spikes or research needed

---

## 4) Story Estimation

_Size each potential sprint item using story points._

### Process
- Review story point scale (1, 2, 3, 5, 8)

For each backlog item:
- <PERSON><PERSON><PERSON> presents understanding
- Team discusses complexity
- Planning poker for consensus
- Document assumptions in Monday.com

### Estimation guidelines (from guide)
- 1 point: 2-4 hours (simple bug fix, configuration)
- 2 points: 1 day (small feature, basic CRUD)
- 3 points: 1.5 days (medium feature with testing)
- 5 points: 2-3 days (complex feature, integration work)
- 8 points: 4-5 days (consider breaking down)

---

## 5) Sprint Backlog Creation

_Commit to specific items for Sprint 1._

### Activities
- Calculate team capacity (accounting for meetings, first sprint learning curve)
- Select items based on:
  - Priority
  - Dependencies
  - Team capacity
  - Balance of work types
- Assign initial owners to items
- Create tasks breakdown for stories > 3 points

---

## 6) Task Breakdown & Assignment

_Break down stories into actionable tasks and establish ownership._

### For each story
- Identify specific technical tasks
- Add tasks to Monday.com under parent story
- Assign primary and backup owners
- Note any pairing opportunities

#### Task examples
- Set up feature branch
- Implement API endpoint
- Write unit tests
- Update documentation
- Code review
- Deploy to staging

---

## 7) Definition of Done Review

_Ensure everyone understands completion criteria._

### Sprint 1 Definition of Done
- Code complete and working
- Unit tests written and passing
- Code reviewed by peer
- Deployed to staging (once available)
- Documentation updated
- Monday.com item updated with notes
- Product Owner accepted

---

## 8) Risk & Dependency Check

_Identify and plan for potential blockers._

### Discussion points
- External dependencies (APIs, client decisions, third-party services)
- Knowledge gaps requiring research
- Environment or access issues
- Resource availability concerns

### Action items
- Document risks in Monday.com
- Assign mitigation owners
- Schedule follow-ups as needed

---

## 9) Calendar & Logistics Review

_Confirm Sprint 1 schedule._

- Week 1 Wednesday: Team check-in (1 hour)
- Week 2 Wednesday: Team check-in (1 hour)
- Week 2 Friday AM: Sprint Review (1 hour)
- Week 2 Friday PM: Sprint Retrospective (30–45 min for first sprint)

Determine additional ad-hoc meetings needed among team members.