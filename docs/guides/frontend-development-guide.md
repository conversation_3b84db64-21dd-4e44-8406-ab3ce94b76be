# Frontend Development Guide

This guide provides comprehensive instructions for developing the React/TypeScript frontend of the XD Incentives Platform, including component development with Storybook, testing strategies, and best practices.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Development Environment](#development-environment)
3. [Project Structure](#project-structure)
4. [Component Development](#component-development)
5. [Storybook Workflow](#storybook-workflow)
6. [State Management](#state-management)
7. [API Integration](#api-integration)
8. [Testing Strategy](#testing-strategy)
9. [Styling & Design System](#styling--design-system)
10. [Accessibility](#accessibility)
11. [Performance](#performance)
12. [Best Practices](#best-practices)

## Quick Start

### Prerequisites
- Node.js 18+ (LTS recommended)
- Docker & Docker Compose
- Git

### Setup Commands

```bash
# Clone and navigate to project
git clone https://github.com/integritystl/xd-incentives
cd xd-incentives

# Start development environment
make frontend-dev

# Start Storybook (in separate terminal)
make storybook-dev

# Run tests
make frontend-test
```

### Development URLs
- **Frontend App**: <http://localhost:3000>
- **Storybook**: <http://localhost:6006>
- **Backend API**: <http://localhost:8000/api>

## Development Environment

### Docker Setup
The frontend runs in a containerized environment for consistency:

```bash
# Start all services
docker-compose up

# Frontend development server only
docker-compose up frontend

# Storybook development server
docker-compose up storybook

# Execute commands in frontend container
docker exec xd-frontend npm run [command]
```

### Local Development
For faster iteration, you can also run locally:

```bash
cd frontend
npm install
npm run dev
```

### VS Code Setup
Recommended extensions:
- TypeScript and JavaScript Nightly
- EsLint (for linting/formatting)
- Storybook Official
- Tailwind CSS IntelliSense
- Auto Rename Tag
- ES7+ React/Redux/React-Native snippets

## Project Structure

```
frontend/
├── src/
│   ├── components/           # React components
│   │   ├── ui/              # Base UI components (Shadcn)
│   │   │   ├── Button.tsx
│   │   │   ├── Button.stories.tsx
│   │   │   ├── Button.test.tsx
│   │   │   └── index.ts
│   │   ├── layout/          # Layout components
│   │   ├── forms/           # Form components
│   │   └── data/            # Data display components
│   ├── pages/               # Page components
│   ├── hooks/               # Custom React hooks
│   ├── services/            # API integration
│   ├── stores/              # Zustand stores
│   ├── types/               # TypeScript definitions
│   ├── utils/               # Utility functions
│   └── assets/              # Static assets
├── .storybook/              # Storybook configuration
├── public/                  # Public assets
├── tests/                   # Test utilities and setup
└── dist/                    # Build output
```

## Component Development

### Component Architecture
We follow **Atomic Design** principles:

1. **Atoms** (`src/components/ui/`): Basic building blocks
2. **Molecules** (`src/components/`): Simple UI combinations
3. **Organisms** (`src/components/`): Complex UI sections
4. **Templates** (`src/pages/`): Page layouts
5. **Pages** (`src/pages/`): Specific page instances

### Creating a New Component

#### 1. Generate Component Structure

```bash
# Create component files
mkdir src/components/ui/NewComponent
touch src/components/ui/NewComponent/index.tsx
touch src/components/ui/NewComponent/NewComponent.stories.tsx
touch src/components/ui/NewComponent/NewComponent.test.tsx
```

#### 2. Component Implementation

```tsx
// src/components/ui/NewComponent/index.tsx
import { cn } from '@/utils/cn'
import { VariantProps, cva } from 'class-variance-authority'
import { forwardRef } from 'react'

const newComponentVariants = cva(
  'base-classes', // base classes
  {
    variants: {
      variant: {
        default: 'default-variant-classes',
        secondary: 'secondary-variant-classes',
      },
      size: {
        sm: 'small-size-classes',
        md: 'medium-size-classes',
        lg: 'large-size-classes',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  }
)

export interface NewComponentProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof newComponentVariants> {
  children?: React.ReactNode
  disabled?: boolean
}

const NewComponent = forwardRef<HTMLDivElement, NewComponentProps>(
  ({ className, variant, size, children, disabled, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(newComponentVariants({ variant, size }), className)}
        aria-disabled={disabled}
        {...props}
      >
        {children}
      </div>
    )
  }
)

NewComponent.displayName = 'NewComponent'

export default NewComponent
export { newComponentVariants }
```

#### 3. TypeScript Types

```tsx
// src/types/components.ts
export interface NewComponentData {
  id: string
  name: string
  status: 'active' | 'inactive'
}

export type NewComponentVariant = 'default' | 'secondary'
export type NewComponentSize = 'sm' | 'md' | 'lg'
```

#### 4. Export from Index

```tsx
// src/components/ui/index.ts
export { default as NewComponent } from './NewComponent'
export type { NewComponentProps } from './NewComponent'
```

## Storybook Workflow

### Creating Stories

```tsx
// src/components/ui/NewComponent/NewComponent.stories.tsx
import type { Meta, StoryObj } from '@storybook/react'
import NewComponent from './index'

const meta = {
  title: 'UI/NewComponent',
  component: NewComponent,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A reusable component that...'
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'radio' },
      options: ['default', 'secondary'],
    },
    size: {
      control: { type: 'radio' },
      options: ['sm', 'md', 'lg'],
    },
    disabled: {
      control: 'boolean',
    },
  },
} satisfies Meta<typeof NewComponent>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    children: 'Default Component',
  },
}

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Secondary Component',
  },
}

export const Small: Story = {
  args: {
    size: 'sm',
    children: 'Small Component',
  },
}

export const Disabled: Story = {
  args: {
    disabled: true,
    children: 'Disabled Component',
  },
}

// Interactive story for testing
export const Interactive: Story = {
  args: {
    children: 'Interactive Component',
  },
  play: async ({ canvasElement }) => {
    // Add interactions for testing
  },
}
```

### Storybook Development Workflow

1. **Start Storybook**: `npm run storybook`
2. **Design Component**: Use Storybook's controls to iterate on design
3. **Document Variants**: Create stories for all component states
4. **Test Interactions**: Use Storybook's interaction testing
5. **Accessibility Check**: Use a11y addon to validate accessibility
6. **Visual Testing**: Review component across different viewports

## State Management

### Server State with Tanstack Query

#### Query Configuration

```tsx
// src/services/query-client.ts
import { QueryClient } from '@tanstack/react-query'

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: (failureCount, error) => {
        if (error?.status === 404) return false
        return failureCount < 3
      },
    },
  },
})
```

#### Custom Hooks

```tsx
// src/hooks/useMembers.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '@/services/api-client'
import type { Member, CreateMemberRequest } from '@/types/api'

export const useMembers = (filters?: MemberFilters) => {
  return useQuery({
    queryKey: ['members', filters],
    queryFn: () => apiClient.get<Member[]>('/api/members/', { params: filters }),
    select: (data) => data.results, // Transform data if needed
  })
}

export const useCreateMember = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: CreateMemberRequest) => 
      apiClient.post<Member>('/api/members/', data),
    onSuccess: (newMember) => {
      // Optimistic update
      queryClient.setQueryData<Member[]>(['members'], (old) => 
        old ? [...old, newMember] : [newMember]
      )
      // Invalidate to refetch fresh data
      queryClient.invalidateQueries({ queryKey: ['members'] })
    },
    onError: (error) => {
      // Handle error (show toast, log, etc.)
      console.error('Failed to create member:', error)
    },
  })
}
```

### Client State with Zustand

#### Store Definition

```tsx
// src/stores/authStore.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface User {
  id: string
  email: string
  name: string
  role: string
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  login: (user: User, token: string) => void
  logout: () => void
  updateUser: (updates: Partial<User>) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      
      login: (user, token) => {
        set({
          user,
          token,
          isAuthenticated: true,
        })
      },
      
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        })
      },
      
      updateUser: (updates) => {
        const currentUser = get().user
        if (currentUser) {
          set({
            user: { ...currentUser, ...updates }
          })
        }
      },
    }),
    {
      name: 'auth-storage', // localStorage key
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
```

#### Store Usage

```tsx
// src/components/Header.tsx
import { useAuthStore } from '@/stores/authStore'

export const Header = () => {
  const { user, logout, isAuthenticated } = useAuthStore()
  
  if (!isAuthenticated) return null
  
  return (
    <header>
      <span>Welcome, {user?.name}</span>
      <button onClick={logout}>Logout</button>
    </header>
  )
}
```

## API Integration

### API Client Setup

```tsx
// src/services/api-client.ts
interface ApiResponse<T> {
  results: T
  count: number
  next?: string
  previous?: string
}

class ApiClient {
  private baseURL = '/api'
  
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...this.getAuthHeaders(),
        ...options.headers,
      },
      ...options,
    })
    
    if (!response.ok) {
      throw new ApiError(response.status, await response.text())
    }
    
    return response.json()
  }
  
  private getAuthHeaders() {
    const token = useAuthStore.getState().token
    return token ? { Authorization: `Bearer ${token}` } : {}
  }
  
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint
    return this.request<T>(url)
  }
  
  async post<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }
  
  // ... other methods
}

export const apiClient = new ApiClient()

export class ApiError extends Error {
  constructor(public status: number, message: string) {
    super(message)
    this.name = 'ApiError'
  }
}
```

### Type-Safe API Hooks

```tsx
// src/hooks/api/useClaims.ts
import { useQuery, useMutation } from '@tanstack/react-query'
import { apiClient } from '@/services/api-client'
import type { Claim, CreateClaimRequest, ClaimFilters } from '@/types/api'

export const useClaims = (filters: ClaimFilters = {}) => {
  return useQuery({
    queryKey: ['claims', filters],
    queryFn: () => apiClient.get<ApiResponse<Claim[]>>('/claims/', filters),
    select: (data) => ({
      claims: data.results,
      totalCount: data.count,
      hasNext: !!data.next,
    }),
  })
}

export const useCreateClaim = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: CreateClaimRequest) => 
      apiClient.post<Claim>('/claims/', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['claims'] })
      // Show success message
      toast.success('Claim submitted successfully!')
    },
    onError: (error: ApiError) => {
      // Handle different error types
      if (error.status === 400) {
        toast.error('Please check your claim details')
      } else if (error.status === 403) {
        toast.error('You are not authorized to submit claims')
      } else {
        toast.error('Failed to submit claim. Please try again.')
      }
    },
  })
}
```

## Testing Strategy

### Unit Testing with Vitest

#### Component Testing

```tsx
// src/components/ui/Button/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { vi } from 'vitest'
import Button from './index'

describe('Button Component', () => {
  it('renders with children', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument()
  })
  
  it('handles click events', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
  
  it('applies variant classes correctly', () => {
    render(<Button variant="secondary">Secondary</Button>)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('secondary-variant-classes')
  })
  
  it('handles disabled state', () => {
    render(<Button disabled>Disabled</Button>)
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(button).toHaveAttribute('aria-disabled', 'true')
  })
})
```

#### Hook Testing

```tsx
// src/hooks/useMembers.test.ts
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { vi } from 'vitest'
import { useMembers } from './useMembers'
import { apiClient } from '@/services/api-client'

// Mock API client
vi.mock('@/services/api-client')

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('useMembers Hook', () => {
  it('fetches members successfully', async () => {
    const mockMembers = [
      { id: '1', name: 'John Doe', email: '<EMAIL>' }
    ]
    
    vi.mocked(apiClient.get).mockResolvedValue({
      results: mockMembers,
      count: 1
    })
    
    const { result } = renderHook(() => useMembers(), {
      wrapper: createWrapper()
    })
    
    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true)
    })
    
    expect(result.current.data).toEqual(mockMembers)
  })
})
```

#### Integration Testing

```tsx
// src/components/MemberList/MemberList.test.tsx
import { render, screen, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { vi } from 'vitest'
import MemberList from './index'
import { apiClient } from '@/services/api-client'

vi.mock('@/services/api-client')

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  })
  
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  )
}

describe('MemberList Integration', () => {
  it('displays members after loading', async () => {
    vi.mocked(apiClient.get).mockResolvedValue({
      results: [
        { id: '1', name: 'John Doe', email: '<EMAIL>' },
        { id: '2', name: 'Jane Smith', email: '<EMAIL>' }
      ]
    })
    
    renderWithProviders(<MemberList />)
    
    // Initially shows loading
    expect(screen.getByText('Loading...')).toBeInTheDocument()
    
    // Wait for members to load
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
    })
  })
})
```

### Storybook Testing

```tsx
// src/components/ui/Button/Button.stories.tsx
import { expect, userEvent, within } from '@storybook/test'

export const InteractiveTest: Story = {
  args: {
    children: 'Click me',
    onClick: fn(), // Storybook action
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement)
    const button = canvas.getByRole('button')
    
    // Test button is rendered
    await expect(button).toBeInTheDocument()
    
    // Test click interaction
    await userEvent.click(button)
    await expect(args.onClick).toHaveBeenCalled()
    
    // Test accessibility
    await expect(button).toHaveAccessibleName('Click me')
  },
}
```

## Styling & Design System

### Tailwind CSS Configuration

```js
// tailwind.config.js
module.exports = {
  content: ['./src/**/*.{ts,tsx}'],
  theme: {
    extend: {
      colors: {
        // Citgo brand colors
        primary: {
          50: '#f0f9ff',
          500: '#0ea5e9',
          900: '#0c4a6e',
        },
        // Custom color system
        brand: {
          green: '#00a651', // "Go for the Green"
          red: '#e11d48',
          yellow: '#facc15',
        }
      },
      fontFamily: {
        sans: ['Univers', 'system-ui', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ],
}
```

### Design Tokens

```tsx
// src/utils/design-tokens.ts
export const tokens = {
  colors: {
    primary: {
      50: 'rgb(240 249 255)',
      500: 'rgb(14 165 233)',
      900: 'rgb(12 74 110)',
    },
    semantic: {
      success: 'rgb(34 197 94)',
      warning: 'rgb(251 191 36)',
      error: 'rgb(239 68 68)',
      info: 'rgb(59 130 246)',
    }
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
  },
  typography: {
    fontSizes: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
    },
    fontWeights: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    }
  }
} as const
```

### Component Styling Patterns

```tsx
// Using cva (class-variance-authority) for variant styling
import { cva } from 'class-variance-authority'

const buttonVariants = cva(
  // Base classes
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)
```

## Accessibility

### WCAG 2.1 AA Compliance

#### Semantic HTML

```tsx
// Good: Semantic structure
const Dashboard = () => (
  <main>
    <header>
      <h1>Dashboard</h1>
      <nav aria-label="Dashboard navigation">
        <ul>
          <li><a href="/claims">Claims</a></li>
          <li><a href="/customers">Customers</a></li>
        </ul>
      </nav>
    </header>
    <section aria-labelledby="recent-claims">
      <h2 id="recent-claims">Recent Claims</h2>
      {/* Content */}
    </section>
  </main>
)

// Bad: Non-semantic structure
const Dashboard = () => (
  <div>
    <div>
      <div>Dashboard</div>
      <div>
        <div><span>Claims</span></div>
        <div><span>Customers</span></div>
      </div>
    </div>
  </div>
)
```

#### ARIA Attributes

```tsx
// Form with proper labeling
const LoginForm = () => (
  <form aria-labelledby="login-title">
    <h2 id="login-title">Sign In</h2>
    
    <div>
      <label htmlFor="email">Email Address</label>
      <input
        id="email"
        type="email"
        required
        aria-describedby="email-error"
        aria-invalid={hasEmailError}
      />
      {hasEmailError && (
        <div id="email-error" role="alert">
          Please enter a valid email address
        </div>
      )}
    </div>
    
    <button type="submit" aria-describedby="submit-help">
      Sign In
    </button>
    <div id="submit-help">
      Press Enter or click to sign in
    </div>
  </form>
)
```

#### Focus Management

```tsx
import { useRef, useEffect } from 'react'

const Modal = ({ isOpen, onClose, title, children }) => {
  const titleRef = useRef<HTMLHeadingElement>(null)
  const previousFocusRef = useRef<HTMLElement>()
  
  useEffect(() => {
    if (isOpen) {
      // Store previously focused element
      previousFocusRef.current = document.activeElement as HTMLElement
      
      // Focus modal title
      titleRef.current?.focus()
    } else {
      // Return focus to previous element
      previousFocusRef.current?.focus()
    }
  }, [isOpen])
  
  if (!isOpen) return null
  
  return (
    <div
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      className="fixed inset-0 z-50 bg-black/50"
    >
      <div className="modal-content">
        <h2 id="modal-title" ref={titleRef} tabIndex={-1}>
          {title}
        </h2>
        {children}
        <button onClick={onClose} aria-label="Close modal">
          ×
        </button>
      </div>
    </div>
  )
}
```

### Accessibility Testing

```tsx
// Automated testing with jest-axe
import { axe, toHaveNoViolations } from 'jest-axe'
import { render } from '@testing-library/react'

expect.extend(toHaveNoViolations)

test('Button should not have accessibility violations', async () => {
  const { container } = render(<Button>Click me</Button>)
  const results = await axe(container)
  expect(results).toHaveNoViolations()
})
```

## Performance

### Code Splitting

```tsx
// Route-based code splitting
import { lazy, Suspense } from 'react'
import { Routes, Route } from '@tanstack/react-router'

const DashboardPage = lazy(() => import('@/pages/Dashboard'))
const ClaimsPage = lazy(() => import('@/pages/Claims'))
const MembersPage = lazy(() => import('@/pages/Members'))

const App = () => (
  <Suspense fallback={<div>Loading...</div>}>
    <Routes>
      <Route path="/" component={DashboardPage} />
      <Route path="/claims" component={ClaimsPage} />
      <Route path="/members" component={MembersPage} />
    </Routes>
  </Suspense>
)
```

### Component Optimization

```tsx
import { memo, useMemo, useCallback } from 'react'

// Memoized component
const MemberCard = memo(({ member, onEdit, onDelete }) => {
  const displayName = useMemo(() => 
    `${member.firstName} ${member.lastName}`, 
    [member.firstName, member.lastName]
  )
  
  const handleEdit = useCallback(() => 
    onEdit(member.id), 
    [member.id, onEdit]
  )
  
  return (
    <div>
      <h3>{displayName}</h3>
      <button onClick={handleEdit}>Edit</button>
      <button onClick={() => onDelete(member.id)}>Delete</button>
    </div>
  )
})
```

### Image Optimization

```tsx
// Responsive images with lazy loading
const OptimizedImage = ({ src, alt, className }) => (
  <img
    src={src}
    alt={alt}
    className={className}
    loading="lazy"
    decoding="async"
    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
  />
)
```

## Best Practices

### TypeScript Guidelines

#### Type Definitions

```tsx
// Define strict types for API responses
interface ApiResponse<T> {
  results: T[]
  count: number
  next?: string | null
  previous?: string | null
}

interface Member {
  readonly id: string
  email: string
  firstName: string
  lastName: string
  status: 'active' | 'inactive' | 'pending'
  createdAt: string
  updatedAt: string
}

// Use branded types for IDs
type MemberId = string & { readonly brand: unique symbol }
type ClaimId = string & { readonly brand: unique symbol }

// Helper type utilities
type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
type CreateMemberRequest = Optional<Member, 'id' | 'createdAt' | 'updatedAt'>
```

#### Error Handling

```tsx
// Custom error types
class ApiError extends Error {
  constructor(
    public status: number,
    message: string,
    public data?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

class ValidationError extends Error {
  constructor(
    message: string,
    public field: string,
    public code: string
  ) {
    super(message)
    this.name = 'ValidationError'
  }
}

// Error handling in components
const MemberForm = () => {
  const createMember = useCreateMember()
  
  const handleSubmit = async (data: CreateMemberRequest) => {
    try {
      await createMember.mutateAsync(data)
      toast.success('Member created successfully')
    } catch (error) {
      if (error instanceof ApiError) {
        if (error.status === 400) {
          toast.error('Please check the form data')
        } else if (error.status === 409) {
          toast.error('A member with this email already exists')
        } else {
          toast.error('Failed to create member')
        }
      } else {
        toast.error('An unexpected error occurred')
      }
    }
  }
  
  // ...rest of component
}
```

### Code Organization

#### File Naming Conventions
- **Components**: PascalCase (`MemberCard.tsx`)
- **Hooks**: camelCase with `use` prefix (`useMembers.ts`)
- **Utilities**: camelCase (`formatCurrency.ts`)
- **Types**: camelCase with descriptive names (`memberTypes.ts`)
- **Constants**: UPPER_SNAKE_CASE (`API_ENDPOINTS.ts`)

#### Import Organization

```tsx
// 1. React and framework imports
import React, { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'

// 2. Third-party libraries
import { toast } from 'sonner'
import { format } from 'date-fns'

// 3. Internal modules (absolute imports)
import { Button } from '@/components/ui'
import { useMembers } from '@/hooks/api'
import { Member } from '@/types/api'

// 4. Relative imports
import './MemberCard.css'
```

### Component Guidelines

#### Props Interface Design

```tsx
// Good: Clear, typed props
interface MemberCardProps {
  member: Member
  variant?: 'default' | 'compact' | 'detailed'
  showActions?: boolean
  onEdit?: (memberId: string) => void
  onDelete?: (memberId: string) => void
  className?: string
}

// Good: Use children appropriately
interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg'
}

// Avoid: Too many optional props
interface BadProps {
  data?: any
  config?: any
  options?: any
  handlers?: any
  // ... 20 more optional props
}
```

#### Component Structure

```tsx
// Consistent component structure
const MemberCard: React.FC<MemberCardProps> = ({
  member,
  variant = 'default',
  showActions = true,
  onEdit,
  onDelete,
  className
}) => {
  // 1. State and refs
  const [isLoading, setIsLoading] = useState(false)
  
  // 2. Hooks (custom hooks after built-in hooks)
  const { data: memberDetails } = useMemberDetails(member.id)
  
  // 3. Computed values
  const displayName = useMemo(() => 
    `${member.firstName} ${member.lastName}`, 
    [member.firstName, member.lastName]
  )
  
  // 4. Event handlers
  const handleEdit = useCallback(() => {
    if (onEdit) onEdit(member.id)
  }, [member.id, onEdit])
  
  const handleDelete = useCallback(async () => {
    if (!onDelete) return
    
    setIsLoading(true)
    try {
      await onDelete(member.id)
    } finally {
      setIsLoading(false)
    }
  }, [member.id, onDelete])
  
  // 5. Effects
  useEffect(() => {
    // Side effects
  }, [])
  
  // 6. Render
  return (
    <div className={cn('member-card', `member-card--${variant}`, className)}>
      {/* Component JSX */}
    </div>
  )
}

export default MemberCard
```

This comprehensive guide should help developers effectively work with the React/TypeScript frontend, understand the Storybook workflow, and maintain high code quality standards throughout the development process.