# Performance Optimization Guide

## Overview

This guide documents the performance optimization strategies implemented for the XD Incentives frontend application, focusing on achieving excellent Core Web Vitals scores.

## Core Web Vitals Targets

| Metric | Good | Target | Description |
|--------|------|--------|-------------|
| **LCP** (Largest Contentful Paint) | < 2.5s | < 2.0s | Loading performance |
| **FID** (First Input Delay) | < 100ms | < 50ms | Interactivity |
| **CLS** (Cumulative Layout Shift) | < 0.1 | < 0.05 | Visual stability |
| **FCP** (First Contentful Paint) | < 1.8s | < 1.5s | Perceived load speed |
| **INP** (Interaction to Next Paint) | < 200ms | < 150ms | Responsiveness |
| **TTFB** (Time to First Byte) | < 800ms | < 600ms | Server response |

## Implemented Optimizations

### 1. Code Splitting & Chunking

**Configuration:** `vite.config.ts`

```typescript
// Automatic vendor chunk splitting
splitVendorChunkPlugin()

// Manual chunks for better caching
manualChunks: {
  'react-vendor': ['react', 'react-dom'],
  'tanstack': ['@tanstack/*'],
  'state': ['zustand'],
  'ui': ['@radix-ui/*']
}
```

**Benefits:**
- Smaller initial bundle size
- Better browser caching
- Parallel loading of chunks
- Reduced parse/compile time

### 2. Lazy Loading

#### Components

Use the `lazyLoadWithRetry` utility for route-based code splitting:

```typescript
import { lazyLoadWithRetry } from '@utils/lazyLoad'

const Dashboard = lazyLoadWithRetry(
  () => import('./routes/dashboard'),
  3, // retry attempts
  1000 // delay between retries
)
```

#### Images

Use the `OptimizedImage` component for automatic lazy loading:

```tsx
import { OptimizedImage } from '@components/OptimizedImage'

<OptimizedImage
  src="/images/hero.webp"
  alt="Hero image"
  priority={false} // lazy load
  blurDataURL="data:image/jpeg;base64,..." // optional placeholder
/>
```

### 3. Compression

**Gzip & Brotli compression** is automatically applied to all assets:

- JavaScript: ~60-70% size reduction
- CSS: ~80-85% size reduction
- HTML: ~70-80% size reduction

### 4. Service Worker & PWA

**Caching strategies implemented:**

- **Network First:** API calls (5-minute cache)
- **Cache First:** Images (30-day cache)
- **Stale While Revalidate:** Static assets

### 5. Bundle Optimization

#### Tree Shaking
- ES modules ensure dead code elimination
- Side-effect free imports
- Production mode removes unused code

#### Minification
- Terser for JavaScript
- CSS minification built-in
- HTML minification in production

### 6. Asset Optimization

#### Images
- Use WebP format (30% smaller than JPEG)
- Responsive images with `<picture>` element
- Lazy loading with Intersection Observer
- Blur placeholders for perceived performance

```tsx
<Picture
  sources={[
    { srcSet: '/images/hero.webp', type: 'image/webp' },
    { srcSet: '/images/hero.jpg', type: 'image/jpeg' }
  ]}
  alt="Hero"
  priority={true}
/>
```

#### Fonts
- Use `font-display: swap` for web fonts
- Preload critical fonts
- Subset fonts to required characters

### 7. Critical CSS

Inline critical CSS in HTML head:

```html
<style>
  /* Critical above-the-fold styles */
  body { margin: 0; font-family: system-ui; }
  .header { /* ... */ }
</style>
```

### 8. Resource Hints

Add to HTML head for faster connections:

```html
<link rel="preconnect" href="https://api.xdincentives.com">
<link rel="dns-prefetch" href="https://cdn.xdincentives.com">
<link rel="preload" href="/fonts/main.woff2" as="font" crossorigin>
```

## Performance Monitoring

### Web Vitals Tracking

Automatic monitoring is enabled in `main.tsx`:

```typescript
import { initWebVitals } from '@utils/webVitals'
initWebVitals()
```

### Testing Performance

#### 1. Development Testing

Run in browser console:
```javascript
testPerformance()
```

#### 2. Build Analysis

Analyze bundle composition:
```bash
npm run build:analyze
```

#### 3. Lighthouse Testing

Run Lighthouse audit:
```bash
npm run lighthouse
```

#### 4. CI Performance Budget

Automated checks in `.lighthouserc.json`:
- Performance score > 90
- Bundle size < 200KB (JS)
- LCP < 2.5s
- CLS < 0.1

## Best Practices

### 1. Import Optimization

**❌ Bad:** Importing entire libraries
```typescript
import * as _ from 'lodash'
```

**✅ Good:** Import specific functions
```typescript
import debounce from 'lodash/debounce'
```

### 2. Dynamic Imports

**❌ Bad:** Loading everything upfront
```typescript
import HeavyComponent from './HeavyComponent'
```

**✅ Good:** Load when needed
```typescript
const HeavyComponent = lazy(() => import('./HeavyComponent'))
```

### 3. Image Optimization

**❌ Bad:** Unoptimized images
```html
<img src="large-photo.jpg" />
```

**✅ Good:** Optimized with lazy loading
```tsx
<OptimizedImage
  src="photo.webp"
  alt="Description"
  loading="lazy"
/>
```

### 4. State Management

**❌ Bad:** Large global state
```typescript
const globalState = useStore(state => state)
```

**✅ Good:** Selective subscriptions
```typescript
const user = useStore(state => state.user)
```

### 5. Memoization

**❌ Bad:** Recreating expensive values
```typescript
const expensiveValue = computeExpensive(data)
```

**✅ Good:** Memoize when appropriate
```typescript
const expensiveValue = useMemo(
  () => computeExpensive(data),
  [data]
)
```

## Performance Checklist

### Before Deployment

- [ ] Run `npm run build:analyze` to check bundle size
- [ ] Test Core Web Vitals with Lighthouse
- [ ] Verify lazy loading is working
- [ ] Check network waterfall for optimization opportunities
- [ ] Test on slow 3G connection
- [ ] Verify service worker caching
- [ ] Check for console errors in production build
- [ ] Test performance on real devices

### Monitoring

- [ ] Set up Real User Monitoring (RUM)
- [ ] Configure performance alerts
- [ ] Track Core Web Vitals over time
- [ ] Monitor bundle size in CI
- [ ] Review performance weekly

## Troubleshooting

### Large Bundle Size

1. Run bundle analyzer: `npm run build:analyze`
2. Check for duplicate dependencies
3. Look for large libraries that can be replaced
4. Ensure tree shaking is working

### Slow Initial Load

1. Check TTFB (server response time)
2. Verify compression is enabled
3. Check for render-blocking resources
4. Optimize critical rendering path

### Poor CLS Score

1. Set explicit dimensions on images/videos
2. Avoid inserting content above existing content
3. Use CSS transforms instead of position changes
4. Reserve space for dynamic content

### High INP/FID

1. Break up long tasks
2. Use `requestIdleCallback` for non-critical work
3. Optimize event handlers
4. Reduce main thread work

## Resources

- [Web Vitals](https://web.dev/vitals/)
- [Lighthouse Documentation](https://developers.google.com/web/tools/lighthouse)
- [Vite Performance Guide](https://vitejs.dev/guide/performance.html)
- [React Performance](https://react.dev/learn/render-and-commit)
- [Bundle Phobia](https://bundlephobia.com/) - Check package sizes