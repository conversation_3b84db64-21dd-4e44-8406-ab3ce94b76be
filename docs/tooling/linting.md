# Linting and Code Quality Guide

This guide covers all linting and code quality tools configured for the XD Incentives project, covering both backend (Python/Django) and frontend (TypeScript/React) codebases.

## Quick Reference

### Backend (Django) Commands
```bash
# Run all formatters (Black + isort)
make format

# Check code style without changes
make lint-check

# Run all linters
make lint

# Fix auto-fixable issues
make lint-fix

# Run security scans
make lint-security

# Complete quality suite
make lint-all

# Pre-commit hooks
make pre-commit-install
make pre-commit-run
```

### Frontend (React) Commands
```bash
# Format TypeScript/React code
make frontend-lint

# Check TypeScript compilation
make frontend-typecheck

# Run frontend tests
make frontend-test

# Build Storybook and check stories
make storybook-test

# Run accessibility tests
make frontend-a11y

# Complete frontend quality suite
make frontend-lint-all
```

### Direct Commands (in Docker)

#### Backend (Django)
```bash
# Format code
docker exec xd-backend black apps/ config/ manage.py
docker exec xd-backend isort apps/ config/ manage.py

# Run linters
docker exec xd-backend flake8 apps/ config/ manage.py
docker exec xd-backend pylint apps/ config/ manage.py
docker exec xd-backend mypy apps/ config/ manage.py
docker exec xd-backend bandit -r apps/ config/ manage.py
```

#### Frontend (React/TypeScript)
```bash
# Format and lint with Biome
docker exec xd-frontend npm run lint
docker exec xd-frontend npm run lint:fix
docker exec xd-frontend npm run format

# TypeScript checking
docker exec xd-frontend npm run typecheck

# Testing
docker exec xd-frontend npm run test
docker exec xd-frontend npm run test:coverage

# Storybook
docker exec xd-frontend npm run storybook:build
docker exec xd-frontend npm run test-storybook
```

## Tools Overview

## Backend Tools (Python/Django)

### 1. **Black** - Code Formatting
- **Purpose**: Automatic Python code formatting
- **Configuration**: `pyproject.toml` → `[tool.black]`
- **Line length**: 88 characters
- **Target**: Python 3.12
- **Excludes**: migrations, node_modules, static files

**Usage:**
```bash
# Format files
black apps/ config/ manage.py

# Check formatting without changes
black --check --diff apps/ config/ manage.py
```

### 2. **isort** - Import Sorting
- **Purpose**: Organize and sort Python imports
- **Configuration**: `pyproject.toml` → `[tool.isort]`
- **Profile**: Black-compatible
- **Sections**: Future, Stdlib, Django, Third-party, First-party, Local

**Usage:**
```bash
# Sort imports
isort apps/ config/ manage.py

# Check only
isort --check-only --diff apps/ config/ manage.py
```

### 3. **Flake8** - Style Linter
- **Purpose**: Style guide enforcement and error detection
- **Configuration**: `.pre-commit-config.yaml`
- **Ignored codes**: `C901` (complexity), `F403,F405` (star imports)
- **Extensions**: Django, bugbear, comprehensions, docstrings, quotes

**Usage:**
```bash
# Run flake8
flake8 apps/ config/ manage.py

# With specific rules
flake8 --ignore=C901,F403,F405 apps/
```

### 4. **Pylint** - Static Analysis
- **Purpose**: Advanced static analysis and code quality
- **Configuration**: `pyproject.toml` → `[tool.pylint]`
- **Max line length**: 88 characters
- **Django support**: Available but disabled for pre-commit compatibility

**Usage:**
```bash
# Run pylint
pylint apps/ config/ manage.py

# Errors only (pre-commit mode)
pylint --errors-only apps/ config/ manage.py
```

### 5. **MyPy** - Type Checking
- **Purpose**: Static type checking
- **Configuration**: `pyproject.toml` → `[tool.mypy]`
- **Python version**: 3.12
- **Django stubs**: Configured but disabled for pre-commit

**Usage:**
```bash
# Run mypy
mypy apps/ config/ manage.py

# With specific config
mypy --ignore-missing-imports --show-error-codes apps/
```

### 6. **Bandit** - Security Scanner
- **Purpose**: Security vulnerability detection
- **Configuration**: `pyproject.toml` → `[tool.bandit]`
- **Excludes**: tests, migrations, node_modules, static
- **Skip codes**: `B101` (assert_used), `B601` (shell=True)

**Usage:**
```bash
# Run security scan
bandit -r apps/ config/ manage.py

# Generate JSON report
bandit -r apps/ -f json -o security-report.json
```

## Frontend Tools (TypeScript/React)

### 1. **Biome** - Fast Linting & Formatting
- **Purpose**: All-in-one toolchain for linting, formatting, and more
- **Configuration**: `biome.json`
- **Language Support**: TypeScript, JavaScript, JSON, CSS
- **Features**: Fast Rust-based tooling, LSP integration

**Usage:**
```bash
# Format and fix issues
npm run lint:fix

# Check only (no changes)
npm run lint

# Format code only
npm run format
```

### 2. **TypeScript** - Type Checking
- **Purpose**: Static type checking for JavaScript
- **Configuration**: `tsconfig.json`, `tsconfig.app.json`, `tsconfig.node.json`
- **Strict Mode**: Enabled for maximum type safety
- **Path Aliases**: Configured for clean imports

**Usage:**
```bash
# Check types
npm run typecheck

# Check types in watch mode
npm run typecheck:watch

# Build with type checking
npm run build
```

### 3. **Vitest** - Testing Framework
- **Purpose**: Unit and integration testing
- **Configuration**: `vitest.config.ts`
- **Features**: Fast, Vite-native testing, TypeScript support
- **Coverage**: Istanbul-based coverage reports

**Usage:**
```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests with UI
npm run test:ui
```

### 4. **Storybook** - Component Development
- **Purpose**: Component documentation and visual testing
- **Configuration**: `.storybook/main.ts`
- **Version**: 9+ with modern architecture
- **Addons**: Accessibility, controls, docs, viewport

**Usage:**
```bash
# Start development server
npm run storybook

# Build static version
npm run build-storybook

# Test stories
npm run test-storybook
```

### 5. **ESLint + Accessibility** - Advanced Linting
- **Purpose**: Accessibility and React-specific linting
- **Plugins**: `@eslint/react`, `@eslint/a11y`, `@eslint/hooks`
- **Integration**: Part of Biome configuration
- **WCAG Compliance**: Automated accessibility checks

**Usage:**
```bash
# Run accessibility checks
npm run lint:a11y

# Auto-fix a11y issues
npm run lint:a11y:fix
```

### 6. **Dependency Auditing** - Security
- **Purpose**: Dependency vulnerability scanning
- **Tools**: `npm audit`, `audit-ci`
- **Integration**: GitHub Actions and pre-commit hooks
- **Auto-fixing**: Automated dependency updates

**Usage:**
```bash
# Check for vulnerabilities
npm audit

# Fix auto-fixable issues
npm audit fix

# Detailed security report
npm run security:check
```

### Frontend Configuration Files

#### biome.json
Contains configuration for:
- Linting rules and complexity limits
- Formatting preferences (Prettier-compatible)
- File organization and import sorting
- TypeScript-specific rules
- React and JSX formatting

#### tsconfig.json
TypeScript compiler configuration:
- Strict type checking enabled
- Path aliases for clean imports
- Modern JavaScript target (ES2022)
- React JSX transform
- Incremental compilation

#### vitest.config.ts
Testing framework configuration:
- Test environment setup (jsdom)
- Coverage reporting
- Test utilities and mocks
- TypeScript support

#### .storybook/main.ts
Storybook configuration:
- Vite integration
- TypeScript support
- Addon configurations
- Build optimizations

## Pre-commit Hooks

The project uses pre-commit hooks to automatically run linters before commits. This ensures code quality and consistency across all team contributions.

### Team Setup (Required for All Developers)

**First-time setup after cloning the repository:**

1. **Install pre-commit hooks** (one-time setup):
   ```bash
   make pre-commit-install
   ```
   This command installs the pre-commit hooks that will run automatically on every commit.

2. **Verify installation**:
   ```bash
   make pre-commit-run
   ```
   This runs all hooks on the entire codebase to ensure everything is working correctly.

### Daily Development Workflow

**Before making any commits:**

1. **Format your code**:
   ```bash
   make format
   ```
   This runs Black and isort to format your code automatically.

2. **Check for issues** (optional):
   ```bash
   make lint-check
   ```
   This shows you any linting issues without making changes.

3. **Commit normally**:
   ```bash
   git add .
   git commit -m "your commit message"
   ```
   The pre-commit hooks will run automatically and either:
   - ✅ **Pass**: Your commit will proceed normally
   - ❌ **Fail**: The commit will be blocked, and you'll see what needs to be fixed

4. **If hooks fail**:
   - The hooks often auto-fix issues (like formatting)
   - Check what was changed: `git diff`
   - Add the auto-fixed files: `git add .`
   - Try committing again: `git commit -m "your commit message"`

**Important Notes:**
- Pre-commit hooks run on **staged files only**
- Some hooks (like Black) will automatically fix issues and you'll need to re-add and commit
- Never skip hooks with `--no-verify` unless absolutely necessary and approved by the team

### Installation
```bash
# Install hooks
make pre-commit-install
# or
pre-commit install
```

### Manual Run
```bash
# Run on all files
make pre-commit-run
# or
pre-commit run --all-files

# Run on staged files only
pre-commit run
```

### Configured Hooks
1. **General cleanup**: trailing whitespace, end-of-file-fixer, yaml/json checks
2. **Black**: Code formatting
3. **isort**: Import sorting
4. **Flake8**: Style checking with Django extensions
5. **Bandit**: Security scanning
6. **MyPy**: Type checking (basic mode)
7. **Pylint**: Static analysis (errors only)

## Configuration Files

### pyproject.toml
Contains configuration for:
- Black formatting rules
- isort import organization
- MyPy type checking settings
- Pylint analysis rules
- Bandit security scanning
- Coverage reporting

### .pre-commit-config.yaml
Defines pre-commit hooks and their configurations, including:
- Tool versions and repositories
- Exclusion patterns
- Additional dependencies
- Hook-specific arguments

## Best Practices

### 1. **Before Committing**
```bash
# Format code
make format

# Check for issues
make lint-check

# Run full quality suite
make lint-all
```

### 2. **Fixing Common Issues**

**Import Organization:**
```bash
# Fix import sorting
isort apps/ config/ manage.py
```

**Code Formatting:**
```bash
# Auto-format code
black apps/ config/ manage.py
```

**Security Issues:**
```bash
# Review security scan
bandit -r apps/ config/ manage.py
```

### 3. **CI/CD Integration**
Pre-commit hooks run automatically on:
- Every commit (if installed)
- GitHub Actions workflows
- Manual execution via `make pre-commit-run`

### 4. **Exclusions**
The following directories are excluded from linting:
- `migrations/` - Django database migrations
- `node_modules/` - JavaScript dependencies
- `static/` - Static files
- `tests/` - Test files (some tools only)

## Troubleshooting

### Common Issues

**1. Pre-commit Hook Failures**
- Run `make format` first to fix formatting
- Check excluded files in configuration
- Update hook versions if needed

**2. MyPy Type Errors**
- Add `# type: ignore` for complex Django model fields
- Use `--ignore-missing-imports` for third-party packages
- Check Django stubs installation

**3. Pylint Django Errors**
- Plugin disabled for pre-commit compatibility
- Run with `--errors-only` flag for essential checks
- Consider using `# pylint: disable=` comments sparingly

**4. Import Sorting Conflicts**
- Ensure isort profile is set to "black"
- Check `known_first_party` settings in configuration
- Use `--diff` flag to preview changes

### Performance Tips

**1. Use Make Commands**
- Leverage `make lint-check` for fast style checking
- Use `make format` for quick formatting
- Run `make lint-all` before major commits

**2. Docker Efficiency**
- Commands run inside Docker containers
- Ensure containers are running: `make dev-up`
- Use `docker exec` for direct container access

**3. Selective Linting**
```bash
# Lint specific app
docker exec xd-backend flake8 apps/member/

# Check single file
docker exec xd-backend black --check apps/member/models.py
```

## Integration with IDEs

### VS Code
Install extensions:
- Python (Microsoft)
- Black Formatter
- isort
- Pylint
- MyPy Type Checker

### PyCharm
Configure external tools:
- Black formatter
- isort import organizer
- Flake8 inspection
- MyPy type checking

## Continuous Improvement

### Regular Maintenance
1. Update tool versions in `.pre-commit-config.yaml`
2. Review and adjust linting rules in `pyproject.toml`
3. Monitor security advisories via Bandit reports
4. Update Django-specific linting rules as framework evolves

### Team Standards
- Enforce consistent formatting with Black
- Require clean pre-commit runs before PR approval
- Address security findings promptly
- Document exceptions to linting rules

## Current Pre-commit Configuration Analysis

### Configuration Rationale

Our current pre-commit setup reflects pragmatic choices made during rapid development while maintaining code quality standards. Here's why each configuration decision was made:

#### **Flake8 Configuration (.flake8)**
```ini
extend-ignore = E203,E501,W503,DJ01,DJ08,D100,D101,D102,D103,D104,D105,D106,D107,D200,D205,D212,D415,Q000,I100,I101,I201,I202,E402,F401,W291,W292,W293,C901,F403,F405,E226,F841,D202,F541,B007,B018,E902
```

**Why we ignore these:**
- `E203,E501,W503`: Black formatter compatibility (spacing and line length)
- `DJ01,DJ08`: Django-specific patterns that are acceptable in our codebase
- `D100-D107,D200,D205,D212,D415`: Docstring requirements relaxed during development phase
- `C901`: Complexity warnings ignored for legacy code (temporary)
- `F403,F405`: Star imports allowed in Django settings files
- `F841`: Unused variables ignored for exception handlers and context managers
- `F541`: F-string placeholder warnings ignored for logging templates
- `B007,B018`: Loop variable and useless expression warnings for generated code
- `E902`: File not found errors from pre-commit tool conflicts

#### **MyPy Configuration**
```yaml
args: [--python-version=3.12, --ignore-missing-imports, --show-error-codes, --no-strict-optional, --disable-error-code=var-annotated, --disable-error-code=attr-defined, --disable-error-code=assignment, --disable-error-code=misc, --disable-error-code=valid-type]
```

**Aggressive suppression rationale:**
- `--ignore-missing-imports`: Many third-party packages lack type stubs
- `--disable-error-code=attr-defined`: Django models have dynamic attributes
- `--disable-error-code=assignment`: Complex Django patterns confuse type checker
- `--disable-error-code=misc,valid-type`: Legacy code compatibility issues

#### **Bandit Security Configuration**
```toml
exclude_dirs = ["tests", "migrations", "node_modules", "static", "*/management/commands/create_sample*"]
skips = ["B101", "B601", "B106", "B311"]
```

**Security trade-offs:**
- `B106,B311`: Hardcoded passwords and weak random allowed in sample data commands
- `B101,B601`: Assert statements and shell=True allowed for development utilities
- Sample data commands excluded entirely to avoid development friction

### Current State Assessment

#### **Strengths**
✅ **Fast Developer Experience**: Minimal friction during commits  
✅ **Automated Formatting**: Black and isort prevent style debates  
✅ **Security Scanning**: Bandit catches real security issues  
✅ **CI/CD Integration**: Hooks run consistently across environments  
✅ **Team Consistency**: All developers use same tool versions  

#### **Weaknesses**
❌ **Overly Permissive**: Too many ignored rules reduce code quality benefits  
❌ **Missing Documentation**: Lack of docstrings reduces maintainability  
❌ **Type Safety Gaps**: MyPy suppressions hide potential runtime errors  
❌ **Technical Debt**: Complexity and unused variable issues accumulating  
❌ **Security Blindspots**: Excluding entire directories may miss real issues  

## Ideal Future State Configuration

### Phase 1: Immediate Improvements (Next Sprint)

#### **Reduce Flake8 Suppressions**
```ini
# Target configuration
extend-ignore = E203,E501,W503,DJ01,DJ08,F403,F405
# Remove: D100-D107 (add docstrings), C901 (fix complexity), F841 (clean unused vars)
```

**Action Items:**
- Add docstrings to all public classes and functions
- Refactor complex functions (C901) into smaller, testable units
- Clean up unused variables and imports
- Fix f-string placeholder warnings

#### **Improve MyPy Configuration**
```yaml
# Target: Enable more strict checking
args: [--python-version=3.12, --ignore-missing-imports, --show-error-codes, --disable-error-code=attr-defined]
# Remove: assignment, misc, valid-type suppressions
```

**Action Items:**
- Add proper type annotations to function signatures
- Install Django stubs: `django-stubs`, `djangorestframework-stubs`
- Create custom type definitions for complex Django patterns
- Use `TYPE_CHECKING` imports for circular dependencies

#### **Enhance Security Configuration**
```toml
# More targeted exclusions
exclude_dirs = ["tests", "migrations", "node_modules", "static"]
skips = ["B101"]  # Only keep assert statements skip
# Remove: B106, B311, B601 - address these issues properly
```

**Action Items:**
- Replace hardcoded passwords with environment variables
- Use `secrets` module instead of `random` for cryptographic purposes
- Implement proper timeout handling for external requests
- Add `# nosec` comments for justified security exceptions

### Phase 2: Advanced Quality Standards (Next Quarter)

#### **Comprehensive Type Checking**
```yaml
args: [--python-version=3.12, --strict, --show-error-codes]
additional_dependencies:
  - django-stubs==4.2.7
  - djangorestframework-stubs==3.14.5
  - types-requests==2.32.0.20241016
```

#### **Documentation Requirements**
```ini
# Enforce docstring standards
extend-ignore = E203,E501,W503,DJ01,DJ08,F403,F405
# All D-codes removed - require proper documentation
```

#### **Performance and Complexity Monitoring**
```yaml
# Add performance-focused hooks
- repo: https://github.com/PyCQA/prospector
  rev: 1.10.3
  hooks:
    - id: prospector
      args: [--profile-path=.prospector.yaml]
```

#### **Advanced Security Scanning**
```yaml
# Add additional security tools
- repo: https://github.com/Yelp/detect-secrets
  rev: v1.4.0
  hooks:
    - id: detect-secrets
- repo: https://github.com/Lucas-C/pre-commit-hooks-safety
  rev: v1.3.2
  hooks:
    - id: python-safety-dependencies-check
```

### Phase 3: Production-Ready Standards (6 Months)

#### **Complete Integration**
- **IDE Integration**: Team-wide VS Code/PyCharm configurations
- **CI/CD Pipeline**: Comprehensive quality gates in GitHub Actions
- **Automated Metrics**: Code quality metrics tracking and reporting
- **Team Training**: Regular workshops on code quality best practices

#### **Quality Metrics Goals**
- **Type Coverage**: >90% of codebase properly type-annotated
- **Documentation Coverage**: 100% of public APIs documented
- **Security Score**: Zero medium/high severity findings
- **Complexity Score**: All functions <10 cyclomatic complexity
- **Test Coverage**: >95% line coverage with quality tests

### Migration Strategy

#### **Week 1-2: Foundation**
1. Update tool versions to latest stable releases
2. Create baseline metrics dashboard
3. Set up quality measurement automation

#### **Week 3-4: Documentation Sprint**
1. Add docstrings to all public classes/functions
2. Create type annotation standards document
3. Update contribution guidelines

#### **Week 5-8: Gradual Strictening**
1. Remove one category of ignored rules per week
2. Fix issues as they surface
3. Monitor team velocity impact

#### **Month 2-3: Advanced Features**
1. Implement custom linting rules for project patterns
2. Add performance monitoring hooks
3. Integrate with code review process

#### **Month 4-6: Optimization**
1. Fine-tune rules based on team feedback
2. Implement automated quality reporting
3. Establish quality gates for production releases

### Benefits of Future State

#### **Developer Experience**
- **Faster Debugging**: Type annotations catch errors at development time
- **Better IDE Support**: Autocomplete and refactoring tools work better
- **Clearer Code Intent**: Documentation and types make code self-explaining
- **Reduced Cognitive Load**: Consistent patterns reduce mental overhead

#### **Team Productivity**
- **Fewer Bugs**: Static analysis catches issues before they reach production
- **Easier Onboarding**: Well-documented, typed code is easier to understand
- **Confident Refactoring**: Type safety enables fearless code changes
- **Knowledge Sharing**: Documentation captures domain knowledge

#### **Business Impact**
- **Reduced Technical Debt**: Proactive quality measures prevent accumulation
- **Faster Feature Development**: Clean codebase enables rapid iteration
- **Lower Maintenance Costs**: Fewer production issues and easier debugging
- **Better Security Posture**: Comprehensive scanning prevents vulnerabilities

### Implementation Commitment

This migration requires dedicated time investment:
- **Developer Time**: ~2 hours/week per developer for 6 months
- **Team Coordination**: Weekly quality review meetings
- **Tooling Investment**: Budget for premium analysis tools if needed
- **Training Resources**: Workshops and documentation creation time

The payoff in code quality, developer productivity, and maintainability makes this investment essential for the long-term success of the XD Incentives platform.

---

This comprehensive linting setup ensures code quality, security, and consistency across the XD Incentives codebase.