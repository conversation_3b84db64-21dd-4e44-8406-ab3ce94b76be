# Docker Setup Guide

This guide provides comprehensive instructions for setting up and running the XD Incentives application using Docker and Docker Compose.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Quick Start](#quick-start)
3. [Detailed Setup Instructions](#detailed-setup-instructions)
4. [Service Architecture](#service-architecture)
5. [Port Mappings](#port-mappings)
6. [Common Commands](#common-commands)
7. [Troubleshooting](#troubleshooting)
8. [Advanced Configuration](#advanced-configuration)

---

## Prerequisites

### Required Software

| Software           | Minimum Version | Recommended Version | Purpose                       |
| ------------------ | --------------- | ------------------- | ----------------------------- |
| **Docker**         | 24.0.0          | 25.0.0+             | Container runtime             |
| **Docker Compose** | 2.20.0          | 2.24.0+             | Multi-container orchestration |
| **Git**            | 2.30.0          | 2.40.0+             | Version control               |
| **Make**           | 4.0             | 4.3+                | Build automation              |

### System Requirements

| Resource       | Minimum                                      | Recommended | Notes                                |
| -------------- | -------------------------------------------- | ----------- | ------------------------------------ |
| **RAM**        | 8 GB                                         | 16 GB       | Multiple containers + databases      |
| **CPU**        | 2 cores                                      | 4+ cores    | Better build and runtime performance |
| **Disk Space** | 10 GB                                        | 20 GB       | Images, volumes, and logs            |
| **OS**         | macOS 10.15+, Ubuntu 20.04+, Windows 10 Pro+ | Latest      | Docker compatibility                 |

### Installation Instructions

#### macOS (Recommended: Docker Desktop)

```bash
# Install Docker Desktop from https://docker.com/products/docker-desktop
# Or use Homebrew
brew install --cask docker
```

#### Ubuntu/Debian

```bash
# Install Docker Engine
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo apt-get install docker-compose-plugin

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker
```

#### Windows

```bash
# Install Docker Desktop from https://docker.com/products/docker-desktop
# Requires WSL 2 backend
```

### Verification

```bash
# Verify Docker installation
docker --version
docker compose version

# Test Docker functionality
docker run hello-world
```

---

## Quick Start

Get up and running in under 5 minutes:

```bash
# 1. Clone repository
git clone https://github.com/integritystl/xd-incentives
cd xd-incentives

# 2. Create environment file
cp .env.example .env
# Edit .env with your settings (see Configuration section)

# 3. Start with full database
make quick-start

# 4. Access the application
open http://localhost:8000  # Backend API
open http://localhost:3000  # Frontend App
```

**Default Access:**

- **Admin Panel**: http://localhost:8000/admin/ (`admin` / `admin123`)
- **API Root**: http://localhost:8000/api/
- **Frontend**: http://localhost:3000/
- **Flower (Celery)**: http://localhost:5555/ (when running)

---

## Detailed Setup Instructions

### Step 1: Environment Configuration

Create and configure your environment file:

```bash
# Copy example environment
cp .env.example .env
```

**Required Environment Variables:**

```bash
# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=true
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
DB_NAME=xd_incentives_db
DB_USER=testu
DB_PASSWORD=testpw
DB_HOST=db
DB_PORT=3306
```

**Optional Environment Variables:**

```bash
# Clerk Authentication (optional)
CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
```

### Step 2: Build and Start Services

#### Option A: Quick Start (Recommended for first time)

```bash
# Complete setup with database seeding
make quick-start
```

#### Option B: Fresh Start (Clean installation)

```bash
# Complete reset and fresh installation
make fresh-start
```

#### Option C: Manual Step-by-Step

```bash
# Build all services
make build

# Start core services
make up

# Wait for services to be ready (15-30 seconds)
make health-check

# Seed database with sample data
make seed-full
```

### Step 3: Verify Installation

```bash
# Check service status
make status

# Run health checks
make health-check

# View logs
make logs
```

---

## Service Architecture

### Core Services

| Service      | Container Name | Purpose                             | Dependencies |
| ------------ | -------------- | ----------------------------------- | ------------ |
| **frontend** | xd-frontend    | React/TypeScript development server | backend      |
| **backend**  | xd-backend     | Django API application              | db, redis    |
| **db**       | xd-mysql       | MySQL 8.4.5 database                | none         |
| **redis**    | xd-redis       | Cache and message broker            | none         |
| **celery**   | xd-celery      | Background task worker              | db, redis    |

### Optional Services

| Service         | Container Name | Purpose                     | Profile     | Port |
| --------------- | -------------- | --------------------------- | ----------- | ---- |
| **celery-beat** | xd-celery-beat | Scheduled task scheduler    | celery-beat | -    |
| **flower**      | xd-flower      | Celery monitoring dashboard | monitoring  | 5555 |
| **nginx**       | xd-nginx       | Reverse proxy (production)  | production  | 80   |

### Service Dependencies

```
frontend ──→ backend ──┬──→ db
                       └──→ redis ──→ celery
                                 └──→ celery-beat
                                 └──→ flower
```

---

## Port Mappings

### Development Ports

| Service      | Host Port | Container Port | URL                   | Purpose                  |
| ------------ | --------- | -------------- | --------------------- | ------------------------ |
| **Frontend** | 3000      | 3000           | http://localhost:3000 | React development server |
| **Backend**  | 8000      | 8000           | http://localhost:8000 | Django API               |
| **MySQL**    | 3306      | 3306           | localhost:3306        | Database access          |
| **Redis**    | 6379      | 6379           | localhost:6379        | Cache/message broker     |

### Debug Ports

| Service            | Host Port | Container Port | Purpose                   |
| ------------------ | --------- | -------------- | ------------------------- |
| **Backend Debug**  | 5678      | 5678           | Python debugger (debugpy) |
| **Django Toolbar** | 8001      | 8001           | Django debug toolbar      |

### Monitoring Ports

| Service    | Host Port | Container Port | URL                   | Purpose           |
| ---------- | --------- | -------------- | --------------------- | ----------------- |
| **Flower** | 5555      | 5555           | http://localhost:5555 | Celery monitoring |

### Production Ports

| Service   | Host Port | Container Port | URL              | Purpose       |
| --------- | --------- | -------------- | ---------------- | ------------- |
| **Nginx** | 80        | 80             | http://localhost | Reverse proxy |

---

## Common Commands

### Daily Development

```bash
# Start development environment
make up

# Stop all services
make down

# Restart services
make down && make up

# View logs from all services
make logs

# View logs from specific service
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f db
```

### Database Operations

```bash
# Create database backup
make backup-db

# Seed database with sample data
make django-sample-data

# Run migrations
make django-migrate

# Access MySQL directly
make mysql-cli

# Reset database (DESTRUCTIVE)
make reset-db
```

### Frontend Development

```bash
# Start frontend with hot reloading
make frontend-up

# Run frontend tests
make frontend-test

# Build frontend for production
make frontend-build-prod

# Access frontend container shell
make frontend-shell
```

### Backend Development

```bash
# Access Django shell
make django-shell

# Run Django tests
make django-test

# Create Django superuser
make django-superuser

# Collect static files
make django-collectstatic
```

### Task Management (Celery)

```bash
# Start all task services (Celery + Beat + Flower)
make tasks-up

# Stop all task services
make tasks-down

# Check task service status
make tasks-status

# View Celery worker logs
make celery-logs

# Start Flower monitoring dashboard
make flower-up
```

### Code Quality

```bash
# Run all linters and formatters
make lint-all

# Format code
make format

# Security scan
make lint-security

# Check Django configuration
make django-check
```

### Monitoring & Debugging

```bash
# Comprehensive health check
make health-check

# Service status overview
make status

# Watch logs in real-time
make watch-logs        # All services
make watch-backend     # Backend only
make watch-db          # Database only
make watch-frontend    # Frontend only
```

### Cleanup

```bash
# Stop services and remove volumes
make clean

# Remove everything including images
make clean-all

# Complete environment reset
make dev-reset
```

---

## Troubleshooting

### Common Issues

#### 1. Port Already in Use

**Error:** `bind: address already in use`

**Solution:**

```bash
# Check what's using the port (example: port 8000)
lsof -i :8000
# Or on Linux
netstat -tlnp | grep :8000

# Kill the process
kill -9 <PID>

# Or change the port in docker-compose.yml
```

#### 2. Database Connection Issues

**Error:** `django.db.utils.OperationalError: (2003, "Can't connect to MySQL server")`

**Solutions:**

```bash
# Check if MySQL container is running
docker-compose ps db

# Check MySQL logs
docker-compose logs db

# Restart database service
docker-compose restart db

# Wait for database to be ready
make health-check

# Verify database credentials in .env file
```

#### 3. Frontend Build Issues

**Error:** `Module not found` or `npm install` failures

**Solutions:**

```bash
# Clear node_modules and reinstall
docker-compose down
docker volume rm xd-incentives_frontend_node_modules
make frontend-up

# Or rebuild frontend service
docker-compose build --no-cache frontend

# Check Node.js version compatibility
docker exec xd-frontend node --version
```

#### 4. Permission Denied Errors

**Error:** `Permission denied` when running scripts

**Solutions:**

```bash
# Make scripts executable
chmod +x scripts/wait-for-db.sh
chmod +x scripts/dump_database.sh
chmod +x scripts/seed_database.sh

# Check Docker daemon is running
sudo systemctl status docker  # Linux
# Restart Docker Desktop on macOS/Windows
```

#### 5. Out of Disk Space

**Error:** `no space left on device`

**Solutions:**

```bash
# Clean up Docker resources
docker system prune -af
docker volume prune -f

# Remove unused images
docker image prune -af

# Check disk usage
docker system df
```

#### 6. Container Health Check Failures

**Error:** Services showing as unhealthy

**Solutions:**

```bash
# Check health status
make health-check

# Inspect specific service health
docker inspect --format='{{json .State.Health}}' xd-backend

# View service logs for errors
docker-compose logs <service-name>

# Restart unhealthy services
docker-compose restart <service-name>
```

### Service-Specific Debugging

#### Backend (Django)

```bash
# Check Django configuration
make django-check

# Run Django in debug mode
docker exec -it xd-backend python manage.py runserver 0.0.0.0:8000 --settings=config.settings

# Check static files
make django-collectstatic

# Verify database migrations
make django-migrate
```

#### Frontend (React)

```bash
# Check Node.js and npm versions
docker exec xd-frontend node --version
docker exec xd-frontend npm --version

# Clear npm cache
docker exec xd-frontend npm cache clean --force

# Rebuild with verbose output
docker exec xd-frontend npm run build -- --verbose
```

#### Database (MySQL)

```bash
# Check MySQL status
docker exec xd-mysql mysqladmin ping -h localhost --silent

# Access MySQL command line
make mysql-cli

# Check database size
docker exec xd-mysql mysql -u testu -ptestpw -e "SELECT table_schema 'Database Name', ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) 'DB Size in MB' FROM information_schema.tables WHERE table_schema='xd_incentives_db';"
```

#### Redis

```bash
# Check Redis connection
make redis-cli

# Monitor Redis commands
docker exec xd-redis redis-cli monitor

# Check Redis memory usage
docker exec xd-redis redis-cli info memory
```

### Complete Reset Procedure

If all else fails, perform a complete environment reset:

```bash
# Warning: This will destroy all local data
make clean-all

# Remove any remaining volumes
docker volume prune -f

# Restart Docker (if needed)
# macOS/Windows: Restart Docker Desktop
# Linux: sudo systemctl restart docker

# Fresh installation
make fresh-start
```

---

## Advanced Configuration

### Custom Container Names

Override default container names:

```bash
# Example: Use custom backend container name
make django-shell BACKEND_CONTAINER=my-custom-backend
```

### Environment-Specific Configurations

#### Development

```bash
# Standard development setup
docker-compose up -d
```

#### Production

```bash
# Use production profile with nginx
docker-compose --profile production up -d
```

#### Monitoring

```bash
# Start with monitoring tools
docker-compose --profile monitoring up -d
```

### Performance Optimization

#### For Development

```bash
# Optimize file watching (macOS)
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# Use cached bind mounts for better performance
# Already configured in docker-compose.yml
```

#### For Production

```bash
# Build optimized production image
docker build -f Dockerfile.multi-stage --target production -t xd-incentives:prod .

# Use production configuration
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Resource Limits

Configure resource limits in `docker-compose.yml`:

```yaml
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 2G
        reservations:
          cpus: "0.5"
          memory: 512M
```

### Backup and Restore

#### Automated Backups

```bash
# Create backup with timestamp
make backup-db

# Schedule backups (add to crontab)
0 2 * * * cd /path/to/project && make backup-db
```

#### Restore from Backup

```bash
# Interactive restore
make restore-db

# Direct restore (example)
gunzip -c data/backup_2024_01_01.sql.gz | docker exec -i xd-mysql mysql -u testu -ptestpw xd_incentives_db
```

---

## Summary

This Docker setup provides a complete development environment for the XD Incentives application. The configuration includes:

- **7 core services** with proper dependency management
- **Comprehensive health checks** for all services
- **Development optimization** with hot reloading and debugging ports
- **Production readiness** with nginx proxy and monitoring
- **Data persistence** through Docker volumes
- **Easy management** through Make commands

For additional help, refer to:

- [CLAUDE.md](../../CLAUDE.md) - Comprehensive development guide
- [README.md](../../README.md) - Project overview and quick start
- Project logs: `make logs`
- Service status: `make status`
- Health checks: `make health-check`
