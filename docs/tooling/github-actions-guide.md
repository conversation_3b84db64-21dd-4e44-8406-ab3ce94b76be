# GitHub Actions Workflows Guide

This guide provides comprehensive documentation for the GitHub Actions workflows implemented in the XD Incentives project. Our CI/CD pipeline consists of three main workflows that handle security scanning, automated code reviews, and Claude integration.

## Table of Contents

- [Overview](#overview)
- [Workflow Architecture](#workflow-architecture)
- [Workflows](#workflows)
  - [Security Scanning](#security-scanning)
  - [Claude Code Review](#claude-code-review)
  - [Claude Integration](#claude-integration)
- [Dependencies and Scripts](#dependencies-and-scripts)
- [Secrets and Configuration](#secrets-and-configuration)
- [Troubleshooting](#troubleshooting)
- [Best Practices](#best-practices)

## Overview

The XD Incentives project uses GitHub Actions to automate:

- **Security scanning** for vulnerabilities and secrets
- **Automated code reviews** using Claude AI on pull requests
- **Integration with Monday.com** for project management
- **Interactive Claude assistance** via issue and PR comments

All workflows are designed with security, reliability, and maintainability in mind, following enterprise-grade practices for client project requirements.

## Workflow Architecture

```mermaid
graph TD
    A[Push/PR to develop] --> B{Trigger Conditions}
    B --> C[Security Scanning]
    B --> D[Claude Code Review]
    D --> E[Monday.com Integration]
    F[Issue/PR Comments with @claude] --> G[Claude Integration]
    
    C --> H[Dependency Check]
    C --> I[Secrets Scan]
    
    D --> J[AI Code Review]
    J --> K[Parse Results]
    K --> E
    E --> L[Create Monday Tasks]
    E --> M[Post PR Comment]
```

## Workflows

### Security Scanning

**File**: `.github/workflows/security.yml`

Automated security scanning that runs on multiple triggers to ensure comprehensive protection.

#### Triggers
- **Push events**: `main`, `develop` branches
- **Pull requests**: Targeting `main`, `develop` branches  
- **Scheduled**: Weekly on Mondays at 2:30 AM UTC (`30 2 * * 1`)

#### Jobs

##### 1. Dependency Vulnerability Check
- **Runner**: `ubuntu-latest`
- **Purpose**: Scans Python dependencies for known vulnerabilities
- **Tools**: Safety CLI tool
- **Output**: JSON report uploaded as artifact

**Steps**:
1. Checkout repository with full history
2. Set up Python 3.12 environment
3. Install Safety security scanner
4. Run safety check against `requirements.txt`
5. Upload results artifact (always runs, even on failures)

**Key Features**:
- Continues on failure (`|| true`) to ensure artifact upload
- Generates machine-readable JSON output
- Preserves results for audit trails

##### 2. Secrets Scanning
- **Runner**: `ubuntu-latest`
- **Purpose**: Detects committed secrets and credentials
- **Tools**: TruffleHog OSS
- **Scope**: Full repository history with focus on changes since main

**Steps**:
1. Checkout with full git history (`fetch-depth: 0`)
2. Run TruffleHog with verification enabled
3. Compare changes between `main` and `HEAD`

**Configuration**:
- `--debug`: Verbose logging for troubleshooting
- `--only-verified`: Reduces false positives by only reporting verified secrets
- Scans from `main` to `HEAD` for focused analysis

### Claude Code Review

**File**: `.github/workflows/claude-code-review.yml`

Advanced AI-powered code review system with Monday.com integration for project management.

#### Triggers
- **Pull requests**: Targeting `develop` branch
- **Events**: `opened`, `synchronize` (new commits)

#### Jobs

##### 1. Claude Review Job
- **Runner**: `ubuntu-latest`
- **Purpose**: Comprehensive AI code review with enterprise framework
- **Model**: Claude Opus 4 (`claude-opus-4-20250514`)

**Permissions**:

```yaml
contents: read          # Read repository code
pull-requests: write    # Post review comments
issues: write          # Create issue comments
id-token: write        # OIDC token exchange
actions: read          # Access CI results
```

**Review Framework**:
The workflow uses a comprehensive review framework covering:

1. **Code Quality & Implementation**
   - Logic efficiency and algorithm optimization
   - Code clarity and naming conventions
   - Consistency with team standards
   - Maintainability and future-proofing
   - DRY, YAGNI, KISS principles
   - Single Responsibility Principle

2. **Architecture & Design Patterns**
   - Design alignment with existing patterns
   - Separation of concerns
   - Component reusability
   - Future requirement support

3. **Security & Compliance**
   - OWASP Top 10 vulnerability checks
   - Data handling and validation
   - Authorization/authentication review
   - SOC 2, PCI, GDPR, A11y compliance

4. **Performance & Scalability**
   - Performance bottleneck identification
   - Database query optimization
   - Frontend performance impact
   - Scalability under load

5. **Testing & Quality Assurance** (Currently disabled for development phase)
   - Test coverage adequacy
   - Test quality assessment
   - Edge case identification
   - Error handling evaluation

6. **Documentation & Communication**
   - Code documentation review
   - PR description assessment
   - Breaking change identification
   - Migration considerations

**Feedback Categories**:
- **Must Fix**: Blocking issues (security, breaking changes, critical performance)
- **Should Fix**: Strong recommendations (significant quality impact)
- **Consider Fixing**: Suggestions (minor improvements)
- **Praise & Recognition**: Acknowledgment of good practices

##### 2. Monday.com Integration Job
- **Runner**: `ubuntu-latest`
- **Dependencies**: Requires successful Claude review
- **Trigger**: Only runs when Claude identifies "Must Fix" or "Should Fix" issues

**Conditional Logic**:

```yaml
if: success() && (contains(needs.claude-review.outputs.result, 'Must Fix') || contains(needs.claude-review.outputs.result, 'Should Fix'))
```

**Workflow Steps**:

1. **Parse Claude Output**
   - Location: `.github/scripts/claude-output-parser.js`
   - Purpose: Extract actionable issues from Claude's review
   - Error Handling: Graceful failure with warning messages

2. **Create Monday.com Tasks**
   - Location: `.github/scripts/monday-api-client.js`
   - Purpose: Convert issues to Monday.com project management tasks
   - Environment: Uses `MONDAY_API_TOKEN` secret

3. **Store Results**
   - Captures task creation results and statistics
   - Provides summary metrics (total, successful, failed, success rate)
   - Logs completion status regardless of success/failure

4. **Comment on PR**
   - Location: `.github/scripts/pr-comment-generator.js`
   - Purpose: Provide visibility into Monday.com integration results
   - Content: Task creation summary and links

### Claude Integration

**File**: `.github/workflows/claude.yml`

Interactive Claude AI assistant triggered by user mentions in issues and PRs.

#### Triggers
- **Issue comments**: Comments containing `@claude`
- **PR review comments**: Review comments containing `@claude`
- **PR reviews**: Review submissions containing `@claude`
- **Issues**: New issues with `@claude` in title or body

#### Conditional Logic

```yaml
if: |
  (github.event_name == 'issue_comment' && contains(github.event.comment.body, '@claude')) ||
  (github.event_name == 'pull_request_review_comment' && contains(github.event.comment.body, '@claude')) ||
  (github.event_name == 'pull_request_review' && contains(github.event.review.body, '@claude')) ||
  (github.event_name == 'issues' && (contains(github.event.issue.body, '@claude') || contains(github.event.issue.title, '@claude')))
```

#### Configuration Options
The workflow supports extensive customization:

- **Model Selection**: Defaults to Claude Sonnet 4, can be changed to Claude Opus 4
- **Trigger Phrase**: Customizable (default: `@claude`)
- **Assignee Trigger**: Trigger when specific user is assigned
- **Allowed Tools**: Restrict Claude's available commands
- **Custom Instructions**: Project-specific behavior guidelines
- **Environment Variables**: Custom environment for Claude

**Example Customizations**:

```yaml
# model: "claude-opus-4-20250514"
# trigger_phrase: "/claude"
# assignee_trigger: "claude-bot"
# allowed_tools: "Bash(npm install),Bash(npm run build),Bash(npm run test:*),Bash(npm run lint:*)"
```

## Dependencies and Scripts

### Script Architecture

The workflows rely on Node.js scripts located in `.github/scripts/`:

#### Core Scripts
- **`claude-output-parser.js`**: Parses Claude review output into structured data
- **`monday-api-client.js`**: Creates Monday.com tasks via API
- **`pr-comment-generator.js`**: Generates PR comments with integration results

#### Testing Scripts
- **`test-claude-parser.js`**: Unit tests for Claude output parsing
- **`quick-test.js`**: Quick integration tests
- **`test-monday-api.js`**: Monday.com API integration tests
- **`test-pr-comment-generator.js`**: PR comment generation tests

#### Package Configuration
**File**: `.github/scripts/package.json`

```json
{
  "name": "monday-integration-scripts",
  "version": "1.0.0",
  "description": "GitHub Actions scripts for Monday.com integration with Claude code reviews",
  "scripts": {
    "test": "npm run test:parser && npm run test:api && npm run test:comments",
    "test:parser": "node test-claude-parser.js",
    "test:api": "node test-monday-api.js",
    "test:comments": "node test-pr-comment-generator.js"
  }
}
```

### External Actions

#### anthropics/claude-code-action@beta
Advanced Claude AI integration with features:
- Multi-model support (Sonnet 4, Opus 4)
- OIDC token authentication
- Customizable permissions and tools
- Project-specific instructions
- CI/CD result access

#### Standard Actions
- **actions/checkout@v4**: Repository checkout with configurable depth
- **actions/setup-python@v4**: Python environment setup
- **actions/setup-node@v4**: Node.js environment for scripts
- **actions/upload-artifact@v4**: Artifact preservation
- **actions/github-script@v7**: GitHub API interactions
- **trufflesecurity/trufflehog@main**: Secrets detection

## Secrets and Configuration

### Required Secrets

#### ANTHROPIC_API_KEY
- **Purpose**: Claude AI API authentication
- **Scope**: Used in both code review and integration workflows
- **Type**: API token
- **Security**: Stored as GitHub repository secret

#### MONDAY_API_TOKEN
- **Purpose**: Monday.com API authentication
- **Scope**: Task creation and project management integration
- **Type**: API token
- **Security**: Stored as GitHub repository secret

### Environment Variables

Workflows use environment variables for:
- **API Configuration**: Token passing to scripts
- **Data Transfer**: Passing parsed results between jobs
- **Metadata**: PR numbers, URLs, timestamps

**Example Usage**:

```yaml
env:
  CLAUDE_OUTPUT: ${{ needs.claude-review.outputs.result }}
  MONDAY_API_TOKEN: ${{ secrets.MONDAY_API_TOKEN }}
  PR_NUMBER: ${{ github.event.pull_request.number }}
```

## Troubleshooting

### Common Issues

#### 1. Security Scan Failures
**Symptoms**: Safety check fails with dependency issues
**Solutions**:
- Verify `requirements.txt` is up to date
- Check Python version compatibility (3.12)
- Review safety database updates

#### 2. Claude Review Not Triggering
**Symptoms**: PR opened but no Claude review
**Solutions**:
- Verify PR targets `develop` branch
- Check `ANTHROPIC_API_KEY` secret configuration
- Ensure proper permissions are set

#### 3. Monday.com Integration Failures
**Symptoms**: Tasks not created, integration errors
**Solutions**:
- Validate `MONDAY_API_TOKEN` secret
- Check Monday.com API rate limits
- Review script logs in workflow output
- Verify JSON parsing in Claude output

#### 4. Secret Detection False Positives
**Symptoms**: TruffleHog reports false secrets
**Solutions**:
- Review with `--only-verified` flag (already enabled)
- Add specific exclusions if needed
- Validate actual secrets aren't committed

### Debugging Workflows

#### Enable Debug Mode
Add to workflow environment:

```yaml
env:
  ACTIONS_STEP_DEBUG: true
  ACTIONS_RUNNER_DEBUG: true
```

#### Access Logs
- GitHub Actions tab in repository
- Specific workflow run details
- Individual job and step logs
- Artifact downloads for reports

#### Script Testing

```bash
cd .github/scripts
npm test                    # Run all tests
npm run test:parser        # Test Claude output parsing
npm run test:api          # Test Monday.com API
npm run test:comments     # Test PR comment generation
```

## Best Practices

### Security

#### Secret Management
- Use GitHub repository secrets for all API tokens
- Never commit secrets to repository
- Rotate secrets regularly
- Use least-privilege access principles

#### Permissions
- Grant minimal required permissions to workflows
- Use `id-token: write` for OIDC when possible
- Separate read and write permissions by job

### Reliability

#### Error Handling
- Use `|| true` for non-critical failures
- Implement graceful degradation
- Provide meaningful error messages
- Always upload artifacts for debugging

#### Dependencies
- Pin action versions (e.g., `@v4`, not `@latest`)
- Lock Node.js versions in setup actions
- Use specific tool versions when possible

### Maintainability

#### Documentation
- Comment complex workflow logic
- Document custom scripts thoroughly
- Maintain this guide with changes
- Include examples in workflows

#### Testing
- Test scripts independently
- Validate workflow changes in feature branches
- Use draft PRs for workflow testing
- Monitor workflow success rates

### Performance

#### Optimization
- Use shallow checkouts when possible (`fetch-depth: 1`)
- Cache dependencies when appropriate
- Parallelize independent jobs
- Set appropriate timeouts

#### Resource Management
- Use `ubuntu-latest` for consistency
- Monitor workflow execution times
- Optimize script performance
- Clean up temporary files

### Monitoring

#### Success Metrics
- Track workflow success rates
- Monitor security scan results
- Review Claude review quality
- Measure Monday.com integration success

#### Alerting
- Set up notifications for workflow failures
- Monitor secret expiration
- Track API rate limit usage
- Review security scan trends

---

## Conclusion

This GitHub Actions setup provides a comprehensive CI/CD pipeline that enhances code quality, security, and project management. The integration of Claude AI with Monday.com creates a powerful workflow that automates routine tasks while maintaining high standards for enterprise client work.

Regular maintenance, monitoring, and updates to this documentation will ensure the continued effectiveness of these workflows as the project evolves.

For questions or issues with these workflows, refer to the troubleshooting section or contact the development team.