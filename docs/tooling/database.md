# XD Incentives Database Seeding & Management

Comprehensive database seeding workflow for local development with Docker integration, multiple seeding options, and advanced backup management.

## 🚀 Quick Start

### Option 1: Using Make Commands (Recommended)
```bash
# Quick start - build, start, and seed with full data
make quick-start

# Fresh start - complete reset and clean installation
make fresh-start

# Seed existing running containers
make seed-full        # Full dump + sample data
make seed-fresh       # Reset DB + seed
make seed-minimal     # Schema only + sample data
```

### Option 2: Direct Script Usage
```bash
# Navigate to data directory
cd data

# Seed with full data and backup
./seed_database.sh --full --backup --sample-data

# Fresh installation
./seed_database.sh --fresh --backup --sample-data
```

## 📁 Directory Structure

```
data/
├── README.md                     # This documentation
├── database_dump_2025_07_18.sql.gz # Primary seed data
├── dump_database.sh              # Basic dump creation
├── seed_database.sh              # Advanced seeding script
├── backup_manager.sh             # Advanced backup management
├── backups/                      # Automated backups
│   ├── daily_YYYY_MM_DD.sql.gz
│   ├── weekly_YYYY_WNN.sql.gz
│   └── monthly_YYYY_MM.sql.gz
└── archives/                     # Long-term archives
    └── archived_backups.sql.gz
```

## 🌱 Seeding Options

### Seeding Types

| Type | Description | Use Case |
|------|-------------|----------|
| `--full` | Complete database with all data | Production-like development |
| `--minimal` | Schema only | Clean slate development |
| `--test` | Test data set | Automated testing |
| `--fresh` | Reset + restore | Complete clean start |

### Sample Data Options
- `--sample-data`: Adds Django management command data
- `--backup`: Creates backup before seeding
- `--force`: Skip confirmation prompts

### Examples
```bash
# Full development setup
./seed_database.sh --full --backup --sample-data

# Clean development start
./seed_database.sh --fresh --backup --sample-data --force

# Testing environment
./seed_database.sh --test --sample-data --force

# Minimal schema for custom development
./seed_database.sh --minimal --sample-data
```

## 💾 Backup Management

### Basic Backup Operations
```bash
# Create backup with timestamp
make backup-db

# Using backup manager directly
./backup_manager.sh --create-daily
./backup_manager.sh --create-weekly
./backup_manager.sh --create-monthly
```

### Advanced Backup Management
```bash
# Auto backup based on schedule
./backup_manager.sh --auto

# List all backups
./backup_manager.sh --list

# Interactive restoration
./backup_manager.sh --restore-interactive

# Verify backup integrity
./backup_manager.sh --verify backup_file.sql.gz

# Archive backup for long-term storage
./backup_manager.sh --archive backup_file.sql.gz
```

### Backup Retention Policy
- **Daily**: 7 days retention
- **Weekly**: 4 weeks retention  
- **Monthly**: 12 months retention
- **Archives**: Manual management

## 🐳 Docker Integration

### Main Services (docker-compose.yml)
```bash
# Start main development services
docker-compose up -d --build

# Access services
# Web: http://localhost:8000
# MySQL: localhost:3306
# Redis: localhost:6379
```

### Seeding Services (docker-compose.seed.yml)
```bash
# Full seeding using Docker
docker-compose -f docker-compose.seed.yml up seed-full

# Fresh installation using Docker
docker-compose -f docker-compose.seed.yml up seed-fresh

# Interactive seeding environment
docker-compose -f docker-compose.seed.yml up -d seed-interactive
docker exec -it xd-seed-interactive sh
```

## 🔧 Make Commands Reference

### Development Commands
```bash
make dev-up           # Start development services
make dev-down         # Stop development services
make dev-logs         # Show service logs
make dev-build        # Build all services
```

### Database Seeding
```bash
make seed-full        # Full database seed
make seed-fresh       # Fresh installation
make seed-minimal     # Minimal schema
make seed-test        # Test data
make seed-interactive # Interactive mode
```

### Database Management
```bash
make backup-db        # Create backup
make restore-db       # Interactive restore
make reset-db         # Reset database (DESTRUCTIVE)
make list-dumps       # List available dumps
```

### Django Commands
```bash
make django-shell     # Django shell
make django-migrate   # Run migrations
make django-test      # Run tests
make django-superuser # Create superuser
```

### Utility Commands
```bash
make clean           # Clean Docker resources
make status          # Show service status
make health-check    # Health check all services
make quick-start     # Complete setup
make fresh-start     # Fresh installation
```

## 🎛️ Configuration

### Database Configuration
- **Database Name**: xd_incentives_db
- **Username**: testu
- **Password**: testpw
- **Host**: localhost (Docker: db)
- **Port**: 3306

### Container Names
- **MySQL**: xd-mysql
- **Backend**: xd-backend
- **Redis**: xd-redis
- **Celery**: xd-celery

### Default Credentials
- **Admin User**: admin / admin123
- **Database User**: testu / testpw
- **Database Root**: root / rootpw

## 🧪 Testing the Workflow

### Complete Workflow Test
```bash
# Test complete seeding workflow
make test-workflow

# Manual testing steps
make dev-build
make dev-up
make backup-db
make seed-minimal
make seed-full
make health-check
```

### Development Reset
```bash
# Reset everything for clean development
make dev-reset

# Or step by step
make dev-down
make clean
make dev-build
make dev-up
make django-migrate
make django-sample-data
```

## 📊 Monitoring & Health Checks

### Service Health
```bash
# Check all service health
make health-check

# Watch logs
make watch-logs      # All services
make watch-web       # Web service only
make watch-db        # Database only
```

### Backup Status
```bash
# Show backup statistics
./backup_manager.sh --status

# Show configuration
./backup_manager.sh --config

# Verify backup integrity
./backup_manager.sh --verify backup_file.sql.gz
```

## 🚨 Troubleshooting

### Common Issues

**Services not starting:**
```bash
make clean
make dev-build
make dev-up
```

**Database connection issues:**
```bash
# Check container status
docker ps
make health-check

# Restart database
docker-compose restart db
```

**Seeding failures:**
```bash
# Check logs
make dev-logs

# Manual container check
docker exec -it xd-mysql mysql -u testu -ptestpw -e "SHOW DATABASES;"
```

**Backup/Restore issues:**
```bash
# Verify backup integrity
./backup_manager.sh --verify backup_file.sql.gz

# Check container status
docker ps | grep xd-mysql
```

### Emergency Recovery
```bash
# Complete system reset
make clean-all
make fresh-start

# Restore from backup
./backup_manager.sh --restore-interactive
```

## 📝 Development Workflow Examples

### New Developer Setup
```bash
git clone <repository>
cd xd-incentives
make fresh-start
# Access: http://localhost:8000 (admin/admin123)
```

### Daily Development
```bash
# Start work
make dev-up

# Make changes...

# Create backup before major changes
make backup-db

# Reset if needed
make seed-fresh
```

### Before Deployment Testing
```bash
# Create production-like environment
make seed-full
make django-test
make health-check
```

## 🔄 Sample Data Details

### Django Management Commands (Included with --sample-data)
- `setup_member_types`: Configure member types and permissions
- `create_sample_data`: Generate sample members and data
- `setup_member_hierarchy`: Create organizational relationships
- `setup_teams_multi`: Setup teams with multiple memberships
- `create_sample_terms`: Generate terms and conditions
- `create_sample_privacy`: Create privacy policies
- `create_sample_communications`: Sample communication templates
- `create_sample_password_resets`: Password reset examples

### Available Data Sets
1. **Full Dump**: Complete production-like data
2. **Minimal**: Schema + basic configuration
3. **Test**: Focused test data for automated testing
4. **Custom**: Django management commands only

## 📚 Additional Resources

- **Main Documentation**: [CLAUDE.md](../CLAUDE.md)
- **Docker Compose**: [docker-compose.yml](../docker-compose.yml)
- **Django Settings**: [config/settings/](../config/settings/)