# Clerk Authentication Implementation Guide for XD Incentives Platform

## Executive Summary

This guide provides a comprehensive approach for integrating Clerk authentication into the XD Incentives platform, replacing the current custom authentication system with <PERSON>'s robust, scalable solution. The implementation will leverage Clerk's React components and hooks on the frontend, and the Python SDK with custom Django integration on the backend.

## Architecture Overview

### High-Level Authentication Flow

```mermaid
flowchart TB
    subgraph "Client Layer"
        A[React SPA]
        B[Clerk React SDK]
        C[TanStack Router]
    end

    subgraph "Clerk Cloud"
        D[Clerk FAPI<br/>Frontend API]
        E[Clerk Auth Service]
        F[JWKS Endpoint]
        G[User Management]
    end

    subgraph "Backend Layer"
        H[Django API]
        I[Clerk Middleware]
        J[JWT Verifier]
        K[User Sync Service]
    end

    subgraph "Data Layer"
        L[(MySQL DB)]
        M[(Redis Cache)]
    end

    A --> B
    B --> C
    B <--> D
    D <--> E
    E --> F
    E --> G

    A -->|API Requests<br/>Bearer Token| H
    H --> I
    I --> J
    J -->|Verify| F
    I --> K
    K <--> L
    J --> M

    style A fill:#e1f5fe
    style H fill:#fff3e0
    style L fill:#f3e5f5
    style D fill:#e8f5e9
```

### Key Components

- **Frontend**: React with Clerk React SDK
- **Backend**: Django with Clerk Python SDK
- **Token Management**: Hybrid stateful/stateless approach
- **Session Duration**: 60-second session tokens, 7-day client tokens
- **Security**: JWKS verification, HTTPS-only cookies, CSRF protection

### API Request Flow with Clerk

```mermaid
sequenceDiagram
    participant C as Client (React)
    participant CS as Clerk SDK
    participant A as API Client
    participant D as Django API
    participant MW as Clerk Middleware
    participant B as Backend Service
    participant DB as Database

    C->>CS: useAuth()
    CS-->>C: Session Info
    C->>CS: getToken()
    CS-->>C: JWT Token

    C->>A: Prepare Request
    A->>A: Add Bearer Token
    A->>D: HTTP Request

    D->>MW: Process Request
    MW->>MW: Extract Token
    MW->>MW: Verify JWT
    MW->>MW: Validate Claims

    alt Token Valid
        MW->>DB: Get/Create User
        DB-->>MW: User Object
        MW->>B: Authorized Request
        B->>DB: Business Logic
        DB-->>B: Data
        B-->>D: Response
        D-->>C: Success Response
    else Token Invalid
        MW-->>D: 401 Unauthorized
        D-->>C: Auth Error
        C->>CS: Refresh Token
    end
```

### Detailed Token Management Flow

```mermaid
sequenceDiagram
    participant U as User
    participant R as React App
    participant CS as Clerk SDK
    participant CF as Clerk FAPI
    participant CA as Clerk Auth
    participant D as Django Backend
    participant DB as MySQL

    U->>R: Access Application
    R->>CS: Initialize Clerk
    CS->>CF: Check Session

    alt No Valid Session
        CF-->>R: Redirect to Sign In
        U->>R: Enter Credentials
        R->>CS: Submit Login
        CS->>CA: Authenticate
        CA-->>CS: Generate Tokens
        CS-->>R: Session Created
    end

    CS->>CF: Get Session Token
    CF-->>CS: Session Token (60s TTL)
    CS->>CF: Get Client Token
    CF-->>CS: Client Token (7 days TTL)

    R->>D: API Request + Bearer Token
    D->>D: Verify JWT
    D->>CA: Validate with JWKS
    CA-->>D: Token Valid
    D->>DB: Get/Create User
    DB-->>D: User Data
    D-->>R: API Response

    Note over CS,CF: Auto-refresh every 50s
    CS->>CF: Refresh Session Token
    CF-->>CS: New Session Token
```

### Organization & Permissions Flow

```mermaid
flowchart TB
    subgraph "Clerk Organizations"
        A[Organization]
        B[Members]
        C[Roles]
        D[Invitations]
    end

    subgraph "Django Permissions"
        E[MemberType]
        F[Permissions JSON]
        G[Page Access]
        H[Feature Flags]
    end

    subgraph "Synchronization"
        I[Webhook Events]
        J[Metadata Sync]
        K[Role Mapping]
    end

    subgraph "Access Control"
        L[Route Guards]
        M[API Permissions]
        N[UI Elements]
        O[Data Filtering]
    end

    A --> B
    A --> C
    B --> D

    C -->|Map| K
    K --> E
    E --> F
    E --> G
    E --> H

    A -->|Webhook| I
    I --> J
    J --> K

    F --> L
    G --> M
    H --> N
    F --> O
```

### Backend Authentication Components

```mermaid
classDiagram
    class ClerkAuthenticationMiddleware {
        +process_request(request)
        +extract_token(request)
        +verify_token(token)
        +set_user(request, user)
        +set_organization(request, org)
    }

    class ClerkBackend {
        +authenticate(request, token)
        +verify_jwt(token)
        +get_jwks_key(kid)
        +create_or_update_user(clerk_id)
        +map_roles(clerk_role)
    }

    class TokenVerification {
        +parse_jwt_header(token)
        +fetch_public_key(kid)
        +verify_rs256_signature()
        +validate_claims(iss, aud, exp)
        +extract_user_data()
    }

    class WebhookHandler {
        +verify_signature(headers, body)
        +process_event(event_type, data)
        +handle_user_created(data)
        +handle_user_updated(data)
        +handle_org_membership(data)
    }

    class Member {
        +clerk_id: string
        +email: string
        +username: string
        +member_type: MemberType
        +organization_id: string
        +sync_with_clerk()
    }

    class MemberType {
        +name: string
        +permissions: JSON
        +page_access: JSON
        +feature_flags: JSON
        +map_from_clerk_role()
    }

    ClerkAuthenticationMiddleware --> ClerkBackend
    ClerkBackend --> TokenVerification
    ClerkBackend --> Member
    Member --> MemberType
    WebhookHandler --> Member
    WebhookHandler --> MemberType
```

### Frontend Component Integration Architecture

```mermaid
flowchart LR
    subgraph "Frontend Components"
        A[ClerkProvider]
        B[SignIn Component]
        C[SignUp Component]
        D[UserButton]
        E[Protected Routes]
        F[Organization Switcher]
    end

    subgraph "React Hooks"
        G[useAuth]
        H[useUser]
        I[useOrganization]
        J[useSession]
    end

    subgraph "Backend Services"
        K[ClerkAuthBackend]
        L[ClerkMiddleware]
        M[WebhookHandler]
        N[UserSyncService]
    end

    A --> B
    A --> C
    A --> D
    A --> E
    A --> F

    E --> G
    D --> H
    F --> I
    G --> J

    G -.->|JWT Token| K
    K --> L
    M --> N
    N -->|Sync| K
```

### User Data Synchronization Flow

```mermaid
flowchart TB
    subgraph "Clerk Events"
        A[user.created]
        B[user.updated]
        C[user.deleted]
        D[session.created]
        E[org.member.created]
    end

    subgraph "Webhook Processing"
        F[Verify Signature]
        G[Parse Event]
        H[Route Handler]
    end

    subgraph "Django Models"
        I[Member Model]
        J[MemberType]
        K[MemberHierarchy]
        L[Team/MemberTeam]
    end

    subgraph "Data Operations"
        M[Create User]
        N[Update User]
        O[Deactivate User]
        P[Update Permissions]
        Q[Sync Organization]
    end

    A --> F
    B --> F
    C --> F
    D --> F
    E --> F

    F --> G
    G --> H

    H -->|user.created| M
    H -->|user.updated| N
    H -->|user.deleted| O
    H -->|session.created| P
    H -->|org.member.created| Q

    M --> I
    N --> I
    O --> I
    P --> J
    Q --> K
    Q --> L
```

### Organization & Role Synchronization

```mermaid
flowchart TB
    subgraph "Clerk Organization"
        A[Organization<br/>org_2xyz789] --> B[Roles]
        B --> C[org:admin]
        B --> D[org:manager]
        B --> E[org:member]
        B --> F[org:viewer]
        B --> G[org:sales_rep]
    end

    subgraph "Role Mapping"
        C --> H[Admin<br/>MemberType]
        D --> I[Manager<br/>MemberType]
        E --> J[Regular<br/>MemberType]
        F --> K[Viewer<br/>MemberType]
        G --> L[Sales Rep<br/>MemberType]
    end

    subgraph "Django Permissions"
        H --> M[Full Access<br/>member.manage]
        I --> N[Team Management<br/>reports.view]
        J --> O[Basic Access<br/>view own data]
        K --> P[Read-only<br/>no write]
        L --> Q[Sales Features<br/>customer.access]
    end

    subgraph "Feature Flags"
        M --> R[reports<br/>analytics<br/>export<br/>bulk_ops]
        N --> S[reports<br/>analytics]
        O --> T[basic<br/>features]
        P --> U[view_only]
        Q --> V[sales<br/>customer]
    end
```

### Error Handling & Recovery

```mermaid
flowchart LR
    subgraph "Error Types"
        A[Auth Failures]
        B[Token Expired]
        C[Network Errors]
        D[Webhook Failures]
        E[Sync Conflicts]
    end

    subgraph "Recovery Actions"
        F[Auto Retry]
        G[Token Refresh]
        H[Fallback Auth]
        I[Queue Webhook]
        J[Manual Sync]
    end

    subgraph "User Experience"
        K[Loading States]
        L[Error Messages]
        M[Redirect Login]
        N[Offline Mode]
    end

    A --> F
    B --> G
    C --> H
    D --> I
    E --> J

    F --> K
    G --> K
    H --> L
    I --> N
    J --> M
```

### Security Layer Architecture

```mermaid
flowchart TB
    subgraph "Security Measures"
        A[HTTPS/TLS]
        B[CSRF Protection]
        C[CORS Policy]
        D[Rate Limiting]
        E[CSP Headers]
    end

    subgraph "Token Security"
        F[HttpOnly Cookies]
        G[Secure Flag]
        H[SameSite Strict]
        I[Short TTL<br/>60 seconds]
        J[Auto Refresh<br/>50 seconds]
    end

    subgraph "Verification"
        K[JWT Signature]
        L[JWKS Rotation]
        M[Webhook HMAC]
        N[Token Claims]
    end

    subgraph "Audit & Monitoring"
        O[Session Logs]
        P[Auth Events]
        Q[Failed Attempts]
        R[Webhook Events]
    end

    A --> F
    B --> G
    C --> H
    D --> I
    E --> J

    F --> K
    G --> L
    H --> M
    I --> N

    K --> O
    L --> P
    M --> Q
    N --> R
```

### Security Headers Configuration

```mermaid
graph TB
    subgraph "Content Security Policy"
        A[CSP Headers] --> B[script-src]
        B --> B1[self]
        B --> B2[clerk.com]
        B --> B3[*.clerk.accounts.dev]

        A --> C[connect-src]
        C --> C1[self]
        C --> C2[clerk.com]
        C --> C3[wss://*.clerk.accounts.dev]

        A --> D[frame-src]
        D --> D1[clerk.com]
        D --> D2[*.clerk.accounts.dev]

        A --> E[img-src]
        E --> E1[self]
        E --> E2[img.clerk.com]
        E --> E3[images.clerk.dev]
    end

    subgraph "CORS Configuration"
        F[CORS Headers] --> G[Allow Origins]
        G --> G1[localhost:3000]
        G --> G2[production.com]

        F --> H[Allow Headers]
        H --> H1[authorization]
        H --> H2[x-clerk-session-id]
        H --> H3[x-clerk-organization-id]

        F --> I[Allow Credentials: true]
    end

    subgraph "Security Middleware"
        J[Security Headers] --> K[X-Frame-Options: DENY]
        J --> L[X-Content-Type: nosniff]
        J --> M[X-XSS-Protection: 1]
        J --> N[HSTS: max-age=********]
        J --> O[Referrer-Policy: strict-origin]
    end
```

###

## Phase 1: Environment Setup

### 1.1 Clerk Account Configuration

1. Create Clerk application at <https://dashboard.clerk.com>
2. Configure authentication methods:
   - Email/Password
   - SMS/Phone authentication
3. Set up development and production instances
4. Configure redirect URLs:

   ```
   Development: http://localhost:3000/*
   Production: https://xd-incentives.com/*
   ```

### 1.2 Environment Variables

```bash
# Frontend (.env)
VITE_CLERK_PUBLISHABLE_KEY=pk_test_xxxxx
VITE_CLERK_SIGN_IN_URL=/sign-in
VITE_CLERK_SIGN_UP_URL=/sign-up
VITE_CLERK_AFTER_SIGN_IN_URL=/dashboard
VITE_CLERK_AFTER_SIGN_UP_URL=/onboarding

# Backend (.env)
CLERK_SECRET_KEY=sk_test_xxxxx
CLERK_PUBLISHABLE_KEY=pk_test_xxxxx
CLERK_JWT_ISSUER=https://your-app.clerk.accounts.dev
CLERK_WEBHOOK_SECRET=whsec_xxxxx
```

## Phase 2: Frontend Implementation

### 2.1 Package Installation

```bash
cd frontend
npm install @clerk/clerk-react @clerk/themes
```

### 2.2 Main Application Setup

```typescript
// frontend/src/main.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import { ClerkProvider } from '@clerk/clerk-react';
import { RouterProvider } from '@tanstack/react-router';
import { router } from './router';

const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

if (!PUBLISHABLE_KEY) {
  throw new Error("Missing Clerk Publishable Key");
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ClerkProvider
      publishableKey={PUBLISHABLE_KEY}
      appearance={{
        baseTheme: 'light',
        variables: {
          colorPrimary: '#1e40af',
          fontFamily: 'Inter, system-ui, sans-serif',
        },
        elements: {
          formButtonPrimary: 'bg-blue-600 hover:bg-blue-700',
          card: 'shadow-xl',
        }
      }}
    >
      <RouterProvider router={router} />
    </ClerkProvider>
  </React.StrictMode>
);
```

### 2.3 Authentication Components

```typescript
// frontend/src/components/auth/ProtectedRoute.tsx
import { useAuth } from '@clerk/clerk-react';
import { Navigate, Outlet } from '@tanstack/react-router';
import { Loading } from '@/components/ui/Loading';

export function ProtectedRoute() {
  const { isLoaded, isSignedIn } = useAuth();

  if (!isLoaded) {
    return <Loading />;
  }

  if (!isSignedIn) {
    return <Navigate to="/sign-in" />;
  }

  return <Outlet />;
}
```

```typescript
// frontend/src/components/auth/UserMenu.tsx
import { UserButton, useUser } from '@clerk/clerk-react';

export function UserMenu() {
  const { user } = useUser();

  return (
    <div className="flex items-center gap-4">
      <span className="text-sm text-gray-700">
        {user?.firstName} {user?.lastName}
      </span>
      <UserButton
        afterSignOutUrl="/"
        appearance={{
          elements: {
            avatarBox: 'h-10 w-10'
          }
        }}
      />
    </div>
  );
}
```

### 2.4 Authentication Pages

```typescript
// frontend/src/routes/sign-in.tsx
import { SignIn } from '@clerk/clerk-react';

export default function SignInPage() {
  return (
    <div className="flex min-h-screen items-center justify-center">
      <SignIn
        path="/sign-in"
        routing="path"
        signUpUrl="/sign-up"
        redirectUrl="/dashboard"
      />
    </div>
  );
}
```

```typescript
// frontend/src/routes/sign-up.tsx
import { SignUp } from '@clerk/clerk-react';

export default function SignUpPage() {
  return (
    <div className="flex min-h-screen items-center justify-center">
      <SignUp
        path="/sign-up"
        routing="path"
        signInUrl="/sign-in"
        redirectUrl="/onboarding"
      />
    </div>
  );
}
```

### 2.5 API Integration with Authentication

```typescript
// frontend/src/services/api-client.ts
import { useAuth } from "@clerk/clerk-react";

export class ApiClient {
  private baseUrl: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_URL || "http://localhost:8000";
  }

  async request(endpoint: string, options: RequestInit = {}) {
    const { getToken } = useAuth();
    const token = await getToken();

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        ...options.headers,
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }

    return response.json();
  }
}
```

### 2.6 React Hooks for Authentication

```typescript
// frontend/src/hooks/useClerkAuth.ts
import { useAuth, useUser, useOrganization } from "@clerk/clerk-react";
import { useEffect, useState } from "react";

export function useClerkAuth() {
  const { isLoaded, isSignedIn, sessionId } = useAuth();
  const { user } = useUser();
  const { organization } = useOrganization();
  const [permissions, setPermissions] = useState<string[]>([]);

  useEffect(() => {
    if (user) {
      // Sync user metadata with backend
      syncUserWithBackend(user);

      // Extract custom permissions from public metadata
      const userPermissions =
        (user.publicMetadata?.permissions as string[]) || [];
      setPermissions(userPermissions);
    }
  }, [user]);

  const hasPermission = (permission: string) => {
    return permissions.includes(permission);
  };

  const hasRole = (role: string) => {
    const userRole = user?.publicMetadata?.role as string;
    return userRole === role;
  };

  return {
    isLoaded,
    isSignedIn,
    user,
    organization,
    sessionId,
    hasPermission,
    hasRole,
    permissions,
  };
}

async function syncUserWithBackend(user: any) {
  try {
    await fetch("/api/clerk/sync", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        clerkId: user.id,
        email: user.emailAddresses[0]?.emailAddress,
        firstName: user.firstName,
        lastName: user.lastName,
        imageUrl: user.imageUrl,
      }),
    });
  } catch (error) {
    console.error("Failed to sync user with backend:", error);
  }
}
```

## Phase 3: Backend Implementation

### 3.1 Package Installation

```bash
cd backend
pip install clerk-backend-api pyjwt cryptography
```

### 3.2 Django Settings Configuration

```python
# backend/config/settings.py
import os
from pathlib import Path

# Clerk Configuration
CLERK_SECRET_KEY = os.environ.get('CLERK_SECRET_KEY')
CLERK_PUBLISHABLE_KEY = os.environ.get('CLERK_PUBLISHABLE_KEY')
CLERK_JWT_ISSUER = os.environ.get('CLERK_JWT_ISSUER')
CLERK_WEBHOOK_SECRET = os.environ.get('CLERK_WEBHOOK_SECRET')

# Add Clerk middleware
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'apps.member.middleware.ClerkAuthenticationMiddleware',  # Add this
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# Authentication backends
AUTHENTICATION_BACKENDS = [
    'apps.member.backends.ClerkBackend',
    'django.contrib.auth.backends.ModelBackend',  # Fallback
]

# CORS configuration for Clerk
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://localhost:5173",
    "https://your-clerk-domain.clerk.accounts.dev",
]

CORS_ALLOW_CREDENTIALS = True
```

### 3.3 Enhanced Clerk Authentication Backend

```python
# backend/apps/member/backends.py
import logging
from typing import Optional, Dict, Any
import jwt
import requests
from django.contrib.auth import get_user_model
from django.contrib.auth.backends import BaseBackend
from django.conf import settings
from django.core.cache import cache
from clerk_backend_api import Clerk
from clerk_backend_api.models import User as ClerkUser

User = get_user_model()
logger = logging.getLogger(__name__)

class ClerkBackend(BaseBackend):
    """
    Enhanced Clerk authentication backend with caching and error handling
    """

    def __init__(self):
        self.clerk = Clerk(bearer_auth=settings.CLERK_SECRET_KEY)
        self._jwks_cache_key = 'clerk_jwks'
        self._jwks_cache_timeout = 3600  # 1 hour

    def authenticate(self, request, token: str = None) -> Optional[User]:
        """
        Authenticate user via Clerk JWT token
        """
        if not token:
            # Extract token from Authorization header
            auth_header = request.META.get('HTTP_AUTHORIZATION', '')
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
            else:
                return None

        try:
            # Verify and decode the JWT token
            payload = self._verify_token(token)
            if not payload:
                return None

            # Get or create Django user
            user = self._get_or_create_user(payload)

            # Update user session
            self._update_user_session(request, user, payload)

            return user

        except Exception as e:
            logger.error(f"Clerk authentication failed: {e}")
            return None

    def _verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Verify JWT token with caching for JWKS
        """
        try:
            # Decode without verification to get headers
            unverified = jwt.decode(token, options={"verify_signature": False})

            # Get JWKS with caching
            jwks = self._get_jwks()
            if not jwks:
                return None

            # Find matching key
            kid = jwt.get_unverified_header(token).get('kid')
            key = next((k for k in jwks['keys'] if k['kid'] == kid), None)

            if not key:
                logger.error(f"No matching key found for kid: {kid}")
                return None

            # Convert JWK to PEM
            public_key = jwt.algorithms.RSAAlgorithm.from_jwk(key)

            # Verify token
            payload = jwt.decode(
                token,
                public_key,
                algorithms=['RS256'],
                audience=unverified.get('aud'),
                issuer=settings.CLERK_JWT_ISSUER,
            )

            return payload

        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid token: {e}")
        except Exception as e:
            logger.error(f"Token verification failed: {e}")

        return None

    def _get_jwks(self) -> Optional[Dict]:
        """
        Get JWKS with caching
        """
        # Try cache first
        jwks = cache.get(self._jwks_cache_key)
        if jwks:
            return jwks

        try:
            # Fetch from Clerk
            response = requests.get(
                f"{settings.CLERK_JWT_ISSUER}/.well-known/jwks.json",
                timeout=10
            )
            response.raise_for_status()
            jwks = response.json()

            # Cache the result
            cache.set(self._jwks_cache_key, jwks, self._jwks_cache_timeout)

            return jwks

        except Exception as e:
            logger.error(f"Failed to fetch JWKS: {e}")
            return None

    def _get_or_create_user(self, payload: Dict[str, Any]) -> User:
        """
        Get or create Django user from Clerk payload
        """
        clerk_id = payload.get('sub')

        # Try to get existing user
        try:
            user = User.objects.get(clerk_id=clerk_id)
            self._update_user_from_payload(user, payload)
            return user
        except User.DoesNotExist:
            pass

        # Fetch full user data from Clerk API
        try:
            clerk_user = self.clerk.users.get(user_id=clerk_id)
            return self._create_user_from_clerk(clerk_user)
        except Exception as e:
            logger.error(f"Failed to fetch Clerk user: {e}")
            # Create basic user from JWT payload
            return self._create_user_from_payload(payload)

    def _create_user_from_clerk(self, clerk_user: ClerkUser) -> User:
        """
        Create Django user from Clerk API response
        """
        email = clerk_user.email_addresses[0].email_address if clerk_user.email_addresses else ""

        user = User.objects.create(
            clerk_id=clerk_user.id,
            username=self._generate_username(email or clerk_user.id),
            email=email,
            first_name=clerk_user.first_name or "",
            last_name=clerk_user.last_name or "",
            is_active=True,
            phone_cell=clerk_user.phone_numbers[0].phone_number if clerk_user.phone_numbers else "",
            profile_image=clerk_user.image_url or "",
        )

        # Set member type based on metadata
        self._set_member_type(user, clerk_user.public_metadata)

        return user

    def _create_user_from_payload(self, payload: Dict[str, Any]) -> User:
        """
        Create basic user from JWT payload
        """
        email = payload.get('email', '')

        user = User.objects.create(
            clerk_id=payload['sub'],
            username=self._generate_username(email or payload['sub']),
            email=email,
            first_name=payload.get('given_name', ''),
            last_name=payload.get('family_name', ''),
            is_active=True,
        )

        return user

    def _update_user_from_payload(self, user: User, payload: Dict[str, Any]) -> None:
        """
        Update existing user with latest data from JWT
        """
        updated = False

        fields_to_update = {
            'email': payload.get('email'),
            'first_name': payload.get('given_name'),
            'last_name': payload.get('family_name'),
        }

        for field, value in fields_to_update.items():
            if value and getattr(user, field) != value:
                setattr(user, field, value)
                updated = True

        if updated:
            user.save()

    def _set_member_type(self, user: User, metadata: Dict) -> None:
        """
        Set member type based on Clerk metadata
        """
        role = metadata.get('role', 'member')

        # Map Clerk roles to MemberType
        role_mapping = {
            'admin': 'admin',
            'manager': 'manager',
            'sales_rep': 'sales_representative',
            'member': 'member',
        }

        member_type_name = role_mapping.get(role, 'member')

        try:
            from apps.member.models import MemberType
            member_type = MemberType.objects.get(name=member_type_name)
            user.member_type = member_type
            user.save()
        except MemberType.DoesNotExist:
            logger.warning(f"MemberType '{member_type_name}' not found")

    def _generate_username(self, base: str) -> str:
        """
        Generate unique username
        """
        username = base.split('@')[0] if '@' in base else base
        username = username[:30]  # Django username max length

        # Ensure uniqueness
        counter = 1
        original = username
        while User.objects.filter(username=username).exists():
            username = f"{original}{counter}"
            counter += 1

        return username

    def _update_user_session(self, request, user: User, payload: Dict) -> None:
        """
        Update user session with Clerk data
        """
        request.session['clerk_session_id'] = payload.get('sid')
        request.session['clerk_org_id'] = payload.get('org_id')
        request.session['clerk_org_role'] = payload.get('org_role')
```

### 3.4 Clerk Middleware

```python
# backend/apps/member/middleware.py
import logging
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth import authenticate
from django.contrib.auth.models import AnonymousUser

logger = logging.getLogger(__name__)

class ClerkAuthenticationMiddleware(MiddlewareMixin):
    """
    Middleware to authenticate requests using Clerk tokens
    """

    def process_request(self, request):
        """
        Authenticate user for each request
        """
        # Skip authentication for certain paths
        skip_paths = ['/health/', '/api/v1/webhook/clerk/']
        if any(request.path.startswith(path) for path in skip_paths):
            return

        # Try to authenticate with Clerk
        user = authenticate(request)

        if user:
            request.user = user
            request._cached_user = user
        else:
            request.user = AnonymousUser()
            request._cached_user = AnonymousUser()
```

### 3.5 API Views for Clerk Integration

```python
# backend/apps/api/views/clerk_views.py
import json
import logging
import hmac
import hashlib
from django.http import JsonResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.conf import settings
from clerk_backend_api import Clerk
from apps.member.models import Member

logger = logging.getLogger(__name__)

@method_decorator(csrf_exempt, name='dispatch')
class ClerkWebhookView(View):
    """
    Handle Clerk webhooks for user synchronization
    """

    def post(self, request):
        """
        Process Clerk webhook events
        """
        # Verify webhook signature
        if not self._verify_webhook_signature(request):
            return JsonResponse({'error': 'Invalid signature'}, status=401)

        try:
            payload = json.loads(request.body)
            event_type = payload.get('type')
            data = payload.get('data')

            # Handle different event types
            if event_type == 'user.created':
                self._handle_user_created(data)
            elif event_type == 'user.updated':
                self._handle_user_updated(data)
            elif event_type == 'user.deleted':
                self._handle_user_deleted(data)
            elif event_type == 'session.created':
                self._handle_session_created(data)
            elif event_type == 'organization.member.created':
                self._handle_org_member_created(data)

            return JsonResponse({'status': 'success'})

        except Exception as e:
            logger.error(f"Webhook processing failed: {e}")
            return JsonResponse({'error': str(e)}, status=500)

    def _verify_webhook_signature(self, request) -> bool:
        """
        Verify Clerk webhook signature
        """
        signature = request.headers.get('Svix-Signature')
        if not signature:
            return False

        # Extract signature parts
        signatures = {}
        for part in signature.split(' '):
            key, value = part.split('=')
            signatures[key] = value

        # Verify timestamp (prevent replay attacks)
        timestamp = request.headers.get('Svix-Timestamp')
        if not timestamp:
            return False

        # Compute expected signature
        signed_content = f"{request.headers.get('Svix-Id')}.{timestamp}.{request.body.decode()}"
        expected_signature = hmac.new(
            settings.CLERK_WEBHOOK_SECRET.encode(),
            signed_content.encode(),
            hashlib.sha256
        ).hexdigest()

        # Compare signatures
        return hmac.compare_digest(signatures.get('v1', ''), expected_signature)

    def _handle_user_created(self, data):
        """
        Handle user.created webhook
        """
        clerk_id = data['id']
        email = data['email_addresses'][0]['email_address'] if data.get('email_addresses') else ''

        Member.objects.create(
            clerk_id=clerk_id,
            email=email,
            first_name=data.get('first_name', ''),
            last_name=data.get('last_name', ''),
            username=self._generate_username(email or clerk_id),
            is_active=True,
        )

        logger.info(f"Created user from webhook: {clerk_id}")

    def _handle_user_updated(self, data):
        """
        Handle user.updated webhook
        """
        try:
            member = Member.objects.get(clerk_id=data['id'])

            # Update fields
            if data.get('email_addresses'):
                member.email = data['email_addresses'][0]['email_address']
            member.first_name = data.get('first_name', member.first_name)
            member.last_name = data.get('last_name', member.last_name)

            member.save()
            logger.info(f"Updated user from webhook: {data['id']}")

        except Member.DoesNotExist:
            logger.warning(f"User not found for update: {data['id']}")

    def _handle_user_deleted(self, data):
        """
        Handle user.deleted webhook
        """
        try:
            member = Member.objects.get(clerk_id=data['id'])
            member.is_active = False
            member.save()
            logger.info(f"Deactivated user from webhook: {data['id']}")
        except Member.DoesNotExist:
            logger.warning(f"User not found for deletion: {data['id']}")

    def _handle_session_created(self, data):
        """
        Handle session.created webhook
        """
        # Log session creation for audit
        logger.info(f"Session created for user: {data.get('user_id')}")

    def _handle_org_member_created(self, data):
        """
        Handle organization.member.created webhook
        """
        # Update user's organization membership
        try:
            member = Member.objects.get(clerk_id=data['public_user_data']['user_id'])
            # Update organization-related fields
            member.save()
            logger.info(f"Updated organization membership: {data['public_user_data']['user_id']}")
        except Member.DoesNotExist:
            logger.warning(f"User not found for org membership: {data['public_user_data']['user_id']}")

    def _generate_username(self, base: str) -> str:
        """
        Generate unique username
        """
        username = base.split('@')[0] if '@' in base else base
        username = username[:30]

        counter = 1
        original = username
        while Member.objects.filter(username=username).exists():
            username = f"{original}{counter}"
            counter += 1

        return username


class ClerkSyncView(View):
    """
    Sync user data between frontend and backend
    """

    def post(self, request):
        """
        Sync user data from frontend
        """
        try:
            data = json.loads(request.body)
            clerk_id = data.get('clerkId')

            if not clerk_id:
                return JsonResponse({'error': 'Missing clerkId'}, status=400)

            # Get or create user
            member, created = Member.objects.get_or_create(
                clerk_id=clerk_id,
                defaults={
                    'email': data.get('email', ''),
                    'first_name': data.get('firstName', ''),
                    'last_name': data.get('lastName', ''),
                    'username': self._generate_username(data.get('email', clerk_id)),
                    'profile_image': data.get('imageUrl', ''),
                    'is_active': True,
                }
            )

            if not created:
                # Update existing user
                member.email = data.get('email', member.email)
                member.first_name = data.get('firstName', member.first_name)
                member.last_name = data.get('lastName', member.last_name)
                member.profile_image = data.get('imageUrl', member.profile_image)
                member.save()

            return JsonResponse({
                'status': 'success',
                'created': created,
                'userId': member.id,
            })

        except Exception as e:
            logger.error(f"User sync failed: {e}")
            return JsonResponse({'error': str(e)}, status=500)

    def _generate_username(self, base: str) -> str:
        """
        Generate unique username
        """
        username = base.split('@')[0] if '@' in base else base
        username = username[:30]

        counter = 1
        original = username
        while Member.objects.filter(username=username).exists():
            username = f"{original}{counter}"
            counter += 1

        return username
```

### 3.6 URL Configuration

```python
# backend/apps/api/urls.py
from django.urls import path
from .views.clerk_views import ClerkWebhookView, ClerkSyncView

urlpatterns = [
    # ... existing URLs ...

    # Clerk integration
    path('webhook/clerk/', ClerkWebhookView.as_view(), name='clerk-webhook'),
    path('clerk/sync/', ClerkSyncView.as_view(), name='clerk-sync'),
]
```

## Phase 4: Migration Strategy

### 4.1 Database Migration

```python
# backend/apps/member/migrations/00XX_add_clerk_fields.py
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('member', '00XX_previous_migration'),
    ]

    operations = [
        migrations.AddField(
            model_name='member',
            name='clerk_id',
            field=models.CharField(max_length=255, unique=True, null=True, blank=True, db_index=True),
        ),
        migrations.AddField(
            model_name='member',
            name='clerk_org_id',
            field=models.CharField(max_length=255, null=True, blank=True, db_index=True),
        ),
        migrations.AddField(
            model_name='member',
            name='clerk_org_role',
            field=models.CharField(max_length=50, null=True, blank=True),
        ),
        migrations.AddField(
            model_name='member',
            name='clerk_metadata',
            field=models.JSONField(default=dict, blank=True),
        ),
    ]
```

### 4.2 User Migration Script

```python
# scripts/migrate_users_to_clerk.py
import os
import sys
import django

sys.path.append('/app/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from clerk_backend_api import Clerk
from apps.member.models import Member
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_users_to_clerk():
    """
    Migrate existing Django users to Clerk
    """
    clerk = Clerk(bearer_auth=os.environ.get('CLERK_SECRET_KEY'))

    members = Member.objects.filter(clerk_id__isnull=True)
    total = members.count()

    logger.info(f"Migrating {total} users to Clerk...")

    for i, member in enumerate(members, 1):
        try:
            # Create user in Clerk
            clerk_user = clerk.users.create(
                email_addresses=[member.email],
                first_name=member.first_name,
                last_name=member.last_name,
                public_metadata={
                    'django_id': str(member.id),
                    'role': member.member_type.name if member.member_type else 'member',
                    'team_id': member.primary_team.id if member.primary_team else None,
                },
                private_metadata={
                    'migrated_from_django': True,
                    'migration_date': str(member.date_joined),
                }
            )

            # Update Django user with Clerk ID
            member.clerk_id = clerk_user.id
            member.save()

            logger.info(f"[{i}/{total}] Migrated user: {member.email}")

        except Exception as e:
            logger.error(f"Failed to migrate user {member.email}: {e}")

    logger.info("Migration completed!")

if __name__ == '__main__':
    migrate_users_to_clerk()
```

## Phase 5: Testing Strategy

### 5.1 Frontend Tests

```typescript
// frontend/src/tests/auth.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import { ClerkProvider } from '@clerk/clerk-react';
import { vi } from 'vitest';

// Mock Clerk
vi.mock('@clerk/clerk-react', () => ({
  ClerkProvider: ({ children }) => <div>{children}</div>,
  useAuth: () => ({
    isLoaded: true,
    isSignedIn: true,
    sessionId: 'test-session',
  }),
  useUser: () => ({
    user: {
      id: 'user_123',
      firstName: 'John',
      lastName: 'Doe',
      emailAddresses: [{ emailAddress: '<EMAIL>' }],
    },
  }),
}));

describe('Clerk Authentication', () => {
  it('should render protected content when authenticated', async () => {
    render(
      <ClerkProvider publishableKey="pk_test">
        <ProtectedRoute />
      </ClerkProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });
  });
});
```

### 5.2 Backend Tests

```python
# backend/apps/member/tests/test_clerk_auth.py
import json
from unittest.mock import patch, MagicMock
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from apps.member.backends import ClerkBackend

User = get_user_model()

class ClerkAuthenticationTest(TestCase):
    def setUp(self):
        self.factory = RequestFactory()
        self.backend = ClerkBackend()

    @patch('apps.member.backends.jwt.decode')
    @patch('apps.member.backends.requests.get')
    def test_authenticate_valid_token(self, mock_get, mock_decode):
        """Test authentication with valid Clerk token"""
        # Mock JWKS response
        mock_get.return_value.json.return_value = {
            'keys': [{'kid': 'test-key', 'kty': 'RSA', 'n': '...', 'e': 'AQAB'}]
        }

        # Mock JWT decode
        mock_decode.return_value = {
            'sub': 'user_123',
            'email': '<EMAIL>',
            'given_name': 'Test',
            'family_name': 'User',
        }

        request = self.factory.get('/')
        request.META['HTTP_AUTHORIZATION'] = 'Bearer test-token'

        user = self.backend.authenticate(request)

        self.assertIsNotNone(user)
        self.assertEqual(user.clerk_id, 'user_123')
        self.assertEqual(user.email, '<EMAIL>')

    def test_webhook_signature_verification(self):
        """Test Clerk webhook signature verification"""
        from apps.api.views.clerk_views import ClerkWebhookView

        view = ClerkWebhookView()
        request = self.factory.post(
            '/api/v1/webhook/clerk/',
            data=json.dumps({'type': 'user.created', 'data': {}}),
            content_type='application/json',
        )

        # Add required headers
        request.headers = {
            'Svix-Id': 'test-id',
            'Svix-Timestamp': '1234567890',
            'Svix-Signature': 'v1=test-signature',
        }

        with patch.object(view, '_verify_webhook_signature', return_value=True):
            response = view.post(request)
            self.assertEqual(response.status_code, 200)
```

## Phase 6: Deployment & Monitoring

### 6.1 Environment Configuration

```yaml
# docker-compose.yml
version: "3.8"

services:
  backend:
    environment:
      - CLERK_SECRET_KEY=${CLERK_SECRET_KEY}
      - CLERK_PUBLISHABLE_KEY=${CLERK_PUBLISHABLE_KEY}
      - CLERK_JWT_ISSUER=${CLERK_JWT_ISSUER}
      - CLERK_WEBHOOK_SECRET=${CLERK_WEBHOOK_SECRET}

  frontend:
    environment:
      - VITE_CLERK_PUBLISHABLE_KEY=${VITE_CLERK_PUBLISHABLE_KEY}
      - VITE_CLERK_SIGN_IN_URL=/sign-in
      - VITE_CLERK_SIGN_UP_URL=/sign-up
```

### 6.2 Monitoring & Logging

```python
# backend/config/logging.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/clerk_auth.log',
            'maxBytes': 1024 * 1024 * 10,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'apps.member.backends': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
        },
        'apps.api.views.clerk_views': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
        },
    },
}
```

## Phase 7: Security Considerations

### 7.1 Security Best Practices

1. **Token Management**
   - Never store tokens in localStorage
   - Use HttpOnly cookies for sensitive data
   - Implement proper CORS configuration
   - Rotate webhook secrets regularly

2. **API Security**
   - Validate all JWT tokens server-side
   - Implement rate limiting on authentication endpoints
   - Use HTTPS in production
   - Enable CSP headers

3. **Data Privacy**
   - Comply with GDPR/CCPA requirements
   - Implement proper data retention policies
   - Encrypt sensitive data at rest
   - Audit authentication events

### 7.2 Security Headers

```python
# backend/config/settings.py
# Security headers
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
SECURE_HSTS_SECONDS = ********
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# Content Security Policy
CSP_DEFAULT_SRC = ("'self'",)
CSP_SCRIPT_SRC = ("'self'", "https://clerk.accounts.dev")
CSP_STYLE_SRC = ("'self'", "'unsafe-inline'")
CSP_IMG_SRC = ("'self'", "https:", "data:")
CSP_CONNECT_SRC = ("'self'", "https://api.clerk.com", "https://clerk.accounts.dev")
```

## Phase 8: Rollback Plan

### 8.1 Feature Flags

```python
# backend/config/settings.py
FEATURE_FLAGS = {
    'USE_CLERK_AUTH': os.environ.get('USE_CLERK_AUTH', 'false').lower() == 'true',
    'CLERK_MIGRATION_MODE': os.environ.get('CLERK_MIGRATION_MODE', 'false').lower() == 'true',
}
```

### 8.2 Dual Authentication Support

```python
# backend/apps/member/backends.py
class HybridAuthBackend(BaseBackend):
    """
    Support both Clerk and Django authentication during migration
    """

    def authenticate(self, request, **kwargs):
        # Try Clerk first
        if settings.FEATURE_FLAGS['USE_CLERK_AUTH']:
            user = ClerkBackend().authenticate(request, **kwargs)
            if user:
                return user

        # Fall back to Django auth
        return DjangoBackend().authenticate(request, **kwargs)
```

## Conclusion

This comprehensive implementation guide provides a structured approach to integrating Clerk authentication into the XD Incentives platform. The phased approach ensures minimal disruption while providing a clear migration path from the existing authentication system to Clerk's robust solution.

### Key Benefits

- **Enhanced Security**: JWT-based authentication with automatic token refresh
- **Scalability**: Clerk's infrastructure handles authentication at scale
- **User Experience**: Pre-built UI components with customization options
- **Compliance**: Built-in support for GDPR, SOC 2, and other standards
- **Developer Experience**: Comprehensive SDKs and documentation
