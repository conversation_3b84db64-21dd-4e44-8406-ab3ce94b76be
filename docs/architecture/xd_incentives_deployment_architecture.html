<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XD Incentives Multi-Client Deployment Architecture</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.1em;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .architecture-overview {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .shared-codebase {
            background: #667eea;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .tech-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            font-size: 0.9em;
        }
        .client-environments {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .client-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .client-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-weight: bold;
            font-size: 1.2em;
        }
        .client-header.citgo {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        }
        .client-header.franchise {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        }
        .environment-tabs {
            display: flex;
            background: #f8f9fa;
        }
        .env-tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            font-weight: 500;
            border-right: 1px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .env-tab:last-child {
            border-right: none;
        }
        .env-tab.active {
            background: #667eea;
            color: white;
        }
        .env-content {
            padding: 20px;
            min-height: 300px;
        }
        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .service-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 10px;
            text-align: center;
            font-size: 0.9em;
        }
        .service-item.database {
            background: #fff3cd;
            border-color: #ffc107;
        }
        .service-item.cache {
            background: #f8d7da;
            border-color: #dc3545;
        }
        .service-item.app {
            background: #d1ecf1;
            border-color: #17a2b8;
        }
        .service-item.monitoring {
            background: #d4edda;
            border-color: #28a745;
        }
        .config-differences {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        .config-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
        }
        .config-title {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        .config-json {
            background: #2d3748;
            color: #e2e8f0;
            padding: 10px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            overflow-x: auto;
        }
        .deployment-flow {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        .flow-step {
            display: flex;
            align-items: center;
            margin: 15px 0;
        }
        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        .step-content {
            flex: 1;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }
        .metric-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>XD Incentives Platform</h1>
            <p>Multi-Client Deployment Architecture</p>
        </div>

        <div class="content">
            <div class="architecture-overview">
                <h2>🏗️ Architecture Overview</h2>
                <p>Multi-tenant SaaS platform with isolated client environments across multiple AWS accounts. Each client maintains separate dev, staging, and production environments while sharing the same codebase.</p>
            </div>

            <div class="shared-codebase">
                <h3>🚀 Shared Technology Stack</h3>
                <div class="tech-stack">
                    <div class="tech-item">React v19+ Frontend</div>
                    <div class="tech-item">Django v5.2.3+ Backend</div>
                    <div class="tech-item">AWS ECS Fargate</div>
                    <div class="tech-item">MySQL v8.4 (RDS)</div>
                    <div class="tech-item">Redis (ElastiCache)</div>
                    <div class="tech-item">Terraform IaC</div>
                    <div class="tech-item">GitHub Actions CI/CD</div>
                    <div class="tech-item">3rd Party Authentication</div>
                </div>
            </div>

            <h2>🏢 Client Environments</h2>
            <div class="client-environments">
                <div class="client-card">
                    <div class="client-header citgo">
                        Citgo Petroleum Corporation (Pilot)
                    </div>
                    <div class="environment-tabs">
                        <div class="env-tab active" onclick="switchTab('citgo', 'dev')">Development</div>
                        <div class="env-tab" onclick="switchTab('citgo', 'staging')">Staging</div>
                        <div class="env-tab" onclick="switchTab('citgo', 'prod')">Production</div>
                    </div>
                    <div class="env-content">
                        <div id="citgo-dev" class="env-details">
                            <h4>Development Environment</h4>
                            <div class="service-grid">
                                <div class="service-item app">ECS Service (1 task)</div>
                                <div class="service-item database">RDS t3.micro</div>
                                <div class="service-item cache">ElastiCache t3.micro</div>
                                <div class="service-item monitoring">CloudWatch Basic</div>
                            </div>
                            <p><strong>Purpose:</strong> Feature development and testing</p>
                            <p><strong>Data:</strong> Synthetic test data</p>
                            <p><strong>Deployment:</strong> On PR creation</p>
                        </div>
                        <div id="citgo-staging" class="env-details hidden">
                            <h4>Staging Environment</h4>
                            <div class="service-grid">
                                <div class="service-item app">ECS Service (1 task)</div>
                                <div class="service-item database">RDS t3.small</div>
                                <div class="service-item cache">ElastiCache t3.small</div>
                                <div class="service-item monitoring">CloudWatch Enhanced</div>
                            </div>
                            <p><strong>Purpose:</strong> User acceptance testing</p>
                            <p><strong>Data:</strong> Sanitized production data</p>
                            <p><strong>Deployment:</strong> On PR merge to develop</p>
                        </div>
                        <div id="citgo-prod" class="env-details hidden">
                            <h4>Production Environment</h4>
                            <div class="service-grid">
                                <div class="service-item app">ECS Service (2+ tasks)</div>
                                <div class="service-item database">RDS t3.medium Multi-AZ</div>
                                <div class="service-item cache">ElastiCache t3.medium</div>
                                <div class="service-item monitoring">CloudWatch + Alerts</div>
                            </div>
                            <p><strong>Purpose:</strong> Live production workload</p>
                            <p><strong>Data:</strong> 200K+ transactions, 1.5K users</p>
                            <p><strong>Deployment:</strong> Manual approval required</p>
                        </div>
                    </div>
                </div>

                <div class="client-card">
                    <div class="client-header franchise">
                        Future Franchise Client
                    </div>
                    <div class="environment-tabs">
                        <div class="env-tab active" onclick="switchTab('franchise', 'dev')">Development</div>
                        <div class="env-tab" onclick="switchTab('franchise', 'staging')">Staging</div>
                        <div class="env-tab" onclick="switchTab('franchise', 'prod')">Production</div>
                    </div>
                    <div class="env-content">
                        <div id="franchise-dev" class="env-details">
                            <h4>Development Environment</h4>
                            <div class="service-grid">
                                <div class="service-item app">ECS Service (1 task)</div>
                                <div class="service-item database">RDS t3.micro</div>
                                <div class="service-item cache">ElastiCache t3.micro</div>
                                <div class="service-item monitoring">CloudWatch Basic</div>
                            </div>
                            <p><strong>Purpose:</strong> Client-specific customization</p>
                            <p><strong>Data:</strong> Client-specific test data</p>
                            <p><strong>Deployment:</strong> Independent CI/CD pipeline</p>
                        </div>
                        <div id="franchise-staging" class="env-details hidden">
                            <h4>Staging Environment</h4>
                            <div class="service-grid">
                                <div class="service-item app">ECS Service (1 task)</div>
                                <div class="service-item database">RDS t3.small</div>
                                <div class="service-item cache">ElastiCache t3.small</div>
                                <div class="service-item monitoring">CloudWatch Enhanced</div>
                            </div>
                            <p><strong>Purpose:</strong> Client UAT and training</p>
                            <p><strong>Data:</strong> Client-provided test data</p>
                            <p><strong>Deployment:</strong> Client-approved releases</p>
                        </div>
                        <div id="franchise-prod" class="env-details hidden">
                            <h4>Production Environment</h4>
                            <div class="service-grid">
                                <div class="service-item app">Auto-scaling ECS</div>
                                <div class="service-item database">RDS Multi-AZ</div>
                                <div class="service-item cache">ElastiCache Cluster</div>
                                <div class="service-item monitoring">Full Monitoring Suite</div>
                            </div>
                            <p><strong>Purpose:</strong> Client production workload</p>
                            <p><strong>Data:</strong> Client-specific live data</p>
                            <p><strong>Deployment:</strong> Client-controlled releases</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="config-differences">
                <h2>⚙️ Configuration Management</h2>
                <p>Each client environment uses environment-specific configurations while maintaining codebase consistency:</p>

                <div class="config-grid">
                    <div class="config-card">
                        <div class="config-title">Tenant Configuration</div>
                        <div class="config-json">{
  "tenant_id": "citgo",
  "domain": "citgogfg.com",
  "branding": {
    "logo": "citgo-logo.svg",
    "primary_color": "#dc3545",
    "secondary_color": "#fd7e14"
  },
  "features": {
    "marketing_funds": true,
    "multi_currency": false,
    "gamification": true
  }
}</div>
                    </div>

                    <div class="config-card">
                        <div class="config-title">AWS Resources</div>
                        <div class="config-json">{
  "rds_instance": "db.t3.medium",
  "cache_node": "cache.t3.medium",
  "ecs_desired_count": 2,
  "auto_scaling": {
    "min_capacity": 1,
    "max_capacity": 10
  },
  "backup_retention": 30
}</div>
                    </div>

                    <div class="config-card">
                        <div class="config-title">Security Settings</div>
                        <div class="config-json">{
  "auth_provider": "3rd Party",
  "mfa_required": true,
  "session_timeout": 3600,
  "encryption": {
    "at_rest": "AES-256",
    "in_transit": "TLS 1.3"
  },
  "compliance": ["SOC2", "PCI-DSS"]
}</div>
                    </div>

                    <div class="config-card">
                        <div class="config-title">Business Logic</div>
                        <div class="config-json">{
  "commission_rates": {
    "year_1": 0.02,
    "year_2": 0.015,
    "year_3_plus": 0.01
  },
  "annual_cap": 35000,
  "audit_percentage": 0.15,
  "dispute_sla_hours": 24
}</div>
                    </div>
                </div>
            </div>

            <div class="deployment-flow">
                <h2>🚀 Deployment Pipeline</h2>
                <div class="flow-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <strong>Code Commit</strong><br>
                        Developer pushes code to feature branch
                    </div>
                </div>
                <div class="flow-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <strong>Automated Testing</strong><br>
                        GitHub Actions runs unit tests, integration tests, and security scans
                    </div>
                </div>
                <div class="flow-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <strong>Dev Deployment</strong><br>
                        On PR creation, deploy to development environment across all client accounts
                    </div>
                </div>
                <div class="flow-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <strong>Staging Deployment</strong><br>
                        On PR merge to develop, deploy to staging environments
                    </div>
                </div>
                <div class="flow-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <strong>Production Deployment</strong><br>
                        Manual approval required for production deployment across all client accounts
                    </div>
                </div>
            </div>

            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">99.9%</div>
                    <div class="metric-label">Uptime SLA</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">&lt;2s</div>
                    <div class="metric-label">Response Time</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">1000+</div>
                    <div class="metric-label">Concurrent Users</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">100K+</div>
                    <div class="metric-label">Daily Transactions</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(client, environment) {
            // Hide all environment details for this client
            const envDetails = document.querySelectorAll(`#${client}-dev, #${client}-staging, #${client}-prod`);
            envDetails.forEach(detail => detail.classList.add('hidden'));

            // Remove active class from all tabs for this client
            const tabs = document.querySelectorAll(`[onclick*="${client}"]`);
            tabs.forEach(tab => tab.classList.remove('active'));

            // Show selected environment and activate tab
            document.getElementById(`${client}-${environment}`).classList.remove('hidden');
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
