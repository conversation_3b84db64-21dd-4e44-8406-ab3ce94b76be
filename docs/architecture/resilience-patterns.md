# Resilience Patterns and Configurations

## Circuit Breaker Implementation

### Overview
Circuit breakers prevent cascading failures by stopping requests to failing services, allowing them time to recover while providing fallback responses to users.

### Circuit Breaker States
1. **CLOSED** - Normal operation, requests pass through
2. **OPEN** - Service is failing, all requests immediately return fallback
3. **HALF-OPEN** - Testing if service has recovered

### Service-Specific Configurations

#### API Gateway Circuit Breaker (Django Backend)
```python
# Circuit Breaker Configuration
CIRCUIT_BREAKER_CONFIG = {
    'failure_threshold': 5,          # Number of failures before opening
    'success_threshold': 2,          # Successes needed to close from half-open
    'timeout': 30,                   # Seconds before attempting half-open
    'expected_exception': RequestException,
    'fallback_function': 'api.utils.fallback_response'
}
```

#### External Service Circuit Breakers

##### Clerk Authentication Service
```python
CLERK_CIRCUIT_BREAKER = {
    'failure_threshold': 3,
    'timeout': 20,
    'fallback': 'cache_based_auth_check',
    'metrics_name': 'clerk_auth_circuit'
}
```

##### UPS Address Validation
```python
UPS_CIRCUIT_BREAKER = {
    'failure_threshold': 10,
    'timeout': 60,
    'fallback': 'manual_address_entry',
    'metrics_name': 'ups_validation_circuit'
}
```

##### AWS SES Email Service
```python
SES_CIRCUIT_BREAKER = {
    'failure_threshold': 5,
    'timeout': 45,
    'fallback': 'queue_for_retry',
    'metrics_name': 'ses_email_circuit'
}
```

### Frontend Circuit Breaker (React)
```typescript
// Tanstack Query Configuration with Circuit Breaker
const circuitBreakerConfig = {
  api: {
    failureThreshold: 5,
    resetTimeout: 30000, // 30 seconds
    halfOpenRequests: 2,
    onOpen: () => showNotification('Service temporarily unavailable'),
    onClose: () => showNotification('Service restored'),
  },
  auth: {
    failureThreshold: 3,
    resetTimeout: 20000,
    fallback: () => checkCachedAuth(),
  }
};
```

## Retry Policies

### API Request Retry Configuration

#### Backend Service Retries (Python/Django)
```python
# Retry Configuration with Exponential Backoff
RETRY_CONFIG = {
    'max_attempts': 3,
    'backoff_base': 2,           # Exponential base
    'backoff_max': 60,           # Maximum backoff in seconds
    'jitter': True,              # Add randomization to prevent thundering herd
}

# Service-Specific Retry Policies
RETRY_POLICIES = {
    'database_read': {
        'max_attempts': 3,
        'initial_delay': 0.1,
        'max_delay': 1,
        'exponential_base': 2,
        'retryable_errors': [OperationalError, InterfaceError]
    },
    'database_write': {
        'max_attempts': 2,
        'initial_delay': 0.5,
        'max_delay': 2,
        'exponential_base': 2,
        'retryable_errors': [OperationalError]
    },
    'external_api': {
        'max_attempts': 4,
        'initial_delay': 1,
        'max_delay': 30,
        'exponential_base': 3,
        'retryable_status_codes': [429, 502, 503, 504]
    },
    'email_send': {
        'max_attempts': 5,
        'initial_delay': 2,
        'max_delay': 120,
        'exponential_base': 2,
        'retryable_errors': [MessageRejected, MailServiceException]
    }
}
```

#### Frontend Retry Policies (TypeScript/React)
```typescript
// Tanstack Query Retry Configuration
export const queryRetryConfig = {
  // Default retry for all queries
  retry: (failureCount: number, error: any) => {
    // Don't retry on 4xx errors except 429 (rate limit)
    if (error.status >= 400 && error.status < 500 && error.status !== 429) {
      return false;
    }
    // Retry up to 3 times
    return failureCount < 3;
  },
  retryDelay: (attemptIndex: number) => {
    // Exponential backoff with jitter
    const baseDelay = Math.min(1000 * 2 ** attemptIndex, 30000);
    const jitter = Math.random() * 1000;
    return baseDelay + jitter;
  }
};

// Mutation-specific retry (more conservative)
export const mutationRetryConfig = {
  retry: 1, // Only retry once for mutations
  retryDelay: 1000 // Fixed 1 second delay
};
```

### HTTP Client Retry Implementation
```typescript
// Fetch wrapper with retry logic
export async function fetchWithRetry(
  url: string,
  options: RequestInit,
  config: RetryConfig = defaultRetryConfig
): Promise<Response> {
  let lastError: Error;
  
  for (let attempt = 0; attempt < config.maxAttempts; attempt++) {
    try {
      const response = await fetch(url, {
        ...options,
        signal: AbortSignal.timeout(config.timeout || 30000)
      });
      
      if (response.ok || !shouldRetry(response.status)) {
        return response;
      }
      
      lastError = new Error(`HTTP ${response.status}`);
    } catch (error) {
      lastError = error as Error;
      
      if (!isRetryableError(error)) {
        throw error;
      }
    }
    
    if (attempt < config.maxAttempts - 1) {
      const delay = calculateBackoff(attempt, config);
      await sleep(delay);
    }
  }
  
  throw lastError;
}
```

## Timeout Configurations

### Service Timeout Matrix

| Service/Operation | Timeout | Retry After Timeout | Notes |
|------------------|---------|-------------------|--------|
| Database Query (Read) | 5s | Yes | Read replicas available |
| Database Query (Write) | 10s | No | Avoid duplicate writes |
| API Gateway | 30s | Yes | Client timeout at 29s |
| External API (UPS) | 15s | Yes | Fallback to manual entry |
| External API (Clerk) | 10s | Yes | Use cached session |
| Email Send (SES) | 20s | Yes | Queue for async retry |
| File Upload (S3) | 120s | Yes | Multipart for large files |
| Report Generation | 300s | No | Async job with progress |
| Bulk Import | 600s | No | Background job |
| Health Check | 3s | Yes | Quick failure detection |

### Implementation Examples

#### Django Timeout Middleware
```python
class TimeoutMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        timeout = self.get_timeout_for_endpoint(request.path)
        
        with timeout_context(timeout):
            response = self.get_response(request)
        
        return response
    
    def get_timeout_for_endpoint(self, path):
        if '/api/health' in path:
            return 3
        elif '/api/reports' in path:
            return 300
        elif '/api/bulk' in path:
            return 600
        else:
            return 30  # Default timeout
```

#### Frontend Timeout Configuration
```typescript
// API client with configurable timeouts
export const apiClient = {
  get: (url: string, timeout = 30000) => 
    fetchWithTimeout(url, { method: 'GET' }, timeout),
  
  post: (url: string, data: any, timeout = 30000) =>
    fetchWithTimeout(url, {
      method: 'POST',
      body: JSON.stringify(data),
      headers: { 'Content-Type': 'application/json' }
    }, timeout),
    
  upload: (url: string, file: File, timeout = 120000) =>
    fetchWithTimeout(url, {
      method: 'POST',
      body: createFormData(file)
    }, timeout)
};
```

## Bulkhead Pattern

### Resource Isolation Configuration
```python
# Thread Pool Isolation for Different Service Types
THREAD_POOLS = {
    'database': {
        'core_size': 10,
        'max_size': 20,
        'queue_size': 100,
        'rejection_policy': 'caller_runs'
    },
    'external_api': {
        'core_size': 5,
        'max_size': 10,
        'queue_size': 50,
        'rejection_policy': 'abort'
    },
    'reporting': {
        'core_size': 2,
        'max_size': 4,
        'queue_size': 10,
        'rejection_policy': 'discard_oldest'
    }
}

# Connection Pool Limits
DATABASE_CONN_MAX_AGE = 600
DATABASE_POOL_SIZE = 20
REDIS_MAX_CONNECTIONS = 50
```

## Health Check Implementation

### Comprehensive Health Check Endpoint
```python
# /api/health endpoint implementation
def health_check():
    checks = {
        'database': check_database_health(),
        'redis': check_redis_health(),
        'storage': check_s3_health(),
        'email': check_ses_health(),
        'auth': check_clerk_health()
    }
    
    overall_status = 'healthy' if all(
        check['status'] == 'healthy' 
        for check in checks.values()
    ) else 'degraded'
    
    return {
        'status': overall_status,
        'timestamp': datetime.utcnow().isoformat(),
        'checks': checks,
        'version': settings.APP_VERSION
    }

def check_database_health():
    try:
        with timeout(3):
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            cursor.close()
        return {'status': 'healthy', 'latency_ms': measure_latency()}
    except Exception as e:
        return {'status': 'unhealthy', 'error': str(e)}
```

## Graceful Degradation Strategies

### Feature Flags for Degradation
```python
DEGRADATION_FLAGS = {
    'disable_real_time_validation': False,  # Fall back to async validation
    'disable_complex_reports': False,       # Serve cached or simple reports
    'disable_email_notifications': False,   # Queue for later delivery
    'disable_bulk_operations': False,       # Limit to single operations
    'read_only_mode': False,                # Disable all writes
}
```

### Progressive Degradation Levels
1. **Level 0 - Normal**: All features enabled
2. **Level 1 - Minor**: Disable real-time validations, increase cache TTL
3. **Level 2 - Moderate**: Disable complex reports, limit bulk operations
4. **Level 3 - Major**: Queue non-critical operations, extend timeouts
5. **Level 4 - Critical**: Read-only mode, serve static fallbacks

### Auto-Scaling Triggers
```yaml
# ECS Auto-scaling Configuration
scaling_policies:
  - name: cpu_scaling
    metric: CPU_Utilization
    target: 70
    scale_up_threshold: 80
    scale_down_threshold: 50
    
  - name: memory_scaling
    metric: Memory_Utilization
    target: 75
    scale_up_threshold: 85
    scale_down_threshold: 60
    
  - name: request_scaling
    metric: Request_Count_Per_Target
    target: 1000
    scale_up_threshold: 1200
    scale_down_threshold: 800
```