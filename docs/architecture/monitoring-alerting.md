# Monitoring and Alerting Configuration

## Overview
This document defines specific metrics, thresholds, and alerting rules for the XD Incentives Platform. All metrics are collected via CloudWatch and Sentry, with alerts configured for proactive incident response.

## Key Performance Indicators (KPIs)

### Application KPIs
| Metric | Target | Warning Threshold | Critical Threshold | Measurement Window |
|--------|--------|------------------|-------------------|-------------------|
| API Response Time (p95) | < 2s | > 2s | > 5s | 5 minutes |
| API Response Time (p99) | < 3s | > 3s | > 10s | 5 minutes |
| Error Rate | < 0.1% | > 0.5% | > 1% | 5 minutes |
| Successful Request Rate | > 99.9% | < 99.5% | < 99% | 5 minutes |
| Active User Sessions | N/A | N/A | > 10,000 | Real-time |

### Infrastructure KPIs
| Metric | Target | Warning Threshold | Critical Threshold | Measurement Window |
|--------|--------|------------------|-------------------|-------------------|
| CPU Utilization (ECS) | < 70% | > 75% | > 90% | 5 minutes |
| Memory Utilization (ECS) | < 75% | > 80% | > 95% | 5 minutes |
| Database CPU | < 60% | > 70% | > 85% | 5 minutes |
| Database Connections | < 80% | > 85% | > 95% | 1 minute |
| Database Storage | < 80% | > 85% | > 95% | 15 minutes |
| Redis Memory Usage | < 75% | > 80% | > 90% | 5 minutes |
| Redis Evictions | 0 | > 10/min | > 100/min | 5 minutes |

### Business KPIs
| Metric | Target | Warning Threshold | Critical Threshold | Measurement Window |
|--------|--------|------------------|-------------------|-------------------|
| Claim Submission Rate | > 100/hour | < 50/hour | < 10/hour | 1 hour |
| Claim Processing Time | < 30s | > 60s | > 120s | 5 minutes |
| Login Success Rate | > 99% | < 95% | < 90% | 15 minutes |
| Payment Processing Success | > 99.9% | < 99% | < 95% | 1 hour |
| Report Generation Time | < 30s | > 60s | > 180s | Per request |

## CloudWatch Alarms Configuration

### Application Alarms

#### High Error Rate Alarm
```json
{
  "AlarmName": "XDIncentives-HighErrorRate",
  "MetricName": "4XXError",
  "Namespace": "AWS/ApiGateway",
  "Statistic": "Average",
  "Period": 300,
  "EvaluationPeriods": 2,
  "Threshold": 0.01,
  "ComparisonOperator": "GreaterThanThreshold",
  "AlarmActions": ["arn:aws:sns:us-east-1:123456789:alerts-critical"]
}
```

#### High Latency Alarm
```json
{
  "AlarmName": "XDIncentives-HighLatency",
  "MetricName": "Latency",
  "Namespace": "AWS/ELB",
  "Statistic": "Average",
  "Period": 300,
  "EvaluationPeriods": 2,
  "Threshold": 2000,
  "ComparisonOperator": "GreaterThanThreshold",
  "AlarmActions": ["arn:aws:sns:us-east-1:123456789:alerts-warning"]
}
```

#### ECS Task Health Alarm
```json
{
  "AlarmName": "XDIncentives-UnhealthyTasks",
  "MetricName": "HealthyTaskCount",
  "Namespace": "AWS/ECS",
  "Dimensions": [
    {
      "Name": "ServiceName",
      "Value": "xd-incentives-api"
    }
  ],
  "Statistic": "Minimum",
  "Period": 60,
  "EvaluationPeriods": 2,
  "Threshold": 2,
  "ComparisonOperator": "LessThanThreshold",
  "AlarmActions": ["arn:aws:sns:us-east-1:123456789:alerts-critical"]
}
```

### Database Alarms

#### RDS CPU Utilization
```json
{
  "AlarmName": "XDIncentives-RDS-HighCPU",
  "MetricName": "CPUUtilization",
  "Namespace": "AWS/RDS",
  "Dimensions": [
    {
      "Name": "DBInstanceIdentifier",
      "Value": "xd-incentives-prod"
    }
  ],
  "Statistic": "Average",
  "Period": 300,
  "EvaluationPeriods": 2,
  "Threshold": 85,
  "ComparisonOperator": "GreaterThanThreshold",
  "AlarmActions": ["arn:aws:sns:us-east-1:123456789:alerts-critical"]
}
```

#### Database Connection Count
```json
{
  "AlarmName": "XDIncentives-RDS-HighConnections",
  "MetricName": "DatabaseConnections",
  "Namespace": "AWS/RDS",
  "Statistic": "Average",
  "Period": 60,
  "EvaluationPeriods": 3,
  "Threshold": 90,
  "ComparisonOperator": "GreaterThanThreshold",
  "AlarmActions": ["arn:aws:sns:us-east-1:123456789:alerts-warning"]
}
```

### Redis Cache Alarms

#### Redis Memory Usage
```json
{
  "AlarmName": "XDIncentives-Redis-HighMemory",
  "MetricName": "DatabaseMemoryUsagePercentage",
  "Namespace": "AWS/ElastiCache",
  "Dimensions": [
    {
      "Name": "CacheClusterId",
      "Value": "xd-incentives-redis"
    }
  ],
  "Statistic": "Average",
  "Period": 300,
  "EvaluationPeriods": 2,
  "Threshold": 90,
  "ComparisonOperator": "GreaterThanThreshold",
  "AlarmActions": ["arn:aws:sns:us-east-1:123456789:alerts-warning"]
}
```

## Sentry Alert Rules

### Error Rate Alerts
```javascript
{
  "name": "High Error Rate",
  "conditions": [
    {
      "id": "event_frequency",
      "value": 100,
      "interval": "5m"
    }
  ],
  "actions": [
    {
      "id": "send_email",
      "targetType": "team",
      "targetIdentifier": "engineering"
    },
    {
      "id": "send_slack",
      "channel": "#alerts-critical"
    }
  ],
  "frequency": 5
}
```

### Performance Alerts
```javascript
{
  "name": "Slow Transaction",
  "conditions": [
    {
      "id": "transaction_duration",
      "value": 5000,  // 5 seconds
      "percentile": 0.95
    }
  ],
  "actions": [
    {
      "id": "send_email",
      "targetType": "team",
      "targetIdentifier": "engineering"
    }
  ],
  "frequency": 10
}
```

### Custom Business Metric Alerts
```javascript
{
  "name": "Claim Processing Failure",
  "conditions": [
    {
      "id": "custom_metric",
      "metric": "claim.processing.failed",
      "value": 10,
      "interval": "10m"
    }
  ],
  "actions": [
    {
      "id": "send_email",
      "targetType": "user",
      "targetIdentifier": "operations-lead"
    },
    {
      "id": "create_jira_ticket",
      "project": "OPS",
      "issueType": "incident"
    }
  ]
}
```

## Alert Escalation Matrix

### Severity Levels and Response Times

| Severity | Response Time | Notification Method | Escalation Path |
|----------|--------------|-------------------|-----------------|
| **P1 - Critical** | 15 minutes | Phone + SMS + Email + Slack | On-call Engineer → Lead Engineer → Engineering Manager → CTO |
| **P2 - High** | 30 minutes | SMS + Email + Slack | On-call Engineer → Lead Engineer → Engineering Manager |
| **P3 - Medium** | 2 hours | Email + Slack | On-call Engineer → Lead Engineer |
| **P4 - Low** | 24 hours | Email + Slack | Engineering Team |

### Alert Routing Rules

#### Critical (P1) Alerts
- Database down or unreachable
- API gateway returning 5xx errors > 10% of requests
- Authentication service unavailable
- Payment processing failures > 5%
- Data corruption detected
- Security breach indicators

#### High (P2) Alerts
- Response time > 5 seconds for p95
- Error rate > 1%
- Database CPU > 85%
- Redis evictions > 100/minute
- Failed deployments
- SSL certificate expiring < 7 days

#### Medium (P3) Alerts
- Response time > 2 seconds for p95
- Error rate > 0.5%
- Disk usage > 85%
- Background job failures > 10%
- Third-party API degradation

#### Low (P4) Alerts
- Non-critical service degradation
- Scheduled maintenance reminders
- Performance optimization opportunities
- Usage threshold notifications

## Dashboard Configuration

### Executive Dashboard Metrics
- Total Active Users (Real-time)
- Claims Processed (Today/Week/Month)
- System Uptime Percentage
- Average Response Time
- Error Rate Trend
- Revenue Impact Metrics

### Operations Dashboard Metrics
- Service Health Matrix (All Services)
- Real-time Error Log Stream
- Database Performance Metrics
- Cache Hit Ratio
- Queue Depths
- Background Job Status

### Development Dashboard Metrics
- Deployment Frequency
- Lead Time for Changes
- Mean Time to Recovery (MTTR)
- Change Failure Rate
- Code Coverage Trends
- Technical Debt Metrics

## Custom CloudWatch Metrics

### Application Metrics
```python
# Django Custom Metrics
import boto3
cloudwatch = boto3.client('cloudwatch')

def publish_custom_metrics():
    cloudwatch.put_metric_data(
        Namespace='XDIncentives/Application',
        MetricData=[
            {
                'MetricName': 'ClaimsProcessed',
                'Value': get_claims_processed_count(),
                'Unit': 'Count'
            },
            {
                'MetricName': 'ActiveUserSessions',
                'Value': get_active_sessions_count(),
                'Unit': 'Count'
            },
            {
                'MetricName': 'CacheHitRate',
                'Value': calculate_cache_hit_rate(),
                'Unit': 'Percent'
            },
            {
                'MetricName': 'PaymentProcessingTime',
                'Value': get_avg_payment_time(),
                'Unit': 'Milliseconds'
            }
        ]
    )
```

### Frontend Metrics (via Sentry)
```typescript
// React Performance Monitoring
import * as Sentry from '@sentry/react';

// Track custom metrics
Sentry.metrics.increment('page_view', 1, {
  tags: { page: window.location.pathname }
});

Sentry.metrics.distribution('api_call_duration', duration, {
  tags: { endpoint: apiEndpoint, status: responseStatus }
});

Sentry.metrics.gauge('memory_usage', performance.memory.usedJSHeapSize, {
  tags: { page: currentPage }
});
```

## Alert Notification Templates

### Critical Alert Template
```
🚨 CRITICAL ALERT: {alert_name}

Environment: {environment}
Service: {service_name}
Time: {timestamp}
Duration: {duration}

Current Value: {current_value}
Threshold: {threshold}

Impact: {estimated_impact}
Affected Users: {affected_users}

Runbook: {runbook_link}
Dashboard: {dashboard_link}

Please acknowledge receipt and begin investigation immediately.
```

### Warning Alert Template
```
⚠️ WARNING: {alert_name}

Environment: {environment}
Service: {service_name}
Time: {timestamp}

Current Value: {current_value}
Threshold: {threshold}

Trend: {trend_direction}
Suggested Action: {suggested_action}

Dashboard: {dashboard_link}
```

## Integration with PagerDuty

### PagerDuty Service Configuration
```json
{
  "service": {
    "name": "XD Incentives Platform",
    "escalation_policy_id": "P1234567",
    "alert_creation": "create_alerts_and_incidents",
    "alert_grouping_parameters": {
      "type": "time",
      "config": {
        "timeout": 5
      }
    },
    "incident_urgency_rule": {
      "type": "use_support_hours",
      "during_support_hours": {
        "type": "constant",
        "urgency": "high"
      },
      "outside_support_hours": {
        "type": "constant",
        "urgency": "low"
      }
    }
  }
}
```

## Monitoring Checklist

### Daily Checks
- [ ] Review error rate trends
- [ ] Check system resource utilization
- [ ] Verify backup completion
- [ ] Review security alerts
- [ ] Check third-party service status

### Weekly Checks
- [ ] Analyze performance trends
- [ ] Review capacity planning metrics
- [ ] Audit alert noise and tune thresholds
- [ ] Review incident post-mortems
- [ ] Update monitoring documentation

### Monthly Checks
- [ ] Review and update alert thresholds
- [ ] Analyze cost optimization opportunities
- [ ] Conduct alert response drills
- [ ] Review SLA compliance
- [ ] Update runbooks and documentation