# XD Incentives Platform Operational Runbook

## Table of Contents
1. [Emergency Contacts](#emergency-contacts)
2. [System Overview](#system-overview)
3. [Common Operational Scenarios](#common-operational-scenarios)
4. [Incident Response Procedures](#incident-response-procedures)
5. [Troubleshooting Guides](#troubleshooting-guides)
6. [Recovery Procedures](#recovery-procedures)
7. [Maintenance Procedures](#maintenance-procedures)

## Emergency Contacts

### Escalation Chain
| Role | Name | Phone | Email | Slack |
|------|------|-------|-------|-------|
| On-Call Engineer | Rotation | PagerDuty | <EMAIL> | @oncall |
| Lead Engineer | TBD | TBD | <EMAIL> | @lead-eng |
| Engineering Manager | TBD | TBD | <EMAIL> | @eng-manager |
| CTO | TBD | TBD | <EMAIL> | @cto |

### External Vendors
| Service | Support Phone | Support Email | Account # |
|---------|--------------|---------------|-----------|
| AWS Support | 1-800-xxx-xxxx | Enterprise Support Console | xxxxx |
| Clerk Auth | support.clerk.dev | <EMAIL> | xxxxx |
| UPS API | 1-800-xxx-xxxx | <EMAIL> | xxxxx |
| Sentry | status.sentry.io | <EMAIL> | xxxxx |

## System Overview

### Critical System Components
```
User Traffic → CloudFront → ALB → ECS Fargate → RDS MySQL
                                ↓
                            Redis Cache
```

### Service Dependencies
- **Primary Database**: RDS MySQL (Multi-AZ)
- **Cache Layer**: ElastiCache Redis
- **Authentication**: Clerk
- **Email Service**: AWS SES
- **File Storage**: S3
- **Monitoring**: CloudWatch + Sentry

## Common Operational Scenarios

### Scenario 1: High API Latency

#### Symptoms
- API response time > 2 seconds
- User complaints about slow loading
- CloudWatch latency alarms triggered

#### Diagnosis Steps
1. Check CloudWatch ECS metrics for CPU/Memory usage
2. Review RDS performance insights for slow queries
3. Check Redis cache hit ratio
4. Review Sentry for performance bottlenecks
5. Check for ongoing deployments

#### Resolution Steps
```bash
# 1. Check ECS task health
aws ecs describe-services --cluster xd-incentives-prod --services xd-api

# 2. Scale ECS if needed
aws ecs update-service --cluster xd-incentives-prod \
  --service xd-api --desired-count 6

# 3. Check database connections
mysql -h xd-prod.rds.amazonaws.com -u admin -p \
  -e "SHOW PROCESSLIST;"

# 4. Clear problematic cache keys if needed
redis-cli -h xd-prod.cache.amazonaws.com
> FLUSHDB

# 5. Enable read replica for read-heavy queries
# Update Django settings to use read replica
```

### Scenario 2: Database Connection Exhaustion

#### Symptoms
- "Too many connections" errors
- Application unable to connect to database
- Connection pool exhausted alerts

#### Diagnosis Steps
1. Check current connection count
2. Identify connection sources
3. Look for connection leaks
4. Review recent code changes

#### Resolution Steps
```sql
-- 1. Check current connections
SELECT count(*) FROM information_schema.processlist;

-- 2. Identify top connection users
SELECT user, host, count(*) as connections 
FROM information_schema.processlist 
GROUP BY user, host 
ORDER BY connections DESC;

-- 3. Kill idle connections
SELECT concat('KILL ', id, ';') 
FROM information_schema.processlist 
WHERE time > 300 AND command = 'Sleep';

-- 4. Increase max connections (temporary)
SET GLOBAL max_connections = 500;
```

```bash
# 5. Restart ECS tasks to reset connection pools
aws ecs update-service --cluster xd-incentives-prod \
  --service xd-api --force-new-deployment
```

### Scenario 3: Memory Leak in Application

#### Symptoms
- Gradual increase in memory usage
- ECS tasks being killed for OOM
- Performance degradation over time

#### Diagnosis Steps
1. Review CloudWatch memory metrics trend
2. Check Sentry for memory-related errors
3. Analyze heap dumps if available
4. Review recent deployments

#### Resolution Steps
```bash
# 1. Get memory stats from running container
aws ecs execute-command --cluster xd-incentives-prod \
  --task <task-id> --container django \
  --command "python manage.py shell"

>>> import tracemalloc
>>> tracemalloc.start()
>>> # Wait and take snapshot
>>> snapshot = tracemalloc.take_snapshot()
>>> top_stats = snapshot.statistics('lineno')
>>> for stat in top_stats[:10]:
...     print(stat)

# 2. Rolling restart of tasks
for task in $(aws ecs list-tasks --cluster xd-incentives-prod \
  --service xd-api --query 'taskArns[]' --output text); do
  aws ecs stop-task --cluster xd-incentives-prod --task $task
  sleep 30
done

# 3. Rollback if issue persists
aws ecs update-service --cluster xd-incentives-prod \
  --service xd-api --task-definition xd-api:previous-version
```

### Scenario 4: Authentication Service Outage

#### Symptoms
- Users unable to log in
- 401/403 errors on all authenticated endpoints
- Clerk service health check failing

#### Diagnosis Steps
1. Check Clerk service status page
2. Verify API keys are valid
3. Check network connectivity to Clerk
4. Review error logs for specific failures

#### Resolution Steps
```python
# 1. Enable fallback authentication mode
# In Django settings
FALLBACK_AUTH_MODE = True

# 2. Use cached sessions for existing users
# Emergency bypass for critical operations
def emergency_auth_bypass(request):
    if settings.FALLBACK_AUTH_MODE:
        # Check Redis for cached session
        session_key = request.COOKIES.get('sessionid')
        if session_key:
            cached_user = cache.get(f'user_session:{session_key}')
            if cached_user:
                request.user = cached_user
                return True
    return False

# 3. Notify users of authentication issues
# Send emergency notification via multiple channels
```

### Scenario 5: Sudden Traffic Spike

#### Symptoms
- 10x normal traffic volume
- API rate limiting triggered
- Response times increasing
- Auto-scaling not keeping up

#### Diagnosis Steps
1. Identify traffic source (legitimate vs DDoS)
2. Check CloudFront and WAF metrics
3. Review auto-scaling policies
4. Monitor system resources

#### Resolution Steps
```bash
# 1. Enable emergency rate limiting
aws wafv2 update-web-acl --scope CLOUDFRONT \
  --id <acl-id> --lock-token <token> \
  --rules file://emergency-rate-limit.json

# 2. Manually scale ECS tasks
aws ecs update-service --cluster xd-incentives-prod \
  --service xd-api --desired-count 20

# 3. Increase RDS instance size (if needed)
aws rds modify-db-instance --db-instance-identifier xd-prod \
  --db-instance-class db.r5.2xlarge --apply-immediately

# 4. Enable read-only mode if necessary
# Set environment variable
aws ecs update-service --cluster xd-incentives-prod \
  --service xd-api \
  --environment "READ_ONLY_MODE=true"
```

## Incident Response Procedures

### Incident Classification

| Severity | Impact | Response Time | Examples |
|----------|--------|--------------|----------|
| **P1** | Complete outage | 15 min | Site down, data loss, security breach |
| **P2** | Major degradation | 30 min | Login failures, payment issues |
| **P3** | Minor degradation | 2 hours | Slow reports, UI glitches |
| **P4** | No immediate impact | 24 hours | Non-critical bugs |

### Incident Response Flow

```mermaid
graph TD
    A[Alert Triggered] --> B{Assess Severity}
    B -->|P1/P2| C[Page On-Call]
    B -->|P3/P4| D[Create Ticket]
    C --> E[Acknowledge within SLA]
    E --> F[Begin Investigation]
    F --> G[Implement Fix]
    G --> H[Verify Resolution]
    H --> I[Update Status Page]
    I --> J[Post-Mortem]
```

### Incident Commander Checklist

#### Initial Response (0-15 minutes)
- [ ] Acknowledge incident
- [ ] Assess severity and impact
- [ ] Open incident channel (#incident-xxx)
- [ ] Assign roles (Commander, Comm Lead, Tech Lead)
- [ ] Post initial status update

#### Investigation (15-30 minutes)
- [ ] Gather system metrics
- [ ] Review recent changes
- [ ] Check all dependencies
- [ ] Identify root cause
- [ ] Develop action plan

#### Mitigation (30+ minutes)
- [ ] Implement immediate fix
- [ ] Monitor system stability
- [ ] Communicate with stakeholders
- [ ] Update status page
- [ ] Plan long-term fix

#### Resolution
- [ ] Verify all systems operational
- [ ] Clear all alerts
- [ ] Final status update
- [ ] Schedule post-mortem
- [ ] Document timeline

## Troubleshooting Guides

### Database Issues

#### Slow Queries
```sql
-- Find slow queries
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    max_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- Check for missing indexes
SELECT
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats
WHERE schemaname = 'public'
    AND n_distinct > 100
    AND correlation < 0.1;
```

#### Connection Issues
```bash
# Test connectivity
nc -zv xd-prod.rds.amazonaws.com 3306

# Check security groups
aws ec2 describe-security-groups --group-ids sg-xxxxxx

# Verify credentials
mysql -h xd-prod.rds.amazonaws.com -u admin -p
```

### Redis Cache Issues

#### Cache Invalidation
```bash
# Connect to Redis
redis-cli -h xd-prod.cache.amazonaws.com

# Check memory usage
INFO memory

# Clear specific pattern
EVAL "return redis.call('del', unpack(redis.call('keys', ARGV[1])))" 0 "pattern:*"

# Monitor commands in real-time
MONITOR
```

### ECS/Container Issues

#### Container Health Checks
```bash
# Get task details
aws ecs describe-tasks --cluster xd-incentives-prod \
  --tasks <task-arn>

# Check container logs
aws logs get-log-events --log-group-name /ecs/xd-api \
  --log-stream-name <stream-name> --limit 100

# Execute command in container
aws ecs execute-command --cluster xd-incentives-prod \
  --task <task-id> --container django \
  --interactive --command "/bin/bash"
```

## Recovery Procedures

### Database Recovery

#### From Automated Backup
```bash
# 1. List available backups
aws rds describe-db-snapshots \
  --db-instance-identifier xd-prod

# 2. Restore to new instance
aws rds restore-db-instance-from-db-snapshot \
  --db-instance-identifier xd-prod-recovery \
  --db-snapshot-identifier <snapshot-id>

# 3. Verify data integrity
mysql -h xd-prod-recovery.rds.amazonaws.com -u admin -p \
  -e "SELECT COUNT(*) FROM claims;"

# 4. Switch application to new instance
# Update connection string in ECS task definition
```

#### Point-in-Time Recovery
```bash
# Restore to specific time
aws rds restore-db-instance-to-point-in-time \
  --source-db-instance-identifier xd-prod \
  --target-db-instance-identifier xd-prod-pitr \
  --restore-time 2024-01-15T03:30:00.000Z
```

### Application Rollback

#### Quick Rollback
```bash
# 1. Get previous task definition
aws ecs describe-task-definition \
  --task-definition xd-api:$((current-1))

# 2. Update service
aws ecs update-service --cluster xd-incentives-prod \
  --service xd-api \
  --task-definition xd-api:$((current-1))

# 3. Monitor rollout
aws ecs wait services-stable --cluster xd-incentives-prod \
  --services xd-api
```

### Cache Recovery

#### Redis Cache Rebuild
```python
# Django management command
# python manage.py rebuild_cache

from django.core.cache import cache
from django.db import models

def rebuild_cache():
    # Clear existing cache
    cache.clear()
    
    # Rebuild user sessions
    for user in User.objects.filter(is_active=True):
        cache.set(f'user:{user.id}', user, 3600)
    
    # Rebuild frequently accessed data
    for customer in Customer.objects.filter(status='active'):
        cache.set(f'customer:{customer.id}', customer, 7200)
    
    # Warm up report caches
    for report in Report.objects.filter(cached=True):
        report.generate_and_cache()
```

## Maintenance Procedures

### Scheduled Maintenance Window

#### Pre-Maintenance Checklist
- [ ] Send notification 48 hours in advance
- [ ] Prepare rollback plan
- [ ] Take database backup
- [ ] Update status page
- [ ] Disable alerts for maintenance window

#### Maintenance Steps
```bash
# 1. Enable maintenance mode
aws ecs update-service --cluster xd-incentives-prod \
  --service xd-api \
  --environment "MAINTENANCE_MODE=true"

# 2. Wait for active requests to complete
sleep 60

# 3. Perform maintenance tasks
# - Database migrations
# - Index rebuilding
# - Cache clearing
# - Infrastructure updates

# 4. Verify system health
./scripts/health_check.sh

# 5. Disable maintenance mode
aws ecs update-service --cluster xd-incentives-prod \
  --service xd-api \
  --environment "MAINTENANCE_MODE=false"
```

### Database Maintenance

#### Weekly Tasks
```sql
-- Analyze tables for query optimization
ANALYZE TABLE claims;
ANALYZE TABLE customers;
ANALYZE TABLE users;

-- Check for fragmentation
SELECT 
    table_name,
    data_length,
    index_length,
    data_free
FROM information_schema.tables
WHERE table_schema = 'xdincentives'
    AND data_free > 100000000;

-- Optimize fragmented tables
OPTIMIZE TABLE claims;
```

#### Monthly Tasks
```bash
# Full backup with verification
mysqldump -h xd-prod.rds.amazonaws.com -u admin -p \
  --single-transaction --routines --triggers \
  xdincentives > backup_$(date +%Y%m%d).sql

# Verify backup
mysql -h xd-test.rds.amazonaws.com -u admin -p \
  xdincentives < backup_$(date +%Y%m%d).sql
```

### Certificate Renewal

#### SSL Certificate Renewal Process
```bash
# 1. Check certificate expiration
openssl s_client -connect api.xdincentives.com:443 \
  -servername api.xdincentives.com 2>/dev/null | \
  openssl x509 -noout -dates

# 2. Request new certificate
aws acm request-certificate \
  --domain-name "*.xdincentives.com" \
  --validation-method DNS

# 3. Update CloudFront distribution
aws cloudfront update-distribution \
  --id E1234567890 \
  --distribution-config file://cf-config.json

# 4. Verify new certificate
curl -vI https://api.xdincentives.com 2>&1 | \
  grep "SSL certificate verify ok"
```

## Monitoring Scripts

### Health Check Script
```bash
#!/bin/bash
# health_check.sh

API_URL="https://api.xdincentives.com"
TIMEOUT=5

# Check API health
response=$(curl -s -o /dev/null -w "%{http_code}" \
  --max-time $TIMEOUT "$API_URL/health")

if [ "$response" != "200" ]; then
    echo "CRITICAL: API health check failed (HTTP $response)"
    exit 1
fi

# Check database
mysql -h xd-prod.rds.amazonaws.com -u monitor -p$DB_PASSWORD \
  -e "SELECT 1" > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "CRITICAL: Database connection failed"
    exit 1
fi

# Check Redis
redis-cli -h xd-prod.cache.amazonaws.com ping > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "WARNING: Redis connection failed"
    exit 2
fi

echo "OK: All systems operational"
exit 0
```

### Performance Check Script
```python
#!/usr/bin/env python3
# performance_check.py

import time
import requests
import statistics
from concurrent.futures import ThreadPoolExecutor

API_ENDPOINTS = [
    '/api/health',
    '/api/v1/claims',
    '/api/v1/customers',
    '/api/v1/users/me'
]

def check_endpoint(endpoint):
    url = f"https://api.xdincentives.com{endpoint}"
    start = time.time()
    try:
        response = requests.get(url, timeout=10)
        duration = time.time() - start
        return {
            'endpoint': endpoint,
            'status': response.status_code,
            'duration': duration
        }
    except Exception as e:
        return {
            'endpoint': endpoint,
            'status': 'error',
            'error': str(e)
        }

def main():
    with ThreadPoolExecutor(max_workers=10) as executor:
        results = list(executor.map(check_endpoint, API_ENDPOINTS))
    
    durations = [r['duration'] for r in results if 'duration' in r]
    
    print(f"Average response time: {statistics.mean(durations):.2f}s")
    print(f"P95 response time: {statistics.quantiles(durations, n=20)[18]:.2f}s")
    
    for result in results:
        if result['status'] != 200:
            print(f"WARNING: {result['endpoint']} returned {result['status']}")

if __name__ == '__main__':
    main()
```