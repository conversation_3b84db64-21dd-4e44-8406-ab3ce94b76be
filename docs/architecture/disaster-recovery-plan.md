# XD Incentives Platform Disaster Recovery Plan

## Executive Summary

This document outlines the disaster recovery (DR) and business continuity procedures for the XD Incentives Platform. Our DR strategy ensures minimal data loss (RPO: 1 hour) and rapid recovery (RTO: 4 hours) in the event of a catastrophic failure.

### Key Metrics
- **Recovery Point Objective (RPO)**: 1 hour
- **Recovery Time Objective (RTO)**: 4 hours
- **Service Level Agreement (SLA)**: 99.9% uptime
- **Maximum Tolerable Downtime (MTD)**: 24 hours

## Disaster Recovery Strategy

### DR Architecture Overview

```mermaid
graph TB
    subgraph Primary Region - US-East-1
        P_CF[CloudFront]
        P_ALB[ALB]
        P_ECS[ECS Fargate]
        P_RDS[RDS MySQL Primary]
        P_Redis[ElastiCache Redis]
        P_S3[S3 Buckets]
    end
    
    subgraph DR Region - US-West-2
        D_ALB[ALB - Standby]
        D_ECS[ECS Fargate - Standby]
        D_RDS[RDS MySQL Read Replica]
        D_Redis[ElastiCache Redis - Standby]
        D_S3[S3 Buckets - Replicated]
    end
    
    P_RDS -->|Async Replication| D_RDS
    P_S3 -->|Cross-Region Replication| D_S3
    P_CF -->|Failover| D_ALB
```

### Disaster Categories and Response

| Disaster Type | Severity | RTO | RPO | Response Strategy |
|--------------|----------|-----|-----|-------------------|
| **Region Failure** | Critical | 4 hours | 1 hour | Full DR failover |
| **AZ Failure** | High | 30 minutes | 0 | Automatic failover |
| **Service Failure** | Medium | 1 hour | 0 | Service restoration |
| **Data Corruption** | High | 2-8 hours | Variable | Point-in-time recovery |
| **Security Breach** | Critical | 1 hour | 0 | Isolation and recovery |
| **Application Failure** | Low | 30 minutes | 0 | Rollback deployment |

## Pre-Disaster Preparation

### Backup Strategy

#### Database Backups
```yaml
backup_configuration:
  automated_backups:
    enabled: true
    retention_period: 30 days
    backup_window: "03:00-04:00 UTC"
    preferred_maintenance_window: "sun:04:00-sun:05:00"
  
  manual_snapshots:
    frequency: weekly
    retention: 90 days
    naming_convention: "xd-prod-manual-{timestamp}"
  
  cross_region_snapshots:
    enabled: true
    target_region: us-west-2
    retention: 7 days
```

#### Application Data Backups
```yaml
s3_backup_configuration:
  versioning: enabled
  lifecycle_rules:
    - transition_to_ia: 30 days
    - transition_to_glacier: 90 days
    - expiration: 365 days
  
  cross_region_replication:
    destination: us-west-2
    storage_class: STANDARD_IA
    
  point_in_time_recovery:
    enabled: true
    retention: 35 days
```

### Infrastructure as Code
All infrastructure is defined in Terraform and stored in Git:
```bash
terraform/
├── modules/
│   ├── primary-region/
│   └── dr-region/
├── environments/
│   ├── production/
│   └── disaster-recovery/
└── shared/
    └── global-resources/
```

### Documentation Requirements
- [ ] Network diagrams updated quarterly
- [ ] Runbooks reviewed monthly
- [ ] Contact lists verified weekly
- [ ] DR procedures tested quarterly
- [ ] Recovery scripts maintained in Git

## Disaster Detection

### Automated Monitoring

#### Health Checks
```python
# Multi-region health check configuration
HEALTH_CHECK_CONFIG = {
    'primary_region': {
        'endpoint': 'https://api.xdincentives.com/health',
        'interval': 30,
        'timeout': 10,
        'healthy_threshold': 2,
        'unhealthy_threshold': 3
    },
    'dr_region': {
        'endpoint': 'https://dr-api.xdincentives.com/health',
        'interval': 60,
        'timeout': 10,
        'healthy_threshold': 2,
        'unhealthy_threshold': 3
    }
}
```

#### Disaster Detection Rules
```yaml
disaster_detection:
  region_failure:
    condition: "primary_health_check_failed AND duration > 5_minutes"
    severity: critical
    auto_failover: false
    notification: immediate
    
  database_failure:
    condition: "rds_connection_failed AND duration > 2_minutes"
    severity: critical
    auto_failover: true
    notification: immediate
    
  mass_service_failure:
    condition: "failed_services > 3"
    severity: high
    auto_failover: false
    notification: immediate
```

### Manual Detection Procedures
1. StatusPage.io monitoring
2. Customer reports via support channels
3. Third-party monitoring services
4. AWS Service Health Dashboard
5. Internal team observations

## Disaster Response Procedures

### Phase 1: Initial Response (0-15 minutes)

#### Incident Commander Actions
```bash
#!/bin/bash
# disaster_response_init.sh

# 1. Declare disaster
aws sns publish --topic-arn arn:aws:sns:us-east-1:xxx:disaster-alert \
  --message "DISASTER DECLARED: $DISASTER_TYPE"

# 2. Activate war room
./scripts/create_war_room.sh --channel "#disaster-recovery"

# 3. Check primary region status
aws health describe-events --region us-east-1 \
  --filter "services=EC2,RDS,ECS"

# 4. Verify DR region readiness
./scripts/verify_dr_readiness.sh --region us-west-2

# 5. Notify stakeholders
./scripts/send_notifications.sh --template disaster_initial \
  --recipients executives,customers,support
```

### Phase 2: Assessment (15-30 minutes)

#### Damage Assessment Checklist
- [ ] Identify affected services
- [ ] Determine data loss potential
- [ ] Assess recovery options
- [ ] Estimate recovery time
- [ ] Document current state

#### Decision Matrix
```python
def determine_recovery_strategy(assessment):
    if assessment['region_available'] == False:
        return 'FULL_DR_FAILOVER'
    elif assessment['database_corrupted'] == True:
        return 'POINT_IN_TIME_RECOVERY'
    elif assessment['services_degraded'] > 0.5:
        return 'PARTIAL_FAILOVER'
    else:
        return 'SERVICE_RESTORATION'
```

### Phase 3: Recovery Execution

```mermaid
flowchart TB
    Start([Disaster Detected]) --> Assess{Assess Impact}
    
    Assess -->|Region Down| FullDR[Full DR Failover]
    Assess -->|Service Degraded| Partial[Partial Failover]
    Assess -->|Data Corruption| PITR[Point-in-Time Recovery]
    Assess -->|App Failure| Rollback[Application Rollback]
    
    subgraph Full_DR["Full DR Failover Process"]
        FullDR --> DB1[1. Database Failover]
        DB1 --> DB1a[Promote Read Replica]
        DB1a --> DB1b[Update Connection Strings]
        DB1b --> DB1c[Verify Data Integrity]
        
        DB1c --> App1[2. Application Failover]
        App1 --> App1a[Deploy to DR Region]
        App1a --> App1b[Scale ECS Tasks]
        App1b --> App1c[Health Checks]
        
        App1c --> DNS1[3. DNS Failover]
        DNS1 --> DNS1a[Update Route53]
        DNS1a --> DNS1b[Verify Propagation]
        
        DNS1b --> Cache1[4. Cache Warming]
        Cache1 --> Cache1a[Clear Stale Data]
        Cache1a --> Cache1b[Warm Critical Caches]
        
        Cache1b --> Val1[5. Validation]
        Val1 --> Val1a[Test All Endpoints]
        Val1a --> Val1b[Verify Transactions]
    end
    
    subgraph Partial_Failover["Partial Failover Process"]
        Partial --> P1[Identify Failed Service]
        P1 --> P2[Failover Specific Service]
        P2 --> P3[Update Load Balancer]
        P3 --> P4[Verify Service Health]
    end
    
    subgraph PITR_Recovery["Point-in-Time Recovery"]
        PITR --> PITR1[Identify Recovery Point]
        PITR1 --> PITR2[Create DB from Backup]
        PITR2 --> PITR3[Verify Data Integrity]
        PITR3 --> PITR4[Switch Application]
        PITR4 --> PITR5[Validate Recovery]
    end
    
    subgraph App_Rollback["Application Rollback"]
        Rollback --> R1[Get Previous Version]
        R1 --> R2[Deploy Previous Version]
        R2 --> R3[Monitor Stability]
        R3 --> R4[Verify Fix]
    end
    
    Val1b --> Complete{Recovery Complete?}
    P4 --> Complete
    PITR5 --> Complete
    R4 --> Complete
    
    Complete -->|Yes| Monitor[Monitor Systems]
    Complete -->|No| Escalate[Escalate to Leadership]
    
    Monitor --> Notify[Notify Stakeholders]
    Escalate --> Manual[Manual Intervention]
    
    Manual --> Notify
    Notify --> PostMortem[Schedule Post-Mortem]
    
    subgraph Failback["Failback to Primary (When Ready)"]
        PostMortem -.-> FB1[Primary Region Ready?]
        FB1 -->|Yes| FB2[Sync Data to Primary]
        FB2 --> FB3[Canary Deployment]
        FB3 --> FB4[Progressive Traffic Shift]
        FB4 --> FB5[Full Failback Complete]
    end

    style Start fill:#ff6b6b
    style Full_DR fill:#4ecdc4
    style Partial_Failover fill:#95e1d3
    style PITR_Recovery fill:#f38181
    style App_Rollback fill:#aa96da
    style Complete fill:#ffd93d
    style Monitor fill:#6bcf7f
    style Failback fill:#c7ceea
```

#### Full DR Failover Procedure

##### Step 1: Database Failover
```bash
# Promote read replica to primary
aws rds promote-read-replica \
  --db-instance-identifier xd-dr-west \
  --backup-retention-period 7

# Wait for promotion
aws rds wait db-instance-available \
  --db-instance-identifier xd-dr-west

# Update connection strings
aws ssm put-parameter --name /xd/prod/db_host \
  --value "xd-dr-west.rds.amazonaws.com" \
  --overwrite
```

##### Step 2: Application Failover
```bash
# Deploy application to DR region
terraform apply -var="region=us-west-2" \
  -var="environment=disaster-recovery" \
  -auto-approve

# Scale up ECS tasks
aws ecs update-service --cluster xd-dr-cluster \
  --service xd-api --desired-count 10 \
  --region us-west-2

# Verify service health
./scripts/health_check.sh --region us-west-2
```

##### Step 3: DNS Failover
```bash
# Update Route53 records
aws route53 change-resource-record-sets \
  --hosted-zone-id Z123456789 \
  --change-batch file://dr-dns-failover.json

# Verify DNS propagation
dig api.xdincentives.com
```

##### Step 4: Cache Warming
```python
# warm_cache.py
import redis
from django.core.management import call_command

def warm_dr_cache():
    r = redis.Redis(host='xd-dr-west.cache.amazonaws.com')
    
    # Clear any stale data
    r.flushall()
    
    # Warm critical caches
    call_command('warm_user_cache')
    call_command('warm_customer_cache')
    call_command('warm_campaign_cache')
    
    print("DR cache warmed successfully")
```

#### Partial Failover Procedure

For service-specific failures:
```bash
# Failover specific service
./scripts/failover_service.sh \
  --service "authentication" \
  --from-region "us-east-1" \
  --to-region "us-west-2"
```

#### Data Recovery Procedure

##### Point-in-Time Recovery
```bash
# Identify recovery point
RECOVERY_TIME="2024-01-15T10:30:00Z"

# Create new instance from backup
aws rds restore-db-instance-to-point-in-time \
  --source-db-instance-identifier xd-prod \
  --target-db-instance-identifier xd-recovery \
  --restore-time $RECOVERY_TIME

# Verify data integrity
mysql -h xd-recovery.rds.amazonaws.com -u admin -p \
  -e "SELECT COUNT(*) FROM claims WHERE created_at > '$RECOVERY_TIME';"

# Switch application to recovered database
./scripts/switch_database.sh --target xd-recovery
```

### Phase 4: Validation (Post-Recovery)

#### System Validation Checklist
- [ ] All services responding
- [ ] Database consistency verified
- [ ] Cache populated
- [ ] Authentication working
- [ ] Payment processing functional
- [ ] Email notifications operational
- [ ] Monitoring restored
- [ ] Backups resuming

#### Validation Scripts
```bash
#!/bin/bash
# validate_recovery.sh

echo "Starting recovery validation..."

# Test API endpoints
for endpoint in health claims customers users payments; do
    response=$(curl -s -o /dev/null -w "%{http_code}" \
      https://api.xdincentives.com/v1/$endpoint)
    if [ "$response" != "200" ]; then
        echo "ERROR: $endpoint endpoint failing"
        exit 1
    fi
done

# Test database
mysql -h $DB_HOST -u admin -p$DB_PASSWORD \
  -e "SELECT COUNT(*) FROM claims;" || exit 1

# Test Redis
redis-cli -h $REDIS_HOST ping || exit 1

# Test S3 access
aws s3 ls s3://xd-incentives-media/ || exit 1

echo "Recovery validation complete!"
```

## Post-Disaster Procedures

### Failback to Primary Region

#### Prerequisite Checks
- [ ] Primary region fully operational
- [ ] No data corruption in primary
- [ ] Network connectivity restored
- [ ] All services healthy

#### Failback Procedure
```bash
# 1. Sync data from DR to primary
./scripts/sync_dr_to_primary.sh

# 2. Prepare primary environment
terraform apply -var="region=us-east-1" \
  -var="environment=production"

# 3. Switch traffic back (canary deployment)
aws route53 change-resource-record-sets \
  --hosted-zone-id Z123456789 \
  --change-batch file://failback-canary-10.json

# Monitor for 30 minutes, then increase traffic
for weight in 25 50 75 100; do
    aws route53 change-resource-record-sets \
      --hosted-zone-id Z123456789 \
      --change-batch file://failback-canary-$weight.json
    sleep 1800  # Wait 30 minutes
done
```

### Post-Mortem Requirements

#### Timeline Documentation
```markdown
## Incident Timeline

- **T+0:00** - Initial detection
- **T+0:05** - Disaster declared
- **T+0:15** - War room activated
- **T+0:30** - Recovery strategy determined
- **T+1:00** - Failover initiated
- **T+4:00** - Services restored
- **T+24:00** - Failback completed
```

#### Root Cause Analysis Template
1. **What happened?**
2. **Why did it happen?**
3. **How was it detected?**
4. **How was it resolved?**
5. **What was the impact?**
6. **What will prevent recurrence?**

### Communication Plan

#### Internal Communications
```yaml
communication_matrix:
  initial_alert:
    - channel: PagerDuty
    - recipients: [on-call, incident-commander]
    - timing: immediate
    
  status_updates:
    - channel: Slack (#incident)
    - recipients: [engineering, leadership]
    - frequency: every 30 minutes
    
  executive_briefing:
    - channel: Email + Phone
    - recipients: [C-suite]
    - timing: within 1 hour
```

#### External Communications
```yaml
customer_communication:
  initial_notification:
    template: service_disruption_initial
    channels: [statuspage, email, in-app]
    timing: within 15 minutes
    
  progress_updates:
    template: service_disruption_update
    channels: [statuspage, twitter]
    frequency: hourly
    
  resolution_notice:
    template: service_restored
    channels: [statuspage, email, in-app]
    timing: immediately upon resolution
```

## Testing and Maintenance

### DR Testing Schedule

| Test Type | Frequency | Duration | Scope |
|-----------|-----------|----------|-------|
| **Backup Verification** | Daily | 30 min | Automated backup integrity |
| **Component Failover** | Weekly | 2 hours | Individual service failover |
| **Partial DR Test** | Monthly | 4 hours | Critical services only |
| **Full DR Test** | Quarterly | 8 hours | Complete failover and failback |
| **Chaos Engineering** | Monthly | 2 hours | Random failure injection |

### Test Scenarios

#### Scenario 1: Database Failure
```bash
# Simulate primary database failure
./tests/simulate_db_failure.sh --duration 30m

# Expected outcome: Automatic failover to read replica
# Recovery time: < 5 minutes
```

#### Scenario 2: Region Outage
```bash
# Simulate complete region failure
./tests/simulate_region_failure.sh --region us-east-1

# Expected outcome: Manual failover to DR region
# Recovery time: < 4 hours
```

#### Scenario 3: Data Corruption
```bash
# Simulate data corruption scenario
./tests/simulate_data_corruption.sh --table claims

# Expected outcome: Point-in-time recovery
# Recovery time: < 2 hours
```

### Maintenance Tasks

#### Weekly Tasks
- [ ] Verify backup completion
- [ ] Test backup restoration (sample)
- [ ] Review monitoring alerts
- [ ] Update contact lists
- [ ] Check DR infrastructure health

#### Monthly Tasks
- [ ] Full backup restoration test
- [ ] DR infrastructure updates
- [ ] Runbook review and update
- [ ] Team training exercise
- [ ] Cost optimization review

#### Quarterly Tasks
- [ ] Full DR drill
- [ ] Infrastructure audit
- [ ] Security review
- [ ] Vendor contract review
- [ ] Capacity planning

## Appendices

### A. Contact Information

| Role | Primary | Backup | Escalation |
|------|---------|--------|------------|
| Incident Commander | Name/Phone | Name/Phone | CTO |
| Database Admin | Name/Phone | Name/Phone | Lead DBA |
| Infrastructure Lead | Name/Phone | Name/Phone | VP Engineering |
| Security Lead | Name/Phone | Name/Phone | CISO |

### B. Critical Dependencies

| Service | Provider | Support | Account |
|---------|----------|---------|---------|
| DNS | Route53 | AWS Support | xxx |
| CDN | CloudFront | AWS Support | xxx |
| Database | RDS MySQL | AWS Support | xxx |
| Auth | Clerk | <EMAIL> | xxx |

### C. Recovery Scripts Location

All recovery scripts are maintained in:
```
GitHub: xdincentives/disaster-recovery
Path: /scripts/dr/
```

### D. Configuration Files

Critical configuration files for DR:
```
/terraform/environments/disaster-recovery/
/scripts/dr/config/
/ansible/playbooks/disaster-recovery/
```

### E. Compliance Requirements

- **SOC 2**: Recovery procedures must be tested quarterly
- **GDPR**: Data must be recoverable within 72 hours
- **PCI DSS**: Payment data must have separate recovery procedures
- **Business Insurance**: DR tests must be documented for coverage