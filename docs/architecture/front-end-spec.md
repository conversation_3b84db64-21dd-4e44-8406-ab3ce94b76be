# XD Incentives Platform UI/UX Specification

## Introduction

This document defines the user experience goals, information architecture, user flows, and visual design specifications for the XD Incentives Platform's user interface. It serves as the foundation for visual design and frontend development, ensuring a cohesive and user-centered experience.

### Overall UX Goals & Principles
This section outlines the core principles that will guide our design. They are based on the findings from the UX audit and the specific needs of our users.

**Target User Personas:**
Our design will cater to four distinct user roles, with an initial primary focus on the MSR:
* **Marketer Sales Rep (MSR):** The primary daily user, focused on submitting sales claims and tracking their earnings and customer status.
* **Marketer Sales Manager (MSM):** Approves or denies claims from their MSRs and monitors team performance.
* **Territory Sales Manager (TSM):** Approves new customers to ensure data validity and oversees territory-level sales analytics.
* **National Sales Manager (NSM):** An oversight role focused on high-level national reporting and dashboards with minimal transactional tasks.

**Usability Goals:**
* **Efficiency:** Streamline core workflows, like claim submission, to be completed in minimal steps with clear direction.
* **Clarity:** Eliminate dashboard clutter by showing only role-relevant content and provide immediate, unambiguous feedback for user actions, such as success confirmations and status indicators.
* **Learnability:** Create an intuitive and predictable interface that requires minimal training, enabling users to confidently perform their tasks from day one.
* **Accessibility:** Ensure the entire platform is fully compliant with **WCAG 2.1 AA** standards, including high-contrast colors, appropriate text sizes, and full keyboard and screen reader support.

**Design Principles:**
1. **Clarity First:** The design will prioritize a clean visual hierarchy, scannable typography, and unambiguous interactive cues over purely aesthetic trends.
2. **Role-Relevant by Default:** The interface will adapt to the user's role, displaying only the modules, data, and navigation items that are relevant to their specific tasks and responsibilities.
3. **Consistency is Key:** We will establish a comprehensive design system for all components, colors, and typography to create a cohesive and professional experience.
4. **Feedback is Paramount:** Every user action must have an immediate and clear system response (e.g., success confirmations, status updates).

## Information Architecture (IA)

### Site Map / Screen Inventory
This diagram illustrates the primary screens and their relationships for the MSR (Marketer Sales Rep) persona.

```mermaid
graph TD
    A[Dashboard] --> B[Claims Management];
    A --> C[Customer Management];
    A --> D[My Profile];
    A --> E[Support];

    subgraph Claims Management
        B1[View Claim History & Status]
        B2[Submit New Claim]
        B3[View Payouts]
    end

    subgraph Customer Management
        C1[View Customer List]
        C2[Submit New Customer]
        C3[Edit Existing Customer]
    end

    subgraph My Profile
        D1[View Personal Info]
        D2[W9 Form Upload & Status]
    end
    
    subgraph Support
        E1[FAQs]
        E2[Tutorials & Videos]
        E3[Contact Information]
    end

    B --> B1 & B2 & B3
    C --> C1 & C2 & C3
    D --> D1 & D2
    E --> E1 & E2 & E3
````

#### Navigation Structure

* **Primary Navigation:** Will be persistently visible on desktop and tablet views. The main navigation items for an MSR will be: Dashboard, Claims Management, Customer Management, My Profile, Support.
* **Secondary Navigation:** Secondary tasks will be accessed through tabs or buttons within their parent section to reduce complexity.
* **Breadcrumb Strategy:** A breadcrumb trail (e.g., `Home > Claims > Claim #12345`) will be present at the top of each page.

### User Flows

#### MSR Submits a New Sales Claim

* **User Goal:** To quickly and accurately submit a sales claim with an invoice.
* **Entry Points:** A "Submit New Claim" button on the main dashboard.
* **Success Criteria:** The MSR receives a clear confirmation that their claim has been successfully submitted and can immediately see its "Pending" status.

<!-- end list -->

```mermaid
graph TD
    A[Start: MSR Clicks 'Submit New Claim'] --> B{Choose Submission Method};
    B -- Upload Invoice (OCR) --> C[User Captures/Uploads Invoice];
    B -- Enter Manually --> D[User Enters Multi-Step Wizard];
    
    C --> E[System Extracts Data via OCR];
    E --> F[User Reviews/Edits Pre-filled Form];
    F --> G[User Confirms Details];
    
    subgraph Manual Entry Wizard
        D --> H[Step 1: Select Approved Customer];
        H --> I[Step 2: Enter Sale/Product Details];
        I --> J[Step 3: Upload Invoice Image];
        J --> G;
    end
    
    G --> K[System Creates Claim Record];
    K --> L[End: Success Confirmation Screen is Displayed];
```

### Wireframes & Mockups

All high-fidelity visual designs will be created in a tool like Figma. The following is a conceptual layout for a key screen based on the UX Audit.

#### Key Screen Layouts

**Screen: MSR Dashboard**

* **Purpose:** To provide the MSR with an at-a-glance, mobile-first view of their most important information.
* **Key Elements:** A prominent "Submit New Claim" button, a "W9 Status" banner, Key Metric cards (YTD Earnings, Estimated Payouts, Contest Standings), a "Recent Claims" list, and a consolidated "Campaigns & Promotions" module.

### Component Library / Design System

#### Design System Approach

We will create a new design system built on **Shadcn UI** and **Tailwind CSS**. **Storybook** will serve as our interactive, living documentation for the component library, providing a collaborative workspace for developers and designers.

#### Core Components

* **Button:** Will have `Primary`, `Secondary`, and `Destructive` variants with `Default`, `Hover`, `Active`, `Disabled`, `Loading` states.
* **Card:** Will have `Default` and `Interactive` variants to provide clear clickable cues.
* **Form Input:** Will have `Default`, `Focus`, `Valid`, `Invalid` states and require an associated `<label>`.

### Branding & Style Guide

#### Visual Identity

The visual identity will prominently incorporate Citgo’s official brand palette to enforce brand recognition.

#### Color Palette

An intentional color system will be implemented to meet WCAG AA contrast standards. The "Go for the Green" theme color will be used as an accent, not a background.

#### Typography

The primary font will be the **Citgo brand font (Univers)**. Body text will have a **minimum font size of 16px** to ensure legibility.

#### Iconography

All icons will be redesigned as modern **SVGs** for performance, scalability, and accessibility.

### Accessibility Requirements

#### Compliance Target

* **Standard:** The platform will adhere to the **Web Content Accessibility Guidelines (WCAG) 2.1 at Level AA**.

#### Key Requirements

* **Visual:** All text will meet a 4.5:1 contrast ratio, links will have non-color indicators, and focus indicators will be highly visible.
* **Interaction:** The entire application will be fully navigable and operable via keyboard, and all touch targets will be sufficiently large.
* **Content:** All images will have descriptive `alt` text, content will use a proper heading structure, and all form fields will have associated labels.

### Responsiveness Strategy

#### Breakpoints

We will use a standard four-point breakpoint system: **Mobile** (\<768px), **Tablet** (768px+), **Desktop** (1024px+), and **Wide** (1440px+).

#### Adaptation Patterns

Layouts will be single-column on mobile and adapt to multi-column grids on larger screens. Navigation will be in a slide-out drawer on mobile and persistently visible on desktop.

### Animation & Micro-interactions

#### Motion Principles

Animation will be **Purposeful**, **Performant**, **Subtle & Quick**, and **Accessible** (respecting `prefers-reduced-motion`).

### Performance Considerations

#### Performance Goals

* **Page Load:** \< 3 seconds for initial load, \< 1 second for subsequent navigations.
* **Interaction Response:** \< 100ms for UI feedback; \< 2 seconds for data requests.

#### Design Strategies

We will use Image Optimization, Lazy Loading, Code Splitting, and Skeleton Loaders to improve actual and perceived performance.