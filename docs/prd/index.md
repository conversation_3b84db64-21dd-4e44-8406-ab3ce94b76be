# XD Incentives Platform Product Requirements Document (PRD)

## Table of Contents

- [XD Incentives Platform Product Requirements Document (PRD)](#table-of-contents)
  - [Goals and Background Context](./goals-and-background-context.md)
    - [Goals](./goals-and-background-context.md#goals)
    - [Background Context](./goals-and-background-context.md#background-context)
  - [Requirements](./requirements.md)
    - [Functional](./requirements.md#functional)
    - [Non-Functional](./requirements.md#non-functional)
  - [User Interface Design Goals](./user-interface-design-goals.md)
    - [Overall UX Vision](./user-interface-design-goals.md#overall-ux-vision)
    - [Key Interaction Paradigms](./user-interface-design-goals.md#key-interaction-paradigms)
    - [Core Screens and Views](./user-interface-design-goals.md#core-screens-and-views)
    - [Accessibility: WCAG 2.1 AA](./user-interface-design-goals.md#accessibility-wcag-21-aa)
    - [Branding](./user-interface-design-goals.md#branding)
    - [Target Device and Platforms: Web Responsive](./user-interface-design-goals.md#target-device-and-platforms-web-responsive)
  - [Technical Assumptions](./technical-assumptions.md)
    - [Repository Structure: Monorepo](./technical-assumptions.md#repository-structure-monorepo)
    - [Service Architecture](./technical-assumptions.md#service-architecture)
    - [Testing Requirements](./technical-assumptions.md#testing-requirements)
    - [Additional Technical Assumptions and Requests](./technical-assumptions.md#additional-technical-assumptions-and-requests)
  - [Epic List](./epic-list.md)
  - [Epic Details](./epic-details.md)
    - [Epic 1: Infrastructure & Platform Foundation (Revised)](./epic-details.md#epic-1-infrastructure-platform-foundation-revised)
      - [Story 1.1: Project Initialization & Quality Tooling](./epic-details.md#story-11-project-initialization-quality-tooling)
      - [Story 1.2: Containerized Local Development Environment](./epic-details.md#story-12-containerized-local-development-environment)
      - [Story 1.3: IaC Setup for Core AWS Services](./epic-details.md#story-13-iac-setup-for-core-aws-services)
      - [Story 1.4: Backend Application Scaffolding](./epic-details.md#story-14-backend-application-scaffolding)
      - [Story 1.5: Frontend Application Scaffolding with Storybook](./epic-details.md#story-15-frontend-application-scaffolding-with-storybook)
      - [Story 1.6: Cypress Test Suite Setup](./epic-details.md#story-16-cypress-test-suite-setup)
      - [Story 1.7: CI/CD Pipeline with Automated Testing](./epic-details.md#story-17-cicd-pipeline-with-automated-testing)
      - [Story 1.8: Health Check Endpoint & E2E Test](./epic-details.md#story-18-health-check-endpoint-e2e-test)
    - [Epic 2: Authentication & Security (Revised)](./epic-details.md#epic-2-authentication-security-revised)
      - [Story 2.1: Basic User Sign-Up and Sign-In](./epic-details.md#story-21-basic-user-sign-up-and-sign-in)
      - [Story 2.2: Model User Attributes and Sales Hierarchy](./epic-details.md#story-22-model-user-attributes-and-sales-hierarchy)
      - [Story 2.3: Enhance JWTs with Dynamic Authorization Claims](./epic-details.md#story-23-enhance-jwts-with-dynamic-authorization-claims)
      - [Story 2.4: Implement a Dynamic API Authorization Middleware](./epic-details.md#story-24-implement-a-dynamic-api-authorization-middleware)
      - [Story 2.5: Implement Relationship-Based Access (ReBAC)](./epic-details.md#story-25-implement-relationship-based-access-rebac)
      - [Story 2.6: Implement Attribute-Based Access (ABAC)](./epic-details.md#story-26-implement-attribute-based-access-abac)
    - [Epic 3: Customer & Claim Management Foundation](./epic-details.md#epic-3-customer-claim-management-foundation)
      - [Story 3.1: Create Customer Data Model](./epic-details.md#story-31-create-customer-data-model)
      - [Story 3.2: Implement New Customer Submission Form](./epic-details.md#story-32-implement-new-customer-submission-form)
      - [Story 3.3: Implement TSM Customer Approval Workflow](./epic-details.md#story-33-implement-tsm-customer-approval-workflow)
      - [Story 3.4: Integrate Address Validation Service](./epic-details.md#story-34-integrate-address-validation-service)
      - [Story 3.5: Create Claim Data Model](./epic-details.md#story-35-create-claim-data-model)
      - [Story 3.6: MSR Claim Submission UI](./epic-details.md#story-36-msr-claim-submission-ui)
    - [Epic 4: Campaign Engine](./epic-details.md#epic-4-campaign-engine)
      - [Story 4.1: Campaign & Rule Data Models](./epic-details.md#story-41-campaign-rule-data-models)
      - [Story 4.2: Admin UI for Basic Campaign Creation](./epic-details.md#story-42-admin-ui-for-basic-campaign-creation)
      - [Story 4.3: Implement Product-Based Payout Rule](./epic-details.md#story-43-implement-product-based-payout-rule)
      - [Story 4.4: Implement Tiered & Capped Payout Logic](./epic-details.md#story-44-implement-tiered-capped-payout-logic)
      - [Story 4.5: Implement Manager Payout Rule](./epic-details.md#story-45-implement-manager-payout-rule)
      - [Story 4.6: Implement Points-Based Contest Logic & Leaderboard](./epic-details.md#story-46-implement-points-based-contest-logic-leaderboard)
    - [Epic 5: Claim Review & Approval](./epic-details.md#epic-5-claim-review-approval)
      - [Story 5.1: MSM Claim Queue API](./epic-details.md#story-51-msm-claim-queue-api)
      - [Story 5.2: MSM Claim Review Dashboard UI](./epic-details.md#story-52-msm-claim-review-dashboard-ui)
      - [Story 5.3: Single Claim Detail View and Actions](./epic-details.md#story-53-single-claim-detail-view-and-actions)
      - [Story 5.4: Implement Batch Claim Operations](./epic-details.md#story-54-implement-batch-claim-operations)
      - [Story 5.5: Capture Claim Denial Reasons](./epic-details.md#story-55-capture-claim-denial-reasons)
    - [Epic 6: Fund Management & Payout Calculation](./epic-details.md#epic-6-fund-management-payout-calculation)
      - [Story 6.1: Payout Calculation Service](./epic-details.md#story-61-payout-calculation-service)
      - [Story 6.2: Implement Fund & Budget Management](./epic-details.md#story-62-implement-fund-budget-management)
      - [Story 6.3: Implement W9 Tax Document Collection](./epic-details.md#story-63-implement-w9-tax-document-collection)
      - [Story 6.4: Payout Export for Finance Teams](./epic-details.md#story-64-payout-export-for-finance-teams)
      - [Story 6.5: User-Facing Dispute Submission](./epic-details.md#story-65-user-facing-dispute-submission)
      - [Story 6.6: Admin Dispute Resolution UI](./epic-details.md#story-66-admin-dispute-resolution-ui)
    - [Epic 7: MSR Dashboard & Portal](./epic-details.md#epic-7-msr-dashboard-portal)
      - [Story 7.1: MSR Dashboard Layout & Navigation](./epic-details.md#story-71-msr-dashboard-layout-navigation)
      - [Story 7.2: Real-Time Earnings Widget](./epic-details.md#story-72-real-time-earnings-widget)
      - [Story 7.3: Claim History & Status List](./epic-details.md#story-73-claim-history-status-list)
      - [Story 7.4: Campaign Progress Visualization](./epic-details.md#story-74-campaign-progress-visualization)
      - [Story 7.5: MSR Profile & Tax Document Page](./epic-details.md#story-75-msr-profile-tax-document-page)
    - [Epic 8: Manager Dashboards (MSM, TSM, NSM)](./epic-details.md#epic-8-manager-dashboards-msm-tsm-nsm)
      - [Story 8.1: MSM Team Performance Dashboard](./epic-details.md#story-81-msm-team-performance-dashboard)
      - [Story 8.2: TSM Territory Oversight Dashboard](./epic-details.md#story-82-tsm-territory-oversight-dashboard)
      - [Story 8.3: TSM Comparative Analytics](./epic-details.md#story-83-tsm-comparative-analytics)
      - [Story 8.4: NSM National KPI Dashboard](./epic-details.md#story-84-nsm-national-kpi-dashboard)
      - [Story 8.5: NSM Performance Drill-Down](./epic-details.md#story-85-nsm-performance-drill-down)
    - [Epic 9: Communications & Content](./epic-details.md#epic-9-communications-content)
      - [Story 9.1: Email Service Integration](./epic-details.md#story-91-email-service-integration)
      - [Story 9.2: Implement Transactional Emails for Claims](./epic-details.md#story-92-implement-transactional-emails-for-claims)
      - [Story 9.3: Real-Time In-App Notification System](./epic-details.md#story-93-real-time-in-app-notification-system)
      - [Story 9.4: Static Content Management System (CMS)](./epic-details.md#story-94-static-content-management-system-cms)
      - [Story 9.5: Display Static Content in Frontend](./epic-details.md#story-95-display-static-content-in-frontend)
    - [Epic 10: Core Reporting & Analytics](./epic-details.md#epic-10-core-reporting-analytics)
      - [Story 10.1: Reporting API Foundation](./epic-details.md#story-101-reporting-api-foundation)
      - [Story 10.2: Sales Performance Report](./epic-details.md#story-102-sales-performance-report)
      - [Story 10.3: Payouts Report](./epic-details.md#story-103-payouts-report)
      - [Story 10.4: Report Export to CSV](./epic-details.md#story-104-report-export-to-csv)
      - [Story 10.5: Role-Based Access to Reports](./epic-details.md#story-105-role-based-access-to-reports)
    - [Epic 11: Data Management Tools (Revised)](./epic-details.md#epic-11-data-management-tools-revised)
      - [Story 11.1: CSV Parsing & Validation Service](./epic-details.md#story-111-csv-parsing-validation-service)
      - [Story 11.2: Admin UI for CSV Import](./epic-details.md#story-112-admin-ui-for-csv-import)
      - [Story 11.3: Implement User Data Import](./epic-details.md#story-113-implement-user-data-import)
      - [Story 11.4: Implement Customer Data Import](./epic-details.md#story-114-implement-customer-data-import)
      - [Story 11.5: Implement Sales Claim Data Import](./epic-details.md#story-115-implement-sales-claim-data-import)
      - [Story 11.6: Import Error Handling & Reporting](./epic-details.md#story-116-import-error-handling-reporting)
    - [Epic 12: Historical Data Migration](./epic-details.md#epic-12-historical-data-migration)
      - [Story 12.1: Data Mapping & Transformation Logic](./epic-details.md#story-121-data-mapping-transformation-logic)
      - [Story 12.2: Develop User Migration Script](./epic-details.md#story-122-develop-user-migration-script)
      - [Story 12.3: Develop Transaction Migration Script](./epic-details.md#story-123-develop-transaction-migration-script)
      - [Story 12.4: Implement Data Validation & Reconciliation Script](./epic-details.md#story-124-implement-data-validation-reconciliation-script)
      - [Story 12.5: Execute Staging Environment Migration & Validation](./epic-details.md#story-125-execute-staging-environment-migration-validation)
      - [Story 12.6: Plan & Document Production Cutover](./epic-details.md#story-126-plan-document-production-cutover)
    - [Epic 13: Performance & Caching](./epic-details.md#epic-13-performance-caching)
      - [Story 13.1: Implement Backend Caching with Redis](./epic-details.md#story-131-implement-backend-caching-with-redis)
      - [Story 13.2: Configure CDN for Frontend Assets](./epic-details.md#story-132-configure-cdn-for-frontend-assets)
      - [Story 13.3: Performance Monitoring & Baseline Measurement](./epic-details.md#story-133-performance-monitoring-baseline-measurement)
      - [Story 13.4: Optimize Slow Database Queries and API Endpoints](./epic-details.md#story-134-optimize-slow-database-queries-and-api-endpoints)
      - [Story 13.5: Frontend Performance Optimization](./epic-details.md#story-135-frontend-performance-optimization)
      - [Story 13.6: Execute Load Testing](./epic-details.md#story-136-execute-load-testing)
    - [Epic 14: Compliance Hardening (SOC 2 & GDPR)](./epic-details.md#epic-14-compliance-hardening-soc-2-gdpr)
      - [Story 14.1: Enhance Audit Trail for Compliance](./epic-details.md#story-141-enhance-audit-trail-for-compliance)
      - [Story 14.2: Verify Data Encryption at Rest](./epic-details.md#story-142-verify-data-encryption-at-rest)
      - [Story 14.3: Implement User Consent Management (GDPR)](./epic-details.md#story-143-implement-user-consent-management-gdpr)
      - [Story 14.4: Implement "Right to Erasure" (GDPR)](./epic-details.md#story-144-implement-right-to-erasure-gdpr)
      - [Story 14.5: Implement Data Portability (GDPR)](./epic-details.md#story-145-implement-data-portability-gdpr)
      - [Story 14.6: Implement Security Monitoring for SOC 2](./epic-details.md#story-146-implement-security-monitoring-for-soc-2)
    - [Epic 15: Accessibility Compliance (WCAG)](./epic-details.md#epic-15-accessibility-compliance-wcag)
      - [Story 15.1: Setup Automated Accessibility Testing](./epic-details.md#story-151-setup-automated-accessibility-testing)
      - [Story 15.2: Keyboard Navigation & Focus Management Audit](./epic-details.md#story-152-keyboard-navigation-focus-management-audit)
      - [Story 15.3: Screen Reader Compatibility Audit](./epic-details.md#story-153-screen-reader-compatibility-audit)
      - [Story 15.4: Color Contrast & Visuals Audit](./epic-details.md#story-154-color-contrast-visuals-audit)
      - [Story 15.5: Forms & Interactive Controls Audit](./epic-details.md#story-155-forms-interactive-controls-audit)
      - [Story 15.6: Generate Final Accessibility Conformance Report](./epic-details.md#story-156-generate-final-accessibility-conformance-report)
