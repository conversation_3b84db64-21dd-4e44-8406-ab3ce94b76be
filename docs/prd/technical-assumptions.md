# Technical Assumptions

## Repository Structure: Monorepo
The project will be managed within a single monorepo. The root of the repository will contain `/frontend` and `/backend` folders to clearly separate the application codebases.
* **Rationale:** This approach simplifies dependency management, promotes code sharing, and streamlines the CI/CD process while maintaining a clear separation of concerns between the frontend and backend.

## Service Architecture
The MVP will be built as a modular monolith.
* **Rationale:** This strategy allows for rapid initial development and deployment while being explicitly designed for a gradual evolution into a microservices architecture in the future as the platform scales. Core domains (auth, campaigns, claims) will be built as distinct modules within the monolith to facilitate this transition.

## Testing Requirements
A comprehensive testing strategy will be implemented, covering the full testing pyramid.
* **Rationale:** To ensure financial accuracy and system reliability, a multi-layered testing approach is mandatory. This includes unit tests (Jest/Pytest), integration tests, and end-to-end (E2E) tests (Cypress) to validate critical user flows and business logic.

## Additional Technical Assumptions and Requests
* **Primary Tech Stack:** The platform will be built using React v19+ (Frontend) and Django v5.2.3+ (Backend).
* **Cloud Provider:** The entire infrastructure will be hosted on Amazon Web Services (AWS).
* **Database:** Amazon RDS for MySQL 8.4 will be the primary transactional database, with AWS ElastiCache (Redis) used for caching.
* **Infrastructure as Code (IaC):** All cloud infrastructure will be managed and provisioned using Terraform to ensure reproducible and consistent environments.
* **Authentication:** User authentication, including SSO and MFA, will be handled by integrating the Clerk service.
* **CI/CD:** The continuous integration and deployment pipeline will be managed using GitHub Actions.
* **Data Migration:** It is assumed that clean data exports in standard formats (CSV/JSON) will be available from the legacy Django/MySQL system for migration.
* **Manual Processes:** The MVP will rely on manual CSV import/export for data management with external systems. Full enterprise integrations (Salesforce, SAP) are out of scope for the MVP.
