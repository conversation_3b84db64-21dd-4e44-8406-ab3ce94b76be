# Epic List

1. **Epic 1: Infrastructure & Platform Foundation:** Establish the multi-tenant AWS infrastructure, CI/CD pipeline, and core data models necessary to support the application.
2. **Epic 2: Authentication & Security:** Implement the 4-tier role system, enterprise SSO, and multi-factor authentication to secure the platform.
3. **Epic 3: Customer & Claim Management Foundation:** Develop the core workflows for customer creation (with TSM approval) and the initial submission of sales claims by MSRs.
4. **Epic 4: Campaign Engine:** Build the backend rule engine to configure and manage all required Citgo incentive campaigns and contests.
5. **Epic 5: Claim Review & Approval:** Create the dashboard and tools for MSMs to efficiently review, approve, or deny submitted claims in batches.
6. **Epic 6: Fund Management & Payout Calculation:** Implement the automated payout calculation engine, budget tracking, and the dispute management system.
7. **Epic 7: MSR Dashboard & Portal:** Develop the personalized, mobile-first portal for MSRs to track their earnings and claim status in real-time.
8. **Epic 8: Manager Dashboards (MSM, TSM, NSM):** Create the role-specific dashboards for all levels of management to monitor performance and KPIs.
9. **Epic 9: Communications & Content:** Integrate email (AWS SES) and in-app notifications, and build the static content management system.
10. **Epic 10: Core Reporting & Analytics:** Implement the standard operational reports and executive dashboards for performance visibility.
11. **Epic 11: Data Management Tools:** Build the CSV import and export tools required for manual data operations in the MVP.
12. **Epic 12: Historical Data Migration:** Execute the complete migration of 9 years of historical transaction and user data from the legacy system.
13. **Epic 13: Performance & Caching:** Implement the multi-layer caching strategy (Redis) and optimize application performance to meet the <2 second response time target.
14. **Epic 14: Compliance Hardening (SOC 2 & GDPR):** Implement the specific controls, audit trails, and features required to prepare for SOC 2 and GDPR compliance.
15. **Epic 15: Accessibility Compliance (WCAG):** Ensure the entire frontend is compliant with WCAG 2.1 AA standards for accessibility.
