# **Statement of Work (SOW)**

## **Citgo - Go For The Green, Powered By XD Incentives**

## **1. Executive Summary**

Integrity XD will develop and deliver the XD Incentives platform, a solution designed to transform how channel-focused organizations manage partner incentives and drive measurable ROI. This comprehensive platform will address critical pain points in channel management including manual process burden, limited visibility, complex commission calculations, and partner engagement challenges.

The project represents a strategic transformation from a services-first approach to a scalable platform-first product offering, leveraging Integrity XD's 15+ years of domain expertise in software development. The platform will initially target the energy sector with Citgo Petroleum Corporation as the pilot customer, addressing their needs across many channel partners and direct sales professionals.

**Key Deliverables:**

- Platform with core incentive management capabilities  
- Partner self-service portals with real-time visibility  
- Automated campaign and fund management systems  
- Mobile-first design supporting field operations  
- Phased integration approach with enterprise systems  
- Comprehensive data migration from existing Django/MySQL system

**Strategic Outcomes:**

- **Enable reallocation of 40% administrative FTEs** to strategic, revenue-generating activities by automating manual processes.  
- **Achieve 95% partner participation** in incentive programs through enhanced transparency and accessibility, driving increased engagement and program effectiveness.  
- **Reduce dispute resolution time to less than 24 hours**, improving partner satisfaction and operational efficiency.  
- **Deliver a 4:1 ROI** on incentive program investments by optimizing incentive structures and maximizing channel revenue growth.

---

## **2. Project Overview & Objectives**

### **2.1 Business Context**

XD Incentives addresses persistent challenges in distributed channel ecosystems where brands and manufacturers struggle with:

- Incentives scattered across email, spreadsheets, and siloed tools  
- Unclear program ROI leading to waste or underutilization  
- Difficulty influencing channel behavior at scale  
- Inconsistent or invisible partner engagement  
- Lack of purpose-built systems for multi-tier ecosystems

### **2.2 Strategic Objectives**

**Primary Business Objectives:**

1. **Transform Operational Efficiency**: Automate manual processes to redirect 40% of administrative time to strategic activities  
2. **Maximize Partner Engagement**: Achieve 95% participation rates through transparent, accessible program management  
3. **Accelerate Revenue Growth**: Enable 12% year-over-year channel revenue growth through optimized incentive structures  
4. **Ensure Compliance**: Meet ASC 606, SOC 2, PCI DSS, and GDPR requirements with built-in controls  
5. **Scale Platform Revenue**: Achieve $4.3M in platform revenue by Year 3

**Technical Objectives:**

1. Build a secure and scalable architecture supporting 1000+ concurrent users  
2. Deliver sub-2 second response times for 95th percentile of requests  
3. Enable phased enterprise integration approach  
4. Provide 99.9% platform availability with zero-downtime deployments  
5. Support offline capabilities for field-based users

### **2.3 Target User Personas**

The platform will serve four primary user personas:

1. **Marketer Sales Rep (MSR) \-** Daily users focused on submitting claims tied to contests and rewards based on Citgo lubricant sales.  
2. **Marketer Sales Manager (MSM) \-** Managers who approve or deny claims submitted by their assigned MSRs. They use the platform to oversee sales performance and are expected to prevent fraud.
3. **Territory Sales Manager (TSM) \-** Approve new customers submitted by reps to ensure validity before claims can be filed. Their role also centers on oversight of territory-specific sales data and analytics.
4. **National Sales Manager (NSM) \-** Oversight role with access to all reports and user data but no direct transactional tasks. Uses dashboards to monitor activity across all users. Primarily uses the platform for reporting and visibility with minimal direct interaction in claim or customer workflows.

---

## **3. Scope Definition**

### **3.0 In-Scope Design Services**

**Discovery & Information Architecture (IA):**

- Persona validation & journey mapping  
- Confirm goals, needs, and friction points for key personas  
- Identify redundant features, inconsistent patterns, or critical path issues  
- Review current platform modules, labels, and flows  
- Understand different user roles and personas  
- Develop new information architecture with persona-based navigation and improved workflows  
- Define how users will move between different dashboard views and task modules

**High Fidelity Wireframes:**

- Design scalable layouts leveraging Citgo frontend improvements  
- Standardize components and layout zones for responsiveness and modularity  
- Ensure consistency across user roles, device types and modules

### **3.1 In-Scope Development Services**

**Phase 1 \- MVP (Months 0-6):**

**Core Platform Foundation:**

- Hierarchical role management (NSM → TSM → MSM → MSR)  
- Authentication system with SSO and MFA support  
- Complete audit trails for all financial transactions  
- Basic reporting and analytics infrastructure  
- Data migration from existing Django/MySQL system

**Essential Modules:**

- **Campaign Manager (Admin-Only)**: Rule-based incentive program configuration. Required Campaign Types:  
  - General Payouts:
    - Product-based payouts tiered by customer years (Year 1, Year 2), capped at $35,000/year  
    - Product-based payouts tiered by customer years (Year 1, 2, 3+), capped at $10,000/customer  
  - Manager Payouts: 20% of sales rep payouts, no cap  
  - Grand Prize: Annual leaderboard contest by line of business  
  - HD Contest: Multi-product purchase point system with leaderboard  
- **Fund Management (Lite)**: Budget tracking, claim processing, and payout management  
- **Basic Rewards Management**: Digital reward catalog display, point redemption, order tracking  
- **Partner Portals (Foundation)**: Self-service access to earnings and claims  
- **Basic Data Import/Export**: CSV and manual data entry capabilities  
- **Communications (Lite):** In App Messaging and Notifications  
- **Static Content Management**: CMS-like functionality for managing static pages including Payout Schedule, FAQs, Privacy Policy, and How-to Guides. Admin users can edit content without code changes, reducing developer dependency by 80% for content updates.

**Phase 2 \- Scale & Automation (Months 6-12):**

- Automate invoice processing with Optical Character Recognition  
- Full Campaign Manager UI with template library  
- Enhanced B2B Portals with customization  
- Automated Communications (Comms+)  
- Real-time ROI Dashboard  
- Campaign template library (12+ templates)  
- Progressive Web App (PWA**)** with native app-like experience without app store requirements, including offline capabilities and automated updates  
- Partner enrollment engine with self-service onboarding  
- Active Promotions Catalog with a comprehensive listing of all current promotional programs with rules, tiers, caps, and calculation methods.
- Territory Performance Analytics with detailed analytics by geographic territory with comparison tools and export capabilities  
- Physical Rewards Fulfillment Integration with fulfillment providers, shipping tracking, inventory management  
- Points Management System with non-cash incentive tracking with balance management and expiration rules  
- Announcement System: with admin broadcasting tool for targeted user communications  
- Client Onboarding Wizard with Guided setup process for new platform clients  
- Data Retention Automation with automated archival and purging based on compliance policies

**Phase 3 \- Intelligence & Differentiation (Months 12-18+):**

- **Enterprise Integrations**: Salesforce bi-directional sync, SAP data integration  
- **Advanced Reporting**: Power BI integration and custom analytics  
- **Advanced Analytics and Reporting:** Provide deep insights into program performance, partner behavior, and ROI through sophisticated analytics and customizable reporting, enabling data-driven decision-making.  
- **Gamification Engine:** Enhance partner engagement and motivation through gamified incentives and recognition programs.  
- **Fax-to-Email Invoice Processing**: Automated OCR system for faxed invoices received via email, supporting rural distributors with 90%+ accuracy  
- Partner health scoring  
- Multi-currency/language support  
- Advanced compliance tools

### **3.2 Out-of-Scope**

**Explicitly Excluded from Current SOW:**

- XD Rewards Catalog module (separate product offering)  
- Deal Pro (Conquest+) standalone product development (see Section 3.3)  
- Custom consulting or implementation services beyond platform development  
- Third-party license fees (Salesforce, SAP, etc.)  
- Ongoing platform operations and maintenance (post-launch)  
- Marketing website or promotional materials  
- Customer-specific customizations beyond configuration

### **3.3 Deal Pro (Conquest+) \- Future Integration Strategy**

While Deal Pro is out-of-scope for this SOW, it represents a strategic adjacent product that will complement XD Incentives:

**Deal Pro Overview:**

- Custom deal acceleration platform for complex B2B sales  
- Enables collaboration between sales, pricing, and leadership  
- Accelerates deal velocity by 30-50%

**Future Integration Points:**

- Unified authentication and user management  
- Shared partner/customer data models  
- Combined reporting and analytics  
- Bundled pricing opportunities  
- Cross-platform workflow automation

**Timeline:** Deal Pro integration planning will begin in Phase 3, with implementation targeted for Year 2 based on market feedback and customer demand.

---

## **4. Deliverables & Milestones**

### **4.1 Phase 1 \- MVP Deliverables (Months 0-6)**

**Sprint 1-2: Foundation (Months 1-2)**

- [ ] Authentication/authorization system with role-based access  
- [ ] Core data models and REST API framework  
- [ ] Basic admin interface with user management  
- [ ] Development, staging, and production environments  
- [ ] Data migration strategy and tools

**Sprint 3-4: Core Modules (Months 3-4)**

- [ ] Campaign Manager backend with rule engine  
- [ ] Fund Management with claim processing workflows  
- [ ] Basic Rewards Management system  
- [ ] Partner Portal foundation with login and navigation  
- [ ] CSV import/export functionality  
- [ ] Initial data migration execution

**Sprint 5-6: Integration & Polish (Months 5-6)**

- [ ] Basic data import capabilities (including import of existing Citgo data)  
- [ ] Manual reporting capabilities with export functions (verified by Citgo’s existing reports)  
- [ ] Mobile optimization for key workflows  
- [ ] Security hardening and penetration testing  
- [ ] Performance optimization and load testing  
- [ ] Complete data migration and validation

**MVP Success Criteria:**

- Support for 1 pilot customer (Citgo Petroleum Corporation)  
- 3 core modules fully functional  
- \<2 second average response time  
- 99.5% uptime during pilot period  
- Successful security audit completion  
- 100% data migration accuracy

### **4.2 Phase 2 \- Scale Deliverables (Months 6-12)**

**Key Deliverables:**

- [ ] Visual Invoice Reader with OCR processing  
- [ ] Full Campaign Manager UI with drag-and-drop interface  
- [ ] B2B Portals with white-label customization  
- [ ] Automated communication workflows (AWS SES for email/AWS SNS for SMS)  
- [ ] Real-time ROI Dashboard  
- [ ] Campaign template library (12+ templates)  
- [ ] Enhanced mobile experience with offline capabilities  
- [ ] Partner enrollment engine with self-service onboarding  
- [ ] Stripe payment processing integration

### **4.3 Phase 3 \- Intelligence Deliverables (Months 12-18+)**

**Enterprise Integration Features:**

- [ ] Salesforce integration (accounts, opportunities, custom objects)  
- [ ] SAP integration for product/pricing data  
- [ ] Power BI integration with direct query support  
- [ ] Advanced API ecosystem for third-party connections

**Advanced Features:**

- [ ] Partner engagement scoring algorithm  
- [ ] Predictive analytics for program optimization  
- [ ] Gamification engine with leaderboards  
- [ ] Multi-currency and multi-language support  
- [ ] Advanced compliance reporting tools  
- [ ] Machine learning models for fraud detection

### **4.4 Documentation Deliverables**

**Technical Documentation:**

| Document Type | Format | Delivery Phase | Description |
| :---- | :---- | :---- | :---- |
| API Documentation | OpenAPI 3.0 / Swagger | Phase 1 | Complete REST API reference with examples |
| Database Schema | ERD \+ SQL DDL | Phase 1 | Full schema documentation with relationships |
| Architecture Guide | Markdown \+ Diagrams | Phase 1 | System architecture, deployment, and scaling |
| Security Documentation | PDF | Phase 1 | Security controls, compliance mappings |
| Integration Guides | Markdown | Phase 3 | Salesforce, SAP, Power BI integration specs |

**User Documentation:**

| Document Type | Format | Delivery Phase | Target Audience |
| :---- | :---- | :---- | :---- |
| Admin Guide | PDF \+ Online Help | Phase 1 | System administrators |
| Partner Portal Guide | PDF \+ In-app Help | Phase 1 | Channel partners |
| User Manual | PDF \+ Videos | Phase 2 | End users (all personas) |
| Quick Start Guides | PDF (2-pagers) | Phase 2 | Role-specific guides |
| Training Materials | PPT \+ Videos | Phase 2 | Trainers and super users |

**Operational Documentation:**

| Document Type | Format | Delivery Phase | Description |
| :---- | :---- | :---- | :---- |
| Deployment Guide | Markdown | Phase 1 | Step-by-step deployment procedures |
| Runbook | Wiki format | Phase 2 | Operational procedures and troubleshooting |
| Disaster Recovery Plan | PDF | Phase 2 | Backup and recovery procedures |
| Performance Tuning Guide | Markdown | Phase 2 | Optimization recommendations |

---

## **5. Technical Requirements & Architecture**

### **5.1 Technology Stack**

**Frontend:**

- Framework: React v19+ with TypeScript & Veit
- UI Library: Tailwind v4+ with Shadcn UI components  
- State Management: Zustand v5+  
- Testing: Cypress (E2E) v14+, Jest/React Testing Library (Unit) v16+

**Backend:**

- Runtime: Python 3.12.3+  
- Framework: Django v5.2.3+  
- Database: Amazon RDS for MySQL v8.4  
- Testing: unittest

**Infrastructure:**

- Hosting: AWS EC2  
- Cache: AWS ElastiCache (Redis)  
- Message Queue: AWS SQS  
- Container Platform: Docker  
- Container Orchestration: AWS ECS  
- Infrastructure Automation: AWS Terraform  
- Monitoring: AWS CloudWatch  
- CI / CD: Github Actions  
- Error Tracking: Sentry

**Security & Compliance:**

- Authentication: WorkOS with enterprise SSO  
- Encryption: AES-256 at rest, TLS 1.3 in transit  
- Compliance Tools: Automated SOC 2, PCI DSS controls (TBD)

### **5.2 Communication & Payment Services**

**AWS Services (Phase 1):**

- **Email**: AWS Simple Email Service (SES)  
- **SMS**: AWS Simple Notification Service (SNS)  
- **File Storage**: AWS S3 for document storage

**Payment Processing (Phase 2):**

- **Primary**: Stripe for payment processing  
- **Features**: PCI compliance, webhook support, recurring payments

### **5.3 Data Migration Requirements**

**Source System:** Existing Django/MySQL production application.
**Data Volume:** Approximately 200,000 transaction records and 1,500 user records.  
**Scope of Data Migration:**

- Historical transaction data (last 9 years)  
- User profiles and associated metadata  
- Active incentive program structures and rules  
- Payout records (last 9 years)  
- Active campaign configurations and rules  
- HD Contest historical data and point calculations  
- Reward redemption history

**Migration Process:**

- A phased migration approach will be utilized to minimize disruption to the live production system.  
- Data extraction from the Django/MySQL database.  
- Data cleansing and transformation to align with the XD Incentives platform data model.  
- Data loading into the new platform.

**Client Responsibility:** Citgo is responsible for providing subject matter expertise for data validation and performing necessary data cleansing on their end prior to extraction.
**Validation:** Post-migration data validation will be performed jointly by Integrity XD and Citgo.

**Key Migration Challenges:**

- Territory hierarchy transformation  
- Commission calculation logic preservation  
- Historical data integrity  
- User permission mapping

### **5.4 Performance Requirements**

- **Response Time**: \<2 seconds for 95th percentile  
- **Concurrent Users**: 1000+  
- **Data Processing**: 100,000+ transactions/day  
- **Availability**: 99.9% uptime SLA  
- **Scalability**: Horizontal scaling capability

### **5.5 Security Requirements**

- **Compliance**: SOC 2 Type II, PCI DSS Level 1, GDPR  
- **Authentication**: MFA, SSO, OAuth 2.0  
- **Data Protection**: Field-level encryption for PII  
- **Audit**: Complete audit trails with 7-year retention  
- **Access Control**: Role-based with attribute-based extensions

---

## **6. Project Timeline & Phases**

### **6.0 Phase 0 \- UX/Design Foundation (2 Months)**

- Project Kickoff and Onboarding  
- Discovery & Current State Analysis  
- Information Architecture Redesign leveraging Citgo frontend learnings  
- Design System Development with reusable components  
- High Fidelity Wireframes for desktop and mobile

### **6.1 Phase 1 \- MVP Timeline (6 Months)**

| Milestone | Duration | Start Date | End Date | Key Deliverables |
| :---- | :---- | :---- | :---- | :---- |
| Project Kickoff | 1 week | Month 1, Week 1 | Month 1, Week 1 | Project charter, team onboarding |
| Sprint 1: Infrastructure | 4 weeks | Month 1, Week 2 | Month 2, Week 1 | System architecture, auth system |
| Sprint 2: Core Data Models  | 4 weeks | Month 2, Week 2 | Month 3, Week 1 | Database schema, data migration, API framework |
| Sprint 3: Campaign Manager | 4 weeks | Month 3, Week 2 | Month 4, Week 1 | Rule engine, admin interface |
| Sprint 4: Fund Management | 4 weeks | Month 4, Week 2 | Month 5, Week 1 | Claims, payouts, disputes |
| Sprint 5: Partner Portal | 4 weeks | Month 5, Week 2 | Month 6, Week 1 | Self-service interface, mobile views |
| Sprint 6: Testing & Launch | 4 weeks | Month 6, Week 2 | Month 6, Week 4 | Final data import, security audit, go-live |

### **6.2 Phase 2 \- Scale Timeline (6 Months)**

- **Months 7-8**: Enhanced UI/UX development  
- **Months 9-10**: Advanced features and automation, Stripe integration  
- **Months 11-12**: Performance optimization and scaling

### **6.3 Phase 3 \- Intelligence Timeline (6+ Months)**

- **Months 13-14**: Enterprise integrations (Salesforce, SAP, Power BI)  
- **Months 15-16**: AI/ML feature development  
- **Months 17-18**: International expansion features  
- **Ongoing**: Continuous improvement and innovation

---

## **7. Resource Requirements & Team Structure**

### **7.1 Integrity XD Team Structure**

**Core Development Team:**

- **[Jeremiah Harris](mailto:<EMAIL>) \- Technical Lead** (0.25 FTE): Architecture, code reviews, technical decisions  
- **Full-Stack Developers** (\~2.5 FTE): Architecture, core platform development, feature development, API implementation, data migration  
  - [Matthew Morgan](mailto:<EMAIL>)  
  - [Daniel Costello](mailto:<EMAIL>)  
  - [Alex Rodriguez](mailto:<EMAIL>)  
- **[Alex Rodriguez](mailto:<EMAIL>) \- DevOps / QA Engineer** (\~0.25 FTE): Infrastructure, CI/CD, monitoring, Test automation, quality assurance

**Design & UX Team:**

- **[Christine Sheldon](mailto:<EMAIL>) \- Lead UI/UX Designer** (\~0.75 FTE): User research, wireframes, prototypes, component library, responsive design

**Project Management:**

- **[Drew Newman](mailto:<EMAIL>) \- Project Manager :**  Timeline, coordination, stakeholder communication  
- **[Alex Rodriguez](mailto:<EMAIL>) \- Business Analyst** (0.25 FTE): requirements gathering, documentation, user story and acceptance criteria creation

**Product Ownership:**

- **[Marshall Welch](mailto:<EMAIL>) \- Product Owner** (0.25 FTE): Vision, prioritization, acceptance  
- **Citgo \- Subject Matter Experts** (As needed): Domain knowledge, validation, acceptance

**Technical Support (Phase 3):**

- **TBD \- Integration Contacts**: Salesforce, SAP system access  
- **TBD \- Security/Compliance**: Audit requirements, compliance validation

**Business Stakeholders:**

- **[Ed Morrissey](mailto:<EMAIL>) \- Executive Sponsor**: Strategic decisions, budget approval  
- **Citgo \- Pilot Users**: UAT participation, feedback

### **7.3 Skill Requirements**

**Critical Technical Skills:**

- AWS services expertise (SES, SNS, S3, etc.)  
- Stripe payment integration experience  
- Financial systems and compliance knowledge  
- Mobile-first responsive design  
- Performance optimization at scale  
- Django/MySQL migration experience

---

## **8. Assumptions & Dependencies**

### **8.1 Key Assumptions**

**Technical Assumptions:**

1. MVP can operate with manual user flows and CSV import/export until Phase 3 integrations  
2. Existing data can be provided in standard formats for import (CSV / JSON)  
3. No significant changes to core business processes during development  
4. Access to existing Django/MySQL system for data migration

**Business Assumptions:**

1. Citgo Petroleum Corporation remains committed as pilot customer throughout MVP  
2. Budget approval for each phases secured before starting the next phase  
3. Key stakeholders are available for bi-weekly reviews and decisions  
4. No major market or regulatory changes affecting requirements

**Resource Assumptions:**

1. Dedicated team members remain assigned throughout project  
2. Subject matter experts available within 48 hours when needed  
3. Pilot users committed to 10+ hours of testing per sprint (starting on sprint 4\)

### **8.2 Critical Dependencies**

**Phase 1 Dependencies:**

- **Work OS Setup**: Enterprise account provisioning  
- **Compliance Auditor**: Availability for security reviews  
- **Citgo Petroleum Corporation Participation**: Pilot user availability  
- **Django System Access**: Read access to production database  
- **AWS Account**: Services provisioned and configured

**Phase 2 Dependencies:**

- **Stripe Account**: Payment processing setup

**Phase 3 Dependencies:**

- **Salesforce API Access**: Admin credentials and sandbox environment  
- **SAP Integration**: Technical documentation and test data  
- **Power BI**: Workspace and authentication setup

---

## **9. Risk Management**

### **9.1 Technical Risks**

| Risk | Probability | Impact | Mitigation Strategy |
| :---- | :---- | :---- | :---- |
| MVP complexity without integrations | Medium | Medium | Focus on excellent import/export tools |
| Performance without caching layer | Low | Medium | Implement Redis early in development |
| Payment processing compliance | Low | High | Use Stripe's built-in compliance tools |
| Mobile offline sync issues | Low | Medium | Proven sync patterns, extensive testing |
| Django data migration complexity | Medium | High | Detailed mapping, phased migration approach |

### **9.2 Business Risks**

| Risk | Probability | Impact | Mitigation Strategy |
| :---- | :---- | :---- | :---- |
| Pilot customer disengagement | Low | Critical | Bi-Weekly stakeholder meetings, early value delivery |
| Manual processes in MVP | Medium | Low | Clear roadmap communication, efficient workflows |
| User adoption without integrations | Medium | Medium | Excellent UX, clear benefits communication |
| Compliance requirement changes | Low | High | Regular compliance reviews, flexible architecture |

### **9.3 Resource Risks**

| Risk | Probability | Impact | Mitigation Strategy |
| :---- | :---- | :---- | :---- |
| Key developer attrition | Low | High | Knowledge documentation, pair programming |
| SME availability constraints | Medium | Medium | Schedule SME time in advance, documentation |
| Budget overrun | Low | Medium | Bi-weekly budget reviews, phased delivery |

---

## **10. Success Criteria & KPIs**

### **10.1 Technical Success Metrics**

**Performance KPIs:**

- **Response Time**: 95th percentile \<2 seconds  
- **System Availability**: 99.9% uptime per month  
- **Concurrent Users**: Support 1000+ concurrent users  
- **Data Accuracy**: 99.5% commission calculation accuracy  
- **Import/Export Success**: 100% data integrity

**Quality KPIs:**

- **Code Coverage**: \>80% test coverage  
- **Security Score**: Pass all penetration tests  
- **Technical Debt**: \<10% of development time

### **10.2 Business Success Metrics**

**Operational KPIs:**

- **Manual Process Reduction**: 40% decrease by Month 9  
- **Dispute Resolution Time**: \<24 hours by MVP completion  
- **Report Generation Time**: 75% reduction by Phase 2  
- **User Support Tickets**: 60% reduction by Month 9

**User Adoption KPIs:**

- **Platform Adoption Rate**: 100% within 90 days of launch  
- **Partner Portal Usage**: 80% weekly active users  
- **Feature Utilization**: 70% of features actively used  
- **User Satisfaction Score**: 8.0+ out of 10

**Business Impact KPIs:**

- **Partner Participation**: 95% in new programs  
- **Revenue Attribution**: 12% YoY channel growth  
- **Program ROI**: 4:1 or higher  
- **Time to Launch Programs**: \<3 days

### **10.3 Persona-Specific Success Criteria**

**Michael Reynolds (CSO):**

- Single-pane visibility achieved  
- ROI reporting automated  
- Partner NPS improved to 8.5+

**Jennifer Martinez (Sales Ops):**

- Data import/export success 100%  
- 95% user adoption within 4 months  
- 40% reduction in manual tasks

**Amanda Foster (Field Manager):**

- Team performance visibility in real-time  
- Partner dispute resolution \<3 days  
- 90% team satisfaction score

**Carlos Martinez (Sales Rep):**

- Mobile entry time \<5 minutes  
- Error rate reduced to \<5%  
- Self-service success rate \>90%

### **10.4 Pilot Success Criteria**

**Criteria for Moving from Pilot to Full Production:**

| Category | Metric | Target | Measurement Method |
| :---- | :---- | :---- | :---- |
| System Performance | Response Time | \<2 sec (95th percentile) | DataDog monitoring |
| System Performance | Uptime | 99.5% | Uptime monitoring |
| User Adoption | Active Users | 80% weekly | Analytics dashboard |
| User Adoption | Task Completion | 90% success rate | User analytics |
| Data Quality | Migration Accuracy | 100% | Reconciliation reports |
| Data Quality | Calculation Accuracy | 99.5% | Audit sampling |
| Business Impact | Process Time Reduction | 30% | Time studies |
| Business Impact | User Satisfaction | 7.5+ / 10 | Survey results |
| Technical Readiness | Security Audit | Pass | Third-party audit |
| Technical Readiness | Load Testing | 1000 users | Performance tests |

**Go/No-Go Decision Points:**

1. **Month 6**: MVP pilot launch decision  
2. **Month 9**: Scale to additional users decision  
3. **Month 12**: Full production rollout decision

---

## **11. Budget Considerations**

### **11.1 Development Investment**

**Phase 1 \- MVP (6 Months):**

- Development Team: $380,000  
- Infrastructure Setup: $25,000  
- Third-party Licenses: $20,000  
- Security Audit: $15,000  
- **Total MVP Investment: $440,000**

**Phase 2 \- Scale (6 Months):**

- Enhanced Development: $380,000  
- AWS Services Setup: $15,000  
- Stripe Integration: $25,000  
- Additional Infrastructure: $35,000  
- **Total Phase 2: $455,000**

**Phase 3 \- Intelligence (6 Months):**

- Enterprise Integrations: $120,000  
- AI/ML Development: $200,000  
- International Features: $85,000  
- Performance Scaling: $50,000  
- **Total Phase 3: $455,000**

### **11.2 Ongoing Operational Costs**

**Annual Operating Expenses:**

- Infrastructure & Hosting: $60,000/year  
- AWS Services (SES, SNS, S3): $24,000/year  
- Stripe Processing Fees: Variable (2.9% \+ $0.30)  
- Third-party Services: $36,000/year  
- Monitoring & Security: $36,000/year  
- Support & Maintenance: $120,000/year

### **11.3 ROI Projections**

**Revenue Projections:**

- Year 1: $500K (1 enterprise deal)  
- Year 2: $1.7M (3-4 enterprise deals)  
- Year 3: $4.3M (8-10 enterprise deals)

**Break-even Timeline:**

- Operational break-even: Month 18  
- Full ROI realization: Month 24

---

## **12. Change Management Process**

### **12.1 Change Request Protocol**

**Change Classification:**

- **Minor Changes**: \<8 hours impact, PM approval  
- **Moderate Changes**: 8-40 hours impact, steering committee approval  
- **Major Changes**: \>40 hours impact, executive approval

**Change Request Process:**

1. Submit formal change request with business justification  
2. Impact analysis by technical team (48 hours)  
3. Review by change advisory board  
4. Approval/rejection decision  
5. Implementation planning if approved  
6. Communication to all stakeholders

### **12.2 Scope Management**

**In-Scope Modifications:**

- Feature prioritization within phases  
- UI/UX refinements based on user testing  
- Performance optimizations  
- Bug fixes and security patches

**Out-of-Scope Requiring Change Request:**

- New modules or major features  
- Additional integrations  
- Significant architectural changes  
- Timeline acceleration

### **12.3 Communication Protocols**

**Regular Communications:**

- Weekly status reports to internal and external stakeholders  
- Bi-weekly steering committee meetings  
- Monthly executive briefings  
- Sprint demos every 4 weeks

**Escalation Path:**

1. Project Manager → Product Owner  
2. Product Owner → Steering Committee  
3. Steering Committee → Executive Sponsors

---

## **13. Training & Knowledge Transfer Plan**

### **13.1 Training Strategy Overview**

The training program will ensure successful adoption across all user personas, with special attention to users like Carlos Martinez who require extensive support. The program includes role-specific training paths, multiple delivery methods, and ongoing support mechanisms.

### **13.2 Training Phases**

**Phase 1: Train-the-Trainer (Month 5\)**

- Duration: 2 weeks  
- Audience: Super users and IT support staff  
- Format: Intensive hands-on workshops  
- Deliverables: Certified trainers, training materials

**Phase 2: Pilot User Training (Month 6\)**

- Duration: 1 week per role  
- Audience: All pilot users by persona  
- Format: Role-specific sessions  
- Deliverables: Trained pilot users, feedback incorporation

**Phase 3: General Rollout Training (Months 7-9)**

- Duration: Ongoing  
- Audience: All system users  
- Format: Mixed delivery methods  
- Deliverables: Trained user base, adoption metrics

### **13.3 Persona-Specific Training Programs**

**Executive Users (Michael Reynolds)**

- Format: Executive briefings and dashboards  
- Duration: 2 hours initial \+ monthly updates  
- Content: ROI tracking, strategic insights, decision support  
- Materials: Executive guide, dashboard tutorials

**Operations Directors (Jennifer Martinez)**

- Format: Technical deep-dives and admin training  
- Duration: 16 hours over 2 weeks  
- Content: System administration, integrations, reporting  
- Materials: Technical documentation, admin playbook

**Field Managers (Amanda Foster)**

- Format: Scenario-based workshops  
- Duration: 8 hours initial \+ weekly coaching  
- Content: Team management tools, partner workflows  
- Materials: Manager toolkit, quick reference guides

**Sales Representatives (Carlos Martinez)**

- Format: Hands-on practice with buddy system  
- Duration: 6 hours initial \+ 2 weeks mentoring  
- Content: Data entry, mobile app, error recovery  
- Materials: Visual guides, video tutorials, laminated cards  
- Special Considerations:  
  - Small group sessions (max 4 people)  
  - Repetitive practice opportunities  
  - Simple, visual instructions  
  - Peer learning emphasis  
  - Extended support period

### **13.4 Training Delivery Methods**

**Instructor-Led Training (ILT)**

- Live workshops for each persona  
- Hands-on practice environments  
- Q\&A sessions  
- Recorded for future reference

**E-Learning Modules**

- Self-paced online courses  
- Interactive simulations  
- Progress tracking  
- Certification upon completion

**Just-in-Time Resources**

- In-app contextual help  
- Tooltips and wizards  
- Video tutorials (2-3 minutes)  
- Searchable knowledge base

**Support Materials**

- Quick reference cards  
- PDF guides by role  
- FAQs  
- Troubleshooting guides

### **13.5 Knowledge Transfer Plan**

**Documentation Transfer**

- All technical documentation delivered in phases  
- Wiki-based knowledge repository  
- Version-controlled documentation  
- Regular documentation reviews

**Technical Knowledge Transfer**

- Code walkthroughs with recordings  
- Architecture deep-dives  
- Deployment procedures training  
- Troubleshooting scenarios

**Operational Knowledge Transfer**

- Runbook creation and training  
- Incident response procedures  
- Monitoring and alerting setup  
- Performance tuning guidance

### **13.6 Post-Launch Support Transition**

**Month 1 Post-Launch: Hypercare**

- Weekly check-ins  
- Dedicated support channel  
- Rapid issue resolution  
- Usage monitoring

**Months 2-3: Stabilization**

- Bi-Weekly check-ins  
- Knowledge base expansion  
- Advanced user training  
- Process optimization

**Months 4-6: Transition to Operations**

- Monthly reviews  
- Self-service emphasis  
- Community building  
- Continuous improvement

**Ongoing Support Model**

- Tiered support structure  
- SLA-based response times  
- Regular training refreshers  
- User community forums

### **13.7 Training Success Metrics**

| Metric | Target | Measurement Method |
| :---- | :---- | :---- |
| Training Completion | 100% of users | LMS tracking |
| Competency Assessment | 80% pass rate | Role-based testing |
| Time to Proficiency | \<30 days | Task completion times |
| User Confidence | 7+ / 10 | Self-assessment surveys |
| Support Ticket Reduction | 50% by Month 3 | Ticket tracking system |
| Feature Adoption | 70% by Month 6 | Usage analytics |

---

## **14. Appendices**

### **Appendix A: Technical Architecture Diagrams**

\[Detailed system architecture diagrams to be provided\]

### **Appendix B: Data Import/Export Specifications**

**CSV Import Format Requirements:**

**Data Validation Rules:**

- All dates in ISO 8601 format  
- Amounts in decimal with 2 precision  
- Status values from predefined list  
- Foreign key validation  
- UTF-8 encoding required

### **Appendix C: Compliance Requirements**

**SOC 2 Type II Controls:**

- Access controls and authentication  
- Data encryption standards  
- Audit logging requirements  
- Change management procedures  
- Incident response protocols

**PCI DSS Requirements:**

- Network segmentation  
- Encryption of cardholder data  
- Access control measures  
- Regular security testing  
- Security policies and procedures

**GDPR Compliance:**

- Data privacy controls  
- Right to erasure implementation  
- Data portability features  
- Consent management  
- Privacy by design principles

### **Appendix D: Glossary of Terms**

| Term | Definition |
| :---- | :---- |
| Channel Partner | Third-party organizations that sell products on behalf of the manufacturer |
| SPIFF | Sales Performance Incentive Fund \- short-term sales incentive |
| MDF | Market Development Funds \- co-op marketing funds |
| Commission Dispute | Disagreement about calculated commission amounts |
| Territory | Geographic or vertical market assignment |
| Tier | Hierarchical level in the distribution channel |
| Conquest | Legacy software program used by multiple client teams for creating promotions/pricing deals to win business from competitors |
| ROI | Return-on-Investment for incentive programs |
| White-label | Customizable branding per customer |

### **Appendix E: Known Deferred Items**

The following features and capabilities are **recognized as strategically important** for the XD Incentives platform and are already referenced in platform vision and partner conversations. However, they are **not included in the current MVP (GFG) scope** due to pending funding approval or client-specific needs. These items will be revisited upon confirmation of program expansion, new client onboarding, or strategic funding milestones.

 XD Rewards Catalog *Deferred until OEM Loyalty proposal is approved or another client requires reward functionality.*

- Configurable catalog engine for digital and physical rewards  
- Points-to-currency conversion logic tied to fund management  
- Eligibility gating based on role, tier, or campaign logic  
- Fulfillment integration (e.g., Tango, Visa)  
- Redemption dashboards and engagement analytics

Hyundai-Specific Loyalty Features (OEM MVP) *Deferred — pending proposal outcome and confirmation of OEM implementation roadmap.*

- Tiered dealer segmentation  
- Custom conquest campaign templates  
- White-labeled partner experience with domain-based login  
- Executive dashboards for corporate-level KPIs  
- Dealer and rep engagement scoring

Advanced Comms & Engagement Logic - *Targeted for Phase 2 (post-GFG launch); currently not prioritized for MVP.*

- Intelligent nudges based on disengagement patterns  
- Partner feedback capture (NPS, CSAT, or other for data intelligence)  
- Behavioral notifications tied to campaign milestones

### **Appendix F: Key Inputs**

**XD Incentives Strategic Approach and Market Oppt:** [Developing the Enterprise SaaS Strategy](https://docs.google.com/document/d/1XeW5lz_zqCp2MQHaIcujCdtcn9lFmQNOGzPc0XZnFcw/edit?tab=t.5mi1ntv0udiv#heading=h.sle33bpoegni)  
**Hyundai Proposal Research and Outlining:** [Hyundai Rewards Platform Proposal](https://docs.google.com/document/d/14RLDHXdno3BIdMAdmAssIWKlxotvOV-fJraOSI3lBa4/edit?tab=t.0)  
**Harvard Research and Implications:** [HBR Incentives Program Paper](https://docs.google.com/document/d/1MOSbq6QyqEkXWk2VhULwas5z4fYSDwjzh_oP4RL5N-Y/edit?tab=t.9v5jx3lnkzo7#heading=h.np8n90i926vb)  
**Module to Feature Inventory:** [XD Incentives Biz to Dev Meshing](https://docs.google.com/spreadsheets/d/1eAodqcburlM8DmVmHx4WQsuDHjptUQpWXcwTEPc4EbM/edit?gid=1355536364#gid=1355536364)  
**RTTC Dev Scoping and Documents**

- [RTTC Redesign - Product Requirements Document](https://docs.google.com/spreadsheets/d/1tnnCeFj658onihPVRJi9oGwOJfSg-10IVR9jQ5JKxx8/edit?gid=0#gid=0)  
- [Internal RTTC Scope of Work](https://docs.google.com/document/d/1Lzz5dKHFFLeqaXzAGI2VVMovOl2poqNmWQrSYTSQl8g/edit?tab=t.0)  
- [RTTC Architecture and Upgrades Project](https://drive.google.com/drive/folders/1XIXh3ksPV5hAZ5LOIaYN9X8RxKEjlmCO)