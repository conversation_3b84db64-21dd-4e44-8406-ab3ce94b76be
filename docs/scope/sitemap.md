# XD Incentives Platform Sitemap

This comprehensive sitemap documents all API endpoints and frontend routes for the XD Incentives Platform, a multi-tenant SaaS application built with React (frontend) and Django REST API (backend).

## API Endpoints (Backend)

### Authentication & Security API

| Endpoint | HTTP Method | Purpose | Request/Response | Access Control |
|----------|-------------|---------|------------------|----------------|
| `/api/v1/health` | GET | Health check endpoint | Returns `{"status": "ok"}` | Public |
| `/api/v1/auth/login` | POST | User authentication | Clerk JWT integration | Public |
| `/api/v1/auth/logout` | POST | Session termination | Invalidate JWT | Authenticated |
| `/api/v1/auth/refresh` | POST | Token refresh | New JWT token | Authenticated |
| `/api/v1/users/me` | GET | Get current user profile | User data with roles | Authenticated |
| `/api/v1/users/me/w9` | POST | Upload W9 document | File upload to S3 | Authenticated |
| `/api/v1/users/me/w9/status` | GET | Get W9 verification status | Status: Not Submitted/Pending/Verified | Authenticated |

### Claims Management API

| Endpoint | HTTP Method | Purpose | Request/Response | Access Control |
|----------|-------------|---------|------------------|----------------|
| `/api/v1/claims` | GET | List user's claims | Paginated claim list | MSR: own claims, MSM: team claims |
| `/api/v1/claims` | POST | Submit new claim | Claim data with customer/product | MSR, MSM |
| `/api/v1/claims/{id}` | GET | Get claim details | Full claim with invoice URL | Claim owner or reviewer |
| `/api/v1/claims/{id}/approve` | POST | Approve claim | Status update | MSM (direct reports only) |
| `/api/v1/claims/{id}/deny` | POST | Deny claim with reason | Status + denial reason | MSM (direct reports only) |
| `/api/v1/claims/queue` | GET | Get pending review queue | Claims awaiting approval | MSM |
| `/api/v1/claims/batch-approve` | POST | Batch approve claims | Array of claim IDs | MSM |
| `/api/v1/claims/batch-deny` | POST | Batch deny claims | Array of IDs + reason | MSM |

### Customer Management API

| Endpoint | HTTP Method | Purpose | Request/Response | Access Control |
|----------|-------------|---------|------------------|----------------|
| `/api/v1/customers` | GET | List user's customers | Paginated customer list | MSR: own customers |
| `/api/v1/customers` | POST | Submit new customer | Customer data with address | MSR, MSM |
| `/api/v1/customers/{id}` | GET | Get customer details | Full customer record | Customer owner |
| `/api/v1/customers/{id}` | PUT | Update customer | Updated customer data | Customer owner |
| `/api/v1/pending-customers` | GET | List pending approvals | Customers awaiting TSM approval | TSM (territory only) |
| `/api/v1/customers/{id}/approve` | POST | Approve customer | Status update | TSM (territory match) |
| `/api/v1/customers/{id}/deny` | POST | Deny customer | Status + reason | TSM (territory match) |
| `/api/v1/customers/validate-address` | POST | Validate address | UPS API integration | Authenticated |

### Campaign & Incentive Management API

| Endpoint | HTTP Method | Purpose | Request/Response | Access Control |
|----------|-------------|---------|------------------|----------------|
| `/api/v1/campaigns` | GET | List active campaigns | Campaign list with rules | Authenticated |
| `/api/v1/campaigns/{id}` | GET | Get campaign details | Full campaign with tiers/caps | Authenticated |
| `/api/v1/campaigns` | POST | Create new campaign | Campaign configuration | Admin only |
| `/api/v1/campaigns/{id}/rules` | POST | Add campaign rule | Rule configuration | Admin only |
| `/api/v1/campaigns/{id}/progress` | GET | Get user's campaign progress | Points/earnings vs caps | Authenticated |
| `/api/v1/leaderboards/{campaign_id}` | GET | Get campaign leaderboard | Sorted participant rankings | Authenticated |

### Payout & Financial Management API

| Endpoint | HTTP Method | Purpose | Request/Response | Access Control |
|----------|-------------|---------|------------------|----------------|
| `/api/v1/payouts` | GET | List user's payouts | Paginated payout history | MSR: own, MSM: team |
| `/api/v1/payouts/{id}` | GET | Get payout details | Full payout with claim link | Payout recipient |
| `/api/v1/payouts/pending` | GET | List pending payouts | Unpaid payout records | Finance admin |
| `/api/v1/payouts/export` | POST | Export for payment | CSV with W9-verified users | Finance admin |
| `/api/v1/payouts/mark-paid` | POST | Mark payouts as paid | Batch status update | Finance admin |
| `/api/v1/disputes` | POST | Submit payout dispute | Dispute details | Authenticated |
| `/api/v1/disputes` | GET | List disputes | Open dispute tickets | Admin |
| `/api/v1/disputes/{id}` | PUT | Update dispute status | Status + resolution notes | Admin |

### Dashboard & Analytics API

| Endpoint | HTTP Method | Purpose | Request/Response | Access Control |
|----------|-------------|---------|------------------|----------------|
| `/api/v1/me/earnings` | GET | Get user earnings summary | Pending/paid totals | Authenticated |
| `/api/v1/me/claims` | GET | Get recent claims | Recent claim history | Authenticated |
| `/api/v1/me/campaign-progress` | GET | Get campaign progress | Progress vs caps | Authenticated |
| `/api/v1/teams/performance` | GET | Get team performance | Team KPIs | MSM |
| `/api/v1/territory/performance` | GET | Get territory metrics | Territory KPIs | TSM |
| `/api/v1/national/kpis` | GET | Get national KPIs | Executive metrics | NSM |

### Reporting API

| Endpoint | HTTP Method | Purpose | Request/Response | Access Control |
|----------|-------------|---------|------------------|----------------|
| `/api/v1/reports/{report_name}` | POST | Generate report | Report data (JSON/CSV) | Role-based filtering |
| `/api/v1/reports/sales-performance` | POST | Sales performance report | Team/territory sales data | MSM, TSM, NSM |
| `/api/v1/reports/payouts` | POST | Payout history report | Detailed payout records | Role-based access |
| `/api/v1/reports/claim-analysis` | POST | Claim analysis report | Claim statistics | MSM, TSM, NSM |
| `/api/v1/reports/campaign-roi` | POST | Campaign ROI report | Campaign effectiveness | Admin, NSM |

### Communication & Notification API

| Endpoint | HTTP Method | Purpose | Request/Response | Access Control |
|----------|-------------|---------|------------------|----------------|
| `/api/v1/notifications` | GET | Get user notifications | Paginated notification list | Authenticated |
| `/api/v1/notifications/{id}/read` | POST | Mark notification as read | Status update | Notification recipient |
| `/api/v1/notifications/mark-all-read` | POST | Mark all as read | Batch status update | Authenticated |
| `/api/v1/communications/email` | POST | Send email campaign | Email configuration | Admin |

### Data Management API

| Endpoint | HTTP Method | Purpose | Request/Response | Access Control |
|----------|-------------|---------|------------------|----------------|
| `/api/v1/import/{entity_type}` | POST | Import CSV data | Validation results | Admin |
| `/api/v1/import/templates/{entity_type}` | GET | Get CSV template | Template file download | Admin |
| `/api/v1/export/{entity_type}` | POST | Export data to CSV | CSV file download | Admin |
| `/api/v1/gdpr/consent` | POST | Record user consent | Consent timestamp | During registration |
| `/api/v1/gdpr/data-request` | POST | Request personal data | Export request ticket | Authenticated |
| `/api/v1/gdpr/erasure-request` | POST | Request data erasure | Erasure request ticket | Authenticated |

### Content Management API

| Endpoint | HTTP Method | Purpose | Request/Response | Access Control |
|----------|-------------|---------|------------------|----------------|
| `/api/v1/content/pages` | GET | List content pages | Page list | Public/Authenticated |
| `/api/v1/content/pages/{slug}` | GET | Get page content | Page HTML/Markdown | Public/Authenticated |
| `/api/v1/content/pages` | POST | Create content page | New page data | Admin |
| `/api/v1/content/pages/{id}` | PUT | Update content page | Updated page data | Admin |
| `/api/v1/content/pages/{id}/publish` | POST | Publish page | Status update | Admin |

## Frontend Routes (React SPA)

### User Role Navigation Flow

```mermaid
graph TB
    Start([User Visits Site]) --> Login["/login<br/>Authentication"]
    Login --> Auth{Authenticated?}
    Auth -->|No| Login
    Auth -->|Yes| RoleCheck{Check User Role}
    
    RoleCheck -->|MSR| MSR_Dash["/dashboard<br/>MSR Dashboard"]
    RoleCheck -->|MSM| MSM_Dash["/dashboard<br/>MSM Dashboard"]
    RoleCheck -->|TSM| TSM_Dash["/dashboard<br/>TSM Dashboard"]
    RoleCheck -->|NSM| NSM_Dash["/dashboard<br/>NSM Dashboard"]
    RoleCheck -->|Admin| Admin_Dash["/admin<br/>Admin Panel"]
    
    subgraph MSR_Routes["MSR Routes"]
        MSR_Dash --> MSR_Claims["/claims<br/>Manage Claims"]
        MSR_Dash --> MSR_Customers["/customers<br/>Manage Customers"]
        MSR_Dash --> MSR_Payouts["/payouts<br/>View Earnings"]
        MSR_Dash --> MSR_Campaigns["/campaigns<br/>View Campaigns"]
        MSR_Claims --> MSR_NewClaim["/claims/new<br/>Submit Claim"]
        MSR_Customers --> MSR_NewCustomer["/customers/new<br/>Add Customer"]
    end
    
    subgraph MSM_Routes["MSM Routes (includes MSR routes)"]
        MSM_Dash --> MSM_Review["/claims/review<br/>Review Team Claims"]
        MSM_Dash --> MSM_Team["/team<br/>Team Performance"]
        MSM_Dash --> MSM_Reports["/reports<br/>Team Reports"]
        MSM_Review --> MSM_Batch[Batch Approve/Deny]
    end
    
    subgraph TSM_Routes["TSM Routes (includes MSM routes)"]
        TSM_Dash --> TSM_Pending["/customers/pending<br/>Approve Customers"]
        TSM_Dash --> TSM_Territory["/territory<br/>Territory Analytics"]
        TSM_Dash --> TSM_Reports["/reports<br/>Territory Reports"]
    end
    
    subgraph NSM_Routes["NSM Routes (includes TSM routes)"]
        NSM_Dash --> NSM_Analytics["/analytics<br/>National Analytics"]
        NSM_Dash --> NSM_Campaigns["/campaigns/manage<br/>Manage Campaigns"]
        NSM_Dash --> NSM_Executive["/reports/executive<br/>Executive Reports"]
    end
    
    subgraph Admin_Routes["Admin Routes (all access)"]
        Admin_Dash --> Admin_Users["/admin/users<br/>User Management"]
        Admin_Dash --> Admin_Campaigns["/admin/campaigns<br/>Campaign Admin"]
        Admin_Dash --> Admin_Import["/admin/import<br/>Data Import"]
        Admin_Dash --> Admin_Content["/admin/content<br/>CMS"]
        Admin_Dash --> Admin_Disputes["/admin/disputes<br/>Dispute Resolution"]
        Admin_Dash --> Admin_Audit["/admin/audit<br/>Audit Logs"]
    end
    
    subgraph Common_Routes["Common Routes (all authenticated)"]
        Profile["/profile<br/>User Profile"]
        W9["/profile/w9<br/>W9 Upload"]
        Notifications["/notifications<br/>View Notifications"]
        Support["/support<br/>Help & Support"]
    end
    
    MSR_Dash -.-> Common_Routes
    MSM_Dash -.-> Common_Routes
    TSM_Dash -.-> Common_Routes
    NSM_Dash -.-> Common_Routes
    Admin_Dash -.-> Common_Routes

    style Start fill:#f9f
    style Login fill:#bbf
    style MSR_Routes fill:#e1f5fe
    style MSM_Routes fill:#c5e1a5
    style TSM_Routes fill:#fff9c4
    style NSM_Routes fill:#ffccbc
    style Admin_Routes fill:#ce93d8
    style Common_Routes fill:#e0e0e0
```

### Public Routes

| Route Path | Component | Purpose | Features |
|------------|-----------|---------|----------|
| `/login` | LoginPage | User authentication | Clerk integration, SSO support, MFA |
| `/signup` | SignupPage | New user registration | Role selection, manager assignment |
| `/forgot-password` | ForgotPasswordPage | Password reset | Email-based recovery |
| `/terms` | TermsPage | Terms of service | Content from CMS |
| `/privacy` | PrivacyPage | Privacy policy | Content from CMS |

### Authenticated Routes - Common

| Route Path | Component | Purpose | Access Control |
|------------|-----------|---------|----------------|
| `/` | Dashboard | Role-based dashboard | Redirects based on user role |
| `/profile` | ProfilePage | User profile management | All authenticated |
| `/profile/w9` | W9UploadPage | W9 document upload | All authenticated |
| `/notifications` | NotificationsPage | View notifications | All authenticated |
| `/support` | SupportPage | Help and support | All authenticated |
| `/support/faq` | FAQPage | Frequently asked questions | All authenticated |

### MSR (Marketer Sales Rep) Routes

| Route Path | Component | Purpose | Key Features |
|------------|-----------|---------|--------------|
| `/dashboard` | MSRDashboard | MSR homepage | Earnings widget, claim status, campaign progress |
| `/claims` | ClaimListPage | View claim history | Status tracking, filters, search |
| `/claims/new` | NewClaimPage | Submit new claim | Multi-step wizard, invoice upload/OCR |
| `/claims/{id}` | ClaimDetailPage | View claim details | Status, denial reasons, invoice view |
| `/customers` | CustomerListPage | Manage customers | List, search, status indicators |
| `/customers/new` | NewCustomerPage | Submit new customer | Address validation, auto-assignment |
| `/customers/{id}` | CustomerDetailPage | Edit customer | Update details, view history |
| `/payouts` | PayoutHistoryPage | View payout history | Detailed records, calculations |
| `/campaigns` | CampaignListPage | View active campaigns | Rules, progress, leaderboards |
| `/campaigns/{id}` | CampaignDetailPage | Campaign details | Progress visualization, rules |

### MSM (Marketer Sales Manager) Routes

| Route Path | Component | Purpose | Key Features |
|------------|-----------|---------|--------------|
| `/dashboard` | MSMDashboard | Manager homepage | Claim queue, team KPIs, leaderboard |
| `/claims/review` | ClaimReviewPage | Review pending claims | Batch operations, denial reasons |
| `/team` | TeamPerformancePage | View team metrics | Individual performance, comparisons |
| `/reports` | ReportsPage | Access reports | Sales, payouts, performance |

### TSM (Territory Sales Manager) Routes

| Route Path | Component | Purpose | Key Features |
|------------|-----------|---------|--------------|
| `/dashboard` | TSMDashboard | Territory dashboard | Customer approvals, territory KPIs |
| `/customers/pending` | PendingCustomersPage | Approve new customers | Territory filtering, batch approval |
| `/territory` | TerritoryAnalyticsPage | Territory performance | Comparative analytics, trends |
| `/reports` | ReportsPage | Territory reports | Detailed analytics, exports |

### NSM (National Sales Manager) Routes

| Route Path | Component | Purpose | Key Features |
|------------|-----------|---------|--------------|
| `/dashboard` | NSMDashboard | Executive dashboard | National KPIs, ROI metrics |
| `/analytics` | NationalAnalyticsPage | Drill-down analytics | Territory comparisons, trends |
| `/campaigns/manage` | CampaignManagementPage | Manage campaigns | Create, configure rules, budgets |
| `/reports/executive` | ExecutiveReportsPage | Executive reports | High-level summaries, exports |

### Admin Routes

| Route Path | Component | Purpose | Key Features |
|------------|-----------|---------|--------------|
| `/admin` | AdminDashboard | Admin homepage | System overview, quick actions |
| `/admin/users` | UserManagementPage | Manage users | Create, edit, role assignment |
| `/admin/campaigns` | CampaignAdminPage | Campaign administration | Create, configure rules, tiers |
| `/admin/campaigns/{id}/rules` | CampaignRulesPage | Configure campaign rules | Product payouts, caps, manager bonuses |
| `/admin/import` | DataImportPage | Bulk data import | CSV upload, validation, error handling |
| `/admin/export` | DataExportPage | Data export | Entity selection, filters |
| `/admin/content` | ContentManagementPage | Manage static content | WYSIWYG editor, page management |
| `/admin/communications` | EmailCampaignPage | Email campaigns | Compose, schedule, track |
| `/admin/disputes` | DisputeManagementPage | Handle disputes | Review, resolve, communicate |
| `/admin/audit` | AuditLogPage | View audit trail | Search, filter, compliance reports |

## Technical Architecture Notes

### API Design Principles
- **RESTful Design**: All endpoints follow REST conventions
- **Versioning**: API versioned at `/api/v1/` for future compatibility
- **Authentication**: JWT-based auth via Clerk integration
- **Authorization**: ABAC/ReBAC middleware for fine-grained access control
- **Response Format**: JSON for all API responses, CSV for exports
- **Pagination**: Consistent pagination for list endpoints
- **Error Handling**: Standardized error response format

### Frontend Architecture
- **SPA Design**: React single-page application
- **Code Splitting**: Route-based splitting for performance
- **State Management**: TanStack Query for server state
- **Component Library**: Shadcn UI with Tailwind CSS
- **Responsive Design**: Mobile-first approach
- **Accessibility**: WCAG 2.1 AA compliance

### Security Features
- **Multi-Factor Authentication**: SMS, email, authenticator app support
- **Single Sign-On**: SAML 2.0 enterprise SSO
- **Encryption**: TLS for transit, AES-256 for storage
- **Audit Trail**: Comprehensive logging of all actions
- **GDPR Compliance**: Consent management, data portability, erasure rights
- **SOC 2 Ready**: Security controls and monitoring

### Performance Optimization
- **Caching**: Redis for API responses, CloudFront for static assets
- **Database**: Read replicas for reporting queries
- **API Response Time**: <2 seconds at 95th percentile
- **Frontend Load Time**: <3 seconds initial, <1 second navigation
- **Scalability**: Auto-scaling ECS tasks, Fargate Spot instances

## User Role Definitions (Simplified)

### Core Roles
- **NSM** (National Sales Manager): Executive oversight, national KPIs, campaign management
- **TSM** (Territory Sales Manager): Territory management, customer approvals, regional analytics
- **MSM** (Marketer Sales Manager): Team management, claim approvals, performance monitoring
- **MSR** (Marketer Sales Rep): Claim submission, customer management, earnings tracking

### Access Control Model
- **Hierarchical Permissions**: Based on organizational structure (reports-to relationships)
- **Attribute-Based**: Territory, region, and other attributes for fine-grained control
- **Relationship-Based**: Managers can only access their direct reports' data
- **Dynamic Authorization**: Permissions encoded in JWT for efficient API authorization