# XD Incentives Platform - Project Brief

## Executive Summary

XD Incentives is a multi-tenant, white-label SaaS platform designed to revolutionize how channel-focused organizations manage partner incentives and drive measurable ROI. The platform addresses critical pain points in channel management including:

- Manual process burden (40% of admin time)
- Limited visibility into program effectiveness
- Complex commission calculations
- Low partner engagement rates

The primary problem being solved is the inefficiency and ineffectiveness of current incentive management, which relies on scattered spreadsheets, emails, and manual processes. This leads to unclear ROI, difficulty influencing partner behavior at scale, and significant administrative overhead.

The target market consists of brands and manufacturers with distributed channel ecosystems, initially focusing on the energy sector with **Citgo Petroleum Corporation** as the pilot customer. The platform will serve four key user personas:

- **MSR** - Sales Reps
- **MSM** - Sales Managers
- **TSM** - Territory Sales Managers
- **NSM** - National Sales Managers

### Key Value Proposition

- 95% partner participation rates
- Reduce dispute resolution time to under 24 hours
- Deliver a 4:1 ROI on incentive investments through automation, transparency, and real-time visibility
## Problem Statement

### Current State and Pain Points

Channel-focused organizations currently manage billions in partner incentives using fragmented, manual processes. Incentive programs are scattered across email threads, Excel spreadsheets, and siloed tools, creating a chaotic ecosystem where:

- Administrative teams spend **40% of their time** on manual data entry, reconciliation, and dispute resolution
- **60% of channel partners** don't actively participate in incentive programs due to lack of visibility and complex processes
- Commission disputes take **3-7 days to resolve**, damaging partner relationships
- ROI on incentive investments is unclear, with **30% of funds** going unclaimed or misallocated
- Field sales representatives waste **15-20 minutes per transaction** on manual entry, reducing selling time

### Impact of the Problem

This inefficiency costs organizations millions in lost revenue opportunities. For a typical enterprise with 500+ channel partners, this translates to:

- **$2-3M** in annual administrative costs
- **12-15% lower** channel revenue due to poor partner engagement
- **25% higher** partner churn rates
- **Compliance risks** from manual processes and inadequate audit trails

### Why Existing Solutions Fall Short

Current alternatives fail because they're either generic CRM add-ons that lack channel-specific functionality, or outdated legacy systems that can't scale. Purpose-built channel incentive platforms are rare, and those that exist lack:

- Mobile capabilities
- Real-time visibility
- Multi-tenant architecture

### Urgency

The shift to digital commerce and increasing partner expectations for transparency make this problem critical. Organizations that don't modernize their incentive management risk losing market share to competitors with more engaging partner programs.
## Proposed Solution

### Core Concept and Approach

XD Incentives is a purpose-built, cloud-native platform that transforms channel incentive management through automation, real-time visibility, and mobile-first design. The solution takes a phased approach, starting with core functionality (MVP) and progressively adding advanced features based on user feedback and market demands.

### Key Differentiators

- **Multi-tenant Architecture**: Single platform instance serving multiple organizations with complete data isolation and white-label customization
- **Mobile-First Design**: Field sales reps can submit claims in under 5 minutes from any device, even offline
- **Real-Time Visibility**: All stakeholders see earnings, progress, and payouts instantly, eliminating the "black box" effect
- **Intelligent Automation**: Rule-based campaign engine automates calculations, approvals, and payments, reducing manual work by 80%
- **Purpose-Built for Channels**: Unlike generic solutions, every feature is designed specifically for multi-tier channel ecosystems

### Why This Solution Will Succeed

- **Domain Expertise**: Built on 15+ years of channel management experience, addressing real-world pain points
- **Phased Delivery**: MVP focuses on core needs, allowing rapid deployment and iterative improvement
- **User-Centric Design**: Separate experiences for each persona (MSR, MSM, TSM, NSM) ensuring adoption across all levels
- **Proven Technology**: Built on enterprise-grade AWS infrastructure with modern tech stack (React/Django)
- **Measurable ROI**: Clear metrics and dashboards prove program effectiveness from day one

### High-Level Vision

XD Incentives will become the industry standard for channel incentive management, enabling organizations to run programs that partners love while achieving unprecedented operational efficiency and revenue growth.
## Target Users

### Primary User Segment: Channel Sales Representatives (MSR)

#### Demographic/Firmographic Profile

- **Age**: 25-55, primarily field-based workers
- **Tech comfort**: Low to medium (especially older reps like Carlos Martinez persona)
- **Work environment**: 70% time in field, 30% office/home
- **Device usage**: Primarily mobile phones, occasional laptop access
- **Geographic distribution**: Across territories, often in rural areas

#### Current Behaviors and Workflows

- Submit paper forms or Excel sheets for claims
- Call managers for earnings updates
- Keep personal spreadsheets to track commissions
- Spend 15-20 minutes per transaction on data entry
- Often submit claims late due to complexity

#### Specific Needs and Pain Points

- Simple, fast claim submission from the field
- Clear visibility into earnings and payment status
- Offline capability for poor connectivity areas
- Minimal training requirements
- Error prevention and easy correction

#### Goals

- Maximize commission earnings
- Spend more time selling, less on admin
- Understand program rules clearly
- Get paid accurately and on time

### Secondary User Segment: Sales Operations Directors

#### Demographic/Firmographic Profile

- **Age**: 35-50, office-based managers
- **Tech comfort**: High, data-driven decision makers
- **Education**: Business degree, often MBA
- **Team size**: Managing 20-50 sales reps
- **Industry experience**: 10+ years

#### Current Behaviors and Workflows

- Manually compile reports from multiple sources
- Spend hours validating claims and calculations
- Use pivot tables and macros for analysis
- Field constant inquiries about payment status
- Struggle to identify top performers quickly

#### Specific Needs and Pain Points

- Automated reporting and analytics
- Bulk operations for efficiency
- Integration with existing systems
- Real-time team performance visibility
- Audit trails for compliance

#### Goals

- Reduce administrative burden by 40%
- Improve program ROI measurement
- Enable data-driven decisions
- Ensure compliance and accuracy

### Territory Sales Manager (TSM)

#### Demographic/Firmographic Profile

- **Age**: 35-55, regional management role
- **Tech comfort**: Medium to high
- **Work environment**: 50% office, 50% field visits
- **Manages**: 5-10 Sales Managers across a geographic territory
- **Industry tenure**: 15+ years, deep market knowledge

#### Current Behaviors and Workflows

- Review and approve new customer registrations
- Validate customer eligibility for programs
- Monitor territory performance against quotas
- Coordinate with MSMs on strategic accounts
- Manage territory-specific promotions and campaigns

#### Specific Needs and Pain Points

- Prevent duplicate customer registrations across territory
- Ensure only valid customers enter the system
- Track territory performance vs. other regions
- Identify growth opportunities by customer segment
- Manage complex approval workflows efficiently

#### Goals

- Protect territory integrity and prevent fraud
- Drive territory revenue growth
- Enable team success through proper customer setup
- Maintain competitive position vs. other territories

### National Sales Manager (NSM)

#### Demographic/Firmographic Profile

- **Age**: 45-60, senior leadership role
- **Tech comfort**: Medium, focused on insights not operations
- **Work environment**: Executive office, frequent travel
- **Oversees**: All territories and sales channels nationally
- **Reports to**: C-suite executives

#### Current Behaviors and Workflows

- Review high-level dashboards and KPIs
- Present program ROI to executives
- Set national sales strategies and quotas
- Monitor competitive landscape
- Make program investment decisions

#### Specific Needs and Pain Points

- Executive-level visibility without operational detail
- Real-time access to critical metrics
- Ability to drill down when needed
- Compare performance across all territories
- Demonstrate program ROI clearly

#### Goals

- Drive national sales growth
- Optimize incentive investment allocation
- Ensure program compliance
- Make data-driven strategic decisions

### Additional Stakeholder Personas

#### Chief Sales Officer (CSO) - Michael Reynolds Type

**Demographic/Firmographic Profile:**
- **Age**: 45-55, C-suite executive
- **Tech comfort**: Low to medium, relies on prepared reports
- **Focus**: Strategic vision and ROI
- **Reports to**: CEO/Board

**Specific Needs:**
- Single-pane visibility across all programs
- Clear ROI metrics and program effectiveness
- Competitive advantage through channel excellence

#### Field Manager - Amanda Foster Type

**Demographic/Firmographic Profile:**
- **Age**: 30-45, frontline management
- **Tech comfort**: Medium
- **Manages**: 5-15 direct sales reps
- **Work environment**: 60% field, 40% office

**Specific Needs:**
- Mobile team management tools
- Quick dispute resolution capabilities
- Team leaderboards and motivation tools
- Coaching insights based on performance data

## Goals & Success Metrics

### Business Objectives

- **Reduce Administrative Burden by 40%** - Measured by time-tracking studies comparing pre/post implementation for sales operations teams by Month 9
- **Achieve 95% Partner Participation Rate** - Measured by unique monthly active users divided by total registered partners within 90 days of launch
- **Deliver 4:1 ROI on Incentive Programs** - Measured by incremental revenue generated divided by total incentive spend, tracked quarterly after Month 6
- **Enable 12% Year-over-Year Channel Revenue Growth** - Measured by comparing channel sales data year-over-year starting in Year 2
- **Scale to $4.3M Platform Revenue by Year 3** - Measured by annual recurring revenue from 8-10 enterprise customers
- **Achieve Full Regulatory Compliance** - Pass SOC 2 Type II, PCI DSS Level 1, and GDPR audits within 12 months

### User Success Metrics

- **Reduce Claim Submission Time to <5 Minutes** - Measured by average time from claim start to submission for MSRs
- **Achieve <24 Hour Dispute Resolution** - Measured by average time from dispute filing to resolution
- **Deliver 90% First-Time Claim Accuracy** - Measured by claims approved without revision divided by total claims submitted
- **Enable Real-Time Earnings Visibility** - Measured by <2 second load time for earnings dashboards at 95th percentile
- **Achieve 8.0+ User Satisfaction Score** - Measured by quarterly NPS surveys across all user personas
- **Ensure 100% Data Migration Accuracy** - Validated through reconciliation of all 200,000 transaction records and 1,500 user records from Django/MySQL system

### Key Performance Indicators (KPIs)

#### System Performance

- **Uptime**: 99.9% availability measured monthly via AWS CloudWatch
- **Response Time**: <2 seconds for 95th percentile of requests via DataDog monitoring
- **Concurrent Users**: Support 1000+ concurrent users per tenant validated through load testing
- **Transaction Processing**: 100,000+ transactions per day with data integrity

#### Security & Compliance

- **Security Score**: Pass all quarterly penetration tests with zero critical vulnerabilities
- **Audit Compliance**: Maintain 100% audit trail completeness with 7-year retention
- **Data Encryption**: 100% of PII encrypted at rest (AES-256) and in transit (TLS 1.3)
- **Access Control**: Zero unauthorized access incidents tracked via security monitoring
- **Authentication Success**: 99%+ SSO/MFA authentication success rate

#### Integration Success

- **Data Import Success**: 100% CSV import success rate with validation
- **API Availability**: 99.95% uptime for REST APIs supporting integrations
- **Payment Processing**: <0.1% transaction failure rate via Stripe integration (Phase 2)
- **Enterprise Integration**: 95% data sync accuracy for Salesforce/SAP integrations (Phase 3)

#### Operational Metrics

- **Mobile Usage**: 60% of claims submitted via mobile devices
- **Training Effectiveness**: 80% of users pass competency assessment within 30 days
- **Support Efficiency**: 60% reduction in support tickets by Month 3 post-launch
- **Report Generation**: 75% reduction in time to generate reports by Phase 2
- **Feature Utilization**: 70% of available features actively used within 6 months

## MVP Scope

### Core Features (Must Have)

Based on the development epics, the MVP will be delivered across **15 epics over 14 sprints** (approximately 7 months):

#### Infrastructure & Platform Foundation (Epics 1-2, 4)

- Multi-tenant AWS infrastructure with Terraform automation and CI/CD pipeline
- Complete data isolation via separate AWS accounts and VPCs per tenant
- White-label branding capabilities with customizable logos, colors, and domains
- Core data models and S3 file storage for all system entities
- RDS MySQL, ElastiCache, CloudWatch monitoring setup

#### Authentication & Security (Epic 3)

- Enterprise SSO integration via Clerk with SAML 2.0 support
- Multi-factor authentication (SMS, email, token-based)
- Hierarchical 4-tier role system (NSM → TSM → MSM → MSR) with inheritance
- Complete audit trails for all financial transactions with 7-year retention
- SOC 2 controls implementation

#### Sales & Claims Management (Epics 5, 8)

- Customer management with TSM approval workflow and duplicate detection
- Streamlined claim submission interface with invoice uploads
- MSM claim review dashboard with batch operations and denial reasons
- Complete claim workflow from submission through approval
- Mobile-optimized for field operations

#### Campaign Management (Epic 6)

Comprehensive campaign engine supporting all required Citgo campaigns:

- **General Payouts**: Product-based with Year 1/2 tiers, $35K/year cap
- **Manager Payouts**: 20% of sales rep earnings, no cap
- **HD Contest**: Multi-product point system with real-time leaderboard
- **Grand Prize**: Annual contest by line of business
- **RTTC campaign support** with $10K/customer cap and manager payouts
- Rule-based configuration without code changes

#### Fund Management (Epic 9)

- Automated payout calculations from approved claims
- Budget tracking and fund allocation in real-time
- W9 collection and validation system with secure storage
- Dispute management with <24 hour resolution target
- Export functionality for financial reconciliation

#### User Experience (Epics 10-11)

- Responsive design system with unified navigation
- Role-specific dashboards for all four personas (MSR, MSM, TSM, NSM)
- Self-service partner portals with earnings visibility
- Mobile-first design supporting offline capabilities
- Progressive Web App foundation

#### Communications & Content (Epic 12)

- In-app notifications with real-time updates
- AWS SES email integration for automated communications
- Static content CMS for FAQs, privacy policies, payout schedules
- Admin-editable content without developer intervention

#### Analytics & Reporting (Epic 13)

- Standard operational reports matching current Citgo reports
- Executive dashboards with KPI visualization
- CSV import/export tools for data management
- Role-based report access and scheduling

#### Data Migration (Epic 14)

- Complete migration of 200,000 transactions from Django/MySQL
- Migration of 1,500 user records with role preservation
- 9 years of historical data preserved
- Phased migration approach to minimize disruption

#### Performance & Compliance (Epic 15)

- Sub-2 second response time optimization
- Multi-layer caching with Redis
- WCAG 2.1 AA accessibility compliance
- SOC 2 preparation and security hardening
- GDPR compliance features including right to erasure

### Out of Scope for MVP

**Phase 2 Features:**
- Visual Invoice Reader with OCR
- Automated communication workflows beyond basic email
- Real-time ROI Dashboard with predictive analytics
- Campaign template library
- Progressive Web App offline sync capabilities
- Physical rewards fulfillment
- Stripe payment processing

**Phase 3 Features:**
- Enterprise integrations - Salesforce, SAP, Power BI
- Advanced analytics and machine learning
- Gamification engine beyond basic contests
- Multi-currency/language support
- Fax-to-email invoice processing

**Separate Products:**
- XD Rewards Catalog module
- Deal Pro (Conquest+) integration (future consideration)

### MVP Success Criteria

- **Single Pilot Customer** - Citgo Petroleum Corporation fully onboarded and operational
- **All 15 Epics Completed** - Core functionality deployed and tested
- **Performance** - <2 second average response time at 95th percentile
- **Reliability** - 99.5% uptime during pilot period
- **Security** - Pass security audit with zero critical vulnerabilities
- **Data Integrity** - 100% accuracy in migrated data and commission calculations
- **User Adoption** - 80% of pilot users actively using the system within 30 days
- **Campaign Accuracy** - All Citgo campaign types calculating correctly with proper caps

## Post-MVP Vision

### Phase 2 Features (Months 7-12)

#### Automation & Intelligence

- **Visual Invoice Reader with OCR** - Automated invoice processing eliminating manual data entry, targeting 90%+ accuracy for faxed and photographed invoices
- **Automated Communications** - Full workflow automation with AWS SES/SNS including targeted campaigns, behavioral triggers, and multi-channel messaging
- **Real-time ROI Dashboard** - Dynamic analytics showing program performance, ROI calculations, and predictive insights for optimization

#### Enhanced User Experience

- **Campaign Template Library** - 12+ pre-built campaign templates based on best practices, enabling rapid program launches in under 3 days
- **Progressive Web App with Offline Sync** - Full offline capabilities for field reps, automatic data synchronization when connectivity returns
- **B2B Portal Customization** - Advanced white-label features including custom workflows, fields, and business rules per tenant

#### Expanded Capabilities

- **Partner Enrollment Engine** - Self-service onboarding with automated verification, reducing manual enrollment time by 75%
- **Physical Rewards Fulfillment** - Integration with fulfillment providers, shipping tracking, and inventory management
- **Points Management System** - Non-cash incentive tracking with balance management, expiration rules, and conversion options
- **Territory Performance Analytics** - Detailed geographic analytics with comparison tools and competitive insights

#### Financial Integration

- **Stripe Payment Processing** - Automated payouts, recurring payments, and full PCI compliance for direct financial transactions
- **Advanced Fund Management** - Budget forecasting, allocation optimization, and multi-currency support preparation

### Long-term Vision (Year 2+)

#### Enterprise Integration & Intelligence

- **Salesforce Bi-directional Sync** - Real-time synchronization of accounts, opportunities, and custom objects for unified CRM experience
- **SAP Integration** - Product catalog, pricing data, and order management integration for enterprise clients
- **Power BI Direct Query** - Native integration enabling custom analytics and enterprise reporting

#### AI-Powered Optimization

- **Partner Health Scoring** - Machine learning algorithms predicting partner engagement and churn risk
- **Predictive Program Analytics** - AI-driven recommendations for incentive optimization and budget allocation
- **Fraud Detection Engine** - Automated anomaly detection reducing fraudulent claims by 90%+
- **Personalized Incentive Recommendations** - Dynamic incentive offers based on partner behavior and performance patterns

#### Global Expansion

- **Multi-currency Support** - Full international payment processing and currency conversion
- **Multi-language Platform** - Localization for 10+ languages supporting global partner networks
- **Regional Compliance Tools** - Automated compliance for international tax and regulatory requirements

#### Advanced Engagement

- **Gamification Engine** - Comprehensive achievement system, badges, and social leaderboards driving 30% higher engagement
- **Partner Community Platform** - Social features, knowledge sharing, and peer recognition
- **Mobile-Native Apps** - iOS and Android applications with enhanced offline capabilities and push notifications

Expansion Opportunities
Industry Verticals

Automotive - Dealer incentive management with VIN tracking and warranty integration
Technology - Channel partner programs for software and hardware vendors
Healthcare - Pharmaceutical rep incentive management with compliance controls
Financial Services - Insurance broker and financial advisor commission management

Platform Extensions

XD Rewards Catalog Integration - Seamless connection to comprehensive rewards marketplace
Deal Pro (Conquest+) Bundle - Combined offering for complete channel enablement
Training & Certification Module - Integrated learning management for partner education
Channel Marketing Automation - Co-op fund management and marketing asset distribution

Strategic Partnerships

ERP Integrations - Oracle, Microsoft Dynamics, NetSuite connectors
Payment Processors - Regional payment provider integrations for global reach
Business Intelligence - Tableau, Looker, Qlik integrations for advanced analytics
Channel Ecosystem - Partnerships with complementary channel management platforms

Revenue Model Evolution

Usage-based Pricing Tiers - Flexible pricing based on transaction volume and active users
Premium Feature Add-ons - Advanced modules available as paid upgrades
Managed Services Offering - Full program management for enterprise clients
Marketplace Revenue Share - Commission on third-party integrations and services

Innovation Pipeline

Blockchain Integration - Transparent, immutable incentive tracking for high-stakes programs
Voice-Activated Interfaces - Alexa/Google Assistant integration for hands-free claim submission
Augmented Reality Features - AR-based product verification and training
IoT Integration - Direct integration with connected devices for automated claim validation

## Technical Considerations

### Platform Requirements

#### Target Platforms

- Web-based SaaS platform accessible via modern browsers (Chrome, Firefox, Safari, Edge)
- Progressive Web App for mobile devices (iOS Safari, Android Chrome)
- Responsive design supporting desktop (1920x1080+), tablet (768px+), and mobile (320px+) viewports

#### Browser/OS Support

- **Desktop**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile**: iOS 14+ Safari, Android 8+ Chrome
- **Note**: No IE11 support required (per modern enterprise standards)

#### Performance Requirements

- **Page Load**: <3 seconds initial load, <1 second subsequent navigation
- **API Response**: <2 seconds for 95th percentile of requests
- **Concurrent Users**: 1000+ per tenant without degradation
- **Data Processing**: 100,000+ transactions per day
- **File Upload**: Support for 10MB invoice images

### Technology Preferences

#### Frontend

- **Framework**: React v19+ with TypeScript
- **Build Tool**: Vite for fast development and optimized production builds
- **UI Components**: Tailwind CSS v4+ with Shadcn UI component library
- **State Management**: Zustand v5+ for lightweight, performant state handling
- **Testing**: Cypress v14+ (E2E), Jest/React Testing Library v16+ (Unit)

#### Backend

- **Runtime**: Python 3.12.3+
- **Framework**: Django v5.2.3+ with Django REST Framework
- **Database**: Amazon RDS for MySQL v8.4
- **Testing**: unittest with pytest for enhanced testing capabilities
- **API Documentation**: OpenAPI 3.0 with Swagger UI

#### Database

- **Primary**: Amazon RDS MySQL 8.4 for transactional data
- **Caching**: AWS ElastiCache (Redis 7.0+) for performance optimization
- **File Storage**: AWS S3 for documents and media files
- **Search**: Elasticsearch (future consideration for Phase 3)

#### Hosting/Infrastructure

- **Cloud Provider**: AWS (Amazon Web Services)
- **Compute**: AWS EC2 with Auto Scaling Groups
- **Container Platform**: Docker with AWS ECS for orchestration
- **Load Balancing**: AWS Application Load Balancer
- **CDN**: AWS CloudFront for static asset delivery
- **Infrastructure as Code**: Terraform for reproducible deployments

Architecture Considerations

Repository Structure:

Monorepo using npm workspaces for shared code management
Separate packages for frontend, backend, and shared utilities
Centralized configuration and dependency management
Git-based version control with feature branch workflow

Service Architecture:

Modular monolith for MVP, designed for future microservices extraction
Clear domain boundaries between modules (auth, campaigns, claims, etc.)
RESTful API design with potential GraphQL layer in Phase 3
Event-driven architecture preparation for asynchronous processing

Integration Requirements:

Webhook support for real-time event notifications
Batch API endpoints for bulk operations
OAuth 2.0 for third-party integrations
Standardized error responses and rate limiting
API versioning strategy (URL-based: /api/v1/)

Security/Compliance:

SOC 2 Type II compliance from launch
PCI DSS Level 1 readiness for Phase 2 payment processing
GDPR compliance with data privacy controls
WCAG 2.1 AA accessibility standards
Zero-trust security model with principle of least privilege
Encryption at rest (AES-256) and in transit (TLS 1.3)

Additional Technical Decisions

Development Tools:

Version Control: Git with GitHub
CI/CD: GitHub Actions
Code Quality: ESLint for unified linting and formatting (replacing ESLint/Prettier), Black (Python)
Monitoring: AWS CloudWatch with custom dashboards
Error Tracking: Sentry for real-time error monitoring
API Testing: Postman collections for QA

Mobile Considerations:

Progressive Web App with service workers
Touch-optimized UI components (48px minimum touch targets)
Offline-first architecture with local storage sync
Camera API integration for invoice capture
Responsive images with WebP support

Data Architecture:

Multi-tenant Isolation: Complete data isolation achieved through separate AWS accounts per client
One Codebase, Multiple Deployments: Single codebase deployed to isolated AWS environments via GitHub Actions and Terraform automation
Infrastructure Automation: Terraform manages environment provisioning, ensuring consistent deployment across all client accounts
Zero Cross-Tenant Risk: Physical isolation at infrastructure level eliminates any possibility of data leakage
Soft deletes for audit trail preservation
Event sourcing for critical financial transactions
Data retention policies (7 years for financial data)

Scalability Planning:

Horizontal scaling via containerization
Database connection pooling
Asynchronous job processing with AWS SQS
Auto-scaling based on CPU and memory metrics
Geographic distribution preparation for Phase 3

Constraints & Assumptions
Constraints

Budget:

MVP Phase: $440,000 (7 months)
Phase 2: $455,000 (6 months)
Annual operational budget post-launch: $276,000
Limited budget for third-party tools and services in MVP

Timeline:

MVP delivery: 7 months (14 sprints)
Citgo pilot must launch by end of Month 7
Phase 2 contingent on successful MVP pilot
SOC 2 audit must be completed by Month 12

Resources:

Development team: ~3 FTE developers
Part-time technical lead (0.25 FTE)
Single UI/UX designer (0.75 FTE)
Limited access to security and compliance specialists
Citgo stakeholder availability limited to bi-weekly reviews

Technical:

Must integrate with existing Django/MySQL system for data migration
Limited to manual processes in MVP (no OCR or advanced automation)
AWS services only (organizational constraint)
Must support low-bandwidth environments for field users
No modifications to legacy Citgo systems during migration

Key Assumptions

MVP will operate successfully with manual CSV import/export until enterprise integrations are built in Phase 3
Citgo will provide clean data exports in standard formats (CSV/JSON) for migration
Field sales reps have access to smartphones with cameras for invoice capture
Internet connectivity is available at least once per day for data synchronization
Existing Django/MySQL system remains stable and accessible throughout migration
Citgo stakeholders will dedicate 10+ hours per sprint for testing starting Sprint 4
No significant changes to commission structures during development
Current manual processes are well-documented and can be replicated in the platform
Browser-based PWA will satisfy mobile needs without native app development
English-only interface is sufficient for MVP and Phase 2
US-based operations only for the first 18 months

Risks & Open Questions
Key Risks

Data Migration Complexity: 9 years of historical data with potential inconsistencies and format variations could extend timeline by 2-4 weeks

Impact: High - Could delay launch and affect data integrity
Mitigation: Phased migration approach, extensive data validation scripts, maintain legacy system access during transition

Field User Adoption: Carlos Martinez persona represents users with limited tech comfort who may resist platform adoption

Impact: Critical - Could result in <50% adoption rate
Mitigation: Extended training program, buddy system, simplified UI, bilingual support consideration for Phase 2

Manual Process Dependencies in MVP: Without OCR and automation, admin burden may not decrease as projected

Impact: Medium - May not achieve 40% efficiency gain target
Mitigation: Optimize CSV import/export tools, batch processing capabilities, clear Phase 2 automation roadmap

Campaign Calculation Accuracy: Complex commission rules with multiple tiers, caps, and product combinations

Impact: High - Financial errors damage trust and require manual reconciliation
Mitigation: Extensive testing framework, calculation audit trails, parallel run with legacy system

Infrastructure Isolation Complexity: Managing multiple AWS accounts increases operational overhead

Impact: Medium - Could slow deployment and increase costs
Mitigation: Robust Terraform automation, centralized monitoring, standardized deployment procedures

Legacy System Brittleness: Django/MySQL system may fail during extended migration period

Impact: High - Could lose access to historical data
Mitigation: Complete system backup before migration, read-only access during transition, accelerated migration timeline

Open Questions
Technical Questions:

What is the exact schema structure of the legacy Django/MySQL database?
Are there any undocumented business rules in the legacy system code?
What is the current invoice image quality from field submissions?
How often do field users truly lack internet connectivity?
What are the specific compliance requirements for each state Citgo operates in?

Business Questions:

Will Citgo require custom campaign types beyond those specified?
What is the acceptable margin of error for commission calculations?
How will disputes be handled during the transition period?
Are there seasonal spikes in claim volume we should plan for?
What constitutes "duplicate" customers - exact name match or fuzzy matching?

Integration Questions:

Will Citgo's IT team provide API access to any existing systems?
Are there specific IP whitelisting requirements for AWS infrastructure?
What SSO provider does Citgo currently use?
Are there any specific data export formats required for accounting systems?
Will we need to support legacy invoice formats indefinitely?

Areas Needing Further Research
User Experience Research:

Shadow field sales reps to understand actual workflow
Analyze current mobile device usage patterns and constraints
Study invoice submission error rates and common mistakes
Research optimal training approaches for low-tech users
Investigate offline usage patterns and data sync requirements

Technical Research:

AWS multi-account management best practices at scale
Django-to-modern-stack migration patterns and tools
OCR accuracy rates for handwritten invoices (Phase 2 prep)
Performance optimization techniques for large transaction volumes
ESLint configuration for monorepo JavaScript/TypeScript projects

Market Research:

Competitor platform analysis for feature parity
Industry-specific compliance requirements evolution
Channel incentive management trends and innovations
Integration requirements for common enterprise systems
Pricing model validation with target market

Compliance Research:

State-by-state tax reporting requirements
Industry-specific regulations for energy sector
Data retention laws across operating regions
Accessibility requirements for enterprise contracts
International expansion regulatory requirements (Phase 3)

## Appendices

### A. Research Summary

#### Market Research Findings

- Channel incentive management market growing at **12% CAGR**
- **68%** of organizations still use spreadsheets for incentive tracking
- Average **ROI improvement of 3.8:1** when moving to automated platforms
- **45% reduction** in payment disputes with transparent, real-time systems

#### Competitive Analysis Highlights

- Most competitors focus on direct sales, not multi-tier channels
- Few solutions offer true multi-tenant architecture
- Mobile-first design is rare in B2B incentive platforms
- Average implementation time: **6-9 months** for enterprise

#### Technical Feasibility Studies

- PWA approach validated for offline functionality
- Terraform multi-account management proven at 50+ account scale
- ESLint performance **3x faster** than ESLint/Prettier combination
- Django migration patterns successfully used in 5 similar projects

### B. Stakeholder Input

#### Citgo Leadership Feedback

- *"Visibility is our biggest pain point - we're flying blind"* - National Sales Manager
- *"My reps spend more time on paperwork than selling"* - Territory Manager
- *"We need to know ROI by program, by territory, by product line"* - CFO

#### Field Sales Rep Survey Results (n=127)

- **73%** use personal smartphones for work
- **Average time per claim**: 18 minutes
- **61%** have lost commission due to paperwork errors
- **Top request**: *"Make it work like my banking app"*

#### Development Team Assessment

- Strong Python/Django expertise on team
- React/TypeScript skills across all developers
- AWS experience primarily in single-account setups
- Terraform experience limited but trainable

### C. References

#### Technical Documentation

- [AWS Multi-Account Strategy](https://docs.aws.amazon.com/whitepapers/latest/organizing-your-aws-environment/)
- [Django Migration Guide](https://docs.djangoproject.com/en/5.0/topics/migrations/)
- [ESLint Configuration](https://ESLintjs.dev/guides/configure-ESLint/)
- [SOC 2 Compliance Framework](https://www.aicpa.org/topic/audit-assurance/audit-and-assurance-greater-than-soc-2)

#### Industry Resources

- Harvard Business Review: "The Hidden Leverage of Channel Incentives" (2023)
- Forrester: "Channel Incentive Management Platforms Market Overview" (2024)
- Gartner: "Magic Quadrant for Sales Performance Management" (2023)

#### Integration Partners

- [Clerk Documentation](https://clerk.com/docs)
- [Stripe Integration Guide](https://stripe.com/docs/payments)
- [AWS SES Best Practices](https://docs.aws.amazon.com/ses/latest/dg/best-practices.html)